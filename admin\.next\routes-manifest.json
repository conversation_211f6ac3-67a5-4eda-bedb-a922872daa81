{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/content/articles/edit/[id]", "regex": "^/content/articles/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/content/articles/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/users/edit/[id]", "regex": "^/users/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/users/edit/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/appointments", "regex": "^/appointments(?:/)?$", "routeKeys": {}, "namedRegex": "^/appointments(?:/)?$"}, {"page": "/consultants", "regex": "^/consultants(?:/)?$", "routeKeys": {}, "namedRegex": "^/consultants(?:/)?$"}, {"page": "/content/articles", "regex": "^/content/articles(?:/)?$", "routeKeys": {}, "namedRegex": "^/content/articles(?:/)?$"}, {"page": "/content/articles/new", "regex": "^/content/articles/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/content/articles/new(?:/)?$"}, {"page": "/content/cases", "regex": "^/content/cases(?:/)?$", "routeKeys": {}, "namedRegex": "^/content/cases(?:/)?$"}, {"page": "/content/cases/new", "regex": "^/content/cases/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/content/cases/new(?:/)?$"}, {"page": "/content/services", "regex": "^/content/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/content/services(?:/)?$"}, {"page": "/content/services/new", "regex": "^/content/services/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/content/services/new(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/inquiries", "regex": "^/inquiries(?:/)?$", "routeKeys": {}, "namedRegex": "^/inquiries(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/logs", "regex": "^/logs(?:/)?$", "routeKeys": {}, "namedRegex": "^/logs(?:/)?$"}, {"page": "/profile", "regex": "^/profile(?:/)?$", "routeKeys": {}, "namedRegex": "^/profile(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/users", "regex": "^/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/users(?:/)?$"}, {"page": "/users/new", "regex": "^/users/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/users/new(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}