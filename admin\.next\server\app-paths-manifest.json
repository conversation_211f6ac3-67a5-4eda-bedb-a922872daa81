{"/_not-found": "app/_not-found.js", "/content/articles/new/page": "app/content/articles/new/page.js", "/consultants/page": "app/consultants/page.js", "/content/banners/new/page": "app/content/banners/new/page.js", "/appointments/page": "app/appointments/page.js", "/content/banners/page": "app/content/banners/page.js", "/content/articles/page": "app/content/articles/page.js", "/content/cases/page": "app/content/cases/page.js", "/content/cases/new/page": "app/content/cases/new/page.js", "/content/faq/page": "app/content/faq/page.js", "/api/auth/route": "app/api/auth/route.js", "/content/services/page": "app/content/services/page.js", "/content/faqs/page": "app/content/faqs/page.js", "/content/services/new/page": "app/content/services/new/page.js", "/dashboard/page": "app/dashboard/page.js", "/content/faqs/new/page": "app/content/faqs/new/page.js", "/inquiries/page": "app/inquiries/page.js", "/login/page": "app/login/page.js", "/logs/page": "app/logs/page.js", "/settings/page": "app/settings/page.js", "/page": "app/page.js", "/users/new/page": "app/users/new/page.js", "/team/page": "app/team/page.js", "/team/new/page": "app/team/new/page.js", "/profile/page": "app/profile/page.js", "/users/page": "app/users/page.js"}