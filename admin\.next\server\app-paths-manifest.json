{"/_not-found": "app/_not-found.js", "/api/auth/route": "app/api/auth/route.js", "/appointments/page": "app/appointments/page.js", "/consultants/page": "app/consultants/page.js", "/content/articles/page": "app/content/articles/page.js", "/content/banners/page": "app/content/banners/page.js", "/content/banners/new/page": "app/content/banners/new/page.js", "/content/articles/new/page": "app/content/articles/new/page.js", "/content/cases/page": "app/content/cases/page.js", "/content/cases/new/page": "app/content/cases/new/page.js", "/content/faqs/new/page": "app/content/faqs/new/page.js", "/content/faqs/page": "app/content/faqs/page.js", "/content/services/new/page": "app/content/services/new/page.js", "/dashboard/page": "app/dashboard/page.js", "/content/faq/page": "app/content/faq/page.js", "/content/services/page": "app/content/services/page.js", "/login/page": "app/login/page.js", "/inquiries/page": "app/inquiries/page.js", "/logs/page": "app/logs/page.js", "/page": "app/page.js", "/profile/page": "app/profile/page.js", "/settings/page": "app/settings/page.js", "/team/page": "app/team/page.js", "/team/new/page": "app/team/new/page.js", "/users/page": "app/users/page.js", "/users/new/page": "app/users/new/page.js"}