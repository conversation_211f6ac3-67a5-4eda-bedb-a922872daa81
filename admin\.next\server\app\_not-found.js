(()=>{var e={};e.id=165,e.ids=[165],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},98074:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=r(50482),n=r(69108),o=r(62563),i=r.n(o),l=r(68300),a={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(t,a);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],d=[],u="/_not-found",h={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89747:(e,t,r)=>{Promise.resolve().then(r.bind(r,67329))},95444:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},99847:(e,t,r)=>{"use strict";r.d(t,{H:()=>a,a:()=>c});var s=r(95344),n=r(3729),o=r(22254),i=r(43932);let l=(0,n.createContext)(void 0);function a({children:e}){let[t,r]=(0,n.useState)(null),[a,c]=(0,n.useState)(!0),d=(0,o.useRouter)(),u=(0,o.usePathname)();(0,n.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(e&&t&&"undefined"!==t&&"null"!==t)try{i.Z.defaults.headers.common.Authorization=`Bearer ${e}`;let s=JSON.parse(t);r(s)}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),"/login"!==u&&d.push("/login")}else"/login"!==u&&d.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{c(!1)}})()},[u,d]);let h=async(e,t)=>{try{console.log("AuthContext: 发送登录请求",{username:e});let s=await i.Z.post("/auth/login",{username:e,password:t});if(console.log("AuthContext: 收到响应",s.data),!s.data||!s.data.data)throw Error("API响应格式错误");let{user:n,token:o}=s.data.data;if(!n||!o)throw Error("响应中缺少用户信息或令牌");return console.log("AuthContext: 解析的用户数据",{user:n,token:o}),localStorage.setItem("adminToken",o),localStorage.setItem("adminUser",JSON.stringify(n)),i.Z.defaults.headers.common.Authorization=`Bearer ${o}`,r(n),console.log("AuthContext: 登录成功，用户状态已更新"),n}catch(e){throw console.error("AuthContext: 登录失败",e),e}};return s.jsx(l.Provider,{value:{user:t,loading:a,login:h,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.Z.defaults.headers.common.Authorization,r(null),d.push("/login")},updateUserInfo:e=>{if(t){let s={...t,...e};r(s),localStorage.setItem("adminUser",JSON.stringify(s))}},isAuthenticated:!!t},children:e})}function c(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(95344);r(3729),r(4047);var n=r(99847),o=r(44669),i=r(22254);function l({children:e}){let{user:t,logout:r,isAuthenticated:o,loading:l}=(0,n.a)(),a=(0,i.usePathname)();return"/login"===a?s.jsx(s.Fragment,{children:e}):l?s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:s.jsx("div",{className:"text-lg",children:"加载中..."})}):o?(0,s.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[s.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[s.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),s.jsx("button",{onClick:r,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,s.jsxs)("div",{className:"flex pt-16",children:[s.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,s.jsxs)("ul",{className:"space-y-2 mt-4",children:[s.jsx("li",{children:s.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===a?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),s.jsx("li",{children:s.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),s.jsx("li",{children:s.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),s.jsx("li",{children:s.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),s.jsx("li",{children:s.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),s.jsx("li",{children:s.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),s.jsx("li",{children:s.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),s.jsx("li",{children:s.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),s.jsx("li",{children:s.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),s.jsx("li",{children:s.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),s.jsx("li",{children:s.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),s.jsx("li",{children:s.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${a.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),s.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[s.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),s.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),s.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function a({children:e}){return s.jsx("html",{lang:"zh",children:s.jsx("body",{children:(0,s.jsxs)(n.H,{children:[s.jsx(l,{children:e}),s.jsx(o.x7,{position:"top-right"})]})})})}},43932:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n,h:()=>s});let s=r(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>e,e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let n=s},82917:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>o,__esModule:()=>n,default:()=>i});let s=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:n,$$typeof:o}=s,i=s.default},4047:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,606],()=>r(98074));module.exports=s})();