(()=>{var e={};e.id=64,e.ids=[64],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},23533:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=s(50482),a=s(69108),n=s(62563),l=s.n(n),i=s(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let d=["",{children:["appointments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,17983)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\appointments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\appointments\\page.tsx"],x="/appointments/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/appointments/page",pathname:"/appointments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93409:(e,t,s)=>{Promise.resolve().then(s.bind(s,12821))},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},12821:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95344),a=s(3729),n=s(99847),l=s(22254),i=s(44669),c=s(43932);function d(){let{isAuthenticated:e,loading:t}=(0,n.a)(),s=(0,l.useRouter)(),[d,x]=(0,a.useState)([]),[u,m]=(0,a.useState)(!0),[h,p]=(0,a.useState)(null),[g,b]=(0,a.useState)(!1),[j,y]=(0,a.useState)("");(0,a.useEffect)(()=>{if(!t&&!e){s.push("/login");return}e&&f()},[t,e,s,j]);let f=async()=>{try{m(!0);let e={};j&&(e.status=j);let t=await c.h.get("/appointments",{params:e});t.data.success&&x(t.data.data.items||t.data.data)}catch(e){console.error("获取预约列表失败:",e),i.ZP.error("获取预约列表失败")}finally{m(!1)}},v=async(e,t)=>{try{await c.h.put(`/appointments/${e}`,{status:t}),i.ZP.success("预约状态更新成功"),f()}catch(e){console.error("状态更新失败:",e),i.ZP.error("状态更新失败")}},N=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-blue-100 text-blue-800";case"completed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"pending":return"待确认";case"confirmed":return"已确认";case"completed":return"已完成";case"cancelled":return"已取消";default:return e}},_=e=>{switch(e){case"education_planning":return"教育规划";case"career_planning":return"职业规划";case"study_abroad":return"留学咨询";case"psychological_counseling":return"心理咨询";case"learning_improvement":return"学习能力提升";default:return e||"其他"}};return t||!e?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"预约管理"}),r.jsx("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("select",{value:j,onChange:e=>y(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md",children:[r.jsx("option",{value:"",children:"全部状态"}),r.jsx("option",{value:"pending",children:"待确认"}),r.jsx("option",{value:"confirmed",children:"已确认"}),r.jsx("option",{value:"completed",children:"已完成"}),r.jsx("option",{value:"cancelled",children:"已取消"})]})})]}),u?r.jsx("div",{className:"text-center py-8",children:"加载中..."}):r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"预约编号"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"客户信息"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"咨询师"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"预约时间"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"服务类型"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"费用"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.appointment_number}),r.jsx("div",{className:"text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.client_name}),r.jsx("div",{className:"text-sm text-gray-500",children:e.client_email}),r.jsx("div",{className:"text-sm text-gray-500",children:e.client_phone})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.consultant_name}),r.jsx("div",{className:"text-sm text-gray-500",children:e.consultant_specialty})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm text-gray-900",children:e.appointment_date}),r.jsx("div",{className:"text-sm text-gray-500",children:e.appointment_time}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.duration,"分钟"]})]}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("div",{className:"text-sm text-gray-900",children:_(e.service_type)})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${N(e.status)}`,children:w(e.status)})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["\xa5",e.total_amount]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[r.jsx("button",{onClick:()=>{p(e),b(!0)},className:"text-blue-600 hover:text-blue-900 mr-3",children:"查看详情"}),(0,r.jsxs)("select",{value:e.status,onChange:t=>v(e.id,t.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1",children:[r.jsx("option",{value:"pending",children:"待确认"}),r.jsx("option",{value:"confirmed",children:"已确认"}),r.jsx("option",{value:"completed",children:"已完成"}),r.jsx("option",{value:"cancelled",children:"已取消"})]})]})]},e.id))})]})}),g&&h&&r.jsx(o,{appointment:h,onClose:()=>{b(!1),p(null)},onUpdate:()=>{b(!1),p(null),f()}})]})}function o({appointment:e,onClose:t,onUpdate:s}){let[n,l]=(0,a.useState)(e.admin_notes||""),[d,o]=(0,a.useState)(!1),x=async()=>{o(!0);try{await c.h.put(`/appointments/${e.id}`,{admin_notes:n}),i.ZP.success("备注更新成功"),s()}catch(e){console.error("更新备注失败:",e),i.ZP.error("更新备注失败")}finally{o(!1)}};return r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[r.jsx("h2",{className:"text-xl font-bold mb-4",children:"预约详情"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"预约信息"}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"预约编号:"})," ",e.appointment_number]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"预约时间:"})," ",e.appointment_date," ",e.appointment_time]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"咨询时长:"})," ",e.duration,"分钟"]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"服务类型:"})," ",e.service_type]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"费用:"})," \xa5",e.total_amount]})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"客户信息"}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"姓名:"})," ",e.client_name]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"邮箱:"})," ",e.client_email]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"电话:"})," ",e.client_phone]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"咨询师信息"}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"姓名:"})," ",e.consultant_name]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"专业:"})," ",e.consultant_specialty]})]}),e.message&&(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"客户需求"}),r.jsx("p",{className:"bg-gray-50 p-3 rounded",children:e.message})]}),e.client_feedback&&(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"客户反馈"}),r.jsx("p",{className:"bg-blue-50 p-3 rounded",children:e.client_feedback}),e.rating&&(0,r.jsxs)("p",{className:"mt-2",children:[r.jsx("strong",{children:"评分:"})," ",e.rating,"/5"]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"管理员备注"}),r.jsx("textarea",{value:n,onChange:e=>l(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"添加管理员备注..."}),r.jsx("button",{onClick:x,disabled:d,className:"mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300",children:d?"更新中...":"更新备注"})]}),r.jsx("div",{className:"flex justify-end",children:r.jsx("button",{onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50",children:"关闭"})})]})})}},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>c,a:()=>d});var r=s(95344),a=s(3729),n=s(22254),l=s(43932);let i=(0,a.createContext)(void 0);function c({children:e}){let[t,s]=(0,a.useState)(null),[c,d]=(0,a.useState)(!0),o=(0,n.useRouter)(),x=(0,n.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(e&&t&&"undefined"!==t&&"null"!==t)try{l.Z.defaults.headers.common.Authorization=`Bearer ${e}`;let r=JSON.parse(t);s(r)}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),"/login"!==x&&o.push("/login")}else"/login"!==x&&o.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[x,o]);let u=async(e,t)=>{try{let{user:r,token:a}=(await l.Z.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),l.Z.defaults.headers.common.Authorization=`Bearer ${a}`,s(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(i.Provider,{value:{user:t,loading:c,login:u,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete l.Z.defaults.headers.common.Authorization,s(null),o.push("/login")},updateUserInfo:e=>{if(t){let r={...t,...e};s(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!t},children:e})}function d(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95344);s(3729),s(4047);var a=s(99847),n=s(44669),l=s(22254);function i({children:e}){let{user:t,logout:s,isAuthenticated:n,loading:i}=(0,a.a)(),c=(0,l.usePathname)();return"/login"===c?r.jsx(r.Fragment,{children:e}):i?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):n?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===c?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function c({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(i,{children:e}),r.jsx(n.x7,{position:"top-right"})]})})})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},17983:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\appointments\page.tsx`),{__esModule:a,$$typeof:n}=r,l=r.default},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:n}=r,l=r.default},4047:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606],()=>s(23533));module.exports=r})();