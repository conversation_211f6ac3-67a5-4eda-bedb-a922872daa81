(()=>{var e={};e.id=64,e.ids=[64],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},23533:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=s(50482),a=s(69108),n=s(62563),i=s.n(n),l=s(68300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let d=["",{children:["appointments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,17983)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\appointments\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\appointments\\page.tsx"],x="/appointments/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/appointments/page",pathname:"/appointments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93409:(e,t,s)=>{Promise.resolve().then(s.bind(s,12821))},12821:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95344),a=s(3729),n=s(99847),i=s(22254),l=s(44669),c=s(43932);function d(){let{isAuthenticated:e,loading:t}=(0,n.a)(),s=(0,i.useRouter)(),[d,x]=(0,a.useState)([]),[p,m]=(0,a.useState)(!0),[u,h]=(0,a.useState)(null),[g,j]=(0,a.useState)(!1),[y,b]=(0,a.useState)("");(0,a.useEffect)(()=>{if(!t&&!e){s.push("/login");return}e&&f()},[t,e,s,y]);let f=async()=>{try{m(!0);let e={};y&&(e.status=y),console.log("开始获取预约数据...");let t=await c.Z.get("/appointments",{params:e});if(console.log("预约API响应:",t.data),t.data.success){let e=t.data.data.items||t.data.data||[];console.log("解析的预约数据:",e),x(e)}else console.error("API返回失败状态:",t.data),l.ZP.error("获取预约列表失败")}catch(e){console.error("获取预约列表失败:",e),l.ZP.error("获取预约列表失败")}finally{m(!1)}},v=async(e,t)=>{try{await c.Z.put(`/appointments/${e}`,{status:t}),l.ZP.success("预约状态更新成功"),f()}catch(e){console.error("状态更新失败:",e),l.ZP.error("状态更新失败")}},N=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"confirmed":return"bg-blue-100 text-blue-800";case"completed":return"bg-green-100 text-green-800";case"cancelled":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},w=e=>{switch(e){case"pending":return"待确认";case"confirmed":return"已确认";case"completed":return"已完成";case"cancelled":return"已取消";default:return e}},_=e=>{switch(e){case"education_planning":return"教育规划";case"career_planning":return"职业规划";case"study_abroad":return"留学咨询";case"psychological_counseling":return"心理咨询";case"learning_improvement":return"学习能力提升";default:return e||"其他"}};return t||!e?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"预约管理"}),r.jsx("div",{className:"flex items-center space-x-4",children:(0,r.jsxs)("select",{value:y,onChange:e=>b(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md",children:[r.jsx("option",{value:"",children:"全部状态"}),r.jsx("option",{value:"pending",children:"待确认"}),r.jsx("option",{value:"confirmed",children:"已确认"}),r.jsx("option",{value:"completed",children:"已完成"}),r.jsx("option",{value:"cancelled",children:"已取消"})]})})]}),p?r.jsx("div",{className:"text-center py-8",children:"加载中..."}):r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"预约编号"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"客户信息"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"咨询师"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"预约时间"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"服务类型"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"费用"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.appointment_number}),r.jsx("div",{className:"text-sm text-gray-500",children:new Date(e.created_at).toLocaleDateString()})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.client_name}),r.jsx("div",{className:"text-sm text-gray-500",children:e.client_email}),r.jsx("div",{className:"text-sm text-gray-500",children:e.client_phone})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.consultant_name}),r.jsx("div",{className:"text-sm text-gray-500",children:e.consultant_specialty})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm text-gray-900",children:e.appointment_date}),r.jsx("div",{className:"text-sm text-gray-500",children:e.appointment_time}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.duration,"分钟"]})]}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("div",{className:"text-sm text-gray-900",children:_(e.service_type)})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${N(e.status)}`,children:w(e.status)})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["\xa5",e.total_amount]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[r.jsx("button",{onClick:()=>{h(e),j(!0)},className:"text-blue-600 hover:text-blue-900 mr-3",children:"查看详情"}),(0,r.jsxs)("select",{value:e.status,onChange:t=>v(e.id,t.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1",children:[r.jsx("option",{value:"pending",children:"待确认"}),r.jsx("option",{value:"confirmed",children:"已确认"}),r.jsx("option",{value:"completed",children:"已完成"}),r.jsx("option",{value:"cancelled",children:"已取消"})]})]})]},e.id))})]})}),g&&u&&r.jsx(o,{appointment:u,onClose:()=>{j(!1),h(null)},onUpdate:()=>{j(!1),h(null),f()}})]})}function o({appointment:e,onClose:t,onUpdate:s}){let[n,i]=(0,a.useState)(e.admin_notes||""),[d,o]=(0,a.useState)(!1),x=async()=>{o(!0);try{await c.Z.put(`/appointments/${e.id}`,{admin_notes:n}),l.ZP.success("备注更新成功"),s()}catch(e){console.error("更新备注失败:",e),l.ZP.error("更新备注失败")}finally{o(!1)}};return r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[r.jsx("h2",{className:"text-xl font-bold mb-4",children:"预约详情"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"预约信息"}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"预约编号:"})," ",e.appointment_number]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"预约时间:"})," ",e.appointment_date," ",e.appointment_time]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"咨询时长:"})," ",e.duration,"分钟"]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"服务类型:"})," ",e.service_type]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"费用:"})," \xa5",e.total_amount]})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"客户信息"}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"姓名:"})," ",e.client_name]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"邮箱:"})," ",e.client_email]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"电话:"})," ",e.client_phone]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"咨询师信息"}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"姓名:"})," ",e.consultant_name]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"专业:"})," ",e.consultant_specialty]})]}),e.message&&(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"客户需求"}),r.jsx("p",{className:"bg-gray-50 p-3 rounded",children:e.message})]}),e.client_feedback&&(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"客户反馈"}),r.jsx("p",{className:"bg-blue-50 p-3 rounded",children:e.client_feedback}),e.rating&&(0,r.jsxs)("p",{className:"mt-2",children:[r.jsx("strong",{children:"评分:"})," ",e.rating,"/5"]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h3",{className:"font-semibold text-gray-700 mb-2",children:"管理员备注"}),r.jsx("textarea",{value:n,onChange:e=>i(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"添加管理员备注..."}),r.jsx("button",{onClick:x,disabled:d,className:"mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300",children:d?"更新中...":"更新备注"})]}),r.jsx("div",{className:"flex justify-end",children:r.jsx("button",{onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50",children:"关闭"})})]})})}},17983:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\appointments\page.tsx`),{__esModule:a,$$typeof:n}=r,i=r.default}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,300,238],()=>s(23533));module.exports=r})();