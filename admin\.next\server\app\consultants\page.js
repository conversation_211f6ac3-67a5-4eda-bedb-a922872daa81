(()=>{var e={};e.id=47,e.ids=[47],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},17828:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>o});var r=s(50482),a=s(69108),l=s(62563),n=s.n(l),i=s(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let o=["",{children:["consultants",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21880)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\consultants\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\consultants\\page.tsx"],u="/consultants/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/consultants/page",pathname:"/consultants",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},42927:(e,t,s)=>{Promise.resolve().then(s.bind(s,44703))},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>c,a:()=>o});var r=s(95344),a=s(3729),l=s(22254),n=s(43932);let i=(0,a.createContext)(void 0);function c({children:e}){let[t,s]=(0,a.useState)(null),[c,o]=(0,a.useState)(!0),d=(0,l.useRouter)(),u=(0,l.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(e&&t&&"undefined"!==t&&"null"!==t)try{n.Z.defaults.headers.common.Authorization=`Bearer ${e}`;let r=JSON.parse(t);s(r)}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),"/login"!==u&&d.push("/login")}else"/login"!==u&&d.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{o(!1)}})()},[u,d]);let x=async(e,t)=>{try{console.log("AuthContext: 发送登录请求",{username:e});let r=await n.Z.post("/auth/login",{username:e,password:t});if(console.log("AuthContext: 收到响应",r.data),!r.data||!r.data.data)throw Error("API响应格式错误");let{user:a,token:l}=r.data.data;if(!a||!l)throw Error("响应中缺少用户信息或令牌");return console.log("AuthContext: 解析的用户数据",{user:a,token:l}),localStorage.setItem("adminToken",l),localStorage.setItem("adminUser",JSON.stringify(a)),n.Z.defaults.headers.common.Authorization=`Bearer ${l}`,s(a),console.log("AuthContext: 登录成功，用户状态已更新"),a}catch(e){throw console.error("AuthContext: 登录失败",e),e}};return r.jsx(i.Provider,{value:{user:t,loading:c,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete n.Z.defaults.headers.common.Authorization,s(null),d.push("/login")},updateUserInfo:e=>{if(t){let r={...t,...e};s(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!t},children:e})}function o(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},44703:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(95344),a=s(3729),l=s(99847),n=s(22254),i=s(44669),c=s(43932);function o(){let{isAuthenticated:e,loading:t}=(0,l.a)(),s=(0,n.useRouter)(),[o,u]=(0,a.useState)([]),[x,m]=(0,a.useState)(!0),[h,p]=(0,a.useState)(!1),[g,b]=(0,a.useState)(null);(0,a.useEffect)(()=>{if(!t&&!e){s.push("/login");return}e&&y()},[t,e,s]);let y=async()=>{try{m(!0);let e=await c.h.get("/consultants");e.data.success&&u(e.data.data.items||e.data.data)}catch(e){console.error("获取咨询师列表失败:",e),i.ZP.error("获取咨询师列表失败")}finally{m(!1)}},j=async e=>{if(confirm("确定要删除这个咨询师吗？"))try{await c.h.delete(`/consultants/${e}`),i.ZP.success("咨询师删除成功"),y()}catch(e){console.error("删除咨询师失败:",e),i.ZP.error("删除咨询师失败")}};return t||!e?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"咨询师管理"}),r.jsx("button",{onClick:()=>p(!0),className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"新增咨询师"})]}),x?r.jsx("div",{className:"text-center py-8",children:"加载中..."}):r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"咨询师信息"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"专业领域"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"经验/费用"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"评分/预约数"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:o.map(e=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.avatar_url?r.jsx("img",{className:"h-10 w-10 rounded-full object-cover",src:e.avatar_url,alt:e.name}):r.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:r.jsx("span",{className:"text-sm font-medium text-gray-700",children:e.name.charAt(0)})})}),(0,r.jsxs)("div",{className:"ml-4",children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),r.jsx("div",{className:"text-sm text-gray-500",children:e.email}),r.jsx("div",{className:"text-sm text-gray-500",children:e.phone})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm text-gray-900",children:e.specialty}),r.jsx("div",{className:"text-sm text-gray-500",children:e.education})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:[e.experience_years,"年经验"]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["\xa5",e.hourly_rate,"/小时"]})]}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-900",children:["评分: ",e.rating]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["预约: ",e.total_appointments,"次"]})]}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"active"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:"active"===e.status?"活跃":"禁用"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[r.jsx("button",{onClick:()=>b(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:"编辑"}),r.jsx("button",{onClick:()=>j(e.id),className:"text-red-600 hover:text-red-900",children:"删除"})]})]},e.id))})]})}),(h||g)&&r.jsx(d,{consultant:g,onClose:()=>{p(!1),b(null)},onSuccess:()=>{p(!1),b(null),y()}})]})}function d({consultant:e,onClose:t,onSuccess:s}){let[l,n]=(0,a.useState)({name:e?.name||"",email:e?.email||"",phone:e?.phone||"",specialty:e?.specialty||"",experience_years:e?.experience_years||0,education:e?.education||"",bio:e?.bio||"",avatar_url:e?.avatar_url||"",hourly_rate:e?.hourly_rate||0,status:e?.status||"active"}),[o,d]=(0,a.useState)(!1),u=async t=>{t.preventDefault(),d(!0);try{e?(await c.h.put(`/consultants/${e.id}`,l),i.ZP.success("咨询师更新成功")):(await c.h.post("/consultants",l),i.ZP.success("咨询师创建成功")),s()}catch(t){console.error("操作失败:",t),i.ZP.error(e?"咨询师更新失败":"咨询师创建失败")}finally{d(!1)}};return r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[r.jsx("h2",{className:"text-xl font-bold mb-4",children:e?"编辑咨询师":"新增咨询师"}),(0,r.jsxs)("form",{onSubmit:u,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"姓名"}),r.jsx("input",{type:"text",value:l.name,onChange:e=>n({...l,name:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"邮箱"}),r.jsx("input",{type:"email",value:l.email,onChange:e=>n({...l,email:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"电话"}),r.jsx("input",{type:"tel",value:l.phone,onChange:e=>n({...l,phone:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"专业领域"}),r.jsx("input",{type:"text",value:l.specialty,onChange:e=>n({...l,specialty:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"如：教育规划,升学指导",required:!0})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"工作经验(年)"}),r.jsx("input",{type:"number",value:l.experience_years,onChange:e=>n({...l,experience_years:parseInt(e.target.value)}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",min:"0",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"时薪(元)"}),r.jsx("input",{type:"number",value:l.hourly_rate,onChange:e=>n({...l,hourly_rate:parseInt(e.target.value)}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",min:"0",required:!0})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"教育背景"}),r.jsx("input",{type:"text",value:l.education,onChange:e=>n({...l,education:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"如：华中师范大学教育学博士",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"头像URL"}),r.jsx("input",{type:"url",value:l.avatar_url,onChange:e=>n({...l,avatar_url:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"https://example.com/avatar.jpg"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"个人简介"}),r.jsx("textarea",{value:l.bio,onChange:e=>n({...l,bio:e.target.value}),rows:4,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"请输入个人简介和专业背景...",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"状态"}),(0,r.jsxs)("select",{value:l.status,onChange:e=>n({...l,status:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",children:[r.jsx("option",{value:"active",children:"活跃"}),r.jsx("option",{value:"inactive",children:"禁用"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[r.jsx("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50",children:"取消"}),r.jsx("button",{type:"submit",disabled:o,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300",children:o?"保存中...":"保存"})]})]})]})})}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95344);s(3729),s(4047);var a=s(99847),l=s(44669),n=s(22254);function i({children:e}){let{user:t,logout:s,isAuthenticated:l,loading:i}=(0,a.a)(),c=(0,n.usePathname)();return"/login"===c?r.jsx(r.Fragment,{children:e}):i?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):l?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===c?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function c({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(i,{children:e}),r.jsx(l.x7,{position:"top-right"})]})})})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},21880:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\consultants\page.tsx`),{__esModule:a,$$typeof:l}=r,n=r.default},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:l}=r,n=r.default},4047:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606],()=>s(17828));module.exports=r})();