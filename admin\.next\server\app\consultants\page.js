(()=>{var e={};e.id=47,e.ids=[47],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},17828:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=s(50482),r=s(69108),l=s(62563),i=s.n(l),n=s(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d=["",{children:["consultants",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,21880)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\consultants\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\consultants\\page.tsx"],x="/consultants/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/consultants/page",pathname:"/consultants",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},42927:(e,t,s)=>{Promise.resolve().then(s.bind(s,44703))},44703:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(95344),r=s(3729),l=s(99847),i=s(22254),n=s(44669),c=s(43932);function d(){let{isAuthenticated:e,loading:t}=(0,l.a)(),s=(0,i.useRouter)(),[d,x]=(0,r.useState)([]),[u,m]=(0,r.useState)(!0),[p,h]=(0,r.useState)(!1),[g,y]=(0,r.useState)(null);(0,r.useEffect)(()=>{if(!t&&!e){s.push("/login");return}e&&b()},[t,e,s]);let b=async()=>{try{m(!0),console.log("开始获取咨询师数据...");let e=await c.Z.get("/consultants");if(console.log("API响应:",e.data),e.data.success){let t=e.data.data.items||e.data.data||[];console.log("解析的咨询师数据:",t),x(t)}else console.error("API返回失败状态:",e.data),n.ZP.error("获取咨询师列表失败")}catch(e){console.error("获取咨询师列表失败:",e),n.ZP.error("获取咨询师列表失败")}finally{m(!1)}},j=async e=>{if(confirm("确定要删除这个咨询师吗？"))try{await c.Z.delete(`/consultants/${e}`),n.ZP.success("咨询师删除成功"),b()}catch(e){console.error("删除咨询师失败:",e),n.ZP.error("删除咨询师失败")}};return t||!e?a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:a.jsx("div",{className:"text-lg",children:"加载中..."})}):(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h1",{className:"text-2xl font-bold",children:"咨询师管理"}),a.jsx("button",{onClick:()=>h(!0),className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"新增咨询师"})]}),u?a.jsx("div",{className:"text-center py-8",children:"加载中..."}):a.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsxs)("table",{className:"min-w-full",children:[a.jsx("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"咨询师信息"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"专业领域"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"经验/费用"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"预约统计"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),a.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),a.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:d.map(e=>(0,a.jsxs)("tr",{children:[a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:e.avatar_url?a.jsx("img",{className:"h-10 w-10 rounded-full object-cover",src:e.avatar_url,alt:e.name}):a.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:a.jsx("span",{className:"text-sm font-medium text-gray-700",children:e.name.charAt(0)})})}),(0,a.jsxs)("div",{className:"ml-4",children:[a.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),a.jsx("div",{className:"text-sm text-gray-500",children:e.email}),a.jsx("div",{className:"text-sm text-gray-500",children:e.phone})]})]})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[a.jsx("div",{className:"text-sm text-gray-900",children:e.specialty}),a.jsx("div",{className:"text-sm text-gray-500",children:e.education})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:[e.experience_years,"年经验"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["\xa5",e.hourly_rate,"/小时"]})]}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-900",children:["总预约: ",e.total_appointments||0,"次"]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["状态: ","active"===e.status?"可预约":"暂停服务"]})]}),a.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.jsx("span",{className:`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${"active"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:"active"===e.status?"活跃":"禁用"})}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[a.jsx("button",{onClick:()=>y(e),className:"text-blue-600 hover:text-blue-900 mr-3",children:"编辑"}),a.jsx("button",{onClick:()=>j(e.id),className:"text-red-600 hover:text-red-900",children:"删除"})]})]},e.id))})]})}),(p||g)&&a.jsx(o,{consultant:g,onClose:()=>{h(!1),y(null)},onSuccess:()=>{h(!1),y(null),b()}}),a.jsx(n.x7,{position:"top-right"})]})}function o({consultant:e,onClose:t,onSuccess:s}){let[l,i]=(0,r.useState)({name:e?.name||"",email:e?.email||"",phone:e?.phone||"",specialty:e?.specialty||"",experience_years:e?.experience_years||0,education:e?.education||"",certifications:e?.certifications?Array.isArray(e.certifications)?e.certifications.join(", "):e.certifications:"",bio:e?.bio||"",avatar_url:e?.avatar_url||"",hourly_rate:e?.hourly_rate||0,languages:e?.languages?Array.isArray(e.languages)?e.languages.join(", "):e.languages:"",available_hours:e?JSON.stringify(e.available_hours||{}):"{}",status:e?.status||"active"}),[d,o]=(0,r.useState)(!1),u=async t=>{t.preventDefault(),o(!0);try{let t={...l,certifications:l.certifications.split(",").map(e=>e.trim()).filter(e=>e),languages:l.languages.split(",").map(e=>e.trim()).filter(e=>e),available_hours:l.available_hours?JSON.parse(l.available_hours):{}};e?(await c.Z.put(`/consultants/${e.id}`,t),n.ZP.success("咨询师更新成功")):(await c.Z.post("/consultants",t),n.ZP.success("咨询师创建成功")),s()}catch(t){console.error("操作失败:",t),n.ZP.error(e?"咨询师更新失败":"咨询师创建失败")}finally{o(!1)}};return a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto",children:[a.jsx("h2",{className:"text-xl font-bold mb-4",children:e?"编辑咨询师":"新增咨询师"}),(0,a.jsxs)("form",{onSubmit:u,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"姓名"}),a.jsx("input",{type:"text",value:l.name,onChange:e=>i({...l,name:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"邮箱"}),a.jsx("input",{type:"email",value:l.email,onChange:e=>i({...l,email:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"电话"}),a.jsx("input",{type:"tel",value:l.phone,onChange:e=>i({...l,phone:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"专业领域"}),a.jsx("input",{type:"text",value:l.specialty,onChange:e=>i({...l,specialty:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"如：教育规划,升学指导",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"工作经验(年)"}),a.jsx("input",{type:"number",value:l.experience_years,onChange:e=>i({...l,experience_years:parseInt(e.target.value)}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",min:"0",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"时薪(元)"}),a.jsx("input",{type:"number",value:l.hourly_rate,onChange:e=>i({...l,hourly_rate:parseInt(e.target.value)}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",min:"0",required:!0})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"教育背景"}),a.jsx("input",{type:"text",value:l.education,onChange:e=>i({...l,education:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"如：华中师范大学教育学博士",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"资质证书"}),a.jsx("input",{type:"text",value:l.certifications,onChange:e=>i({...l,certifications:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"多个证书用逗号分隔，如：国家认证教育规划师, 高级职业规划师"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"语言能力"}),a.jsx("input",{type:"text",value:l.languages,onChange:e=>i({...l,languages:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"多种语言用逗号分隔，如：中文, 英语, 法语"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"头像URL"}),a.jsx("input",{type:"url",value:l.avatar_url,onChange:e=>i({...l,avatar_url:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"https://example.com/avatar.jpg"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"个人简介"}),a.jsx("textarea",{value:l.bio,onChange:e=>i({...l,bio:e.target.value}),rows:4,className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"请输入个人简介和专业背景...",required:!0})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"可预约时间设置"}),a.jsx(x,{value:l.available_hours,onChange:e=>i({...l,available_hours:e})})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"状态"}),(0,a.jsxs)("select",{value:l.status,onChange:e=>i({...l,status:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md",children:[a.jsx("option",{value:"active",children:"活跃"}),a.jsx("option",{value:"inactive",children:"禁用"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[a.jsx("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50",children:"取消"}),a.jsx("button",{type:"submit",disabled:d,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300",children:d?"保存中...":"保存"})]})]})]})})}function x({value:e,onChange:t}){let[s,l]=(0,r.useState)(()=>{try{return JSON.parse(e||"{}")}catch{return{}}}),i=["09:00","09:30","10:00","10:30","11:00","11:30","12:00","12:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00","17:30","18:00","18:30","19:00","19:30","20:00","20:30"],n=e=>{l(e),t(JSON.stringify(e))},c=e=>{let t={...s};t[e]||(t[e]=[]),t[e].push("09:00-10:00"),n(t)},d=(e,t)=>{let a={...s};a[e]&&(a[e].splice(t,1),0===a[e].length&&delete a[e]),n(a)},o=(e,t,a)=>{let r={...s};r[e]||(r[e]=[]),r[e][t]=a,n(r)};return(0,a.jsxs)("div",{className:"border border-gray-300 rounded-md p-4 space-y-4",children:[[{key:"monday",label:"周一"},{key:"tuesday",label:"周二"},{key:"wednesday",label:"周三"},{key:"thursday",label:"周四"},{key:"friday",label:"周五"},{key:"saturday",label:"周六"},{key:"sunday",label:"周日"}].map(({key:e,label:t})=>(0,a.jsxs)("div",{className:"border-b border-gray-200 pb-3 last:border-b-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[a.jsx("h4",{className:"font-medium text-gray-700",children:t}),a.jsx("button",{type:"button",onClick:()=>c(e),className:"text-sm bg-blue-100 text-blue-600 px-2 py-1 rounded hover:bg-blue-200",children:"+ 添加时间段"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(s[e]||[]).map((t,s)=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("select",{value:t.split("-")[0]||"09:00",onChange:a=>{let r=t.split("-")[1]||"10:00";o(e,s,`${a.target.value}-${r}`)},className:"px-2 py-1 border border-gray-300 rounded text-sm",children:i.map(e=>a.jsx("option",{value:e,children:e},e))}),a.jsx("span",{className:"text-gray-500",children:"至"}),a.jsx("select",{value:t.split("-")[1]||"10:00",onChange:a=>{let r=t.split("-")[0]||"09:00";o(e,s,`${r}-${a.target.value}`)},className:"px-2 py-1 border border-gray-300 rounded text-sm",children:i.map(e=>a.jsx("option",{value:e,children:e},e))}),a.jsx("button",{type:"button",onClick:()=>d(e,s),className:"text-red-600 hover:text-red-800 text-sm",children:"删除"})]},s)),(!s[e]||0===s[e].length)&&a.jsx("div",{className:"text-sm text-gray-500",children:"暂无可预约时间"})]})]},e)),(0,a.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded",children:[a.jsx("strong",{children:"说明："}),"设置每周的可预约时间段。客户只能在这些时间段内预约咨询服务。"]})]})}},21880:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>r,default:()=>i});let a=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\consultants\page.tsx`),{__esModule:r,$$typeof:l}=a,i=a.default}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,300,238],()=>s(17828));module.exports=a})();