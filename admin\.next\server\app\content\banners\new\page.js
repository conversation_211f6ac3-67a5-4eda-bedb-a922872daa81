(()=>{var e={};e.id=886,e.ids=[886],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},62019:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(50482),a=s(69108),n=s(62563),i=s.n(n),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["content",{children:["banners",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,88081)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\banners\\new\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\banners\\new\\page.tsx"],m="/content/banners/new/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/content/banners/new/page",pathname:"/content/banners/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},40989:(e,t,s)=>{Promise.resolve().then(s.bind(s,87689))},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>o,a:()=>d});var r=s(95344),a=s(3729),n=s(22254),i=s(43932);let l=(0,a.createContext)(void 0);function o({children:e}){let[t,s]=(0,a.useState)(null),[o,d]=(0,a.useState)(!0),c=(0,n.useRouter)(),m=(0,n.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(e&&t&&"undefined"!==t&&"null"!==t)try{i.Z.defaults.headers.common.Authorization=`Bearer ${e}`;let r=JSON.parse(t);s(r)}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),"/login"!==m&&c.push("/login")}else"/login"!==m&&c.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[m,c]);let u=async(e,t)=>{try{let{user:r,token:a}=(await i.Z.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),i.Z.defaults.headers.common.Authorization=`Bearer ${a}`,s(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(l.Provider,{value:{user:t,loading:o,login:u,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.Z.defaults.headers.common.Authorization,s(null),c.push("/login")},updateUserInfo:e=>{if(t){let r={...t,...e};s(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!t},children:e})}function d(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},87689:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(95344),a=s(3729),n=s(22254),i=s(60708),l=s(44669),o=s(32456),d=s(20783),c=s.n(d),m=s(43932);function u(){let e=(0,n.useRouter)(),{register:t,handleSubmit:s,formState:{errors:d},watch:u,setValue:x}=(0,i.cI)(),[h,p]=(0,a.useState)(!1),[g,b]=(0,a.useState)(null),j=async t=>{p(!0);let s=new FormData;s.append("title",t.title),t.image&&t.image[0]&&s.append("image",t.image[0]),s.append("link",t.link),s.append("position",t.position),s.append("order",t.order.toString()),s.append("status",t.status),t.startDate&&s.append("startDate",t.startDate),t.endDate&&s.append("endDate",t.endDate);try{await m.h.post("/content/banners",s,{headers:{"Content-Type":"multipart/form-data"}}),l.ZP.success("Banner创建成功！"),e.push("/content/banners")}catch(e){console.error("创建Banner失败:",e),l.ZP.error("创建Banner失败，请稍后再试。")}finally{p(!1)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[r.jsx(l.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-800",children:"创建新Banner"}),r.jsx(c(),{href:"/content/banners",children:(0,r.jsxs)("button",{className:"bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(o.Ao2,{className:"mr-2"}),"返回列表"]})})]}),(0,r.jsxs)("form",{onSubmit:s(j),className:"bg-white p-8 rounded-xl shadow-xl space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["Banner标题 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"text",id:"title",...t("title",{required:"Banner标题不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.title?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),d.title&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.title.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"image",className:"block text-sm font-medium text-gray-700 mb-1",children:["Banner图片 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"mt-1 flex items-center space-x-4",children:[r.jsx("span",{className:"inline-block h-24 w-48 rounded-md overflow-hidden bg-gray-100",children:g?r.jsx("img",{src:g,alt:"Banner预览",className:"h-full w-full object-cover"}):r.jsx("div",{className:"h-full w-full flex items-center justify-center text-gray-400",children:r.jsx(o.Yjd,{className:"h-8 w-8"})})}),r.jsx("label",{htmlFor:"image-upload",className:"cursor-pointer bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out text-sm",children:"选择图片"}),r.jsx("input",{id:"image-upload",type:"file",accept:"image/*",...t("image",{required:"Banner图片不能为空"}),className:"sr-only",onChange:e=>{if(e.target.files&&e.target.files[0]){let t=e.target.files[0];b(URL.createObjectURL(t)),x("image",e.target.files)}}})]}),d.image&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.image.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"link",className:"block text-sm font-medium text-gray-700 mb-1",children:["链接地址 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"text",id:"link",...t("link",{required:"链接地址不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.link?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`,placeholder:"例如: /services/study-abroad"}),d.link&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.link.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-1",children:["显示位置 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"position",...t("position",{required:"显示位置不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.position?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`,children:[r.jsx("option",{value:"",children:"选择位置"}),r.jsx("option",{value:"home_top",children:"首页顶部"}),r.jsx("option",{value:"home_middle",children:"首页中部"}),r.jsx("option",{value:"services_page",children:"服务列表页"}),r.jsx("option",{value:"cases_page",children:"案例列表页"})]}),d.position&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.position.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"order",className:"block text-sm font-medium text-gray-700 mb-1",children:["排序 (数字越小越靠前) ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"number",id:"order",...t("order",{required:"排序不能为空",valueAsNumber:!0,min:{value:0,message:"排序不能小于0"}}),className:`mt-1 block w-full px-4 py-2 border ${d.order?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`,defaultValue:0}),d.order&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.order.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-1",children:["状态 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"status",...t("status",{required:"状态不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.status?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`,defaultValue:"active",children:[r.jsx("option",{value:"active",children:"启用"}),r.jsx("option",{value:"inactive",children:"禁用"})]}),d.status&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.status.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"startDate",className:"block text-sm font-medium text-gray-700 mb-1",children:"开始日期 (可选)"}),r.jsx("input",{type:"date",id:"startDate",...t("startDate"),className:"mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"endDate",className:"block text-sm font-medium text-gray-700 mb-1",children:"结束日期 (可选)"}),r.jsx("input",{type:"date",id:"endDate",...t("endDate"),className:"mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})]})]}),r.jsx("div",{className:"flex justify-end pt-4",children:(0,r.jsxs)("button",{type:"submit",disabled:h,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[h?(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):r.jsx(o.mW3,{className:"mr-2"}),h?"正在保存...":"保存Banner"]})})]})]})}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(95344);s(3729),s(4047);var a=s(99847),n=s(44669),i=s(22254);function l({children:e}){let{user:t,logout:s,isAuthenticated:n,loading:l}=(0,a.a)(),o=(0,i.usePathname)();return"/login"===o?r.jsx(r.Fragment,{children:e}):l?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):n?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===o?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function o({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(l,{children:e}),r.jsx(n.x7,{position:"top-right"})]})})})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},88081:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\banners\new\page.tsx`),{__esModule:a,$$typeof:n}=r,i=r.default},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:n}=r,i=r.default},4047:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606,783,708,456],()=>s(62019));module.exports=r})();