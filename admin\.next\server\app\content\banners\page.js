(()=>{var e={};e.id=100,e.ids=[100],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},20183:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>d,routeModule:()=>u,tree:()=>l});var r=s(50482),n=s(69108),a=s(62563),i=s.n(a),c=s(68300),o={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>c[e]);s.d(t,o);let l=["",{children:["content",{children:["banners",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,70286)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\banners\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\banners\\page.tsx"],x="/content/banners/page",p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/content/banners/page",pathname:"/content/banners",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},69968:(e,t,s)=>{Promise.resolve().then(s.bind(s,2128))},2128:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(95344),n=s(3729),a=s(20783),i=s.n(a),c=s(44669),o=s(43932);function l(){let[e,t]=(0,n.useState)([]),[s,a]=(0,n.useState)(!0);(0,n.useEffect)(()=>{(async()=>{a(!0);try{let e=await o.h.get("/content/banners");t(e.data.sort((e,t)=>e.order-t.order))}catch(e){c.ZP.error("获取Banner列表失败"),console.error("获取Banner列表失败:",e)}a(!1)})()},[]);let[l,d]=(0,n.useState)(!1),[x,p]=(0,n.useState)(null),u=async s=>{d(!0),p(s);try{await o.h.delete(`/content/banners/${s}`),t(e.filter(e=>e.id!==s)),c.ZP.success("Banner已成功删除")}catch(e){console.error("删除Banner失败:",e),c.ZP.error("删除Banner失败，请重试")}finally{d(!1),p(null)}},h=async(s,r)=>{let n="active"===r?"inactive":"active";try{await o.h.patch(`/content/banners/${s}`,{status:n}),t(e.map(e=>e.id===s?{...e,status:n}:e)),c.ZP.success(`Banner状态已更改为${"active"===n?"启用":"禁用"}`)}catch(e){console.error("更改Banner状态失败:",e),c.ZP.error("更改Banner状态失败，请重试")}},m=async(s,r)=>{let n=e.findIndex(e=>e.id===s);if(-1===n||"up"===r&&0===n||"down"===r&&n===e.length-1)return;let a=[...e],i="up"===r?n-1:n+1;[a[n],a[i]]=[a[i],a[n]],a[n].order=n+1,a[i].order=i+1;try{let e=a.map(e=>({id:e.id,order:e.order}));await o.h.patch("/content/banners/reorder",{banners:e}),t(a.sort((e,t)=>e.order-t.order)),c.ZP.success("Banner排序已更新")}catch(e){console.error("更新Banner排序失败:",e),c.ZP.error("更新Banner排序失败，请重试")}},g=e=>({home_top:"首页顶部",home_middle:"首页中部",home_bottom:"首页底部",services_page:"服务页面",about_page:"关于我们页面",contact_page:"联系我们页面"})[e]||e;return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"Banner管理"}),r.jsx(i(),{href:"/content/banners/new",className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"添加Banner"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"排序"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"预览"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"标题"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"位置"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"链接"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"有效期"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(t=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{children:t.order}),(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx("button",{onClick:()=>m(t.id,"up"),className:"text-gray-500 hover:text-gray-700 focus:outline-none",disabled:0===e.indexOf(t),children:"▲"}),r.jsx("button",{onClick:()=>m(t.id,"down"),className:"text-gray-500 hover:text-gray-700 focus:outline-none",disabled:e.indexOf(t)===e.length-1,children:"▼"})]})]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("div",{className:"h-12 w-20 relative bg-gray-200 rounded overflow-hidden",children:r.jsx("div",{className:"absolute inset-0 flex items-center justify-center text-gray-500 text-xs",children:"Banner预览"})})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t.title}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:g(t.position)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate",children:t.link}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"active"===t.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:"active"===t.status?"启用":"禁用"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[t.startDate," 至 ",t.endDate]}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>h(t.id,t.status),className:"text-indigo-600 hover:text-indigo-900",children:"active"===t.status?"禁用":"启用"}),r.jsx(i(),{href:`/content/banners/edit/${t.id}`,className:"text-blue-600 hover:text-blue-900",children:"编辑"}),r.jsx("button",{onClick:()=>u(t.id),disabled:l&&x===t.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:l&&x===t.id?"删除中...":"删除"})]})})]},t.id))})]})}),r.jsx(c.x7,{position:"top-right"})]})}},70286:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\banners\page.tsx`),{__esModule:n,$$typeof:a}=r,i=r.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,300,238],()=>s(20183));module.exports=r})();