(()=>{var e={};e.id=100,e.ids=[100],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},20183:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>o});var r=s(50482),n=s(69108),a=s(62563),i=s.n(a),l=s(68300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o=["",{children:["content",{children:["banners",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,70286)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\banners\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\banners\\page.tsx"],x="/content/banners/page",u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/content/banners/page",pathname:"/content/banners",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},69968:(e,t,s)=>{Promise.resolve().then(s.bind(s,2128))},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>c,a:()=>o});var r=s(95344),n=s(3729),a=s(22254),i=s(43932);let l=(0,n.createContext)(void 0);function c({children:e}){let[t,s]=(0,n.useState)(null),[c,o]=(0,n.useState)(!0),d=(0,a.useRouter)(),x=(0,a.usePathname)();(0,n.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(i.Z.defaults.headers.common.Authorization=`Bearer ${e}`,s(JSON.parse(t))):"/login"!==x&&d.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{o(!1)}})()},[x,d]);let u=async(e,t)=>{try{let{user:r,token:n}=(await i.Z.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",n),localStorage.setItem("adminUser",JSON.stringify(r)),i.Z.defaults.headers.common.Authorization=`Bearer ${n}`,s(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(l.Provider,{value:{user:t,loading:c,login:u,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.Z.defaults.headers.common.Authorization,s(null),d.push("/login")},updateUserInfo:e=>{if(t){let r={...t,...e};s(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!t},children:e})}function o(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2128:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(95344),n=s(3729),a=s(20783),i=s.n(a),l=s(44669),c=s(43932);function o(){let[e,t]=(0,n.useState)([]),[s,a]=(0,n.useState)(!0);(0,n.useEffect)(()=>{(async()=>{a(!0);try{let e=await c.h.get("/content/banners");t(e.data.sort((e,t)=>e.order-t.order))}catch(e){l.ZP.error("获取Banner列表失败"),console.error("获取Banner列表失败:",e)}a(!1)})()},[]);let[o,d]=(0,n.useState)(!1),[x,u]=(0,n.useState)(null),h=async s=>{d(!0),u(s);try{await c.h.delete(`/content/banners/${s}`),t(e.filter(e=>e.id!==s)),l.ZP.success("Banner已成功删除")}catch(e){console.error("删除Banner失败:",e),l.ZP.error("删除Banner失败，请重试")}finally{d(!1),u(null)}},p=async(s,r)=>{let n="active"===r?"inactive":"active";try{await c.h.patch(`/content/banners/${s}`,{status:n}),t(e.map(e=>e.id===s?{...e,status:n}:e)),l.ZP.success(`Banner状态已更改为${"active"===n?"启用":"禁用"}`)}catch(e){console.error("更改Banner状态失败:",e),l.ZP.error("更改Banner状态失败，请重试")}},m=async(s,r)=>{let n=e.findIndex(e=>e.id===s);if(-1===n||"up"===r&&0===n||"down"===r&&n===e.length-1)return;let a=[...e],i="up"===r?n-1:n+1;[a[n],a[i]]=[a[i],a[n]],a[n].order=n+1,a[i].order=i+1;try{let e=a.map(e=>({id:e.id,order:e.order}));await c.h.patch("/content/banners/reorder",{banners:e}),t(a.sort((e,t)=>e.order-t.order)),l.ZP.success("Banner排序已更新")}catch(e){console.error("更新Banner排序失败:",e),l.ZP.error("更新Banner排序失败，请重试")}},g=e=>({home_top:"首页顶部",home_middle:"首页中部",home_bottom:"首页底部",services_page:"服务页面",about_page:"关于我们页面",contact_page:"联系我们页面"})[e]||e;return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"Banner管理"}),r.jsx(i(),{href:"/content/banners/new",className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"添加Banner"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"排序"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"预览"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"标题"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"位置"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"链接"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"有效期"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(t=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{children:t.order}),(0,r.jsxs)("div",{className:"flex flex-col",children:[r.jsx("button",{onClick:()=>m(t.id,"up"),className:"text-gray-500 hover:text-gray-700 focus:outline-none",disabled:0===e.indexOf(t),children:"▲"}),r.jsx("button",{onClick:()=>m(t.id,"down"),className:"text-gray-500 hover:text-gray-700 focus:outline-none",disabled:e.indexOf(t)===e.length-1,children:"▼"})]})]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("div",{className:"h-12 w-20 relative bg-gray-200 rounded overflow-hidden",children:r.jsx("div",{className:"absolute inset-0 flex items-center justify-center text-gray-500 text-xs",children:"Banner预览"})})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t.title}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:g(t.position)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate",children:t.link}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"active"===t.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:"active"===t.status?"启用":"禁用"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[t.startDate," 至 ",t.endDate]}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>p(t.id,t.status),className:"text-indigo-600 hover:text-indigo-900",children:"active"===t.status?"禁用":"启用"}),r.jsx(i(),{href:`/content/banners/edit/${t.id}`,className:"text-blue-600 hover:text-blue-900",children:"编辑"}),r.jsx("button",{onClick:()=>h(t.id),disabled:o&&x===t.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:o&&x===t.id?"删除中...":"删除"})]})})]},t.id))})]})}),r.jsx(l.x7,{position:"top-right"})]})}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95344);s(3729),s(4047);var n=s(99847),a=s(44669),i=s(22254);function l({children:e}){let{user:t,logout:s,isAuthenticated:a,loading:l}=(0,n.a)(),c=(0,i.usePathname)();return"/login"===c?r.jsx(r.Fragment,{children:e}):l?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):a?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===c?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function c({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(n.H,{children:[r.jsx(l,{children:e}),r.jsx(a.x7,{position:"top-right"})]})})})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let n=r},70286:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\banners\page.tsx`),{__esModule:n,$$typeof:a}=r,i=r.default},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:n,$$typeof:a}=r,i=r.default},4047:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606,783],()=>s(20183));module.exports=r})();