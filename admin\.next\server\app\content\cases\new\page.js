(()=>{var e={};e.id=875,e.ids=[875],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},89479:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>u,tree:()=>d});var r=t(50482),a=t(69108),l=t(62563),n=t.n(l),i=t(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d=["",{children:["content",{children:["cases",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94632)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\cases\\new\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\cases\\new\\page.tsx"],m="/content/cases/new/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/content/cases/new/page",pathname:"/content/cases/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19419:(e,s,t)=>{Promise.resolve().then(t.bind(t,5377))},89747:(e,s,t)=>{Promise.resolve().then(t.bind(t,67329))},95444:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},99847:(e,s,t)=>{"use strict";t.d(s,{H:()=>c,a:()=>d});var r=t(95344),a=t(3729),l=t(22254),n=t(43932);let i=(0,a.createContext)(void 0);function c({children:e}){let[s,t]=(0,a.useState)(null),[c,d]=(0,a.useState)(!0),o=(0,l.useRouter)(),m=(0,l.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),s=localStorage.getItem("adminUser");e&&s?(n.h.defaults.headers.common.Authorization=`Bearer ${e}`,t(JSON.parse(s))):"/login"!==m&&o.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[m,o]);let x=async(e,s)=>{try{let{user:r,token:a}=(await n.h.post("/auth/login",{username:e,password:s})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),n.h.defaults.headers.common.Authorization=`Bearer ${a}`,t(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(i.Provider,{value:{user:s,loading:c,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete n.h.defaults.headers.common.Authorization,t(null),o.push("/login")},isAuthenticated:!!s},children:e})}function d(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},5377:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(95344),a=t(3729),l=t(22254),n=t(60708),i=t(44669),c=t(43932);function d(){let e=(0,l.useRouter)(),[s,t]=(0,a.useState)(!1),[d,o]=(0,a.useState)(null),m=(0,a.useRef)(null),{register:x,handleSubmit:u,formState:{errors:h},watch:p}=(0,n.cI)({defaultValues:{status:"draft",featured:!1,category:"留学案例"}}),b=p("title"),g=e=>e.toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g,"-").replace(/^-+|-+$/g,""),j=async s=>{t(!0);try{s.slug||(s.slug=g(s.title));let t=m.current?.files?.[0],r=new FormData;Object.entries(s).forEach(([e,s])=>{"boolean"==typeof s?r.append(e,String(s)):r.append(e,s)}),t&&r.append("thumbnail",t),await c.h.post("/content/cases",r,{headers:{"Content-Type":"multipart/form-data"}}),i.ZP.success("案例创建成功"),e.push("/content/cases")}catch(e){console.error("创建案例失败:",e),i.ZP.error("创建案例失败，请重试"),t(!1)}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"添加新案例"}),r.jsx("p",{className:"text-gray-600",children:"创建新的成功案例"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("form",{onSubmit:u(j),className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例标题 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"title",type:"text",className:`w-full px-3 py-2 border rounded-md ${h.title?"border-red-500":"border-gray-300"}`,placeholder:"输入案例标题",...x("title",{required:"请输入案例标题"})}),h.title&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.title.message})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"slug",className:"block text-sm font-medium text-gray-700 mb-1",children:"URL别名"}),r.jsx("input",{id:"slug",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:b?g(b):"自动生成或手动输入",...x("slug")}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"留空将根据标题自动生成"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例分类 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"category",className:`w-full px-3 py-2 border rounded-md ${h.category?"border-red-500":"border-gray-300"}`,...x("category",{required:"请选择案例分类"}),children:[r.jsx("option",{value:"留学案例",children:"留学案例"}),r.jsx("option",{value:"保研案例",children:"保研案例"}),r.jsx("option",{value:"考研案例",children:"考研案例"}),r.jsx("option",{value:"职业转型",children:"职业转型"}),r.jsx("option",{value:"职场晋升",children:"职场晋升"}),r.jsx("option",{value:"其他案例",children:"其他案例"})]}),h.category&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.category.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"thumbnail",className:"block text-sm font-medium text-gray-700 mb-1",children:["缩略图 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("input",{id:"thumbnail",type:"file",accept:"image/*",ref:m,onChange:e=>{let s=e.target.files?.[0];s&&o(URL.createObjectURL(s))},className:"hidden"}),r.jsx("button",{type:"button",onClick:()=>m.current?.click(),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"选择图片"}),d&&r.jsx("div",{className:"relative h-20 w-32 bg-gray-200 rounded overflow-hidden",children:r.jsx("img",{src:d,alt:"缩略图预览",className:"h-full w-full object-cover"})}),!d&&r.jsx("div",{className:"h-20 w-32 bg-gray-200 rounded flex items-center justify-center text-gray-500 text-sm",children:"无预览"})]}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"建议尺寸: 800x600px, 最大文件大小: 2MB"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"summary",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例摘要 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"summary",rows:3,className:`w-full px-3 py-2 border rounded-md ${h.summary?"border-red-500":"border-gray-300"}`,placeholder:"简要描述案例的主要内容和亮点",...x("summary",{required:"请输入案例摘要"})}),h.summary&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.summary.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"client",className:"block text-sm font-medium text-gray-700 mb-1",children:["客户信息 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"client",rows:3,className:`w-full px-3 py-2 border rounded-md ${h.client?"border-red-500":"border-gray-300"}`,placeholder:"描述客户的背景、需求和挑战",...x("client",{required:"请输入客户信息"})}),h.client&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.client.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例详情 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"content",rows:10,className:`w-full px-3 py-2 border rounded-md ${h.content?"border-red-500":"border-gray-300"}`,placeholder:"详细描述案例的过程、方法和策略",...x("content",{required:"请输入案例详情"})}),h.content&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.content.message}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"支持Markdown格式"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"result",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例结果 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"result",rows:3,className:`w-full px-3 py-2 border rounded-md ${h.result?"border-red-500":"border-gray-300"}`,placeholder:"描述案例的最终结果和成果",...x("result",{required:"请输入案例结果"})}),h.result&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.result.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("label",{className:"inline-flex items-center",children:[r.jsx("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"draft",...x("status")}),r.jsx("span",{className:"ml-2",children:"草稿"})]}),(0,r.jsxs)("label",{className:"inline-flex items-center",children:[r.jsx("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"published",...x("status")}),r.jsx("span",{className:"ml-2",children:"发布"})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"精选案例"}),(0,r.jsxs)("label",{className:"inline-flex items-center",children:[r.jsx("input",{type:"checkbox",className:"form-checkbox h-4 w-4 text-primary-600",...x("featured")}),r.jsx("span",{className:"ml-2",children:"设为精选案例（将在首页展示）"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[r.jsx("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>e.back(),disabled:s,children:"取消"}),r.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:s,children:s?"保存中...":"保存"})]})]})}),r.jsx(i.x7,{position:"top-right"})]})}},67329:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(95344);t(3729),t(4047);var a=t(99847),l=t(44669),n=t(20783),i=t.n(n),c=t(22254);function d({children:e}){let{user:s,logout:t,isAuthenticated:l,loading:n}=(0,a.a)(),d=(0,c.usePathname)();return"/login"===d?r.jsx(r.Fragment,{children:e}):n?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):l?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",s?.name]}),r.jsx("button",{onClick:t,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx(i(),{href:"/dashboard",className:`block p-2 hover:bg-gray-200 rounded ${"/dashboard"===d?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx(i(),{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx(i(),{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx(i(),{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function o({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(d,{children:e}),r.jsx(l.x7,{position:"top-right"})]})})})}},43932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a,h:()=>r});let r=t(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},94632:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>n});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\cases\new\page.tsx`),{__esModule:a,$$typeof:l}=r,n=r.default},82917:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>n});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:l}=r,n=r.default},4047:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,300,708],()=>t(89479));module.exports=r})();