(()=>{var e={};e.id=734,e.ids=[734],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},83071:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>o});var r=s(50482),a=s(69108),i=s(62563),n=s.n(i),l=s(68300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);s.d(t,c);let o=["",{children:["content",{children:["cases",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,14076)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\cases\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\cases\\page.tsx"],x="/content/cases/page",u={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/content/cases/page",pathname:"/content/cases",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},11502:(e,t,s)=>{Promise.resolve().then(s.bind(s,90512))},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>c,a:()=>o});var r=s(95344),a=s(3729),i=s(22254),n=s(43932);let l=(0,a.createContext)(void 0);function c({children:e}){let[t,s]=(0,a.useState)(null),[c,o]=(0,a.useState)(!0),d=(0,i.useRouter)(),x=(0,i.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(n.h.defaults.headers.common.Authorization=`Bearer ${e}`,s(JSON.parse(t))):"/login"!==x&&d.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{o(!1)}})()},[x,d]);let u=async(e,t)=>{try{let{user:r,token:a}=(await n.h.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),n.h.defaults.headers.common.Authorization=`Bearer ${a}`,s(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(l.Provider,{value:{user:t,loading:c,login:u,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete n.h.defaults.headers.common.Authorization,s(null),d.push("/login")},isAuthenticated:!!t},children:e})}function o(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},90512:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(95344),a=s(3729),i=s(20783),n=s.n(i),l=s(44669),c=s(43932);function o(){let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{i(!0);try{let e=await c.Z.get("/cases");if(e.data.success&&e.data.data.items){let s=e.data.data.items.map(e=>({id:e.id,title:e.title,category:e.category||"general",status:e.status,featured:e.is_featured||!1,updatedAt:e.updated_at}));t(s)}else t([])}catch(e){l.ZP.error("获取案例列表失败"),console.error("获取案例列表失败:",e)}i(!1)})()},[]);let[o,d]=(0,a.useState)(!1),[x,u]=(0,a.useState)(null),h=async s=>{d(!0),u(s);try{await c.Z.delete(`/cases/${s}`),t(e.filter(e=>e.id!==s)),l.ZP.success("案例已成功删除")}catch(e){console.error("删除案例失败:",e),l.ZP.error("删除案例失败，请重试")}finally{d(!1),u(null)}},p=async(s,r)=>{let a="published"===r?"draft":"published";try{await c.Z.patch(`/cases/${s}`,{status:a}),t(e.map(e=>e.id===s?{...e,status:a}:e)),l.ZP.success(`案例状态已更改为${"published"===a?"已发布":"草稿"}`)}catch(e){console.error("更改案例状态失败:",e),l.ZP.error("更改案例状态失败，请重试")}},m=async(s,r)=>{try{await c.Z.patch(`/cases/${s}`,{featured:!r}),t(e.map(e=>e.id===s?{...e,featured:!r}:e)),l.ZP.success(`案例已${r?"取消":"设为"}精选`)}catch(e){console.error("更改精选状态失败:",e),l.ZP.error("更改精选状态失败，请重试")}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"案例管理"}),r.jsx(n(),{href:"/content/cases/new",className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"添加案例"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"缩略图"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"标题"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"分类"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"精选"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"更新日期"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("div",{className:"h-12 w-16 relative bg-gray-200 rounded overflow-hidden",children:r.jsx("div",{className:"absolute inset-0 flex items-center justify-center text-gray-500 text-xs",children:"缩略图"})})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.title}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.category}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"published"===e.status?"已发布":"草稿"})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${e.featured?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"}`,children:e.featured?"是":"否"})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.updatedAt}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>p(e.id,e.status),className:"text-indigo-600 hover:text-indigo-900",children:"published"===e.status?"设为草稿":"发布"}),r.jsx("button",{onClick:()=>m(e.id,e.featured),className:"text-purple-600 hover:text-purple-900",children:e.featured?"取消精选":"设为精选"}),r.jsx(n(),{href:`/content/cases/edit/${e.id}`,className:"text-blue-600 hover:text-blue-900",children:"编辑"}),r.jsx("button",{onClick:()=>h(e.id),disabled:o&&x===e.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:o&&x===e.id?"删除中...":"删除"})]})})]},e.id))})]})}),r.jsx(l.x7,{position:"top-right"})]})}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95344);s(3729),s(4047);var a=s(99847),i=s(44669),n=s(20783),l=s.n(n),c=s(22254);function o({children:e}){let{user:t,logout:s,isAuthenticated:i,loading:n}=(0,a.a)(),o=(0,c.usePathname)();return"/login"===o?r.jsx(r.Fragment,{children:e}):n?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):i?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx(l(),{href:"/dashboard",className:`block p-2 hover:bg-gray-200 rounded ${"/dashboard"===o?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx(l(),{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx(l(),{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx(l(),{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function d({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(o,{children:e}),r.jsx(i.x7,{position:"top-right"})]})})})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},14076:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\cases\page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default},4047:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,300],()=>s(83071));module.exports=r})();