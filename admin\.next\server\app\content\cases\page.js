(()=>{var e={};e.id=734,e.ids=[734],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},83071:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d});var r=s(50482),a=s(69108),i=s(62563),n=s.n(i),c=s(68300),l={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>c[e]);s.d(t,l);let d=["",{children:["content",{children:["cases",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,14076)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\cases\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\cases\\page.tsx"],x="/content/cases/page",p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/content/cases/page",pathname:"/content/cases",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},11502:(e,t,s)=>{Promise.resolve().then(s.bind(s,90512))},90512:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95344),a=s(3729),i=s(20783),n=s.n(i),c=s(44669),l=s(43932);function d(){let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{i(!0);try{let e=await l.h.get("/cases");if(e.data.success&&e.data.data.items){let s=e.data.data.items.map(e=>({id:e.id,title:e.title,category:e.category||"general",status:e.status,featured:e.is_featured||!1,updatedAt:e.updated_at}));t(s)}else t([])}catch(e){c.ZP.error("获取案例列表失败"),console.error("获取案例列表失败:",e)}i(!1)})()},[]);let[d,o]=(0,a.useState)(!1),[x,p]=(0,a.useState)(null),u=async s=>{o(!0),p(s);try{await l.h.delete(`/cases/${s}`),t(e.filter(e=>e.id!==s)),c.ZP.success("案例已成功删除")}catch(e){console.error("删除案例失败:",e),c.ZP.error("删除案例失败，请重试")}finally{o(!1),p(null)}},h=async(s,r)=>{let a="published"===r?"draft":"published";try{await l.h.patch(`/cases/${s}`,{status:a}),t(e.map(e=>e.id===s?{...e,status:a}:e)),c.ZP.success(`案例状态已更改为${"published"===a?"已发布":"草稿"}`)}catch(e){console.error("更改案例状态失败:",e),c.ZP.error("更改案例状态失败，请重试")}},m=async(s,r)=>{try{await l.h.patch(`/cases/${s}`,{featured:!r}),t(e.map(e=>e.id===s?{...e,featured:!r}:e)),c.ZP.success(`案例已${r?"取消":"设为"}精选`)}catch(e){console.error("更改精选状态失败:",e),c.ZP.error("更改精选状态失败，请重试")}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"案例管理"}),r.jsx(n(),{href:"/content/cases/new",className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"添加案例"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"缩略图"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"标题"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"分类"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"精选"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"更新日期"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("div",{className:"h-12 w-16 relative bg-gray-200 rounded overflow-hidden",children:r.jsx("div",{className:"absolute inset-0 flex items-center justify-center text-gray-500 text-xs",children:"缩略图"})})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.title}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.category}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"published"===e.status?"已发布":"草稿"})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${e.featured?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"}`,children:e.featured?"是":"否"})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.updatedAt}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>h(e.id,e.status),className:"text-indigo-600 hover:text-indigo-900",children:"published"===e.status?"设为草稿":"发布"}),r.jsx("button",{onClick:()=>m(e.id,e.featured),className:"text-purple-600 hover:text-purple-900",children:e.featured?"取消精选":"设为精选"}),r.jsx(n(),{href:`/content/cases/edit/${e.id}`,className:"text-blue-600 hover:text-blue-900",children:"编辑"}),r.jsx("button",{onClick:()=>u(e.id),disabled:d&&x===e.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:d&&x===e.id?"删除中...":"删除"})]})})]},e.id))})]})}),r.jsx(c.x7,{position:"top-right"})]})}},14076:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\cases\page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606,783,238],()=>s(83071));module.exports=r})();