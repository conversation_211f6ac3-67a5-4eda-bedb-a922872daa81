(()=>{var e={};e.id=770,e.ids=[770],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},69560:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=s(50482),a=s(69108),i=s(62563),n=s.n(i),d=s(68300),l={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);s.d(t,l);let o=["",{children:["content",{children:["faq",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5996)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\faq\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\faq\\page.tsx"],p="/content/faq/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/content/faq/page",pathname:"/content/faq",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},56123:(e,t,s)=>{Promise.resolve().then(s.bind(s,35301))},35301:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(95344),a=s(3729),i=s(20783),n=s.n(i),d=s(44669);let l=[{id:1,question:"申请留学需要准备哪些材料？",answer:"申请留学通常需要准备以下材料：个人陈述、推荐信、成绩单、语言成绩证明（如托福、雅思）、简历、作品集（视专业而定）等。具体要求因学校和专业而异，建议提前查询目标院校的官方要求。",category:"留学申请",order:1,status:"published",createdAt:"2023-10-15",updatedAt:"2023-11-01"},{id:2,question:"保研和考研有什么区别？",answer:"保研是免试推荐攻读研究生的方式，通常需要本科期间成绩优异，且获得学校的推荐资格。考研则是通过全国统一的研究生入学考试，参加初试和复试后被录取。保研免去了考试环节，但名额有限且竞争激烈；考研则机会更加公平，但需要付出更多备考努力。",category:"考研保研",order:1,status:"published",createdAt:"2023-10-16",updatedAt:"2023-10-30"},{id:3,question:"如何确定适合自己的职业方向？",answer:"确定适合自己的职业方向可以从以下几个方面考虑：1）自我评估：了解自己的兴趣、能力、价值观和性格特点；2）市场调研：了解不同行业和职位的发展前景、薪资水平和工作内容；3）尝试实习或项目：通过实际体验来验证自己的兴趣和适应性；4）寻求专业指导：可以咨询职业规划师或行业内的专业人士。",category:"职业规划",order:1,status:"published",createdAt:"2023-10-17",updatedAt:"2023-10-29"},{id:4,question:"职业转型需要具备哪些条件？",answer:"成功的职业转型通常需要：1）明确的目标和规划；2）相关的技能储备或学习能力；3）对新领域的了解和研究；4）良好的人际网络；5）足够的财务准备以应对过渡期；6）积极的心态和抗压能力。根据个人情况不同，可能还需要额外的教育背景或证书。",category:"职业转型",order:1,status:"published",createdAt:"2023-10-18",updatedAt:"2023-10-28"},{id:5,question:"贵公司的服务收费标准是怎样的？",answer:"我们的服务收费根据不同的服务类型和客户需求而定，没有统一的标准价格。我们会根据您的具体情况和需求提供定制化的服务方案和相应的报价。您可以通过预约咨询，与我们的顾问详细沟通，获取适合您的服务方案和价格信息。",category:"服务咨询",order:1,status:"published",createdAt:"2023-10-19",updatedAt:"2023-10-27"}];function o(){let[e,t]=(0,a.useState)(l),[s,i]=(0,a.useState)(!1),[o,c]=(0,a.useState)(null),[p,x]=(0,a.useState)(null),u=async s=>{i(!0),c(s);try{await new Promise(e=>setTimeout(e,500)),t(e.filter(e=>e.id!==s)),d.ZP.success("FAQ已成功删除")}catch(e){console.error("删除FAQ失败:",e),d.ZP.error("删除FAQ失败，请重试")}finally{i(!1),c(null)}},m=async(s,r)=>{let a="published"===r?"draft":"published";try{await new Promise(e=>setTimeout(e,500)),t(e.map(e=>e.id===s?{...e,status:a}:e)),d.ZP.success(`FAQ状态已更改为${"published"===a?"已发布":"草稿"}`)}catch(e){console.error("更改FAQ状态失败:",e),d.ZP.error("更改FAQ状态失败，请重试")}},h=e=>{x(p===e?null:e)};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"FAQ管理"}),r.jsx(n(),{href:"/content/faq/new",className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"添加FAQ"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"问题"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"分类"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"更新日期"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("tr",{className:"hover:bg-gray-50 cursor-pointer",onClick:()=>h(e.id),children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),r.jsx("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("span",{className:"mr-2",children:p===e.id?"▼":"▶"}),e.question]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.category}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"published"===e.status?"已发布":"草稿"})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.updatedAt}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",onClick:e=>e.stopPropagation(),children:[r.jsx("button",{onClick:()=>m(e.id,e.status),className:"text-indigo-600 hover:text-indigo-900",children:"published"===e.status?"设为草稿":"发布"}),r.jsx(n(),{href:`/content/faq/edit/${e.id}`,className:"text-blue-600 hover:text-blue-900",children:"编辑"}),r.jsx("button",{onClick:()=>u(e.id),disabled:s&&o===e.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:s&&o===e.id?"删除中...":"删除"})]})})]},e.id),p===e.id&&r.jsx("tr",{children:r.jsx("td",{colSpan:6,className:"px-6 py-4 bg-gray-50",children:(0,r.jsxs)("div",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:[r.jsx("p",{className:"font-semibold mb-2",children:"答案："}),e.answer]})})})]}))})]})}),r.jsx(d.x7,{position:"top-right"})]})}},5996:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\faq\page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606,783,238],()=>s(69560));module.exports=r})();