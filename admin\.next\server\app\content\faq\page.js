(()=>{var e={};e.id=770,e.ids=[770],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},69560:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=s(50482),a=s(69108),i=s(62563),n=s.n(i),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["content",{children:["faq",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5996)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\faq\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\faq\\page.tsx"],u="/content/faq/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/content/faq/page",pathname:"/content/faq",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},56123:(e,t,s)=>{Promise.resolve().then(s.bind(s,35301))},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>o,a:()=>c});var r=s(95344),a=s(3729),i=s(22254),n=s(43932);let l=(0,a.createContext)(void 0);function o({children:e}){let[t,s]=(0,a.useState)(null),[o,c]=(0,a.useState)(!0),d=(0,i.useRouter)(),u=(0,i.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(e&&t&&"undefined"!==t&&"null"!==t)try{n.Z.defaults.headers.common.Authorization=`Bearer ${e}`;let r=JSON.parse(t);s(r)}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),"/login"!==u&&d.push("/login")}else"/login"!==u&&d.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{c(!1)}})()},[u,d]);let x=async(e,t)=>{try{let{user:r,token:a}=(await n.Z.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),n.Z.defaults.headers.common.Authorization=`Bearer ${a}`,s(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(l.Provider,{value:{user:t,loading:o,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete n.Z.defaults.headers.common.Authorization,s(null),d.push("/login")},updateUserInfo:e=>{if(t){let r={...t,...e};s(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!t},children:e})}function c(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},35301:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95344),a=s(3729),i=s(20783),n=s.n(i),l=s(44669);let o=[{id:1,question:"申请留学需要准备哪些材料？",answer:"申请留学通常需要准备以下材料：个人陈述、推荐信、成绩单、语言成绩证明（如托福、雅思）、简历、作品集（视专业而定）等。具体要求因学校和专业而异，建议提前查询目标院校的官方要求。",category:"留学申请",order:1,status:"published",createdAt:"2023-10-15",updatedAt:"2023-11-01"},{id:2,question:"保研和考研有什么区别？",answer:"保研是免试推荐攻读研究生的方式，通常需要本科期间成绩优异，且获得学校的推荐资格。考研则是通过全国统一的研究生入学考试，参加初试和复试后被录取。保研免去了考试环节，但名额有限且竞争激烈；考研则机会更加公平，但需要付出更多备考努力。",category:"考研保研",order:1,status:"published",createdAt:"2023-10-16",updatedAt:"2023-10-30"},{id:3,question:"如何确定适合自己的职业方向？",answer:"确定适合自己的职业方向可以从以下几个方面考虑：1）自我评估：了解自己的兴趣、能力、价值观和性格特点；2）市场调研：了解不同行业和职位的发展前景、薪资水平和工作内容；3）尝试实习或项目：通过实际体验来验证自己的兴趣和适应性；4）寻求专业指导：可以咨询职业规划师或行业内的专业人士。",category:"职业规划",order:1,status:"published",createdAt:"2023-10-17",updatedAt:"2023-10-29"},{id:4,question:"职业转型需要具备哪些条件？",answer:"成功的职业转型通常需要：1）明确的目标和规划；2）相关的技能储备或学习能力；3）对新领域的了解和研究；4）良好的人际网络；5）足够的财务准备以应对过渡期；6）积极的心态和抗压能力。根据个人情况不同，可能还需要额外的教育背景或证书。",category:"职业转型",order:1,status:"published",createdAt:"2023-10-18",updatedAt:"2023-10-28"},{id:5,question:"贵公司的服务收费标准是怎样的？",answer:"我们的服务收费根据不同的服务类型和客户需求而定，没有统一的标准价格。我们会根据您的具体情况和需求提供定制化的服务方案和相应的报价。您可以通过预约咨询，与我们的顾问详细沟通，获取适合您的服务方案和价格信息。",category:"服务咨询",order:1,status:"published",createdAt:"2023-10-19",updatedAt:"2023-10-27"}];function c(){let[e,t]=(0,a.useState)(o),[s,i]=(0,a.useState)(!1),[c,d]=(0,a.useState)(null),[u,x]=(0,a.useState)(null),h=async s=>{i(!0),d(s);try{await new Promise(e=>setTimeout(e,500)),t(e.filter(e=>e.id!==s)),l.ZP.success("FAQ已成功删除")}catch(e){console.error("删除FAQ失败:",e),l.ZP.error("删除FAQ失败，请重试")}finally{i(!1),d(null)}},p=async(s,r)=>{let a="published"===r?"draft":"published";try{await new Promise(e=>setTimeout(e,500)),t(e.map(e=>e.id===s?{...e,status:a}:e)),l.ZP.success(`FAQ状态已更改为${"published"===a?"已发布":"草稿"}`)}catch(e){console.error("更改FAQ状态失败:",e),l.ZP.error("更改FAQ状态失败，请重试")}},m=e=>{x(u===e?null:e)};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"FAQ管理"}),r.jsx(n(),{href:"/content/faq/new",className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"添加FAQ"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"问题"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"分类"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"更新日期"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("tr",{className:"hover:bg-gray-50 cursor-pointer",onClick:()=>m(e.id),children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),r.jsx("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("span",{className:"mr-2",children:u===e.id?"▼":"▶"}),e.question]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.category}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"published"===e.status?"已发布":"草稿"})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.updatedAt}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",onClick:e=>e.stopPropagation(),children:[r.jsx("button",{onClick:()=>p(e.id,e.status),className:"text-indigo-600 hover:text-indigo-900",children:"published"===e.status?"设为草稿":"发布"}),r.jsx(n(),{href:`/content/faq/edit/${e.id}`,className:"text-blue-600 hover:text-blue-900",children:"编辑"}),r.jsx("button",{onClick:()=>h(e.id),disabled:s&&c===e.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:s&&c===e.id?"删除中...":"删除"})]})})]},e.id),u===e.id&&r.jsx("tr",{children:r.jsx("td",{colSpan:6,className:"px-6 py-4 bg-gray-50",children:(0,r.jsxs)("div",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:[r.jsx("p",{className:"font-semibold mb-2",children:"答案："}),e.answer]})})})]}))})]})}),r.jsx(l.x7,{position:"top-right"})]})}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(95344);s(3729),s(4047);var a=s(99847),i=s(44669),n=s(22254);function l({children:e}){let{user:t,logout:s,isAuthenticated:i,loading:l}=(0,a.a)(),o=(0,n.usePathname)();return"/login"===o?r.jsx(r.Fragment,{children:e}):l?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):i?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===o?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function o({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(l,{children:e}),r.jsx(i.x7,{position:"top-right"})]})})})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},5996:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\faq\page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default},4047:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606,783],()=>s(69560));module.exports=r})();