(()=>{var e={};e.id=127,e.ids=[127],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},7337:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(50482),a=s(69108),o=s(62563),n=s.n(o),i=s(68300),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d=["",{children:["content",{children:["faqs",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,79882)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\faqs\\new\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\faqs\\new\\page.tsx"],u="/content/faqs/new/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/content/faqs/new/page",pathname:"/content/faqs/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},28397:(e,t,s)=>{Promise.resolve().then(s.bind(s,66587))},66587:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var r=s(95344),a=s(3729),o=s(22254),n=s(60708),i=s(44669),l=s(32456),d=s(20783),c=s.n(d),u=s(43932);function x(){let e=(0,o.useRouter)(),{register:t,handleSubmit:s,formState:{errors:d},setValue:x}=(0,n.cI)(),[m,p]=(0,a.useState)(!1),[h,g]=(0,a.useState)([]);(0,a.useEffect)(()=>{(async()=>{try{let e=await u.h.get("/content/faqs/categories");g(e.data)}catch(e){i.ZP.error("获取分类列表失败"),console.error("获取分类列表失败:",e)}})()},[]);let f=async t=>{p(!0);let s={...t,categoryId:t.categoryId?Number(t.categoryId):null};try{await u.h.post("/content/faqs",s),i.ZP.success("FAQ创建成功！"),e.push("/content/faqs")}catch(e){console.error("创建FAQ失败:",e),i.ZP.error("创建FAQ失败，请稍后再试。")}finally{p(!1)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[r.jsx(i.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-800",children:"创建新FAQ"}),r.jsx(c(),{href:"/content/faqs",children:(0,r.jsxs)("button",{className:"bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(l.Ao2,{className:"mr-2"}),"返回列表"]})})]}),(0,r.jsxs)("form",{onSubmit:s(f),className:"bg-white p-8 rounded-xl shadow-xl space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"question",className:"block text-sm font-medium text-gray-700 mb-1",children:["问题 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"text",id:"question",...t("question",{required:"问题不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.question?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),d.question&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.question.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"answer",className:"block text-sm font-medium text-gray-700 mb-1",children:["答案 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"answer",rows:6,...t("answer",{required:"答案不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.answer?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),d.answer&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.answer.message})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"categoryId",className:"block text-sm font-medium text-gray-700 mb-1",children:"分类"}),(0,r.jsxs)("select",{id:"categoryId",...t("categoryId"),className:`mt-1 block w-full px-4 py-2 border ${d.categoryId?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`,defaultValue:"",children:[r.jsx("option",{value:"",children:"选择分类 (可选)"}),h.map(e=>r.jsx("option",{value:e.id,children:e.name},e.id))]}),d.categoryId&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.categoryId.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-1",children:["状态 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"status",...t("status",{required:"状态不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.status?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`,defaultValue:"draft",children:[r.jsx("option",{value:"draft",children:"草稿"}),r.jsx("option",{value:"published",children:"已发布"})]}),d.status&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.status.message})]}),r.jsx("div",{className:"flex justify-end pt-4",children:(0,r.jsxs)("button",{type:"submit",disabled:m,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[m?(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):r.jsx(l.mW3,{className:"mr-2"}),m?"正在创建...":"创建FAQ"]})})]})]})}},79882:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>o,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\faqs\new\page.tsx`),{__esModule:a,$$typeof:o}=r,n=r.default}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606,783,708,456,238],()=>s(7337));module.exports=r})();