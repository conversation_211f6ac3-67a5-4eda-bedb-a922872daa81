(()=>{var e={};e.id=344,e.ids=[344],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},24165:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(50482),a=s(69108),i=s(62563),n=s.n(i),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["content",{children:["faqs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,31683)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\faqs\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\faqs\\page.tsx"],u="/content/faqs/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/content/faqs/page",pathname:"/content/faqs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10922:(e,t,s)=>{Promise.resolve().then(s.bind(s,15073))},15073:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95344),a=s(3729),i=s(20783),n=s.n(i),l=s(44669),o=s(32456),d=s(43932);function c(){let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)([]),[c,u]=(0,a.useState)(!0),[x,m]=(0,a.useState)(""),[g,h]=(0,a.useState)(""),[p,f]=(0,a.useState)(null),[b,y]=(0,a.useState)(!1),[j,v]=(0,a.useState)(""),[w,N]=(0,a.useState)(null),q=async()=>{u(!0);try{let e=await d.Z.get("/faqs",{params:{search:x,category:g}});if(e.data.success&&e.data.data.items){let s=e.data.data.items.map(e=>({id:e.id,question:e.question,answer:e.answer,category:e.category||"general",status:e.status,updatedAt:e.updated_at}));t(s)}else t([])}catch(e){l.ZP.error("获取FAQ列表失败"),console.error("获取FAQ列表失败:",e)}u(!1)},k=async()=>{try{let e=await d.Z.get("/content/faqs/categories");i(e.data)}catch(e){l.ZP.error("获取分类列表失败"),console.error("获取分类列表失败:",e)}};(0,a.useEffect)(()=>{q(),k()},[]),(0,a.useEffect)(()=>{let e=setTimeout(()=>{q()},300);return()=>clearTimeout(e)},[x,g]);let P=async s=>{if(window.confirm("确定要删除此FAQ吗？"))try{await d.Z.delete(`/faqs/${s}`),t(e.filter(e=>e.id!==s)),l.ZP.success("FAQ删除成功")}catch(e){l.ZP.error("删除FAQ失败"),console.error("删除FAQ失败:",e)}},C=e=>{f(e)},F=async s=>{if(s.preventDefault(),p)try{let s=await d.Z.put(`/faqs/${p.id}`,p);t(e.map(e=>e.id===p.id?s.data:e)),l.ZP.success("FAQ更新成功"),f(null)}catch(e){l.ZP.error("更新FAQ失败"),console.error("更新FAQ失败:",e)}},A=async()=>{if(!j.trim()){l.ZP.error("分类名称不能为空");return}try{let e=await d.Z.post("/content/faqs/categories",{name:j});i([...s,e.data]),v(""),l.ZP.success("分类添加成功")}catch(e){l.ZP.error("添加分类失败"),console.error("添加分类失败:",e)}},Z=e=>{N(e),v(e.name)},Q=async()=>{if(!w||!j.trim()){l.ZP.error("分类名称不能为空");return}try{let e=await d.Z.put(`/content/faqs/categories/${w.id}`,{name:j});i(s.map(t=>t.id===w.id?e.data:t)),v(""),N(null),l.ZP.success("分类更新成功")}catch(e){l.ZP.error("更新分类失败"),console.error("更新分类失败:",e)}},_=async e=>{if(window.confirm("确定要删除此分类吗？删除分类将导致该分类下的FAQ变为未分类。"))try{await d.Z.delete(`/content/faqs/categories/${e}`),i(s.filter(t=>t.id!==e)),q(),l.ZP.success("分类删除成功")}catch(e){l.ZP.error("删除分类失败"),console.error("删除分类失败:",e)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[r.jsx(l.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-800",children:"FAQ管理"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>y(!0),className:"bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(o.Ihx,{className:"mr-2"})," 管理分类"]}),r.jsx(n(),{href:"/content/faqs/new",children:(0,r.jsxs)("button",{className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(o.O9D,{className:"mr-2"})," 添加新FAQ"]})})]})]}),r.jsx("div",{className:"mb-6 p-4 bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700",children:"搜索问题"}),(0,r.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(o.jRj,{className:"text-gray-400"})}),r.jsx("input",{type:"text",id:"search",className:"focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-2",placeholder:"输入关键词...",value:x,onChange:e=>m(e.target.value)})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"categoryFilter",className:"block text-sm font-medium text-gray-700",children:"按分类筛选"}),(0,r.jsxs)("select",{id:"categoryFilter",className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",value:g,onChange:e=>h(e.target.value),children:[r.jsx("option",{value:"",children:"所有分类"}),s.map(e=>r.jsx("option",{value:e.name,children:e.name},e.id))]})]})]})}),c?r.jsx("div",{className:"text-center py-10",children:r.jsx("p",{className:"text-lg text-gray-500",children:"正在加载FAQ..."})}):0===e.length?(0,r.jsxs)("div",{className:"text-center py-10 bg-white rounded-lg shadow",children:[r.jsx(o.$Rx,{className:"mx-auto text-gray-400 text-5xl mb-4"}),r.jsx("p",{className:"text-lg text-gray-500",children:"未找到FAQ。"}),(0,r.jsxs)("p",{className:"text-sm text-gray-400",children:["尝试调整搜索词或筛选条件，或",r.jsx(n(),{href:"/content/faqs/new",className:"text-indigo-600 hover:underline",children:"添加新的FAQ"}),"。"]})]}):r.jsx("div",{className:"bg-white shadow-xl rounded-lg overflow-hidden",children:r.jsx("ul",{className:"divide-y divide-gray-200",children:e.map(e=>r.jsx("li",{className:"p-4 hover:bg-gray-50 transition duration-150",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-indigo-700",children:e.question}),r.jsx("p",{className:"mt-1 text-sm text-gray-600 whitespace-pre-wrap",children:e.answer}),(0,r.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:[(0,r.jsxs)("span",{children:["分类: ",e.category||"未分类"]})," |",(0,r.jsxs)("span",{children:["状态: ",r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"published"===e.status?"已发布":"草稿"})]})," |",(0,r.jsxs)("span",{children:["最后更新: ",new Date(e.updatedAt).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:"flex-shrink-0 flex space-x-2 ml-4",children:[r.jsx("button",{onClick:()=>C(e),className:"text-blue-600 hover:text-blue-800 transition duration-150 p-1 rounded-full hover:bg-blue-100",title:"编辑",children:r.jsx(o.vPQ,{size:18})}),r.jsx("button",{onClick:()=>P(e.id),className:"text-red-600 hover:text-red-800 transition duration-150 p-1 rounded-full hover:bg-red-100",title:"删除",children:r.jsx(o.Ybf,{size:18})})]})]})},e.id))})}),p&&r.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-xl w-full max-w-2xl transform transition-all",children:[r.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-800",children:"编辑FAQ"}),(0,r.jsxs)("form",{onSubmit:F,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"editQuestion",className:"block text-sm font-medium text-gray-700",children:"问题"}),r.jsx("input",{type:"text",id:"editQuestion",value:p.question,onChange:e=>f({...p,question:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"editAnswer",className:"block text-sm font-medium text-gray-700",children:"答案"}),r.jsx("textarea",{id:"editAnswer",rows:5,value:p.answer,onChange:e=>f({...p,answer:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"editCategory",className:"block text-sm font-medium text-gray-700",children:"分类"}),(0,r.jsxs)("select",{id:"editCategory",value:p.category,onChange:e=>f({...p,category:e.target.value}),className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",children:[r.jsx("option",{value:"",children:"选择分类"}),s.map(e=>r.jsx("option",{value:e.name,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"editStatus",className:"block text-sm font-medium text-gray-700",children:"状态"}),(0,r.jsxs)("select",{id:"editStatus",value:p.status,onChange:e=>f({...p,status:e.target.value}),className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",children:[r.jsx("option",{value:"published",children:"已发布"}),r.jsx("option",{value:"draft",children:"草稿"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[r.jsx("button",{type:"button",onClick:()=>f(null),className:"bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out",children:"取消"}),(0,r.jsxs)("button",{type:"submit",className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(o.mW3,{className:"mr-2"})," 保存更改"]})]})]})]})}),b&&r.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-xl w-full max-w-lg transform transition-all",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"管理分类"}),r.jsx("button",{onClick:()=>{y(!1),v(""),N(null)},className:"text-gray-500 hover:text-gray-700",children:r.jsx(o.$Rx,{size:24})})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("label",{htmlFor:"newCategoryName",className:"block text-sm font-medium text-gray-700",children:w?"编辑分类名称":"添加新分类"}),(0,r.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[r.jsx("input",{type:"text",id:"newCategoryName",value:j,onChange:e=>v(e.target.value),className:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-l-md sm:text-sm border-gray-300 px-3 py-2",placeholder:"例如：留学申请"}),(0,r.jsxs)("button",{type:"button",onClick:w?Q:A,className:`${w?"bg-green-500 hover:bg-green-600":"bg-blue-500 hover:bg-blue-600"} inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150`,children:[r.jsx(o.mW3,{className:"mr-2"})," ",w?"更新":"添加"]})]}),w&&r.jsx("button",{type:"button",onClick:()=>{N(null),v("")},className:"mt-2 text-sm text-gray-600 hover:text-gray-800",children:"取消编辑"})]}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"现有分类"}),0===s.length?r.jsx("p",{className:"text-sm text-gray-500",children:"暂无分类。"}):r.jsx("ul",{className:"divide-y divide-gray-200 max-h-60 overflow-y-auto border rounded-md",children:s.map(e=>(0,r.jsxs)("li",{className:"px-4 py-3 flex justify-between items-center hover:bg-gray-50",children:[r.jsx("span",{className:"text-sm text-gray-800",children:e.name}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>Z(e),className:"text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-100",title:"编辑分类",children:r.jsx(o.vPQ,{size:16})}),r.jsx("button",{onClick:()=>_(e.id),className:"text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-100",title:"删除分类",children:r.jsx(o.Ybf,{size:16})})]})]},e.id))})]})})]})}},31683:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\faqs\page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,300,456,238],()=>s(24165));module.exports=r})();