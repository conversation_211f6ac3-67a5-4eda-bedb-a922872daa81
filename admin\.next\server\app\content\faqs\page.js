(()=>{var e={};e.id=344,e.ids=[344],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},24165:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(50482),a=s(69108),l=s(62563),n=s.n(l),i=s(68300),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d=["",{children:["content",{children:["faqs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,31683)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\faqs\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\faqs\\page.tsx"],u="/content/faqs/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/content/faqs/page",pathname:"/content/faqs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10922:(e,t,s)=>{Promise.resolve().then(s.bind(s,15073))},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>o,a:()=>d});var r=s(95344),a=s(3729),l=s(22254),n=s(43932);let i=(0,a.createContext)(void 0);function o({children:e}){let[t,s]=(0,a.useState)(null),[o,d]=(0,a.useState)(!0),c=(0,l.useRouter)(),u=(0,l.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(e&&t&&"undefined"!==t&&"null"!==t)try{n.Z.defaults.headers.common.Authorization=`Bearer ${e}`;let r=JSON.parse(t);s(r)}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),"/login"!==u&&c.push("/login")}else"/login"!==u&&c.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[u,c]);let x=async(e,t)=>{try{console.log("AuthContext: 发送登录请求",{username:e});let r=await n.Z.post("/auth/login",{username:e,password:t});if(console.log("AuthContext: 收到响应",r.data),!r.data||!r.data.data)throw Error("API响应格式错误");let{user:a,token:l}=r.data.data;if(!a||!l)throw Error("响应中缺少用户信息或令牌");return console.log("AuthContext: 解析的用户数据",{user:a,token:l}),localStorage.setItem("adminToken",l),localStorage.setItem("adminUser",JSON.stringify(a)),n.Z.defaults.headers.common.Authorization=`Bearer ${l}`,s(a),console.log("AuthContext: 登录成功，用户状态已更新"),a}catch(e){throw console.error("AuthContext: 登录失败",e),e}};return r.jsx(i.Provider,{value:{user:t,loading:o,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete n.Z.defaults.headers.common.Authorization,s(null),c.push("/login")},updateUserInfo:e=>{if(t){let r={...t,...e};s(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!t},children:e})}function d(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},15073:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95344),a=s(3729),l=s(20783),n=s.n(l),i=s(44669),o=s(32456),d=s(43932);function c(){let[e,t]=(0,a.useState)([]),[s,l]=(0,a.useState)([]),[c,u]=(0,a.useState)(!0),[x,m]=(0,a.useState)(""),[h,g]=(0,a.useState)(""),[p,b]=(0,a.useState)(null),[f,j]=(0,a.useState)(!1),[y,v]=(0,a.useState)(""),[N,w]=(0,a.useState)(null),k=async()=>{u(!0);try{let e=await d.h.get("/faqs",{params:{search:x,category:h}});t(e.data)}catch(e){i.ZP.error("获取FAQ列表失败"),console.error("获取FAQ列表失败:",e)}u(!1)},q=async()=>{try{let e=await d.h.get("/content/faqs/categories");l(e.data)}catch(e){i.ZP.error("获取分类列表失败"),console.error("获取分类列表失败:",e)}};(0,a.useEffect)(()=>{k(),q()},[]),(0,a.useEffect)(()=>{let e=setTimeout(()=>{k()},300);return()=>clearTimeout(e)},[x,h]);let P=async s=>{if(window.confirm("确定要删除此FAQ吗？"))try{await d.h.delete(`/faqs/${s}`),t(e.filter(e=>e.id!==s)),i.ZP.success("FAQ删除成功")}catch(e){i.ZP.error("删除FAQ失败"),console.error("删除FAQ失败:",e)}},A=e=>{b(e)},C=async s=>{if(s.preventDefault(),p)try{let s=await d.h.put(`/faqs/${p.id}`,p);t(e.map(e=>e.id===p.id?s.data:e)),i.ZP.success("FAQ更新成功"),b(null)}catch(e){i.ZP.error("更新FAQ失败"),console.error("更新FAQ失败:",e)}},S=async()=>{if(!y.trim()){i.ZP.error("分类名称不能为空");return}try{let e=await d.h.post("/content/faqs/categories",{name:y});l([...s,e.data]),v(""),i.ZP.success("分类添加成功")}catch(e){i.ZP.error("添加分类失败"),console.error("添加分类失败:",e)}},F=e=>{w(e),v(e.name)},$=async()=>{if(!N||!y.trim()){i.ZP.error("分类名称不能为空");return}try{let e=await d.h.put(`/content/faqs/categories/${N.id}`,{name:y});l(s.map(t=>t.id===N.id?e.data:t)),v(""),w(null),i.ZP.success("分类更新成功")}catch(e){i.ZP.error("更新分类失败"),console.error("更新分类失败:",e)}},Q=async e=>{if(window.confirm("确定要删除此分类吗？删除分类将导致该分类下的FAQ变为未分类。"))try{await d.h.delete(`/content/faqs/categories/${e}`),l(s.filter(t=>t.id!==e)),k(),i.ZP.success("分类删除成功")}catch(e){i.ZP.error("删除分类失败"),console.error("删除分类失败:",e)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[r.jsx(i.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-800",children:"FAQ管理"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>j(!0),className:"bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(o.Ihx,{className:"mr-2"})," 管理分类"]}),r.jsx(n(),{href:"/content/faqs/new",children:(0,r.jsxs)("button",{className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(o.O9D,{className:"mr-2"})," 添加新FAQ"]})})]})]}),r.jsx("div",{className:"mb-6 p-4 bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700",children:"搜索问题"}),(0,r.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(o.jRj,{className:"text-gray-400"})}),r.jsx("input",{type:"text",id:"search",className:"focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-2",placeholder:"输入关键词...",value:x,onChange:e=>m(e.target.value)})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"categoryFilter",className:"block text-sm font-medium text-gray-700",children:"按分类筛选"}),(0,r.jsxs)("select",{id:"categoryFilter",className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",value:h,onChange:e=>g(e.target.value),children:[r.jsx("option",{value:"",children:"所有分类"}),s.map(e=>r.jsx("option",{value:e.name,children:e.name},e.id))]})]})]})}),c?r.jsx("div",{className:"text-center py-10",children:r.jsx("p",{className:"text-lg text-gray-500",children:"正在加载FAQ..."})}):0===e.length?(0,r.jsxs)("div",{className:"text-center py-10 bg-white rounded-lg shadow",children:[r.jsx(o.$Rx,{className:"mx-auto text-gray-400 text-5xl mb-4"}),r.jsx("p",{className:"text-lg text-gray-500",children:"未找到FAQ。"}),(0,r.jsxs)("p",{className:"text-sm text-gray-400",children:["尝试调整搜索词或筛选条件，或",r.jsx(n(),{href:"/content/faqs/new",className:"text-indigo-600 hover:underline",children:"添加新的FAQ"}),"。"]})]}):r.jsx("div",{className:"bg-white shadow-xl rounded-lg overflow-hidden",children:r.jsx("ul",{className:"divide-y divide-gray-200",children:e.map(e=>r.jsx("li",{className:"p-4 hover:bg-gray-50 transition duration-150",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-indigo-700",children:e.question}),r.jsx("p",{className:"mt-1 text-sm text-gray-600 whitespace-pre-wrap",children:e.answer}),(0,r.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:[(0,r.jsxs)("span",{children:["分类: ",e.category||"未分类"]})," |",(0,r.jsxs)("span",{children:["状态: ",r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"published"===e.status?"已发布":"草稿"})]})," |",(0,r.jsxs)("span",{children:["最后更新: ",new Date(e.updatedAt).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:"flex-shrink-0 flex space-x-2 ml-4",children:[r.jsx("button",{onClick:()=>A(e),className:"text-blue-600 hover:text-blue-800 transition duration-150 p-1 rounded-full hover:bg-blue-100",title:"编辑",children:r.jsx(o.vPQ,{size:18})}),r.jsx("button",{onClick:()=>P(e.id),className:"text-red-600 hover:text-red-800 transition duration-150 p-1 rounded-full hover:bg-red-100",title:"删除",children:r.jsx(o.Ybf,{size:18})})]})]})},e.id))})}),p&&r.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-xl w-full max-w-2xl transform transition-all",children:[r.jsx("h2",{className:"text-2xl font-bold mb-6 text-gray-800",children:"编辑FAQ"}),(0,r.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"editQuestion",className:"block text-sm font-medium text-gray-700",children:"问题"}),r.jsx("input",{type:"text",id:"editQuestion",value:p.question,onChange:e=>b({...p,question:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"editAnswer",className:"block text-sm font-medium text-gray-700",children:"答案"}),r.jsx("textarea",{id:"editAnswer",rows:5,value:p.answer,onChange:e=>b({...p,answer:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"editCategory",className:"block text-sm font-medium text-gray-700",children:"分类"}),(0,r.jsxs)("select",{id:"editCategory",value:p.category,onChange:e=>b({...p,category:e.target.value}),className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",children:[r.jsx("option",{value:"",children:"选择分类"}),s.map(e=>r.jsx("option",{value:e.name,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"editStatus",className:"block text-sm font-medium text-gray-700",children:"状态"}),(0,r.jsxs)("select",{id:"editStatus",value:p.status,onChange:e=>b({...p,status:e.target.value}),className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",children:[r.jsx("option",{value:"published",children:"已发布"}),r.jsx("option",{value:"draft",children:"草稿"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[r.jsx("button",{type:"button",onClick:()=>b(null),className:"bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out",children:"取消"}),(0,r.jsxs)("button",{type:"submit",className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(o.mW3,{className:"mr-2"})," 保存更改"]})]})]})]})}),f&&r.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-xl w-full max-w-lg transform transition-all",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-800",children:"管理分类"}),r.jsx("button",{onClick:()=>{j(!1),v(""),w(null)},className:"text-gray-500 hover:text-gray-700",children:r.jsx(o.$Rx,{size:24})})]}),(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("label",{htmlFor:"newCategoryName",className:"block text-sm font-medium text-gray-700",children:N?"编辑分类名称":"添加新分类"}),(0,r.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[r.jsx("input",{type:"text",id:"newCategoryName",value:y,onChange:e=>v(e.target.value),className:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-l-md sm:text-sm border-gray-300 px-3 py-2",placeholder:"例如：留学申请"}),(0,r.jsxs)("button",{type:"button",onClick:N?$:S,className:`${N?"bg-green-500 hover:bg-green-600":"bg-blue-500 hover:bg-blue-600"} inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150`,children:[r.jsx(o.mW3,{className:"mr-2"})," ",N?"更新":"添加"]})]}),N&&r.jsx("button",{type:"button",onClick:()=>{w(null),v("")},className:"mt-2 text-sm text-gray-600 hover:text-gray-800",children:"取消编辑"})]}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"现有分类"}),0===s.length?r.jsx("p",{className:"text-sm text-gray-500",children:"暂无分类。"}):r.jsx("ul",{className:"divide-y divide-gray-200 max-h-60 overflow-y-auto border rounded-md",children:s.map(e=>(0,r.jsxs)("li",{className:"px-4 py-3 flex justify-between items-center hover:bg-gray-50",children:[r.jsx("span",{className:"text-sm text-gray-800",children:e.name}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>F(e),className:"text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-100",title:"编辑分类",children:r.jsx(o.vPQ,{size:16})}),r.jsx("button",{onClick:()=>Q(e.id),className:"text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-100",title:"删除分类",children:r.jsx(o.Ybf,{size:16})})]})]},e.id))})]})})]})}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(95344);s(3729),s(4047);var a=s(99847),l=s(44669),n=s(22254);function i({children:e}){let{user:t,logout:s,isAuthenticated:l,loading:i}=(0,a.a)(),o=(0,n.usePathname)();return"/login"===o?r.jsx(r.Fragment,{children:e}):i?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):l?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===o?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function o({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(i,{children:e}),r.jsx(l.x7,{position:"top-right"})]})})})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},31683:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\faqs\page.tsx`),{__esModule:a,$$typeof:l}=r,n=r.default},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:l}=r,n=r.default},4047:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606,783,456],()=>s(24165));module.exports=r})();