(()=>{var e={};e.id=834,e.ids=[834],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},44250:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(50482),l=s(69108),a=s(62563),i=s.n(a),n=s(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c=["",{children:["content",{children:["services",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71321)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\services\\new\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\services\\new\\page.tsx"],u="/content/services/new/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/content/services/new/page",pathname:"/content/services/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},90300:(e,t,s)=>{Promise.resolve().then(s.bind(s,39532))},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>o,a:()=>c});var r=s(95344),l=s(3729),a=s(22254),i=s(43932);let n=(0,l.createContext)(void 0);function o({children:e}){let[t,s]=(0,l.useState)(null),[o,c]=(0,l.useState)(!0),d=(0,a.useRouter)(),u=(0,a.usePathname)();(0,l.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(i.h.defaults.headers.common.Authorization=`Bearer ${e}`,s(JSON.parse(t))):"/login"!==u&&d.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{c(!1)}})()},[u,d]);let x=async(e,t)=>{try{let{user:r,token:l}=(await i.h.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",l),localStorage.setItem("adminUser",JSON.stringify(r)),i.h.defaults.headers.common.Authorization=`Bearer ${l}`,s(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(n.Provider,{value:{user:t,loading:o,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.h.defaults.headers.common.Authorization,s(null),d.push("/login")},isAuthenticated:!!t},children:e})}function c(){let e=(0,l.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},39532:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95344),l=s(3729),a=s(22254),i=s(60708),n=s(44669),o=s(43932);function c(){let e=(0,a.useRouter)(),[t,s]=(0,l.useState)(!1),{register:c,handleSubmit:d,formState:{errors:u},watch:x}=(0,i.cI)({defaultValues:{status:"draft",category:"学业规划"}}),m=x("title"),h=e=>e.toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g,"-").replace(/^-+|-+$/g,""),p=async t=>{s(!0);try{t.slug||(t.slug=h(t.title)),await o.h.post("/content/services",t),n.ZP.success("服务创建成功"),e.push("/content/services")}catch(e){console.error("创建服务失败:",e),n.ZP.error("创建服务失败，请重试"),s(!1)}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"添加新服务"}),r.jsx("p",{className:"text-gray-600",children:"创建新的服务项目"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("form",{onSubmit:d(p),className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务标题 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"title",type:"text",className:`w-full px-3 py-2 border rounded-md ${u.title?"border-red-500":"border-gray-300"}`,placeholder:"输入服务标题",...c("title",{required:"请输入服务标题"})}),u.title&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:u.title.message})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"slug",className:"block text-sm font-medium text-gray-700 mb-1",children:"URL别名"}),r.jsx("input",{id:"slug",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:m?h(m):"自动生成或手动输入",...c("slug")}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"留空将根据标题自动生成"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务分类 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"category",className:`w-full px-3 py-2 border rounded-md ${u.category?"border-red-500":"border-gray-300"}`,...c("category",{required:"请选择服务分类"}),children:[r.jsx("option",{value:"学业规划",children:"学业规划"}),r.jsx("option",{value:"职业发展",children:"职业发展"}),r.jsx("option",{value:"留学服务",children:"留学服务"}),r.jsx("option",{value:"其他服务",children:"其他服务"})]}),u.category&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:u.category.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务简介 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"description",rows:3,className:`w-full px-3 py-2 border rounded-md ${u.description?"border-red-500":"border-gray-300"}`,placeholder:"简要描述服务内容和特点",...c("description",{required:"请输入服务简介"})}),u.description&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:u.description.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务详情 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"content",rows:10,className:`w-full px-3 py-2 border rounded-md ${u.content?"border-red-500":"border-gray-300"}`,placeholder:"详细描述服务内容、流程、特色等",...c("content",{required:"请输入服务详情"})}),u.content&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:u.content.message}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"支持Markdown格式"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"icon",className:"block text-sm font-medium text-gray-700 mb-1",children:"服务图标"}),r.jsx("input",{id:"icon",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"输入图标名称或URL",...c("icon")}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"可以是图标名称或图片URL"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"order",className:"block text-sm font-medium text-gray-700 mb-1",children:"排序"}),r.jsx("input",{id:"order",type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"数字越小排序越靠前",...c("order",{valueAsNumber:!0})})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("label",{className:"inline-flex items-center",children:[r.jsx("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"draft",...c("status")}),r.jsx("span",{className:"ml-2",children:"草稿"})]}),(0,r.jsxs)("label",{className:"inline-flex items-center",children:[r.jsx("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"published",...c("status")}),r.jsx("span",{className:"ml-2",children:"发布"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[r.jsx("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>e.back(),disabled:t,children:"取消"}),r.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:t,children:t?"保存中...":"保存"})]})]})}),r.jsx(n.x7,{position:"top-right"})]})}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95344);s(3729),s(4047);var l=s(99847),a=s(44669),i=s(20783),n=s.n(i),o=s(22254);function c({children:e}){let{user:t,logout:s,isAuthenticated:a,loading:i}=(0,l.a)(),c=(0,o.usePathname)();return"/login"===c?r.jsx(r.Fragment,{children:e}):i?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):a?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx(n(),{href:"/dashboard",className:`block p-2 hover:bg-gray-200 rounded ${"/dashboard"===c?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx(n(),{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx(n(),{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx(n(),{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function d({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(l.H,{children:[r.jsx(c,{children:e}),r.jsx(a.x7,{position:"top-right"})]})})})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let l=r},71321:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>l,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\services\new\page.tsx`),{__esModule:l,$$typeof:a}=r,i=r.default},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>l,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:l,$$typeof:a}=r,i=r.default},4047:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,300,708],()=>s(44250));module.exports=r})();