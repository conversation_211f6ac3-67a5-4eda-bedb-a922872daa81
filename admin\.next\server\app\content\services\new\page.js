(()=>{var e={};e.id=834,e.ids=[834],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},44250:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(50482),a=s(69108),i=s(62563),l=s.n(i),n=s(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d=["",{children:["content",{children:["services",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71321)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\services\\new\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\content\\services\\new\\page.tsx"],x="/content/services/new/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/content/services/new/page",pathname:"/content/services/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},90300:(e,t,s)=>{Promise.resolve().then(s.bind(s,39532))},39532:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95344),a=s(3729),i=s(22254),l=s(60708),n=s(44669),o=s(43932);function d(){let e=(0,i.useRouter)(),[t,s]=(0,a.useState)(!1),{register:d,handleSubmit:c,formState:{errors:x},watch:p}=(0,l.cI)({defaultValues:{status:"draft",category:"学业规划"}}),m=p("title"),u=e=>e.toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g,"-").replace(/^-+|-+$/g,""),h=async t=>{s(!0);try{t.slug||(t.slug=u(t.title)),await o.h.post("/content/services",t),n.ZP.success("服务创建成功"),e.push("/content/services")}catch(e){console.error("创建服务失败:",e),n.ZP.error("创建服务失败，请重试"),s(!1)}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"添加新服务"}),r.jsx("p",{className:"text-gray-600",children:"创建新的服务项目"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("form",{onSubmit:c(h),className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务标题 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"title",type:"text",className:`w-full px-3 py-2 border rounded-md ${x.title?"border-red-500":"border-gray-300"}`,placeholder:"输入服务标题",...d("title",{required:"请输入服务标题"})}),x.title&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:x.title.message})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"slug",className:"block text-sm font-medium text-gray-700 mb-1",children:"URL别名"}),r.jsx("input",{id:"slug",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:m?u(m):"自动生成或手动输入",...d("slug")}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"留空将根据标题自动生成"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务分类 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"category",className:`w-full px-3 py-2 border rounded-md ${x.category?"border-red-500":"border-gray-300"}`,...d("category",{required:"请选择服务分类"}),children:[r.jsx("option",{value:"学业规划",children:"学业规划"}),r.jsx("option",{value:"职业发展",children:"职业发展"}),r.jsx("option",{value:"留学服务",children:"留学服务"}),r.jsx("option",{value:"其他服务",children:"其他服务"})]}),x.category&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:x.category.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务简介 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"description",rows:3,className:`w-full px-3 py-2 border rounded-md ${x.description?"border-red-500":"border-gray-300"}`,placeholder:"简要描述服务内容和特点",...d("description",{required:"请输入服务简介"})}),x.description&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:x.description.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务详情 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"content",rows:10,className:`w-full px-3 py-2 border rounded-md ${x.content?"border-red-500":"border-gray-300"}`,placeholder:"详细描述服务内容、流程、特色等",...d("content",{required:"请输入服务详情"})}),x.content&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:x.content.message}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"支持Markdown格式"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"icon",className:"block text-sm font-medium text-gray-700 mb-1",children:"服务图标"}),r.jsx("input",{id:"icon",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"输入图标名称或URL",...d("icon")}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"可以是图标名称或图片URL"})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"order",className:"block text-sm font-medium text-gray-700 mb-1",children:"排序"}),r.jsx("input",{id:"order",type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"数字越小排序越靠前",...d("order",{valueAsNumber:!0})})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("label",{className:"inline-flex items-center",children:[r.jsx("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"draft",...d("status")}),r.jsx("span",{className:"ml-2",children:"草稿"})]}),(0,r.jsxs)("label",{className:"inline-flex items-center",children:[r.jsx("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"published",...d("status")}),r.jsx("span",{className:"ml-2",children:"发布"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[r.jsx("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>e.back(),disabled:t,children:"取消"}),r.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:t,children:t?"保存中...":"保存"})]})]})}),r.jsx(n.x7,{position:"top-right"})]})}},71321:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\content\services\new\page.tsx`),{__esModule:a,$$typeof:i}=r,l=r.default}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,300,708,238],()=>s(44250));module.exports=r})();