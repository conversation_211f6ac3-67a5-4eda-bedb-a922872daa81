(()=>{var t={};t.id=702,t.ids=[702],t.modules={47849:t=>{"use strict";t.exports=require("next/dist/client/components/action-async-storage.external")},55403:t=>{"use strict";t.exports=require("next/dist/client/components/request-async-storage.external")},94749:t=>{"use strict";t.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:t=>{"use strict";t.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:t=>{"use strict";t.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:t=>{"use strict";t.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:t=>{"use strict";t.exports=require("assert")},6113:t=>{"use strict";t.exports=require("crypto")},82361:t=>{"use strict";t.exports=require("events")},57147:t=>{"use strict";t.exports=require("fs")},13685:t=>{"use strict";t.exports=require("http")},95687:t=>{"use strict";t.exports=require("https")},22037:t=>{"use strict";t.exports=require("os")},71017:t=>{"use strict";t.exports=require("path")},12781:t=>{"use strict";t.exports=require("stream")},76224:t=>{"use strict";t.exports=require("tty")},57310:t=>{"use strict";t.exports=require("url")},73837:t=>{"use strict";t.exports=require("util")},59796:t=>{"use strict";t.exports=require("zlib")},11925:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>f,pages:()=>s,routeModule:()=>h,tree:()=>l});var n=r(50482),o=r(69108),i=r(62563),a=r.n(i),c=r(68300),u={};for(let t in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,79865)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],s=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\dashboard\\page.tsx"],f="/dashboard/page",p={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},28584:(t,e,r)=>{Promise.resolve().then(r.bind(r,53451))},89747:(t,e,r)=>{Promise.resolve().then(r.bind(r,67329))},95444:(t,e,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},99847:(t,e,r)=>{"use strict";r.d(e,{H:()=>u,a:()=>l});var n=r(95344),o=r(3729),i=r(22254),a=r(43932);let c=(0,o.createContext)(void 0);function u({children:t}){let[e,r]=(0,o.useState)(null),[u,l]=(0,o.useState)(!0),s=(0,i.useRouter)(),f=(0,i.usePathname)();(0,o.useEffect)(()=>{(async()=>{try{let t=localStorage.getItem("adminToken"),e=localStorage.getItem("adminUser");t&&e?(a.Z.defaults.headers.common.Authorization=`Bearer ${t}`,r(JSON.parse(e))):"/login"!==f&&s.push("/login")}catch(t){console.error("认证检查失败:",t)}finally{l(!1)}})()},[f,s]);let p=async(t,e)=>{try{let{user:n,token:o}=(await a.Z.post("/auth/login",{username:t,password:e})).data;return localStorage.setItem("adminToken",o),localStorage.setItem("adminUser",JSON.stringify(n)),a.Z.defaults.headers.common.Authorization=`Bearer ${o}`,r(n),n}catch(t){throw console.error("登录失败:",t),t}};return n.jsx(c.Provider,{value:{user:e,loading:u,login:p,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete a.Z.defaults.headers.common.Authorization,r(null),s.push("/login")},updateUserInfo:t=>{if(e){let n={...e,...t};r(n),localStorage.setItem("adminUser",JSON.stringify(n))}},isAuthenticated:!!e},children:t})}function l(){let t=(0,o.useContext)(c);if(void 0===t)throw Error("useAuth must be used within an AuthProvider");return t}},53451:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>yF});var n={};r.r(n),r.d(n,{scaleBand:()=>nx,scaleDiverging:()=>function t(){var e=ii(cw()(oY));return e.copy=function(){return cg(e,t())},nd.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=iy(cw()).domain([.1,1,10]);return e.copy=function(){return cg(e,t()).base(e.base())},nd.apply(e,arguments)},scaleDivergingPow:()=>cj,scaleDivergingSqrt:()=>cS,scaleDivergingSymlog:()=>function t(){var e=ib(cw());return e.copy=function(){return cg(e,t()).constant(e.constant())},nd.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t=+t)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,oG),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,oG):[0,1],ii(n)},scaleImplicit:()=>nb,scaleLinear:()=>ia,scaleLog:()=>function t(){let e=iy(o1()).domain([1,10]);return e.copy=()=>o0(e,t()).base(e.base()),nh.apply(e,arguments),e},scaleOrdinal:()=>ng,scalePoint:()=>nO,scalePow:()=>ij,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=on){if(!(!(n=t.length)||isNaN(e=+e))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:n[oi(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e=+e)||r.push(e);return r.sort(n9),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nh.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[oi(i,t,0,o)]:e}function u(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r=+r,n=+n,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nh.apply(ii(c),arguments)},scaleRadial:()=>function t(){var e,r=o2(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(iP(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,oG)).map(iP)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},nh.apply(i,arguments),ii(i)},scaleSequential:()=>function t(){var e=ii(cb()(oY));return e.copy=function(){return cg(e,t())},nd.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=iy(cb()).domain([1,10]);return e.copy=function(){return cg(e,t()).base(e.base())},nd.apply(e,arguments)},scaleSequentialPow:()=>cx,scaleSequentialQuantile:()=>function t(){var e=[],r=oY;function n(t){if(null!=t&&!isNaN(t=+t))return r((oi(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r=+r)||e.push(r);return e.sort(n9),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n=+n)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e=+e))){if(e<=0||n<2)return ik(t);if(e>=1)return iA(t);var n,o=(n-1)*e,i=Math.floor(o),a=iA((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?iE:function(t=n9){if(t===n9)return iE;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(o,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,i)}let a=e[r],c=n,u=o;for(iT(e,n,r),i(e[o],a)>0&&iT(e,n,o);c<u;){for(iT(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?iT(e,n,u):iT(e,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return e})(t,i).subarray(0,i+1));return a+(ik(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nd.apply(n,arguments)},scaleSequentialSqrt:()=>cO,scaleSequentialSymlog:()=>function t(){var e=ib(cb());return e.copy=function(){return cg(e,t()).constant(e.constant())},nd.apply(e,arguments)},scaleSqrt:()=>iS,scaleSymlog:()=>function t(){var e=ib(o1());return e.copy=function(){return o0(e,t()).constant(e.constant())},nh.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[oi(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},nh.apply(i,arguments)},scaleTime:()=>cv,scaleUtc:()=>cm,tickFormat:()=>io});var o=r(95344),i=r(3729),a=r.n(i),c=r(32456);let u=function(){for(var t,e,r=0,n="",o=arguments.length;r<o;r++)(t=arguments[r])&&(e=function t(e){var r,n,o="";if("string"==typeof e||"number"==typeof e)o+=e;else if("object"==typeof e){if(Array.isArray(e)){var i=e.length;for(r=0;r<i;r++)e[r]&&(n=t(e[r]))&&(o&&(o+=" "),o+=n)}else for(n in e)e[n]&&(o&&(o+=" "),o+=n)}return o}(t))&&(n&&(n+=" "),n+=e);return n};var l=r(4011),s=r.n(l),f=r(62055),p=r.n(f),h=r(44505),d=r.n(h),y=r(41365),v=r.n(y),m=r(73791),b=r.n(m),g=function(t){return 0===t?0:t>0?1:-1},x=function(t){return p()(t)&&t.indexOf("%")===t.length-1},O=function(t){return b()(t)&&!d()(t)},w=function(t){return O(t)||p()(t)},j=0,S=function(t){var e=++j;return"".concat(t||"").concat(e)},P=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!O(t)&&!p()(t))return n;if(x(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return d()(r)&&(r=n),o&&r>e&&(r=e),r},A=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},k=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},E=function(t,e){return O(t)&&O(e)?function(r){return t+r*(e-t)}:function(){return e}};function T(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):v()(t,e))===r}):null}var _=function(t,e){return O(t)&&O(e)?t-e:p()(t)&&p()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))},M=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},C=r(56995),N=r.n(C),D=r(51282),I=r.n(D),B=r(60986),R=r.n(B),L=r(99046);function z(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function U(t){return(U="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var F=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],$=["points","pathLength"],q={svg:["viewBox","children"],polygon:$,polyline:$},W=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],X=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,i.isValidElement)(t)&&(r=t.props),!R()(r))return null;var n={};return Object.keys(r).forEach(function(t){W.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},H=function(t,e,r){if(!R()(t)||"object"!==U(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];W.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n},V=["children"],G=["children"];function K(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function Y(t){return(Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Z={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},J=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},Q=null,tt=null,te=function t(e){if(e===Q&&Array.isArray(tt))return tt;var r=[];return i.Children.forEach(e,function(e){N()(e)||((0,L.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),tt=r,Q=e,r};function tr(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return J(t)}):[J(e)],te(t).forEach(function(t){var e=v()(t,"type.displayName")||v()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tn(t,e){var r=tr(t,e);return r&&r[0]}var to=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!O(r)&&!(r<=0)&&!!O(n)&&!(n<=0)},ti=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],ta=function(t,e,r,n){var o,i=null!==(o=null==q?void 0:q[n])&&void 0!==o?o:[];return e.startsWith("data-")||!I()(t)&&(n&&i.includes(e)||F.includes(e))||r&&W.includes(e)},tc=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,i.isValidElement)(t)&&(n=t.props),!R()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;ta(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},tu=function t(e,r){if(e===r)return!0;var n=i.Children.count(e);if(n!==i.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tl(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var a=e[o],c=r[o];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!tl(a,c))return!1}return!0},tl=function(t,e){if(N()(t)&&N()(e))return!0;if(!N()(t)&&!N()(e)){var r=t.props||{},n=r.children,o=K(r,V),i=e.props||{},a=i.children,c=K(i,G);if(n&&a)return z(o,c)&&tu(n,a);if(!n&&!a)return z(o,c)}return!1},ts=function(t,e){var r=[],n={};return te(t).forEach(function(t,o){if(t&&t.type&&p()(t.type)&&ti.indexOf(t.type)>=0)r.push(t);else if(t){var i=J(t.type),a=e[i]||{},c=a.handler,u=a.once;if(c&&(!u||!n[i])){var l=c(t,i,o);r.push(l),n[i]=!0}}}),r},tf=function(t){var e=t&&t.type;return e&&Z[e]?Z[e]:null};function tp(t){return(tp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function th(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function td(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?th(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=tp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tp(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):th(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ty(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tv=(0,i.forwardRef)(function(t,e){var r,n=t.aspect,o=t.initialDimension,c=void 0===o?{width:-1,height:-1}:o,l=t.width,f=void 0===l?"100%":l,p=t.height,h=void 0===p?"100%":p,d=t.minWidth,y=void 0===d?0:d,v=t.minHeight,m=t.maxHeight,b=t.children,g=t.debounce,O=void 0===g?0:g,w=t.id,j=t.className,S=t.onResize,P=t.style,A=(0,i.useRef)(null),k=(0,i.useRef)();k.current=S,(0,i.useImperativeHandle)(e,function(){return Object.defineProperty(A.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),A.current},configurable:!0})});var E=function(t){if(Array.isArray(t))return t}(r=(0,i.useState)({containerWidth:c.width,containerHeight:c.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return ty(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ty(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),T=E[0],_=E[1],C=(0,i.useCallback)(function(t,e){_(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,i.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;C(n,o),null===(e=k.current)||void 0===e||e.call(k,n,o)};O>0&&(t=s()(t,O,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=A.current.getBoundingClientRect();return C(r.width,r.height),e.observe(A.current),function(){e.disconnect()}},[C,O]);var N=(0,i.useMemo)(function(){var t=T.containerWidth,e=T.containerHeight;if(t<0||e<0)return null;M(x(f)||x(h),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",f,h),M(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=x(f)?t:f,o=x(h)?e:h;n&&n>0&&(r?o=r/n:o&&(r=o*n),m&&o>m&&(o=m)),M(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,f,h,y,v,n);var c=!Array.isArray(b)&&J(b.type).endsWith("Chart");return a().Children.map(b,function(t){return a().isValidElement(t)?(0,i.cloneElement)(t,td({width:r,height:o},c?{style:td({height:"100%",width:"100%",maxHeight:o,maxWidth:r},t.props.style)}:{})):t})},[n,b,h,m,v,y,T,f]);return a().createElement("div",{id:w?"".concat(w):void 0,className:u("recharts-responsive-container",j),style:td(td({},void 0===P?{}:P),{},{width:f,height:h,minWidth:y,minHeight:v,maxHeight:m}),ref:A},N)}),tm=r(18590),tb=r.n(tm),tg=r(16476),tx=r.n(tg);function tO(t,e){if(!t)throw Error("Invariant failed")}var tw=["children","width","height","viewBox","className","style","title","desc"];function tj(){return(tj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tS(t){var e=t.children,r=t.width,n=t.height,o=t.viewBox,i=t.className,c=t.style,l=t.title,s=t.desc,f=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tw),p=o||{width:r,height:n,x:0,y:0},h=u("recharts-surface",i);return a().createElement("svg",tj({},tc(f,!0,"svg"),{className:h,width:r,height:n,style:c,viewBox:"".concat(p.x," ").concat(p.y," ").concat(p.width," ").concat(p.height)}),a().createElement("title",null,l),a().createElement("desc",null,s),e)}var tP=["children","className"];function tA(){return(tA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tk=a().forwardRef(function(t,e){var r=t.children,n=t.className,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tP),i=u("recharts-layer",n);return a().createElement("g",tA({className:i},tc(o,!0),{ref:e}),r)});function tE(t){return(tE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tT(){return(tT=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function t_(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tM(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tC(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tM(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=tE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tE(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tM(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tN(t){return Array.isArray(t)&&w(t[0])&&w(t[1])?t.join(" ~ "):t}var tD=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,o=t.itemStyle,i=void 0===o?{}:o,c=t.labelStyle,l=t.payload,s=t.formatter,f=t.itemSorter,p=t.wrapperClassName,h=t.labelClassName,d=t.label,y=t.labelFormatter,v=t.accessibilityLayer,m=tC({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),b=tC({margin:0},void 0===c?{}:c),g=!N()(d),x=g?d:"",O=u("recharts-default-tooltip",p),j=u("recharts-tooltip-label",h);return g&&y&&null!=l&&(x=y(d,l)),a().createElement("div",tT({className:O,style:m},void 0!==v&&v?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:j,style:b},a().isValidElement(x)?x:"".concat(x)),function(){if(l&&l.length){var t=(f?tx()(l,f):l).map(function(t,e){if("none"===t.type)return null;var n=tC({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i),o=t.formatter||s||tN,c=t.value,u=t.name,f=c,p=u;if(o&&null!=f&&null!=p){var h=o(c,u,t,e,l);if(Array.isArray(h)){var d=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return t_(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return t_(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=d[0],p=d[1]}else f=h}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},w(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,w(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tI(t){return(tI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tB(t,e,r){var n;return(n=function(t,e){if("object"!=tI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tI(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var tR="recharts-tooltip-wrapper",tL={visibility:"hidden"};function tz(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&O(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function tU(t){return(tU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tF(Object(r),!0).forEach(function(e){tV(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tq(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tG(n.key),n)}}function tW(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tW=function(){return!!t})()}function tX(t){return(tX=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tH(t,e){return(tH=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tV(t,e,r){return(e=tG(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tG(t){var e=function(t,e){if("object"!=tU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tU(e)?e:e+""}var tK=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=tX(e),tV(t=function(t,e){if(e&&("object"===tU(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tW()?Reflect.construct(e,r||[],tX(this).constructor):e.apply(this,r)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),tV(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tH(t,e)}(n,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,o,i,c,l,s,f,p,h,d,y,v,m,b,g,x,w=this,j=this.props,S=j.active,P=j.allowEscapeViewBox,A=j.animationDuration,k=j.animationEasing,E=j.children,T=j.coordinate,_=j.hasPayload,M=j.isAnimationActive,C=j.offset,N=j.position,D=j.reverseDirection,I=j.useTranslate3d,B=j.viewBox,R=j.wrapperStyle,L=(h=(t={allowEscapeViewBox:P,coordinate:T,offsetTopLeft:C,position:N,reverseDirection:D,tooltipBox:this.state.lastBoundingBox,useTranslate3d:I,viewBox:B}).allowEscapeViewBox,d=t.coordinate,y=t.offsetTopLeft,v=t.position,m=t.reverseDirection,b=t.tooltipBox,g=t.useTranslate3d,x=t.viewBox,b.height>0&&b.width>0&&d?(r=(e={translateX:f=tz({allowEscapeViewBox:h,coordinate:d,key:"x",offsetTopLeft:y,position:v,reverseDirection:m,tooltipDimension:b.width,viewBox:x,viewBoxDimension:x.width}),translateY:p=tz({allowEscapeViewBox:h,coordinate:d,key:"y",offsetTopLeft:y,position:v,reverseDirection:m,tooltipDimension:b.height,viewBox:x,viewBoxDimension:x.height}),useTranslate3d:g}).translateX,n=e.translateY,s={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):s=tL,{cssProperties:s,cssClasses:(i=(o={translateX:f,translateY:p,coordinate:d}).coordinate,c=o.translateX,l=o.translateY,u(tR,tB(tB(tB(tB({},"".concat(tR,"-right"),O(c)&&i&&O(i.x)&&c>=i.x),"".concat(tR,"-left"),O(c)&&i&&O(i.x)&&c<i.x),"".concat(tR,"-bottom"),O(l)&&i&&O(i.y)&&l>=i.y),"".concat(tR,"-top"),O(l)&&i&&O(i.y)&&l<i.y)))}),z=L.cssClasses,U=L.cssProperties,F=t$(t$({transition:M&&S?"transform ".concat(A,"ms ").concat(k):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&S&&_?"visible":"hidden",position:"absolute",top:0,left:0},R);return a().createElement("div",{tabIndex:-1,className:z,style:F,ref:function(t){w.wrapperNode=t}},E)}}],tq(n.prototype,e),r&&tq(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent),tY={isSsr:!0,get:function(t){return tY[t]},set:function(t,e){if("string"==typeof t)tY[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){tY[e]=t[e]})}}},tZ=r(99541),tJ=r.n(tZ);function tQ(t,e,r){return!0===e?tJ()(t,r):I()(e)?tJ()(t,e):t}function t0(t){return(t0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t1(Object(r),!0).forEach(function(e){t7(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t3(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t8(n.key),n)}}function t6(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t6=function(){return!!t})()}function t5(t){return(t5=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t4(t,e){return(t4=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function t7(t,e,r){return(e=t8(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t8(t){var e=function(t,e){if("object"!=t0(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t0(e)?e:e+""}function t9(t){return t.dataKey}var et=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=t5(t),function(t,e){if(e&&("object"===t0(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t6()?Reflect.construct(t,e||[],t5(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&t4(t,e)}(n,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,c=r.animationEasing,u=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=h?h:[];s&&x.length&&(x=tQ(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,t9));var O=x.length>0;return a().createElement(tK,{allowEscapeViewBox:o,animationDuration:i,animationEasing:c,isAnimationActive:f,active:n,coordinate:l,hasPayload:O,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=t2(t2({},this.props),{},{payload:x}),a().isValidElement(u)?a().cloneElement(u,t):"function"==typeof u?a().createElement(u,t):a().createElement(tD,t)))}}],t3(n.prototype,e),r&&t3(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);t7(et,"displayName","Tooltip"),t7(et,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!tY.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var ee=r(88523),er=r.n(ee);let en=Math.cos,eo=Math.sin,ei=Math.sqrt,ea=Math.PI,ec=2*ea,eu={draw(t,e){let r=ei(e/ea);t.moveTo(r,0),t.arc(0,0,r,0,ec)}},el=ei(1/3),es=2*el,ef=eo(ea/10)/eo(7*ea/10),ep=eo(ec/10)*ef,eh=-en(ec/10)*ef,ed=ei(3),ey=ei(3)/2,ev=1/ei(12),em=(ev/2+1)*3;function eb(t){return function(){return t}}let eg=Math.PI,ex=2*eg,eO=ex-1e-6;function ew(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class ej{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?ew:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return ew;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t=+t,e=+e,r=+r,n=+n,(o=+o)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6){if(Math.abs(s*c-u*l)>1e-6&&o){let p=r-i,h=n-a,d=c*c+u*u,y=Math.sqrt(d),v=Math.sqrt(f),m=o*Math.tan((eg-Math.acos((d+f-(p*p+h*h))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${o},${o},0,0,${+(s*p>l*h)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}}arc(t,e,r,n,o,i){if(t=+t,e=+e,i=!!i,(r=+r)<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%ex+ex),f>eO?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=eg)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function eS(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new ej(e)}function eP(t){return(eP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}ej.prototype,ei(3),ei(3);var eA=["type","size","sizeType"];function ek(){return(ek=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eE(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=eP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eP(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var e_={symbolCircle:eu,symbolCross:{draw(t,e){let r=ei(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=ei(e/es),n=r*el;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=ei(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=ei(.8908130915292852*e),n=ep*r,o=eh*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=ec*e/5,a=en(i),c=eo(i);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-ei(e/(3*ed));t.moveTo(0,2*r),t.lineTo(-ed*r,-r),t.lineTo(ed*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=ei(e/em),n=r/2,o=r*ev,i=r*ev+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-ey*o,ey*n+-.5*o),t.lineTo(-.5*n-ey*i,ey*n+-.5*i),t.lineTo(-.5*a-ey*i,ey*a+-.5*i),t.lineTo(-.5*n+ey*o,-.5*o-ey*n),t.lineTo(-.5*n+ey*i,-.5*i-ey*n),t.lineTo(-.5*a+ey*i,-.5*i-ey*a),t.closePath()}}},eM=Math.PI/180,eC=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eM;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eN=function(t){var e,r=t.type,n=void 0===r?"circle":r,o=t.size,i=void 0===o?64:o,c=t.sizeType,l=void 0===c?"area":c,s=eT(eT({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,eA)),{},{type:n,size:i,sizeType:l}),f=s.className,p=s.cx,h=s.cy,d=tc(s,!0);return p===+p&&h===+h&&i===+i?a().createElement("path",ek({},d,{className:u("recharts-symbols",f),transform:"translate(".concat(p,", ").concat(h,")"),d:(e=e_["symbol".concat(er()(n))]||eu,(function(t,e){let r=null,n=eS(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:eb(t||eu),e="function"==typeof e?e:eb(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:eb(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:eb(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(eC(i,l,n))())})):null};function eD(t){return(eD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eI(){return(eI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eR(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e$(n.key),n)}}function eL(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eL=function(){return!!t})()}function ez(t){return(ez=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eU(t,e){return(eU=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eF(t,e,r){return(e=e$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e$(t){var e=function(t,e){if("object"!=eD(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eD(e)?e:e+""}eN.registerSymbol=function(t,e){e_["symbol".concat(er()(t))]=e};var eq=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=ez(t),function(t,e){if(e&&("object"===eD(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eL()?Reflect.construct(t,e||[],ez(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eU(t,e)}(n,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return a().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(t.legendIcon)){var i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eB(Object(r),!0).forEach(function(e){eF(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete i.legendIcon,a().cloneElement(t.legendIcon,i)}return a().createElement(eN,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,o=e.layout,i=e.formatter,c=e.inactiveColor,l={x:0,y:0,width:32,height:32},s={display:"horizontal"===o?"inline-block":"block",marginRight:10},f={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var o=e.formatter||i,p=u(eF(eF({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var h=I()(e.value)?null:e.value;M(!I()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var d=e.inactive?c:e.color;return a().createElement("li",eI({className:p,style:s,key:"legend-item-".concat(r)},H(t.props,e,r)),a().createElement(tS,{width:n,height:n,viewBox:l,style:f},t.renderIcon(e)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:d}},o?o(h,e,r):h))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],eR(n.prototype,e),r&&eR(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function eW(t){return(eW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eF(eq,"displayName","Legend"),eF(eq,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var eX=["ref"];function eH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eH(Object(r),!0).forEach(function(e){eJ(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eG(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eQ(n.key),n)}}function eK(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eK=function(){return!!t})()}function eY(t){return(eY=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eZ(t,e){return(eZ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function eJ(t,e,r){return(e=eQ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eQ(t){var e=function(t,e){if("object"!=eW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eW(e)?e:e+""}function e0(t){return t.value}var e1=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=eY(e),eJ(t=function(t,e){if(e&&("object"===eW(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eK()?Reflect.construct(e,r||[],eY(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eZ(t,e)}(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?eV({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),eV(eV({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,o=e.height,i=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=eV(eV({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return a().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(a().isValidElement(t))return a().cloneElement(t,e);if("function"==typeof t)return a().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,eX);return a().createElement(eq,r)}(r,eV(eV({},this.props),{},{payload:tQ(u,c,e0)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=eV(eV({},this.defaultProps),t.props).layout;return"vertical"===r&&O(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&eG(n.prototype,e),r&&eG(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function e2(){return(e2=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}eJ(e1,"displayName","Legend"),eJ(e1,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var e3=function(t){var e=t.cx,r=t.cy,n=t.r,o=u("recharts-dot",t.className);return e===+e&&r===+r&&n===+n?a().createElement("circle",e2({},tc(t,!1),X(t),{className:o,cx:e,cy:r,r:n})):null},e6=r(7470),e5=r.n(e6),e4=Object.getOwnPropertyNames,e7=Object.getOwnPropertySymbols,e8=Object.prototype.hasOwnProperty;function e9(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function rt(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function re(t){return e4(t).concat(e7(t))}var rr=Object.hasOwn||function(t,e){return e8.call(t,e)};function rn(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var ro=Object.getOwnPropertyDescriptor,ri=Object.keys;function ra(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rc(t,e){return rn(t.getTime(),e.getTime())}function ru(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rl(t,e){return t===e}function rs(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,h=o.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function rf(t,e,r){var n=ri(t),o=n.length;if(ri(e).length!==o)return!1;for(;o-- >0;)if(!rb(t,e,r,n[o]))return!1;return!0}function rp(t,e,r){var n,o,i,a=re(t),c=a.length;if(re(e).length!==c)return!1;for(;c-- >0;)if(!rb(t,e,r,n=a[c])||(o=ro(t,n),i=ro(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rh(t,e){return rn(t.valueOf(),e.valueOf())}function rd(t,e){return t.source===e.source&&t.flags===e.flags}function ry(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(o=u.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rv(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rm(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rb(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||rr(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rg=Array.isArray,rx="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rO=Object.assign,rw=Object.prototype.toString.call.bind(Object.prototype.toString),rj=rS();function rS(t){void 0===t&&(t={});var e,r,n,o,i,a,c,u,l,s,f,p,h,d=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?rp:ra,areDatesEqual:rc,areErrorsEqual:ru,areFunctionsEqual:rl,areMapsEqual:n?e9(rs,rp):rs,areNumbersEqual:rn,areObjectsEqual:n?rp:rf,arePrimitiveWrappersEqual:rh,areRegExpsEqual:rd,areSetsEqual:n?e9(ry,rp):ry,areTypedArraysEqual:n?rp:rv,areUrlsEqual:rm};if(r&&(o=rO({},o,r(o))),e){var i=rt(o.areArraysEqual),a=rt(o.areMapsEqual),c=rt(o.areObjectsEqual),u=rt(o.areSetsEqual);o=rO({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,h=e.areUrlsEqual,function(t,e,d){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,d):"function"===y&&i(t,e,d);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,d);if(rg(t))return r(t,e,d);if(null!=rx&&rx(t))return p(t,e,d);if(v===Date)return n(t,e,d);if(v===RegExp)return s(t,e,d);if(v===Map)return a(t,e,d);if(v===Set)return f(t,e,d);var m=rw(t);return"[object Date]"===m?n(t,e,d):"[object RegExp]"===m?s(t,e,d):"[object Map]"===m?a(t,e,d):"[object Set]"===m?f(t,e,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,d):"[object URL]"===m?h(t,e,d):"[object Error]"===m?o(t,e,d):"[object Arguments]"===m?u(t,e,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,d)}),g=y?y(b):function(t,e,r,n,o,i,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:o,meta:c.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==d&&d,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rP(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rA(t){return(rA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rk(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rE(t){return(rE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r_(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rT(Object(r),!0).forEach(function(e){rM(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rM(t,e,r){var n;return(n=function(t,e){if("object"!==rE(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rE(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rS({strict:!0}),rS({circular:!0}),rS({circular:!0,strict:!0}),rS({createInternalComparator:function(){return rn}}),rS({strict:!0,createInternalComparator:function(){return rn}}),rS({circular:!0,createInternalComparator:function(){return rn}}),rS({circular:!0,createInternalComparator:function(){return rn},strict:!0});var rC=function(t){return t},rN=function(t,e){return Object.keys(e).reduce(function(r,n){return r_(r_({},r),{},rM({},n,t(n,e[n])))},{})},rD=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rI=function(t,e,r,n,o,i,a,c){};function rB(t,e){if(t){if("string"==typeof t)return rR(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rR(t,e)}}function rR(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rL=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},rz=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},rU=function(t,e){return function(r){return rz(rL(t,e),r)}},rF=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,u=1;break;case"ease":i=.25,a=.1,c=.25,u=1;break;case"ease-in":i=.42,a=0,c=1,u=1;break;case"ease-out":i=.42,a=0,c=.58,u=1;break;case"ease-in-out":i=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(s,4)||rB(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],u=f[3]}else rI(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rI([i,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=rU(i,c),h=rU(a,u),d=(t=i,e=c,function(r){var n;return rz([].concat(function(t){if(Array.isArray(t))return rR(t)}(n=rL(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rB(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},r$=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(i)?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},rq=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return rF(n);case"spring":return r$();default:if("cubic-bezier"===n.split("(")[0])return rF(n);rI(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rI(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function rW(t){return(rW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rX(t){return function(t){if(Array.isArray(t))return rY(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||rK(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rH(Object(r),!0).forEach(function(e){rG(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rG(t,e,r){var n;return(n=function(t,e){if("object"!==rW(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rW(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rK(t,e){if(t){if("string"==typeof t)return rY(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rY(t,e)}}function rY(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rZ=function(t,e,r){return t+(e-t)*r},rJ=function(t){return t.from!==t.to},rQ=function t(e,r,n){var o=rN(function(t,r){if(rJ(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(n,2)||rK(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return rV(rV({},r),{},{from:i,velocity:a})}return r},r);return n<1?rN(function(t,e){return rJ(e)?rV(rV({},e),{},{velocity:rZ(e.velocity,o[t].velocity,n),from:rZ(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};function r0(t){return(r0="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var r1=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function r2(t){return function(t){if(Array.isArray(t))return r3(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return r3(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r3(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r3(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function r6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r5(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r6(Object(r),!0).forEach(function(e){r4(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r4(t,e,r){return(e=r8(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function r7(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,r8(n.key),n)}}function r8(t){var e=function(t,e){if("object"!==r0(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r0(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===r0(e)?e:String(e)}function r9(t,e){return(r9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nt(t,e){if(e&&("object"===r0(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return ne(t)}function ne(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function nr(t){return(nr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var nn=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&r9(t,e)}(c,t);var e,r,n,o=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=nr(c);if(e){var n=nr(this).constructor;t=Reflect.construct(r,arguments,n)}else t=r.apply(this,arguments);return nt(this,t)});function c(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,c);var r,n=(r=o.call(this,t,e)).props,i=n.isActive,a=n.attributeName,u=n.from,l=n.to,s=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(ne(r)),r.changeStyle=r.changeStyle.bind(ne(r)),!i||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),nt(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},nt(r);r.state={style:a?r4({},a,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:o?r4({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(l);return}if(!rj(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?c:t.to;if(this.state&&u){var p={style:o?r4({},o,f):f};(o&&u[o]!==f||!o&&u!==f)&&this.setState(p)}this.runAnimation(r5(r5({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e,r,n,o,i,a,c,u,l,s=this,f=t.from,p=t.to,h=t.duration,d=t.easing,y=t.begin,v=t.onAnimationEnd,m=t.onAnimationStart,b=(e=rq(d),r=this.changeStyle,a=(i=[Object.keys(f),Object.keys(p)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})})).reduce(function(t,e){return rV(rV({},t),{},rG({},e,[f[e],p[e]]))},{}),c=i.reduce(function(t,e){return rV(rV({},t),{},rG({},e,{from:f[e],velocity:0,to:p[e]}))},{}),u=-1,l=function(){return null},l=e.isStepper?function(t){n||(n=t);var o=(t-n)/e.dt;c=rQ(e,c,o),r(rV(rV(rV({},f),p),rN(function(t,e){return e.from},c))),n=t,Object.values(c).filter(rJ).length&&(u=requestAnimationFrame(l))}:function(t){o||(o=t);var n=(t-o)/h,i=rN(function(t,r){return rZ.apply(void 0,rX(r).concat([e(n)]))},a);if(r(rV(rV(rV({},f),p),i)),n<1)u=requestAnimationFrame(l);else{var c=rN(function(t,r){return rZ.apply(void 0,rX(r).concat([e(1)]))},a);r(rV(rV(rV({},f),p),c))}},function(){return requestAnimationFrame(l),function(){cancelAnimationFrame(u)}});this.manager.start([m,y,function(){s.stopJSAnimation=b()},h,v])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(r2(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(r2(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var h=rD(p,i,c),d=r5(r5(r5({},f.style),u),{},{transition:h});return[].concat(r2(t),[d,i,s]).filter(rC)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,r,n;this.manager=(e=function(){return null},r=!1,n=function t(n){if(!r){if(Array.isArray(n)){if(!n.length)return;var o=function(t){if(Array.isArray(t))return t}(n)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||function(t,e){if(t){if("string"==typeof t)return rk(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rk(t,e)}}(n)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){rP(t.bind(null,a),i);return}t(i),rP(t.bind(null,a));return}"object"===rA(n)&&e(n),"function"==typeof n&&n()}},{stop:function(){r=!0},start:function(t){r=!1,n(t)},subscribe:function(t){return e=t,function(){e=function(){return null}}}})}var o=t.begin,i=t.duration,a=t.attributeName,c=t.to,u=t.easing,l=t.onAnimationStart,s=t.onAnimationEnd,f=t.steps,p=t.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof u||"function"==typeof p||"spring"===u){this.runJSAnimation(t);return}if(f.length>1){this.runStepAnimation(t);return}var d=a?r4({},a,c):c,y=rD(Object.keys(d),i,u);h.start([l,o,r5(r5({},d),{},{transition:y}),i,s])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,r1)),c=i.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!n||0===c||r<=0)return e;var l=function(t){var e=t.props,r=e.style,n=e.className;return(0,i.cloneElement)(t,r5(r5({},o),{},{style:r5(r5({},void 0===r?{}:r),u),className:n}))};return 1===c?l(i.Children.only(e)):a().createElement("div",null,i.Children.map(e,function(t){return l(t)}))}}],r7(c.prototype,r),n&&r7(c,n),Object.defineProperty(c,"prototype",{writable:!1}),c}(i.PureComponent);function no(t){return(no="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ni(){return(ni=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function na(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nc(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=no(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=no(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==no(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nc(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}nn.displayName="Animate",nn.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},nn.propTypes={from:e5().oneOfType([e5().object,e5().string]),to:e5().oneOfType([e5().object,e5().string]),attributeName:e5().string,duration:e5().number,begin:e5().number,easing:e5().oneOfType([e5().string,e5().func]),steps:e5().arrayOf(e5().shape({duration:e5().number.isRequired,style:e5().object.isRequired,easing:e5().oneOfType([e5().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),e5().func]),properties:e5().arrayOf("string"),onAnimationEnd:e5().func})),children:e5().oneOfType([e5().node,e5().func]),isActive:e5().bool,canBegin:e5().bool,onAnimationEnd:e5().func,shouldReAnimate:e5().bool,onAnimationStart:e5().func,onAnimationReStart:e5().func};var nl=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},ns=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;return!!(Math.abs(a)>0&&Math.abs(c)>0)&&r>=Math.min(o,o+a)&&r<=Math.max(o,o+a)&&n>=Math.min(i,i+c)&&n<=Math.max(i,i+c)},nf={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},np=function(t){var e,r=nu(nu({},nf),t),n=(0,i.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,i.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return na(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return na(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],l=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&l(t)}catch(t){}},[]);var s=r.x,f=r.y,p=r.width,h=r.height,d=r.radius,y=r.className,v=r.animationEasing,m=r.animationDuration,b=r.animationBegin,g=r.isAnimationActive,x=r.isUpdateAnimationActive;if(s!==+s||f!==+f||p!==+p||h!==+h||0===p||0===h)return null;var O=u("recharts-rectangle",y);return x?a().createElement(nn,{canBegin:c>0,from:{width:p,height:h,x:s,y:f},to:{width:p,height:h,x:s,y:f},duration:m,animationEasing:v,isActive:x},function(t){var e=t.width,o=t.height,i=t.x,u=t.y;return a().createElement(nn,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:b,duration:m,isActive:g,easing:v},a().createElement("path",ni({},tc(r,!0),{className:O,d:nl(i,u,e,o,d),ref:n})))}):a().createElement("path",ni({},tc(r,!0),{className:O,d:nl(s,f,p,h,d)}))};function nh(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function nd(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class ny extends Map{constructor(t,e=nm){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nv(this,t))}has(t){return super.has(nv(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nv({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nm(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nb=Symbol("implicit");function ng(){var t=new ny,e=[],r=[],n=nb;function o(o){let i=t.get(o);if(void 0===i){if(n!==nb)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new ny,r))t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return ng(e,r).unknown(n)},nh.apply(o,arguments),o}function nx(){var t,e,r=ng().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,h=f?i:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=(function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return p+t*e});return o(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i=+i,a=+a,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i=+i,a=+a,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nx(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},nh.apply(f(),arguments)}function nO(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nx.apply(null,arguments).paddingInner(1))}function nw(t){return(nw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nj(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nS(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nj(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=nw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nw(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nj(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var nP={widthCache:{},cacheCount:0},nA={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nk="recharts_measurement_span",nE=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||tY.isSsr)return{width:0,height:0};var n=(Object.keys(e=nS({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:n});if(nP.widthCache[o])return nP.widthCache[o];try{var i=document.getElementById(nk);i||((i=document.createElement("span")).setAttribute("id",nk),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nS(nS({},nA),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return nP.widthCache[o]=u,++nP.cacheCount>2e3&&(nP.cacheCount=0,nP.widthCache={}),u}catch(t){return{width:0,height:0}}};function nT(t){return(nT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n_(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nM(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nM(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nM(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nC(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nT(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nT(e)?e:e+""}(n.key),n)}}var nN=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nD=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nI=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nB=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nR={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nL=Object.keys(nR),nz=function(){var t,e;function r(t,e){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nI.test(e)||(this.num=NaN,this.unit=""),nL.includes(e)&&(this.num=t*nR[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=n_(null!==(e=nB.exec(t))&&void 0!==e?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&nC(r.prototype,t),e&&nC(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function nU(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=n_(null!==(r=nN.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=nz.parse(null!=o?o:""),u=nz.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(nN,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=n_(null!==(s=nD.exec(e))&&void 0!==s?s:[],4),p=f[1],h=f[2],d=f[3],y=nz.parse(null!=p?p:""),v=nz.parse(null!=d?d:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nD,m.toString())}return e}var nF=/\(([^()]*)\)/;function n$(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=n_(nF.exec(e),2)[1];e=e.replace(nF,nU(r))}return e}(e),e=nU(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var nq=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],nW=["dx","dy","angle","className","breakAll"];function nX(){return(nX=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nH(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function nV(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nG(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nG(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nG(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nK=/[ \f\n\r\t\v\u2028\u2029]+/,nY=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];N()(e)||(o=r?e.toString().split(""):e.toString().split(nK));var i=o.map(function(t){return{word:t,width:nE(t,n).width}}),a=r?0:nE("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(t){return null}},nZ=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=O(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):t.push({words:[i],width:a}),t},[])},p=f(e);if(!s)return p;for(var h=function(t){var e=f(nY({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},d=0,y=c.length-1,v=0;d<=y&&v<=c.length-1;){var m=Math.floor((d+y)/2),b=nV(h(m-1),2),g=b[0],x=b[1],w=nV(h(m),1)[0];if(g||w||(d=m+1),g&&w&&(y=m-1),!g&&w){i=x;break}v++}return i||p},nJ=function(t){return[{words:N()(t)?[]:t.toString().split(nK)}]},nQ=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!tY.isSsr){var c=nY({breakAll:i,children:n,style:o});return c?nZ({breakAll:i,children:n,maxLines:a,style:o},c.wordsWithComputedWidth,c.spaceWidth,e,r):nJ(n)}return nJ(n)},n0="#808080",n1=function(t){var e,r=t.x,n=void 0===r?0:r,o=t.y,c=void 0===o?0:o,l=t.lineHeight,s=void 0===l?"1em":l,f=t.capHeight,p=void 0===f?"0.71em":f,h=t.scaleToFit,d=void 0!==h&&h,y=t.textAnchor,v=t.verticalAnchor,m=t.fill,b=void 0===m?n0:m,g=nH(t,nq),x=(0,i.useMemo)(function(){return nQ({breakAll:g.breakAll,children:g.children,maxLines:g.maxLines,scaleToFit:d,style:g.style,width:g.width})},[g.breakAll,g.children,g.maxLines,d,g.style,g.width]),j=g.dx,S=g.dy,P=g.angle,A=g.className,k=g.breakAll,E=nH(g,nW);if(!w(n)||!w(c))return null;var T=n+(O(j)?j:0),_=c+(O(S)?S:0);switch(void 0===v?"end":v){case"start":e=n$("calc(".concat(p,")"));break;case"middle":e=n$("calc(".concat((x.length-1)/2," * -").concat(s," + (").concat(p," / 2))"));break;default:e=n$("calc(".concat(x.length-1," * -").concat(s,")"))}var M=[];if(d){var C=x[0].width,N=g.width;M.push("scale(".concat((O(N)?N/C:1)/C,")"))}return P&&M.push("rotate(".concat(P,", ").concat(T,", ").concat(_,")")),M.length&&(E.transform=M.join(" ")),a().createElement("text",nX({},tc(E,!0),{x:T,y:_,className:u("recharts-text",A),textAnchor:void 0===y?"start":y,fill:b.includes("url")?n0:b}),x.map(function(t,r){var n=t.words.join(k?"":" ");return a().createElement("tspan",{x:T,dy:0===r?e:s,key:"".concat(n,"-").concat(r)},n)}))};let n2=Math.sqrt(50),n3=Math.sqrt(10),n6=Math.sqrt(2);function n5(t,e,r){let n,o,i;let a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=n2?10:u>=n3?5:u>=n6?2:1;return(c<0?(n=Math.round(t*(i=Math.pow(10,-c)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,c)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?n5(t,e,2*r):[n,o,i]}function n4(t,e,r){if(e=+e,t=+t,!((r=+r)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?n5(e,t,r):n5(t,e,r);if(!(i>=o))return[];let c=i-o+1,u=Array(c);if(n){if(a<0)for(let t=0;t<c;++t)u[t]=-((i-t)/a);else for(let t=0;t<c;++t)u[t]=(i-t)*a}else if(a<0)for(let t=0;t<c;++t)u[t]=-((o+t)/a);else for(let t=0;t<c;++t)u[t]=(o+t)*a;return u}function n7(t,e,r){return n5(t=+t,e=+e,r=+r)[2]}function n8(t,e,r){e=+e,t=+t,r=+r;let n=e<t,o=n?n7(e,t,r):n7(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function n9(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ot(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function oe(t){let e,r,n;function o(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>r(t[e],n)?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=n9,r=(e,r)=>n9(t(e),r),n=(e,r)=>t(e)-r):(e=t===n9||t===ot?t:or,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function or(){return 0}function on(t){return null===t?NaN:+t}let oo=oe(n9),oi=oo.right;function oa(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function oc(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function ou(){}oo.left,oe(on).center;var ol="\\s*([+-]?\\d+)\\s*",os="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",of="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",op=/^#([0-9a-f]{3,8})$/,oh=RegExp(`^rgb\\(${ol},${ol},${ol}\\)$`),od=RegExp(`^rgb\\(${of},${of},${of}\\)$`),oy=RegExp(`^rgba\\(${ol},${ol},${ol},${os}\\)$`),ov=RegExp(`^rgba\\(${of},${of},${of},${os}\\)$`),om=RegExp(`^hsl\\(${os},${of},${of}\\)$`),ob=RegExp(`^hsla\\(${os},${of},${of},${os}\\)$`),og={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function ox(){return this.rgb().formatHex()}function oO(){return this.rgb().formatRgb()}function ow(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=op.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?oj(e):3===r?new oA(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?oS(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?oS(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=oh.exec(t))?new oA(e[1],e[2],e[3],1):(e=od.exec(t))?new oA(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=oy.exec(t))?oS(e[1],e[2],e[3],e[4]):(e=ov.exec(t))?oS(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=om.exec(t))?oC(e[1],e[2]/100,e[3]/100,1):(e=ob.exec(t))?oC(e[1],e[2]/100,e[3]/100,e[4]):og.hasOwnProperty(t)?oj(og[t]):"transparent"===t?new oA(NaN,NaN,NaN,0):null}function oj(t){return new oA(t>>16&255,t>>8&255,255&t,1)}function oS(t,e,r,n){return n<=0&&(t=e=r=NaN),new oA(t,e,r,n)}function oP(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof ou||(o=ow(o)),o)?new oA((o=o.rgb()).r,o.g,o.b,o.opacity):new oA:new oA(t,e,r,null==n?1:n)}function oA(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function ok(){return`#${oM(this.r)}${oM(this.g)}${oM(this.b)}`}function oE(){let t=oT(this.opacity);return`${1===t?"rgb(":"rgba("}${o_(this.r)}, ${o_(this.g)}, ${o_(this.b)}${1===t?")":`, ${t})`}`}function oT(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function o_(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function oM(t){return((t=o_(t))<16?"0":"")+t.toString(16)}function oC(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new oD(t,e,r,n)}function oN(t){if(t instanceof oD)return new oD(t.h,t.s,t.l,t.opacity);if(t instanceof ou||(t=ow(t)),!t)return new oD;if(t instanceof oD)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+(r<n)*6:r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new oD(a,c,u,t.opacity)}function oD(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function oI(t){return(t=(t||0)%360)<0?t+360:t}function oB(t){return Math.max(0,Math.min(1,t||0))}function oR(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function oL(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}oa(ou,ow,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:ox,formatHex:ox,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return oN(this).formatHsl()},formatRgb:oO,toString:oO}),oa(oA,oP,oc(ou,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oA(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oA(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new oA(o_(this.r),o_(this.g),o_(this.b),oT(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:ok,formatHex:ok,formatHex8:function(){return`#${oM(this.r)}${oM(this.g)}${oM(this.b)}${oM((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:oE,toString:oE})),oa(oD,function(t,e,r,n){return 1==arguments.length?oN(t):new oD(t,e,r,null==n?1:n)},oc(ou,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oD(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oD(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new oA(oR(t>=240?t-240:t+120,o,n),oR(t,o,n),oR(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new oD(oI(this.h),oB(this.s),oB(this.l),oT(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=oT(this.opacity);return`${1===t?"hsl(":"hsla("}${oI(this.h)}, ${100*oB(this.s)}%, ${100*oB(this.l)}%${1===t?")":`, ${t})`}`}}));let oz=t=>()=>t;function oU(t,e){var r=e-t;return r?function(e){return t+e*r}:oz(isNaN(t)?e:t)}let oF=function t(e){var r,n=1==(r=+(r=e))?oU:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):oz(isNaN(t)?e:t)};function o(t,e){var r=n((t=oP(t)).r,(e=oP(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=oU(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function o$(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=oP(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}function oq(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}o$(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return oL((r-n/e)*e,a,o,i,c)}}),o$(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return oL((r-n/e)*e,o,i,a,c)}});var oW=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,oX=RegExp(oW.source,"g");function oH(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?oz(e):("number"===o?oq:"string"===o?(n=ow(e))?(e=n,oF):function(t,e){var r,n,o,i,a,c=oW.lastIndex=oX.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(o=oW.exec(t))&&(i=oX.exec(e));)(a=i.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(o=o[0])===(i=i[0])?l[u]?l[u]+=i:l[++u]=i:(l[++u]=null,s.push({i:u,x:oq(o,i)})),c=oX.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof ow?oF:e instanceof Date?function(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}:(r=e,!ArrayBuffer.isView(r)||r instanceof DataView)?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=oH(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=oH(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:oq:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function oV(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function oG(t){return+t}var oK=[0,1];function oY(t){return t}function oZ(t,e){var r;return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function oJ(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=oZ(o,n),i=r(a,i)):(n=oZ(n,o),i=r(i,a)),function(t){return i(n(t))}}function oQ(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=oZ(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=oi(t,e,1,n)-1;return i[r](o[r](e))}}function o0(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function o1(){var t,e,r,n,o,i,a=oK,c=oK,u=oH,l=oY;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==oY&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?oQ:oJ,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),oq)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,oG),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=oV,s()},f.clamp=function(t){return arguments.length?(l=!!t||oY,s()):l!==oY},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function o2(){return o1()(oY,oY)}var o3=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function o6(t){var e;if(!(e=o3.exec(t)))throw Error("invalid format: "+t);return new o5({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function o5(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function o4(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function o7(t){return(t=o4(Math.abs(t)))?t[1]:NaN}function o8(t,e){var r=o4(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}o6.prototype=o5.prototype,o5.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let o9={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>o8(100*t,e),r:o8,s:function(t,e){var r=o4(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(c_=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+o4(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function it(t){return t}var ie=Array.prototype.map,ir=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function io(t,e,r,n){var o,i,a=n8(t,e,r);switch((n=o6(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(i=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(o7(c)/3)))-o7(Math.abs(a))))||(n.precision=i),cN(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=Math.max(0,o7(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=a)))-o7(o))+1)||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=Math.max(0,-o7(Math.abs(a))))||(n.precision=i-("%"===n.type)*2)}return cC(n)}function ii(t){var e=t.domain;return t.ticks=function(t){var r=e();return n4(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return io(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=n7(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else if(o<0)u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function ia(){var t=o2();return t.copy=function(){return o0(t,ia())},nh.apply(t,arguments),ii(t)}function ic(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function iu(t){return Math.log(t)}function il(t){return Math.exp(t)}function is(t){return-Math.log(-t)}function ip(t){return-Math.exp(-t)}function ih(t){return isFinite(t)?+("1e"+t):t<0?0:t}function id(t){return(e,r)=>-t(-e,r)}function iy(t){let e,r;let n=t(iu,il),o=n.domain,i=10;function a(){var a,c;return e=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=i)?ih:c===Math.E?Math.exp:t=>Math.pow(c,t),o()[0]<0?(e=id(e),r=id(r),t(is,ip)):t(iu,il),n}return n.base=function(t){return arguments.length?(i=+t,a()):i},n.domain=function(t){return arguments.length?(o(t),a()):o()},n.ticks=t=>{let n,a;let c=o(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(i%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}2*d.length<h&&(d=n4(u,l,h))}else d=n4(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=o6(o)).precision||(o.trim=!0),o=cC(o)),t===1/0)return o;let a=Math.max(1,i*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*i<i-.5&&(n*=i),n<=a?o(t):""}},n.nice=()=>o(ic(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function iv(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function im(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function ib(t){var e=1,r=t(iv(1),im(e));return r.constant=function(r){return arguments.length?t(iv(e=+r),im(e)):e},ii(r)}function ig(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function ix(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function iO(t){return t<0?-t*t:t*t}function iw(t){var e=t(oY,oY),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(oY,oY):.5===r?t(ix,iO):t(ig(r),ig(1/r)):r},ii(e)}function ij(){var t=iw(o1());return t.copy=function(){return o0(t,ij()).exponent(t.exponent())},nh.apply(t,arguments),t}function iS(){return ij.apply(null,arguments).exponent(.5)}function iP(t){return Math.sign(t)*t*t}function iA(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function ik(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function iE(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function iT(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}cC=(cM=function(t){var e,r,n,o=void 0===t.grouping||void 0===t.thousands?it:(e=ie.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?it:(n=ie.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=o6(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):o9[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",O=o9[b],w=/[defgprs%]/.test(b);function j(t){var i,a,l,p=g,j=x;if("c"===b)j=O(t)+j,t="";else{var S=(t=+t)<0||1/t<0;if(t=isNaN(t)?f:O(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?ir[8+c_/3]:"")+j+(S&&"("===n?")":""),w){for(i=-1,a=t.length;++i<a;)if(48>(l=t.charCodeAt(i))||l>57){j=(46===l?c+t.slice(i+1):t.slice(i))+j,t=t.slice(0,i);break}}}y&&!h&&(t=o(t,1/0));var P=p.length+t.length+j.length,A=P<d?Array(d-P+1).join(e):"";switch(y&&h&&(t=o(A+t,A.length?d-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=o6(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(o7(e)/3))),o=Math.pow(10,-n),i=ir[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cN=cM.formatPrefix;let i_=new Date,iM=new Date;function iC(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a;let c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return c},o.filter=r=>iC(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t){if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}}),r&&(o.count=(e,n)=>(i_.setTime(+e),iM.setTime(+n),t(i_),t(iM),Math.floor(r(i_,iM))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let iN=iC(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);iN.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?iC(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):iN:null,iN.range;let iD=iC(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());iD.range;let iI=iC(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());iI.range;let iB=iC(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());iB.range;let iR=iC(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());iR.range;let iL=iC(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());iL.range;let iz=iC(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);iz.range;let iU=iC(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);iU.range;let iF=iC(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function i$(t){return iC(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}iF.range;let iq=i$(0),iW=i$(1),iX=i$(2),iH=i$(3),iV=i$(4),iG=i$(5),iK=i$(6);function iY(t){return iC(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}iq.range,iW.range,iX.range,iH.range,iV.range,iG.range,iK.range;let iZ=iY(0),iJ=iY(1),iQ=iY(2),i0=iY(3),i1=iY(4),i2=iY(5),i3=iY(6);iZ.range,iJ.range,iQ.range,i0.range,i1.range,i2.range,i3.range;let i6=iC(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());i6.range;let i5=iC(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());i5.range;let i4=iC(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());i4.every=t=>isFinite(t=Math.floor(t))&&t>0?iC(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,i4.range;let i7=iC(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function i8(t,e,r,n,o,i){let a=[[iD,1,1e3],[iD,5,5e3],[iD,15,15e3],[iD,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let o=Math.abs(r-e)/n,i=oe(([,,t])=>t).right(a,o);if(i===a.length)return t.every(n8(e/31536e6,r/31536e6,n));if(0===i)return iN.every(Math.max(n8(e,r,n),1));let[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}i7.every=t=>isFinite(t=Math.floor(t))&&t>0?iC(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,i7.range;let[i9,at]=i8(i7,i5,iZ,iF,iL,iB),[ae,ar]=i8(i4,i6,iq,iz,iR,iI);function an(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ao(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ai(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var aa={"-":"",_:" ",0:"0"},ac=/^\s*\d+/,au=/^%/,al=/[\\^$*+?|[\]().{}]/g;function as(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function af(t){return t.replace(al,"\\$&")}function ap(t){return RegExp("^(?:"+t.map(af).join("|")+")","i")}function ah(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function ad(t,e,r){var n=ac.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function ay(t,e,r){var n=ac.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function av(t,e,r){var n=ac.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function am(t,e,r){var n=ac.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function ab(t,e,r){var n=ac.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function ag(t,e,r){var n=ac.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function ax(t,e,r){var n=ac.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aO(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aw(t,e,r){var n=ac.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aj(t,e,r){var n=ac.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function aS(t,e,r){var n=ac.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aP(t,e,r){var n=ac.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aA(t,e,r){var n=ac.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function ak(t,e,r){var n=ac.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aE(t,e,r){var n=ac.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aT(t,e,r){var n=ac.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function a_(t,e,r){var n=ac.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aM(t,e,r){var n=au.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aC(t,e,r){var n=ac.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aN(t,e,r){var n=ac.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aD(t,e){return as(t.getDate(),e,2)}function aI(t,e){return as(t.getHours(),e,2)}function aB(t,e){return as(t.getHours()%12||12,e,2)}function aR(t,e){return as(1+iz.count(i4(t),t),e,3)}function aL(t,e){return as(t.getMilliseconds(),e,3)}function az(t,e){return aL(t,e)+"000"}function aU(t,e){return as(t.getMonth()+1,e,2)}function aF(t,e){return as(t.getMinutes(),e,2)}function a$(t,e){return as(t.getSeconds(),e,2)}function aq(t){var e=t.getDay();return 0===e?7:e}function aW(t,e){return as(iq.count(i4(t)-1,t),e,2)}function aX(t){var e=t.getDay();return e>=4||0===e?iV(t):iV.ceil(t)}function aH(t,e){return t=aX(t),as(iV.count(i4(t),t)+(4===i4(t).getDay()),e,2)}function aV(t){return t.getDay()}function aG(t,e){return as(iW.count(i4(t)-1,t),e,2)}function aK(t,e){return as(t.getFullYear()%100,e,2)}function aY(t,e){return as((t=aX(t)).getFullYear()%100,e,2)}function aZ(t,e){return as(t.getFullYear()%1e4,e,4)}function aJ(t,e){var r=t.getDay();return as((t=r>=4||0===r?iV(t):iV.ceil(t)).getFullYear()%1e4,e,4)}function aQ(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+as(e/60|0,"0",2)+as(e%60,"0",2)}function a0(t,e){return as(t.getUTCDate(),e,2)}function a1(t,e){return as(t.getUTCHours(),e,2)}function a2(t,e){return as(t.getUTCHours()%12||12,e,2)}function a3(t,e){return as(1+iU.count(i7(t),t),e,3)}function a6(t,e){return as(t.getUTCMilliseconds(),e,3)}function a5(t,e){return a6(t,e)+"000"}function a4(t,e){return as(t.getUTCMonth()+1,e,2)}function a7(t,e){return as(t.getUTCMinutes(),e,2)}function a8(t,e){return as(t.getUTCSeconds(),e,2)}function a9(t){var e=t.getUTCDay();return 0===e?7:e}function ct(t,e){return as(iZ.count(i7(t)-1,t),e,2)}function ce(t){var e=t.getUTCDay();return e>=4||0===e?i1(t):i1.ceil(t)}function cr(t,e){return t=ce(t),as(i1.count(i7(t),t)+(4===i7(t).getUTCDay()),e,2)}function cn(t){return t.getUTCDay()}function co(t,e){return as(iJ.count(i7(t)-1,t),e,2)}function ci(t,e){return as(t.getUTCFullYear()%100,e,2)}function ca(t,e){return as((t=ce(t)).getUTCFullYear()%100,e,2)}function cc(t,e){return as(t.getUTCFullYear()%1e4,e,4)}function cu(t,e){var r=t.getUTCDay();return as((t=r>=4||0===r?i1(t):i1.ceil(t)).getUTCFullYear()%1e4,e,4)}function cl(){return"+0000"}function cs(){return"%"}function cf(t){return+t}function cp(t){return Math.floor(+t/1e3)}function ch(t){return new Date(t)}function cd(t){return t instanceof Date?+t:+new Date(+t)}function cy(t,e,r,n,o,i,a,c,u,l){var s=o2(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function O(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,cd)):p().map(ch)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?O:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(ic(r,t)):s},s.copy=function(){return o0(s,cy(t,e,r,n,o,i,a,c,u,l))},s}function cv(){return nh.apply(cy(ae,ar,i4,i6,iq,iz,iR,iI,iD,cI).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cm(){return nh.apply(cy(i9,at,i7,i5,iZ,iU,iL,iB,iD,cB).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cb(){var t,e,r,n,o,i=0,a=1,c=oY,u=!1;function l(e){return null==e||isNaN(e=+e)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(oH),l.rangeRound=s(oV),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function cg(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cx(){var t=iw(cb());return t.copy=function(){return cg(t,cx()).exponent(t.exponent())},nd.apply(t,arguments)}function cO(){return cx.apply(null,arguments).exponent(.5)}function cw(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=oY,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=oH);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c=+c),e=i(u=+u),r=i(l=+l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(oH),h.rangeRound=d(oV),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function cj(){var t=iw(cw());return t.copy=function(){return cg(t,cj()).exponent(t.exponent())},nd.apply(t,arguments)}function cS(){return cj.apply(null,arguments).exponent(.5)}function cP(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cA(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function ck(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cE(t,e){return t[e]}function cT(t){let e=[];return e.key=t,e}cI=(cD=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=ap(o),s=ah(o),f=ap(i),p=ah(i),h=ap(a),d=ah(a),y=ap(c),v=ah(c),m=ap(u),b=ah(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aD,e:aD,f:az,g:aY,G:aJ,H:aI,I:aB,j:aR,L:aL,m:aU,M:aF,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cf,s:cp,S:a$,u:aq,U:aW,V:aH,w:aV,W:aG,x:null,X:null,y:aK,Y:aZ,Z:aQ,"%":cs},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:a0,e:a0,f:a5,g:ca,G:cu,H:a1,I:a2,j:a3,L:a6,m:a4,M:a7,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cf,s:cp,S:a8,u:a9,U:ct,V:cr,w:cn,W:co,x:null,X:null,y:ci,Y:cc,Z:cl,"%":cs},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:aS,e:aS,f:a_,g:ax,G:ag,H:aA,I:aA,j:aP,L:aT,m:aj,M:ak,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aw,Q:aC,s:aN,S:aE,u:ay,U:av,V:am,w:ad,W:ab,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:ax,Y:ag,Z:aO,"%":aM};function w(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=aa[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=ai(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=ao(ai(i.y,0,1))).getUTCDay())>4||0===o?iJ.ceil(n):iJ(n),n=iU.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=an(ai(i.y,0,1))).getDay())>4||0===o?iW.ceil(n):iW(n),n=iz.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?ao(ai(i.y,0,1)).getUTCDay():an(ai(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,ao(i)):an(i)}}function S(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in aa?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cD.parse,cB=cD.utcFormat,cD.utcParse,Array.prototype.slice;var c_,cM,cC,cN,cD,cI,cB,cR,cL,cz=r(55409),cU=r.n(cz),cF=r(35534),c$=r.n(cF),cq=r(40927),cW=r.n(cq),cX=r(1149),cH=r.n(cX),cV=!0,cG="[DecimalError] ",cK=cG+"Invalid argument: ",cY=cG+"Exponent out of range: ",cZ=Math.floor,cJ=Math.pow,cQ=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,c0=cZ(1286742750677284.5),c1={};function c2(t,e){var r,n,o,i,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),cV?ur(e,f):e;if(u=t.d,l=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=l.length):(n=l,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=l.length)<0&&(i=c,n=l,l=u,u=n),r=0;i;)r=(u[--i]=u[i]+l[i]+r)/1e7|0,u[i]%=1e7;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,cV?ur(e,f):e}function c3(t,e,r){if(t!==~~t||t<e||t>r)throw Error(cK+t)}function c6(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=c9(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=c9(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}c1.absoluteValue=c1.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},c1.comparedTo=c1.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},c1.decimalPlaces=c1.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},c1.dividedBy=c1.div=function(t){return c5(this,new this.constructor(t))},c1.dividedToIntegerBy=c1.idiv=function(t){var e=this.constructor;return ur(c5(this,new e(t),0,1),e.precision)},c1.equals=c1.eq=function(t){return!this.cmp(t)},c1.exponent=function(){return c7(this)},c1.greaterThan=c1.gt=function(t){return this.cmp(t)>0},c1.greaterThanOrEqualTo=c1.gte=function(t){return this.cmp(t)>=0},c1.isInteger=c1.isint=function(){return this.e>this.d.length-2},c1.isNegative=c1.isneg=function(){return this.s<0},c1.isPositive=c1.ispos=function(){return this.s>0},c1.isZero=function(){return 0===this.s},c1.lessThan=c1.lt=function(t){return 0>this.cmp(t)},c1.lessThanOrEqualTo=c1.lte=function(t){return 1>this.cmp(t)},c1.logarithm=c1.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(cL))throw Error(cG+"NaN");if(this.s<1)throw Error(cG+(this.s?"NaN":"-Infinity"));return this.eq(cL)?new r(0):(cV=!1,e=c5(ut(this,o),ut(t,o),o),cV=!0,ur(e,n))},c1.minus=c1.sub=function(t){return t=new this.constructor(t),this.s==t.s?un(this,t):c2(this,(t.s=-t.s,t))},c1.modulo=c1.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(cG+"NaN");return this.s?(cV=!1,e=c5(this,t,0,1).times(t),cV=!0,this.minus(e)):ur(new r(this),n)},c1.naturalExponential=c1.exp=function(){return c4(this)},c1.naturalLogarithm=c1.ln=function(){return ut(this)},c1.negated=c1.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},c1.plus=c1.add=function(t){return t=new this.constructor(t),this.s==t.s?c2(this,t):un(this,(t.s=-t.s,t))},c1.precision=c1.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(cK+t);if(e=c7(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},c1.squareRoot=c1.sqrt=function(){var t,e,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(cG+"NaN")}for(t=c7(this),cV=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=c6(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=cZ((t+1)/2)-(t<0||t%2),n=new c(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(c5(this,i,a+2)).times(.5),c6(i.d).slice(0,a)===(e=c6(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(ur(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return cV=!0,ur(n,r)},c1.times=c1.mul=function(t){var e,r,n,o,i,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=u,u=l,l=a),i=[],n=a=u+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+p[n]*f[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,cV?ur(t,s.precision):t},c1.toDecimalPlaces=c1.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(c3(t,0,1e9),void 0===e?e=n.rounding:c3(e,0,8),ur(r,t+c7(r)+1,e))},c1.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=uo(n,!0):(c3(t,0,1e9),void 0===e?e=o.rounding:c3(e,0,8),r=uo(n=ur(new o(n),t+1,e),!0,t+1)),r},c1.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?uo(this):(c3(t,0,1e9),void 0===e?e=o.rounding:c3(e,0,8),r=uo((n=ur(new o(this),t+c7(this)+1,e)).abs(),!1,t+c7(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},c1.toInteger=c1.toint=function(){var t=this.constructor;return ur(new t(this),c7(this)+1,t.rounding)},c1.toNumber=function(){return+this},c1.toPower=c1.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(cL);if(!(c=new u(c)).s){if(t.s<1)throw Error(cG+"Infinity");return c}if(c.eq(cL))return c;if(n=u.precision,t.eq(cL))return ur(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=9007199254740991){for(o=new u(cL),e=Math.ceil(n/7+4),cV=!1;r%2&&ui((o=o.times(c)).d,e),0!==(r=cZ(r/2));)ui((c=c.times(c)).d,e);return cV=!0,t.s<0?new u(cL).div(o):ur(o,n)}}else if(i<0)throw Error(cG+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,cV=!1,o=t.times(ut(c,n+12)),cV=!0,(o=c4(o)).s=i,o},c1.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=c7(o),n=uo(o,r<=i.toExpNeg||r>=i.toExpPos)):(c3(t,1,1e9),void 0===e?e=i.rounding:c3(e,0,8),r=c7(o=ur(new i(o),t,e)),n=uo(o,t<=r||r<=i.toExpNeg,t)),n},c1.toSignificantDigits=c1.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(c3(t,1,1e9),void 0===e?e=r.rounding:c3(e,0,8)),ur(new r(this),t,e)},c1.toString=c1.valueOf=c1.val=c1.toJSON=c1[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=c7(this),e=this.constructor;return uo(this,t<=e.toExpNeg||t>=e.toExpPos)};var c5=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,O,w,j,S,P=n.constructor,A=n.s==o.s?1:-1,k=n.d,E=o.d;if(!n.s)return new P(n);if(!o.s)throw Error(cG+"Division by zero");for(l=0,u=n.e-o.e,j=E.length,O=k.length,d=(h=new P(A)).d=[];E[l]==(k[l]||0);)++l;if(E[l]>(k[l]||0)&&--u,(b=null==i?i=P.precision:a?i+(c7(n)-c7(o))+1:i)<0)return new P(0);if(b=b/7+2|0,l=0,1==j)for(s=0,E=E[0],b++;(l<O||s)&&b--;l++)g=1e7*s+(k[l]||0),d[l]=g/E|0,s=g%E|0;else{for((s=1e7/(E[0]+1)|0)>1&&(E=t(E,s),k=t(k,s),j=E.length,O=k.length),x=j,v=(y=k.slice(0,j)).length;v<j;)y[v++]=0;(S=E.slice()).unshift(0),w=E[0],E[1]>=1e7/2&&++w;do s=0,(c=e(E,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/w|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(E,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?S:E,p))):(0==s&&(c=s=1),f=E.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(E,y,j,v))<1&&(s++,r(y,j<v?S:E,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=k[x]||0:(y=[k[x]],v=1);while((x++<O||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,ur(h,a?i+c7(h)+1:i)}}();function c4(t,e){var r,n,o,i,a,c=0,u=0,l=t.constructor,s=l.precision;if(c7(t)>16)throw Error(cY+c7(t));if(!t.s)return new l(cL);for(null==e?(cV=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(cJ(2,u))/Math.LN10*2+5|0,r=n=o=new l(cL),l.precision=a;;){if(n=ur(n.times(t),a),r=r.times(++c),c6((i=o.plus(c5(n,r,a))).d).slice(0,a)===c6(o.d).slice(0,a)){for(;u--;)o=ur(o.times(o),a);return l.precision=s,null==e?(cV=!0,ur(o,s)):o}o=i}}function c7(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function c8(t,e,r){if(e>t.LN10.sd())throw cV=!0,r&&(t.precision=r),Error(cG+"LN10 precision limit exceeded");return ur(new t(t.LN10),e)}function c9(t){for(var e="";t--;)e+="0";return e}function ut(t,e){var r,n,o,i,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(cG+(p.s?"NaN":"-Infinity"));if(p.eq(cL))return new d(0);if(null==e?(cV=!1,l=y):l=e,p.eq(10))return null==e&&(cV=!0),c8(d,l);if(l+=10,d.precision=l,n=(r=c6(h)).charAt(0),!(15e14>Math.abs(i=c7(p))))return u=c8(d,l+2,y).times(i+""),p=ut(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(cV=!0,ur(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=c6((p=p.times(t)).d)).charAt(0),f++;for(i=c7(p),n>1?(p=new d("0."+r),i++):p=new d(n+"."+r.slice(1)),c=a=p=c5(p.minus(cL),p.plus(cL),l),s=ur(p.times(p),l),o=3;;){if(a=ur(a.times(s),l),c6((u=c.plus(c5(a,new d(o),l))).d).slice(0,l)===c6(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(c8(d,l+2,y).times(i+""))),c=c5(c,new d(f),l),d.precision=y,null==e?(cV=!0,ur(c,y)):c;c=u,o+=2}}function ue(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=cZ(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),cV&&(t.e>c0||t.e<-c0))throw Error(cY+r)}else t.s=0,t.e=0,t.d=[0];return t}function ur(t,e,r){var n,o,i,a,c,u,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(a=1,l=i=f[s];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=l/(i=cJ(10,a-o-1))%10|0,u=e<0||void 0!==f[s+1]||l%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?l/cJ(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=c7(t),f.length=1,e=e-i-1,f[0]=cJ(10,(7-e%7)%7),t.e=cZ(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=cJ(10,7-n),f[s]=o>0?(l/cJ(10,a-o)%cJ(10,o)|0)*i:0),u)for(;;){if(0==s){1e7==(f[0]+=i)&&(f[0]=1,++t.e);break}if(f[s]+=i,1e7!=f[s])break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(cV&&(t.e>c0||t.e<-c0))throw Error(cY+c7(t));return t}function un(t,e){var r,n,o,i,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),cV?ur(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(o=Math.max(Math.ceil(h/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){s=u[o]<f[o];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=1e7-1;--u[i],u[o]+=1e7}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,cV?ur(e,h):e):new p(0)}function uo(t,e,r){var n,o=c7(t),i=c6(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+c9(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+c9(-o-1)+i,r&&(n=r-a)>0&&(i+=c9(n))):o>=a?(i+=c9(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+c9(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=c9(n))),t.s<0?"-"+i:i}function ui(t,e){if(t.length>e)return t.length=e,!0}function ua(t){if(!t||"object"!=typeof t)throw Error(cG+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(cZ(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(cK+r+": "+n)}if(void 0!==(n=t[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(cK+r+": "+n)}return this}var cR=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(cK+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return ue(this,t.toString())}if("string"!=typeof t)throw Error(cK+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,cQ.test(t))ue(this,t);else throw Error(cK+t)}if(i.prototype=c1,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=ua,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});cL=new cR(1);let uc=cR;function uu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var ul=function(t){return t},us={},uf=function(t){return t===us},up=function(t){return function e(){return 0==arguments.length||1==arguments.length&&uf(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},uh=function(t){return function t(e,r){return 1===e?r:up(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==us}).length;return a>=e?r.apply(void 0,o):t(e-a,up(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return uf(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return uu(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return uu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uu(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},ud=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},uy=uh(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),uv=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return ul;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},um=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},ub=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};let ug={rangeStep:function(t,e,r){for(var n=new uc(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new uc(t).abs().log(10).toNumber())+1},interpolateNumber:uh(function(t,e,r){var n=+t;return n+r*(+e-n)}),uninterpolateNumber:uh(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),uninterpolateTruncation:uh(function(t,e,r){var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))})};function ux(t){return function(t){if(Array.isArray(t))return uj(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uw(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uO(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(t,e)||uw(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uw(t,e){if(t){if("string"==typeof t)return uj(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uj(t,e)}}function uj(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uS(t){var e=uO(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function uP(t,e,r){if(t.lte(0))return new uc(0);var n=ug.getDigitCount(t.toNumber()),o=new uc(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new uc(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new uc(Math.ceil(c))}function uA(t,e,r){var n=1,o=new uc(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new uc(10).pow(ug.getDigitCount(t)-1),o=new uc(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new uc(Math.floor(t)))}else 0===t?o=new uc(Math.floor((e-1)/2)):r||(o=new uc(Math.floor(t)));var a=Math.floor((e-1)/2);return uv(uy(function(t){return o.add(new uc(t-a).mul(n)).toNumber()}),ud)(0,e)}var uk=ub(function(t){var e=uO(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uO(uS([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(ux(ud(0,o-1).map(function(){return 1/0}))):[].concat(ux(ud(0,o-1).map(function(){return-1/0})),[l]);return r>n?um(s):s}if(u===l)return uA(u,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new uc(0),tickMin:new uc(0),tickMax:new uc(0)};var c=uP(new uc(r).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&r>=0?new uc(0):(i=new uc(e).add(r).div(2)).sub(new uc(i).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new uc(r).sub(i).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:i.sub(new uc(u).mul(c)),tickMax:i.add(new uc(l).mul(c))})}(u,l,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=ug.rangeStep(h,d.add(new uc(.1).mul(p)),p);return r>n?um(y):y});ub(function(t){var e=uO(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uO(uS([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uA(u,o,i);var s=uP(new uc(l).sub(u).div(a-1),i,0),f=uv(uy(function(t){return new uc(u).add(new uc(t).mul(s)).toNumber()}),ud)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?um(f):f});var uE=ub(function(t,e){var r=uO(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=uO(uS([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=uP(new uc(u).sub(c).div(Math.max(e,2)-1),i,0),s=[].concat(ux(ug.rangeStep(new uc(c),new uc(u).sub(new uc(.99).mul(l)),l)),[u]);return n>o?um(s):s}),uT=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function u_(t){return(u_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uM(){return(uM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uC(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uN(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uL(n.key),n)}}function uD(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uD=function(){return!!t})()}function uI(t){return(uI=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uB(t,e){return(uB=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uR(t,e,r){return(e=uL(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uL(t){var e=function(t,e){if("object"!=u_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u_(e)?e:e+""}var uz=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=uI(t),function(t,e){if(e&&("object"===u_(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,uD()?Reflect.construct(t,e||[],uI(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&uB(t,e)}(n,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,o=t.dataKey,i=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=tc(function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,uT),!1);"x"===this.props.direction&&"number"!==u.type&&tO(!1);var f=i.map(function(t){var i,f,p=c(t,o),h=p.x,d=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return uC(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uC(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=b[0],f=b[1]}else i=f=v;if("vertical"===r){var g=u.scale,x=d+e,O=x+n,w=x-n,j=g(y-i),S=g(y+f);m.push({x1:S,y1:O,x2:S,y2:w}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:O,x2:j,y2:w})}else if("horizontal"===r){var P=l.scale,A=h+e,k=A-n,E=A+n,T=P(y-i),_=P(y+f);m.push({x1:k,y1:_,x2:E,y2:_}),m.push({x1:A,y1:T,x2:A,y2:_}),m.push({x1:k,y1:T,x2:E,y2:T})}return a().createElement(tk,uM({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},s),m.map(function(t){return a().createElement("line",uM({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return a().createElement(tk,{className:"recharts-errorBars"},f)}}],uN(n.prototype,e),r&&uN(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a().Component);function uU(t){return(uU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uF(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=uU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uU(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uR(uz,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),uR(uz,"displayName","ErrorBar");var uq=function(t){var e,r=t.children,n=t.formattedGraphicalItems,o=t.legendWidth,i=t.legendContent,a=tn(r,e1);if(!a)return null;var c=e1.defaultProps,u=void 0!==c?u$(u$({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u$(u$({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:uQ(e),value:i||o,payload:n}}),u$(u$(u$({},u),e1.getWithHeight(a,o)),{},{payload:e,item:a})};function uW(t){return(uW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uX(t){return function(t){if(Array.isArray(t))return uH(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return uH(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uH(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uH(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uV(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function uG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uV(Object(r),!0).forEach(function(e){uK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uV(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function uK(t,e,r){var n;return(n=function(t,e){if("object"!=uW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==uW(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uY(t,e,r){return N()(t)||N()(e)?r:w(e)?v()(t,e,r):I()(e)?e(t):r}function uZ(t,e,r,n){var o=cW()(t,function(t){return uY(t,e)});if("number"===r){var i=o.filter(function(t){return O(t)||parseFloat(t)});return i.length?[c$()(i),cU()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!N()(t)}):o).map(function(t){return w(t)||t instanceof Date?t:""})}var uJ=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(g(s-l)!==g(f-s)){var h=[];if(g(f-s)===g(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var x=0;x<a;x++)if(0===x&&t<=(r[x].coordinate+r[x+1].coordinate)/2||x>0&&x<a-1&&t>(r[x].coordinate+r[x-1].coordinate)/2&&t<=(r[x].coordinate+r[x+1].coordinate)/2||x===a-1&&t>(r[x].coordinate+r[x-1].coordinate)/2){i=r[x].index;break}return i},uQ=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?uG(uG({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},u0=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return J(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?uG(uG({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=N()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:N()(O)?void 0:P(O,r,0)})}}return i},u1=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=P(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=o&&(h-=(u-1)*l,l=0),h>=o&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((o-h)/2>>0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(uX(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=P(n,o,0,!0);o-2*y-(u-1)*l<=0&&(l=0);var v=(o-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(uX(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},u2=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=uq({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==f&&O(t[f]))return uG(uG({},t),{},uK({},f,t[f]+(l||0)));if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==p&&O(t[p]))return uG(uG({},t),{},uK({},p,t[p]+(s||0)))}return t},u3=function(t,e,r,n,o){var i=tr(e.props.children,uz).filter(function(t){var e;return e=t.props.direction,!!N()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=uY(e,r);if(N()(n))return t;var o=Array.isArray(n)?[c$()(n),cU()(n)]:[n,n],i=a.reduce(function(t,r){var n=uY(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},u6=function(t,e,r,n,o){var i=e.map(function(e){return u3(t,e,r,o,n)}).filter(function(t){return!N()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},u5=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&u3(t,e,i,n)||uZ(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},u4=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},u7=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var o,i,a=t.map(function(t){return t.coordinate===e&&(o=!0),t.coordinate===r&&(i=!0),t.coordinate});return o||a.push(e),i||a.push(r),a},u8=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*g(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!d()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},u9=new WeakMap,lt=function(t,e){if("function"!=typeof e)return t;u9.has(t)||u9.set(t,new WeakMap);var r=u9.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},le=function(t,e,r){var o=t.scale,i=t.type,a=t.layout,c=t.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nx(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:ia(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nO(),realScaleType:"point"}:"category"===i?{scale:nx(),realScaleType:"band"}:{scale:ia(),realScaleType:"linear"};if(p()(o)){var u="scale".concat(er()(o));return{scale:(n[u]||nO)(),realScaleType:n[u]?u:"point"}}return I()(o)?{scale:o}:{scale:nO(),realScaleType:"point"}},lr=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},ln=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},lo=function(t,e){if(!e||2!==e.length||!O(e[0])||!O(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!O(t[0])||t[0]<r)&&(o[0]=r),(!O(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},li={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=d()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}cP(t,e)}},none:cP,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}cP(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,cP(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=d()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},la=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=li[r];return(function(){var t=eb([]),e=ck,r=cP,n=cE;function o(o){var i,a,c=Array.from(t.apply(this,arguments),cT),u=c.length,l=-1;for(let t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=cA(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:eb(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:eb(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?ck:"function"==typeof t?t:eb(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?cP:t,o):r},o})().keys(n).value(function(t,e){return+uY(t,e,0)}).order(ck).offset(o)(t)},lc=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?uG(uG({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(w(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[S("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return uG(uG({},t),{},uK({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return uG(uG({},e),{},uK({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:la(t,a.items,o)}))},{})),uG(uG({},e),{},uK({},i,c))},{})},lu=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=uk(u,o,a);return t.domain([c$()(l),cU()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:uE(t.domain(),o,a)}:null};function ll(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!N()(o[e.dataKey])){var c=T(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=uY(o,N()(a)?e.dataKey:a);return N()(u)?null:e.scale(u)}var ls=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=uY(i,e.dataKey,e.domain[a]);return N()(c)?null:e.scale(c)-o/2+n},lf=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},lp=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?uG(uG({},t.type.defaultProps),t.props):t.props).stackId;if(w(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},lh=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[c$()(e.concat([t[0]]).filter(O)),cU()(e.concat([t[1]]).filter(O))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},ld=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ly=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lv=function(t,e,r){if(I()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(O(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(ld.test(t[0])){var o=+ld.exec(t[0])[1];n[0]=e[0]-o}else I()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(O(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(ly.test(t[1])){var i=+ly.exec(t[1])[1];n[1]=e[1]+i}else I()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lm=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=tx()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},lb=function(t,e,r){return!t||!t.length||cH()(t,v()(r,"type.defaultProps.domain"))?e:t},lg=function(t,e){var r=t.type.defaultProps?uG(uG({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return uG(uG({},tc(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:uQ(t),value:uY(e,n),type:c,payload:e,chartType:u,hide:l})};function lx(t){return(lx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lO(Object(r),!0).forEach(function(e){lj(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lj(t,e,r){var n;return(n=function(t,e){if("object"!=lx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lx(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var lS=["Webkit","Moz","O","ms"],lP=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=lS.reduce(function(t,n){return lw(lw({},t),{},lj({},n+r,e))},{});return n[t]=e,n};function lA(t){return(lA="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lk(){return(lk=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lT(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lE(Object(r),!0).forEach(function(e){lD(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l_(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lI(n.key),n)}}function lM(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lM=function(){return!!t})()}function lC(t){return(lC=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lN(t,e){return(lN=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lD(t,e,r){return(e=lI(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lI(t){var e=function(t,e){if("object"!=lA(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lA(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lA(e)?e:e+""}var lB=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nO().domain(tb()(0,c)).range([o,o+i-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},lR=function(t){return t.changedTouches&&!!t.changedTouches.length},lL=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=lC(r),lD(e=function(t,e){if(e&&("object"===lA(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,lM()?Reflect.construct(r,o||[],lC(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),lD(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),lD(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),lD(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),lD(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),lD(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),lD(e,"handleSlideDragStart",function(t){var r=lR(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lN(t,e)}(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=n.getIndexInRange(o,Math.min(e,r)),l=n.getIndexInRange(o,Math.max(e,r));return{startIndex:u-u%a,endIndex:l===c?c:l-l%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=uY(r[t],o,t);return I()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=lR(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||o<i&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||o>i&&b===t};this.setState(lD(lD({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=o.length)){var s=o[l];"startX"===e&&s>=a||"endX"===e&&s<=i||this.setState(lD({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.fill,c=t.stroke;return a().createElement("rect",{stroke:c,fill:i,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,c=t.data,u=t.children,l=t.padding,s=i.Children.only(u);return s?a().cloneElement(s,{x:e,y:r,width:n,height:o,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,i=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=lT(lT({},tc(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null===(r=h[d])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=h[y])||void 0===o?void 0:o.name);return a().createElement(tk,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,i=r.stroke,c=r.travellerWidth;return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:Math.min(t,e)+c,y:n,width:Math.max(Math.abs(e-t)-c,0),height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,i=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return a().createElement(tk,{className:"recharts-brush-texts"},a().createElement(n1,lk({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+o/2},f),this.getTextOfTick(e)),a().createElement(n1,lk({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+i+5,y:n+o/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,i=t.y,c=t.width,l=t.height,s=t.alwaysShowText,f=this.state,p=f.startX,h=f.endX,d=f.isTextActive,y=f.isSlideMoving,v=f.isTravellerMoving,m=f.isTravellerFocused;if(!e||!e.length||!O(o)||!O(i)||!O(c)||!O(l)||c<=0||l<=0)return null;var b=u("recharts-brush",r),g=1===a().Children.count(n),x=lP("userSelect","none");return a().createElement(tk,{className:b,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:x},this.renderBackground(),g&&this.renderPanorama(),this.renderSlide(p,h),this.renderTravellerLayer(p,"startX"),this.renderTravellerLayer(h,"endX"),(d||y||v||m||s)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=t.stroke,c=Math.floor(r+o/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:e,y:r,width:n,height:o,fill:i,stroke:"none"}),a().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){return a().isValidElement(t)?a().cloneElement(t,e):I()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return lT({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?lB({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&l_(n.prototype,e),r&&l_(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function lz(t){return(lz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lF(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lU(Object(r),!0).forEach(function(e){l$(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l$(t,e,r){var n;return(n=function(t,e){if("object"!=lz(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lz(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lz(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lq(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}lD(lL,"displayName","Brush"),lD(lL,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var lW=Math.PI/180,lX=function(t,e,r,n){return{x:t+Math.cos(-lW*n)*r,y:e+Math.sin(-lW*n)*r}},lH=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0};return Math.min(Math.abs(t-(r.left||0)-(r.right||0)),Math.abs(e-(r.top||0)-(r.bottom||0)))/2},lV=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},lG=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=lV({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},lK=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},lY=function(t,e){var r,n=lG({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,c=e.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var u=lK(e),l=u.startAngle,s=u.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?lF(lF({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null},lZ=function(t){return(0,i.isValidElement)(t)||I()(t)||"boolean"==typeof t?"":t.className};function lJ(t){return(lJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var lQ=["offset"];function l0(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function l1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l1(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=lJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lJ(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function l3(){return(l3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var l6=function(t){var e=t.value,r=t.formatter,n=N()(t.children)?e:t.children;return I()(r)?r(n):n},l5=function(t,e,r){var n,o,i=t.position,c=t.viewBox,l=t.offset,s=t.className,f=c.cx,p=c.cy,h=c.innerRadius,d=c.outerRadius,y=c.startAngle,v=c.endAngle,m=c.clockWise,b=(h+d)/2,x=g(v-y)*Math.min(Math.abs(v-y),360),O=x>=0?1:-1;"insideStart"===i?(n=y+O*l,o=m):"insideEnd"===i?(n=v-O*l,o=!m):"end"===i&&(n=v+O*l,o=m),o=x<=0?o:!o;var w=lX(f,p,b,n),j=lX(f,p,b,n+(o?1:-1)*359),P="M".concat(w.x,",").concat(w.y,"\n    A").concat(b,",").concat(b,",0,1,").concat(o?0:1,",\n    ").concat(j.x,",").concat(j.y),A=N()(t.id)?S("recharts-radial-line-"):t.id;return a().createElement("text",l3({},r,{dominantBaseline:"central",className:u("recharts-radial-bar-label",s)}),a().createElement("defs",null,a().createElement("path",{id:A,d:P})),a().createElement("textPath",{xlinkHref:"#".concat(A)},e))},l4=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=lX(o,i,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=lX(o,i,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},l7=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",h=c>=0?1:-1,d=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===o)return l2(l2({},{x:i+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return l2(l2({},{x:i+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===o){var m={x:i-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return l2(l2({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===o){var b={x:i+c+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return l2(l2({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===o?l2({x:i+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?l2({x:i+c-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?l2({x:i+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?l2({x:i+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?l2({x:i+d,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?l2({x:i+c-d,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?l2({x:i+d,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?l2({x:i+c-d,y:a+u-s,textAnchor:y,verticalAnchor:f},g):R()(o)&&(O(o.x)||x(o.x))&&(O(o.y)||x(o.y))?l2({x:i+P(o.x,c),y:a+P(o.y,u),textAnchor:"end",verticalAnchor:"end"},g):l2({x:i+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function l8(t){var e,r=t.offset,n=l2({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,lQ)),o=n.viewBox,c=n.position,l=n.value,s=n.children,f=n.content,p=n.className,h=n.textBreakAll;if(!o||N()(l)&&N()(s)&&!(0,i.isValidElement)(f)&&!I()(f))return null;if((0,i.isValidElement)(f))return(0,i.cloneElement)(f,n);if(I()(f)){if(e=(0,i.createElement)(f,n),(0,i.isValidElement)(e))return e}else e=l6(n);var d="cx"in o&&O(o.cx),y=tc(n,!0);if(d&&("insideStart"===c||"insideEnd"===c||"end"===c))return l5(n,e,y);var v=d?l4(n):l7(n);return a().createElement(n1,l3({className:u("recharts-label",void 0===p?"":p)},y,v,{breakAll:h}),e)}l8.displayName="Label";var l9=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(O(d)&&O(y)){if(O(s)&&O(f))return{x:s,y:f,width:d,height:y};if(O(p)&&O(h))return{x:p,y:h,width:d,height:y}}return O(s)&&O(f)?{x:s,y:f,width:0,height:0}:O(e)&&O(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};l8.parseViewBox=l9,l8.renderCallByParent=function(t,e){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var c=t.children,u=l9(t),l=tr(c,l8).map(function(t,r){return(0,i.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});return o?[(r=t.label,n=e||u,r?!0===r?a().createElement(l8,{key:"label-implicit",viewBox:n}):w(r)?a().createElement(l8,{key:"label-implicit",viewBox:n,value:r}):(0,i.isValidElement)(r)?r.type===l8?(0,i.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(l8,{key:"label-implicit",content:r,viewBox:n}):I()(r)?a().createElement(l8,{key:"label-implicit",content:r,viewBox:n}):R()(r)?a().createElement(l8,l3({viewBox:n},r,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return l0(t)}(l)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(l)||function(t,e){if(t){if("string"==typeof t)return l0(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l0(t,e)}}(l)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):l};var st=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},se=r(69167),sr=r.n(se),sn=r(28611),so=r.n(sn),si=function(t){return null};si.displayName="Cell";var sa=r(10182),sc=r.n(sa);function su(t){return(su="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sl=["valueAccessor"],ss=["data","dataKey","clockWise","id","textBreakAll"];function sf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sp(){return(sp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sd(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sh(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=su(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=su(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==su(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sh(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sy(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var sv=function(t){return Array.isArray(t.value)?sc()(t.value):t.value};function sm(t){var e=t.valueAccessor,r=void 0===e?sv:e,n=sy(t,sl),o=n.data,i=n.dataKey,c=n.clockWise,u=n.id,l=n.textBreakAll,s=sy(n,ss);return o&&o.length?a().createElement(tk,{className:"recharts-label-list"},o.map(function(t,e){var n=N()(i)?r(t,e):uY(t&&t.payload,i),o=N()(u)?{}:{id:"".concat(u,"-").concat(e)};return a().createElement(l8,sp({},tc(t,!0),s,o,{parentViewBox:t.parentViewBox,value:n,textBreakAll:l,viewBox:l8.parseViewBox(N()(c)?t:sd(sd({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}sm.displayName="LabelList",sm.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var o=tr(t.children,sm).map(function(t,r){return(0,i.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label)?!0===r?a().createElement(sm,{key:"labelList-implicit",data:e}):a().isValidElement(r)||I()(r)?a().createElement(sm,{key:"labelList-implicit",data:e,content:r}):R()(r)?a().createElement(sm,sp({data:e},r,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return sf(t)}(o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return sf(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sf(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var sb=r(24319),sg=r.n(sb),sx=r(84228),sO=r.n(sx);function sw(t){return(sw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sj(){return(sj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sS(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sP(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=sw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sw(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sk=function(t,e,r,n,o){var i=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-i/2,",").concat(e+o)+"L ".concat(t+r-i/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},sE={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sT=function(t){var e,r=sA(sA({},sE),t),n=(0,i.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,i.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return sS(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sS(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=o[0],l=o[1];(0,i.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&l(t)}catch(t){}},[]);var s=r.x,f=r.y,p=r.upperWidth,h=r.lowerWidth,d=r.height,y=r.className,v=r.animationEasing,m=r.animationDuration,b=r.animationBegin,g=r.isUpdateAnimationActive;if(s!==+s||f!==+f||p!==+p||h!==+h||d!==+d||0===p&&0===h||0===d)return null;var x=u("recharts-trapezoid",y);return g?a().createElement(nn,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:d,x:s,y:f},to:{upperWidth:p,lowerWidth:h,height:d,x:s,y:f},duration:m,animationEasing:v,isActive:g},function(t){var e=t.upperWidth,o=t.lowerWidth,i=t.height,u=t.x,l=t.y;return a().createElement(nn,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:b,duration:m,easing:v},a().createElement("path",sj({},tc(r,!0),{className:x,d:sk(u,l,e,o,i),ref:n})))}):a().createElement("g",null,a().createElement("path",sj({},tc(r,!0),{className:x,d:sk(s,f,p,h,d)})))};function s_(t){return(s_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sM(){return(sM=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sC(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sN(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sC(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=s_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s_(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sC(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sD=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/lW,f=u?o:o+i*s;return{center:lX(e,r,l,f),circleTangency:lX(e,r,n,f),lineTangency:lX(e,r,l*Math.cos(s*lW),u?o-i*s:o),theta:s}},sI=function(t){var e,r=t.cx,n=t.cy,o=t.innerRadius,i=t.outerRadius,a=t.startAngle,c=g((e=t.endAngle)-a)*Math.min(Math.abs(e-a),359.999),u=a+c,l=lX(r,n,i,a),s=lX(r,n,i,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(a>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(o>0){var p=lX(r,n,o,a),h=lX(r,n,o,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(o,",").concat(o,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(a<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(r,",").concat(n," Z");return f},sB=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=g(l-u),f=sD({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=sD({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,x=c?Math.abs(u-l):Math.abs(u-l)-d-b;if(x<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):sI({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var O="M ".concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(x>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=sD({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),j=w.circleTangency,S=w.lineTangency,P=w.theta,A=sD({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),k=A.circleTangency,E=A.lineTangency,T=A.theta,_=c?Math.abs(u-l):Math.abs(u-l)-P-T;if(_<0&&0===i)return"".concat(O,"L").concat(e,",").concat(r,"Z");O+="L".concat(E.x,",").concat(E.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(k.x,",").concat(k.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(_>180),",").concat(+(s>0),",").concat(j.x,",").concat(j.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(S.x,",").concat(S.y,"Z")}else O+="L".concat(e,",").concat(r,"Z");return O},sR={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sL=function(t){var e,r=sN(sN({},sR),t),n=r.cx,o=r.cy,i=r.innerRadius,c=r.outerRadius,l=r.cornerRadius,s=r.forceCornerRadius,f=r.cornerIsExternal,p=r.startAngle,h=r.endAngle,d=r.className;if(c<i||p===h)return null;var y=u("recharts-sector",d),v=c-i,m=P(l,v,0,!0);return e=m>0&&360>Math.abs(p-h)?sB({cx:n,cy:o,innerRadius:i,outerRadius:c,cornerRadius:Math.min(m,v/2),forceCornerRadius:s,cornerIsExternal:f,startAngle:p,endAngle:h}):sI({cx:n,cy:o,innerRadius:i,outerRadius:c,startAngle:p,endAngle:h}),a().createElement("path",sM({},tc(r,!0),{className:y,d:e,role:"img"}))},sz=["option","shapeType","propTransformer","activeClassName","isActive"];function sU(t){return(sU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sF(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=sU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sU(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sq(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return a().createElement(np,r);case"trapezoid":return a().createElement(sT,r);case"sector":return a().createElement(sL,r);case"symbols":if("symbols"===e)return a().createElement(eN,r);break;default:return null}}function sW(t){var e,r=t.option,n=t.shapeType,o=t.propTransformer,c=t.activeClassName,u=t.isActive,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sz);if((0,i.isValidElement)(r))e=(0,i.cloneElement)(r,s$(s$({},l),(0,i.isValidElement)(r)?r.props:r));else if(I()(r))e=r(l);else if(sg()(r)&&!sO()(r)){var s=(void 0===o?function(t,e){return s$(s$({},e),t)}:o)(r,l);e=a().createElement(sq,{shapeType:n,elementProps:s})}else e=a().createElement(sq,{shapeType:n,elementProps:l});return u?a().createElement(tk,{className:void 0===c?"recharts-active-shape":c},e):e}function sX(t,e){return null!=e&&"trapezoids"in t.props}function sH(t,e){return null!=e&&"sectors"in t.props}function sV(t,e){return null!=e&&"points"in t.props}function sG(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function sK(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function sY(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}var sZ=["x","y"];function sJ(t){return(sJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sQ(){return(sQ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s0(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=sJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sJ(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s2(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sZ),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||o.height),10),u=parseInt("".concat(e.width||o.width),10);return s1(s1(s1(s1(s1({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function s3(t){return a().createElement(sW,sQ({shapeType:"rectangle",propTransformer:s2,activeClassName:"recharts-active-bar"},t))}var s6=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||tO(!1),e)}},s5=["value","background"];function s4(t){return(s4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s7(){return(s7=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s8(Object(r),!0).forEach(function(e){fo(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ft(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fi(n.key),n)}}function fe(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fe=function(){return!!t})()}function fr(t){return(fr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fn(t,e){return(fn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fo(t,e,r){return(e=fi(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fi(t){var e=function(t,e){if("object"!=s4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s4(e)?e:e+""}var fa=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=fr(e),fo(t=function(t,e){if(e&&("object"===s4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fe()?Reflect.construct(e,r||[],fr(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),fo(t,"id",S("recharts-bar-")),fo(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),fo(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fn(t,e)}(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,c=r.activeBar,u=tc(this.props,!1);return t&&t.map(function(t,r){var l=r===i,s=s9(s9(s9({},u),t),{},{isActive:l,option:l?c:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return a().createElement(tk,s7({className:"recharts-bar-rectangle"},H(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),a().createElement(s3,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,i=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return a().createElement(nn,{begin:i,duration:c,isActive:o,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=r.map(function(t,e){var r=s&&s[e];if(r){var i=E(r.x,t.x),a=E(r.y,t.y),c=E(r.width,t.width),u=E(r.height,t.height);return s9(s9({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var l=E(0,t.height)(o);return s9(s9({},t),{},{y:t.y+t.height-l,height:l})}var f=E(0,t.width)(o);return s9(s9({},t),{},{width:f})});return a().createElement(tk,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!cH()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,i=tc(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,s5);if(!c)return null;var l=s9(s9(s9(s9(s9({},u),{},{fill:"#eee"},c),i),H(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(s3,s7({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,c=r.layout,u=tr(r.children,uz);if(!u)return null;var l="vertical"===c?n[0].height/2:n[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:uY(t,e)}};return a().createElement(tk,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:c,offset:l,dataPointFormatter:s})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.xAxis,i=t.yAxis,c=t.left,l=t.top,s=t.width,f=t.height,p=t.isAnimationActive,h=t.background,d=t.id;if(e||!r||!r.length)return null;var y=this.state.isAnimationFinished,v=u("recharts-bar",n),m=o&&o.allowDataOverflow,b=i&&i.allowDataOverflow,g=m||b,x=N()(d)?this.id:d;return a().createElement(tk,{className:v},m||b?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(x)},a().createElement("rect",{x:m?c:c-s/2,y:b?l:l-f/2,width:m?s:2*s,height:b?f:2*f}))):null,a().createElement(tk,{className:"recharts-bar-rectangles",clipPath:g?"url(#clipPath-".concat(x,")"):null},h?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(g,x),(!p||y)&&sm.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&ft(n.prototype,e),r&&ft(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function fc(t){return(fc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fu(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fp(n.key),n)}}function fl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fs(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fl(Object(r),!0).forEach(function(e){ff(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fl(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ff(t,e,r){return(e=fp(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fp(t){var e=function(t,e){if("object"!=fc(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fc(e)?e:e+""}fo(fa,"displayName","Bar"),fo(fa,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!tY.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fo(fa,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=ln(n,r);if(!h)return null;var d=e.layout,y=r.type.defaultProps,v=void 0!==y?s9(s9({},y),r.props):r.props,m=v.dataKey,b=v.children,x=v.minPointSize,O="horizontal"===d?a:i,w=l?O.scale.domain():null,j=lf({numericAxis:O}),S=tr(b,si),P=f.map(function(t,e){l?f=lo(l[s+e],w):Array.isArray(f=uY(t,m))||(f=[j,f]);var n=s6(x,fa.defaultProps.minPointSize)(f[1],e);if("horizontal"===d){var f,p,y,v,b,O,P,A=[a.scale(f[0]),a.scale(f[1])],k=A[0],E=A[1];p=ls({axis:i,ticks:c,bandSize:o,offset:h.offset,entry:t,index:e}),y=null!==(P=null!=E?E:k)&&void 0!==P?P:void 0,v=h.size;var T=k-E;if(b=Number.isNaN(T)?0:T,O={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var _=g(b||n)*(Math.abs(n)-Math.abs(b));y-=_,b+=_}}else{var M=[i.scale(f[0]),i.scale(f[1])],C=M[0],N=M[1];if(p=C,y=ls({axis:a,ticks:u,bandSize:o,offset:h.offset,entry:t,index:e}),v=N-C,b=h.size,O={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var D=g(v||n)*(Math.abs(n)-Math.abs(v));v+=D}}return s9(s9(s9({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:O},S&&S[e]&&S[e].props),{},{tooltipPayload:[lg(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return s9({data:P,layout:d},p)});var fh=function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tn(u,fa);return l.reduce(function(i,a){var u,l,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,O=y.reversed,w="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,A=y.categoricalDomain.sort(_);if(A.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(A[e-1]||0),S))}),Number.isFinite(S)){var k=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=k*E/2),"no-gap"===y.padding){var T=P(t.barCategoryGap,k*E),M=k*E/2;u=M-T-(M-T)/E*T}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,O&&(l=[l[1],l[0]]);var C=le(y,o,f),N=C.scale,D=C.realScaleType;N.domain(m).range(l),lr(N);var I=lu(N,fs(fs({},y),{},{realScaleType:D}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[w]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=s[w]-d*y.width,h=r.top);var B=fs(fs(fs({},y),I),{},{realScaleType:D,x:p,y:h,scale:N,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return B.bandSize=lm(B,I),y.hide||"xAxis"!==n?y.hide||(s[w]+=(d?-1:1)*B.width):s[w]+=(d?-1:1)*B.height,fs(fs({},i),{},ff({},a,B))},{})},fd=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},fy=function(){var t,e;function r(t){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fu(r.prototype,t),e&&fu(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();ff(fy,"EPS",1e-4);var fv=function(t){var e=Object.keys(t).reduce(function(e,r){return fs(fs({},e),{},ff({},r,fy.create(t[r])))},{});return fs(fs({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return sr()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return so()(t,function(t,r){return e[r].isInRange(t)})}})},fm=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))};function fb(){return(fb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fg(t){return(fg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fx(Object(r),!0).forEach(function(e){fA(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fw(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fk(n.key),n)}}function fj(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fj=function(){return!!t})()}function fS(t){return(fS=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fP(t,e){return(fP=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fA(t,e,r){return(e=fk(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fk(t){var e=function(t,e){if("object"!=fg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fg(e)?e:e+""}var fE=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=fv({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return st(t,"discard")&&!i.isInRange(a)?null:a},fT=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=fS(t),function(t,e){if(e&&("object"===fg(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fj()?Reflect.construct(t,e||[],fS(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fP(t,e)}(n,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,r=t.y,o=t.r,i=t.alwaysShow,c=t.clipPathId,l=w(e),s=w(r);if(M(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!l||!s)return null;var f=fE(this.props);if(!f)return null;var p=f.x,h=f.y,d=this.props,y=d.shape,v=d.className,m=fO(fO({clipPath:st(this.props,"hidden")?"url(#".concat(c,")"):void 0},tc(this.props,!0)),{},{cx:p,cy:h});return a().createElement(tk,{className:u("recharts-reference-dot",v)},n.renderDot(y,m),l8.renderCallByParent(this.props,{x:p-o,y:h-o,width:2*o,height:2*o}))}}],fw(n.prototype,e),r&&fw(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a().Component);fA(fT,"displayName","ReferenceDot"),fA(fT,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fA(fT,"renderDot",function(t,e){return a().isValidElement(t)?a().cloneElement(t,e):I()(t)?t(e):a().createElement(e3,fb({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var f_=r(94137),fM=r.n(f_),fC=r(94784),fN=r.n(fC),fD=r(84722),fI=r.n(fD)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fB=(0,i.createContext)(void 0),fR=(0,i.createContext)(void 0),fL=(0,i.createContext)(void 0),fz=(0,i.createContext)({}),fU=(0,i.createContext)(void 0),fF=(0,i.createContext)(0),f$=(0,i.createContext)(0),fq=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,i=t.clipPathId,c=t.children,u=t.width,l=t.height,s=fI(o);return a().createElement(fB.Provider,{value:r},a().createElement(fR.Provider,{value:n},a().createElement(fz.Provider,{value:o},a().createElement(fL.Provider,{value:s},a().createElement(fU.Provider,{value:i},a().createElement(fF.Provider,{value:l},a().createElement(f$.Provider,{value:u},c)))))))},fW=function(t){var e=(0,i.useContext)(fB);null!=e||tO(!1);var r=e[t];return null!=r||tO(!1),r},fX=function(){var t=(0,i.useContext)(fR);return fN()(t,function(t){return so()(t.domain,Number.isFinite)})||A(t)},fH=function(t){var e=(0,i.useContext)(fR);null!=e||tO(!1);var r=e[t];return null!=r||tO(!1),r},fV=function(){return(0,i.useContext)(f$)},fG=function(){return(0,i.useContext)(fF)};function fK(t){return(fK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fY(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,f3(n.key),n)}}function fZ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fZ=function(){return!!t})()}function fJ(t){return(fJ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fQ(t,e){return(fQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function f0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f0(Object(r),!0).forEach(function(e){f2(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function f2(t,e,r){return(e=f3(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f3(t){var e=function(t,e){if("object"!=fK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fK(e)?e:e+""}function f6(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f5(){return(f5=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var f4=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var h=u.y,d=t.y.apply(h,{position:i});if(st(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(st(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return st(u,"discard")&&fM()(g,function(e){return!t.isInRange(e)})?null:g}return null};function f7(t){var e,r,n,o=t.x,c=t.y,l=t.segment,s=t.xAxisId,f=t.yAxisId,p=t.shape,h=t.className,d=t.alwaysShow,y=(0,i.useContext)(fU),v=fW(s),m=fH(f),b=(0,i.useContext)(fL);if(!y||!b)return null;M(void 0===d,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var g=f4(fv({x:v.scale,y:m.scale}),w(o),w(c),l&&2===l.length,b,t.position,v.orientation,m.orientation,t);if(!g)return null;var x=function(t){if(Array.isArray(t))return t}(g)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(g,2)||function(t,e){if(t){if("string"==typeof t)return f6(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f6(t,e)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),O=x[0],j=O.x,S=O.y,P=x[1],A=P.x,k=P.y,E=f1(f1({clipPath:st(t,"hidden")?"url(#".concat(y,")"):void 0},tc(t,!0)),{},{x1:j,y1:S,x2:A,y2:k});return a().createElement(tk,{className:u("recharts-reference-line",h)},(e=p,r=E,a().isValidElement(e)?a().cloneElement(e,r):I()(e)?e(r):a().createElement("line",f5({},r,{className:"recharts-reference-line-line"}))),l8.renderCallByParent(t,fd({x:(n={x1:j,y1:S,x2:A,y2:k}).x1,y:n.y1},{x:n.x2,y:n.y2})))}var f8=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=fJ(t),function(t,e){if(e&&("object"===fK(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fZ()?Reflect.construct(t,e||[],fJ(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fQ(t,e)}(n,t),e=[{key:"render",value:function(){return a().createElement(f7,this.props)}}],fY(n.prototype,e),r&&fY(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a().Component);function f9(){return(f9=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pt(t){return(pt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pe(Object(r),!0).forEach(function(e){pc(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pe(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function pn(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pu(n.key),n)}}function po(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(po=function(){return!!t})()}function pi(t){return(pi=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pa(t,e){return(pa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pc(t,e,r){return(e=pu(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pu(t){var e=function(t,e){if("object"!=pt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pt(e)?e:e+""}f2(f8,"displayName","ReferenceLine"),f2(f8,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var pl=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=fv({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!st(o,"discard")||f.isInRange(p)&&f.isInRange(h)?fd(p,h):null},ps=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=pi(t),function(t,e){if(e&&("object"===pt(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,po()?Reflect.construct(t,e||[],pi(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&pa(t,e)}(n,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,r=t.x2,o=t.y1,i=t.y2,c=t.className,l=t.alwaysShow,s=t.clipPathId;M(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var f=w(e),p=w(r),h=w(o),d=w(i),y=this.props.shape;if(!f&&!p&&!h&&!d&&!y)return null;var v=pl(f,p,h,d,this.props);if(!v&&!y)return null;var m=st(this.props,"hidden")?"url(#".concat(s,")"):void 0;return a().createElement(tk,{className:u("recharts-reference-area",c)},n.renderRect(y,pr(pr({clipPath:m},tc(this.props,!0)),v)),l8.renderCallByParent(this.props,v))}}],pn(n.prototype,e),r&&pn(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a().Component);function pf(t){return function(t){if(Array.isArray(t))return pp(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pp(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pp(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}pc(ps,"displayName","ReferenceArea"),pc(ps,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pc(ps,"renderRect",function(t,e){return a().isValidElement(t)?a().cloneElement(t,e):I()(t)?t(e):a().createElement(np,f9({},e,{className:"recharts-reference-area-rect"}))});var ph=function(t,e,r,n,o){var i=tr(t,f8),a=tr(t,fT),c=[].concat(pf(i),pf(a)),u=tr(t,ps),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&st(e.props,"extendDomain")&&O(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&st(e.props,"extendDomain")&&O(e.props[p])&&O(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return O(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pd=r(14149),py=new(r.n(pd)()),pv="recharts.syncMouseEvents";function pm(t){return(pm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pb(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,px(n.key),n)}}function pg(t,e,r){return(e=px(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function px(t){var e=function(t,e){if("object"!=pm(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pm(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pm(e)?e:e+""}var pO=function(){var t,e,r;return t=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),pg(this,"activeIndex",0),pg(this,"coordinateList",[]),pg(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],pb(t.prototype,e),r&&pb(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}();function pw(){}function pj(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pS(t){this._context=t}function pP(t){this._context=t}function pA(t){this._context=t}pS.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pj(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pj(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pP.prototype={areaStart:pw,areaEnd:pw,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pj(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pA.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pj(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class pk{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pE(t){this._context=t}function pT(t){this._context=t}function p_(t){return new pT(t)}function pM(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function pC(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pN(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function pD(t){this._context=t}function pI(t){this._context=new pB(t)}function pB(t){this._context=t}function pR(t){this._context=t}function pL(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function pz(t,e){this._context=t,this._t=e}function pU(t){return t[0]}function pF(t){return t[1]}function p$(t,e){var r=eb(!0),n=null,o=p_,i=null,a=eS(c);function c(c){var u,l,s,f=(c=cA(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?pU:eb(t),e="function"==typeof e?e:void 0===e?pF:eb(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:eb(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:eb(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:eb(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function pq(t,e,r){var n=null,o=eb(!0),i=null,a=p_,c=null,u=eS(l);function l(l){var s,f,p,h,d,y=(l=cA(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&o(h=l[s],s,l))===v){if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return p$().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?pU:eb(+t),e="function"==typeof e?e:void 0===e?eb(0):eb(+e),r="function"==typeof r?r:void 0===r?pF:eb(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:eb(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:eb(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:eb(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:eb(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:eb(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:eb(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:eb(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}function pW(t){return(pW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pX(){return(pX=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pH(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=pW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pW(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pE.prototype={areaStart:pw,areaEnd:pw,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},pT.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pD.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pN(this,this._t0,pC(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pN(this,pC(this,r=pM(this,t,e)),r);break;default:pN(this,this._t0,r=pM(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pI.prototype=Object.create(pD.prototype)).point=function(t,e){pD.prototype.point.call(this,e,t)},pB.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},pR.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pL(t),o=pL(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pz.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var pG={curveBasisClosed:function(t){return new pP(t)},curveBasisOpen:function(t){return new pA(t)},curveBasis:function(t){return new pS(t)},curveBumpX:function(t){return new pk(t,!0)},curveBumpY:function(t){return new pk(t,!1)},curveLinearClosed:function(t){return new pE(t)},curveLinear:p_,curveMonotoneX:function(t){return new pD(t)},curveMonotoneY:function(t){return new pI(t)},curveNatural:function(t){return new pR(t)},curveStep:function(t){return new pz(t,.5)},curveStepAfter:function(t){return new pz(t,1)},curveStepBefore:function(t){return new pz(t,0)}},pK=function(t){return t.x===+t.x&&t.y===+t.y},pY=function(t){return t.x},pZ=function(t){return t.y},pJ=function(t,e){if(I()(t))return t;var r="curve".concat(er()(t));return("curveMonotone"===r||"curveBump"===r)&&e?pG["".concat(r).concat("vertical"===e?"Y":"X")]:pG[r]||p_},pQ=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=pJ(void 0===r?"linear":r,a),s=u?o.filter(function(t){return pK(t)}):o;if(Array.isArray(i)){var f=u?i.filter(function(t){return pK(t)}):i,p=s.map(function(t,e){return pV(pV({},t),{},{base:f[e]})});return(e="vertical"===a?pq().y(pZ).x1(pY).x0(function(t){return t.base.x}):pq().x(pY).y1(pZ).y0(function(t){return t.base.y})).defined(pK).curve(l),e(p)}return(e="vertical"===a&&O(i)?pq().y(pZ).x1(pY).x0(i):O(i)?pq().x(pY).y1(pZ).y0(i):p$().x(pY).y(pZ)).defined(pK).curve(l),e(s)},p0=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?pQ(t):n;return a().createElement("path",pX({},tc(t,!1),X(t),{className:u("recharts-curve",e),d:i,ref:o}))};function p1(t){return(p1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var p2=["x","y","top","left","width","height","className"];function p3(){return(p3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var p5=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,i=t.top,c=void 0===i?0:i,l=t.left,s=void 0===l?0:l,f=t.width,p=void 0===f?0:f,h=t.height,d=void 0===h?0:h,y=t.className,v=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p6(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=p1(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p1(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p1(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:c,left:s,width:p,height:d},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,p2));return O(r)&&O(o)&&O(p)&&O(d)&&O(c)&&O(s)?a().createElement("path",p3({},tc(v,!0),{className:u("recharts-cross",y),d:"M".concat(r,",").concat(c,"v").concat(d,"M").concat(s,",").concat(o,"h").concat(p)})):null};function p4(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[lX(e,r,n,o),lX(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function p7(t){return(p7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p8(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=p7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p7(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ht(t){var e,r,n,o,a=t.element,c=t.tooltipEventType,l=t.isActive,s=t.activeCoordinate,f=t.activePayload,p=t.offset,h=t.activeTooltipIndex,d=t.tooltipAxisBandSize,y=t.layout,v=t.chartName,m=null!==(r=a.props.cursor)&&void 0!==r?r:null===(n=a.type.defaultProps)||void 0===n?void 0:n.cursor;if(!a||!m||!l||!s||"ScatterChart"!==v&&"axis"!==c)return null;var b=p0;if("ScatterChart"===v)o=s,b=p5;else if("BarChart"===v)e=d/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===y?s.x-e:p.left+.5,y:"horizontal"===y?p.top+.5:s.y-e,width:"horizontal"===y?d:p.width-1,height:"horizontal"===y?p.height-1:d},b=np;else if("radial"===y){var g=p4(s),x=g.cx,O=g.cy,w=g.radius;o={cx:x,cy:O,startAngle:g.startAngle,endAngle:g.endAngle,innerRadius:w,outerRadius:w},b=sL}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return p4(e);var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=lX(c,u,l,f),h=lX(c,u,s,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(y,s,p)},b=p0;var j=p9(p9(p9(p9({stroke:"#ccc",pointerEvents:"none"},p),o),tc(m,!1)),{},{payload:f,payloadIndex:h,className:u("recharts-tooltip-cursor",m.className)});return(0,i.isValidElement)(m)?(0,i.cloneElement)(m,j):(0,i.createElement)(b,j)}var he=["item"],hr=["children","className","width","height","style","compact","title","desc"];function hn(t){return(hn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ho(){return(ho=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hi(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||hp(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ha(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hm(n.key),n)}}function hu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hu=function(){return!!t})()}function hl(t){return(hl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hs(t,e){return(hs=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hf(t){return function(t){if(Array.isArray(t))return hh(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hp(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hp(t,e){if(t){if("string"==typeof t)return hh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hh(t,e)}}function hh(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hy(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hd(Object(r),!0).forEach(function(e){hv(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hd(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hv(t,e,r){return(e=hm(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hm(t){var e=function(t,e){if("object"!=hn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hn(e)?e:e+""}var hb={xAxis:["bottom","top"],yAxis:["left","right"]},hg={width:"100%",height:"100%"},hx={x:0,y:0};function hO(t){return t}var hw=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return hy(hy(hy({},n),lX(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return hy(hy(hy({},n),lX(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return hx},hj=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(hf(t),hf(r)):t},[]);return i.length>0?i:t&&t.length&&O(n)&&O(o)?t.slice(n,o+1):[]};function hS(t){return"number"===t?[0,"auto"]:void 0}var hP=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=hj(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=i.dataKey&&!i.allowDuplicatedCategory?T(void 0===s?a:s,i.dataKey,n):s&&s[r]||a[r])?[].concat(hf(o),[lg(c,l)]):o},[])},hA=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=uJ(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=hP(t,e,l,s),p=hw(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},hk=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=u4(l,o);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?hy(hy({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,w=h[i];if(e[w])return e;var j=hj(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===w}),dataStartIndex:c,dataEndIndex:u}),S=j.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&O(n)&&O(o))return!0}return!1})(h.domain,v,d)&&(E=lv(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(_=uZ(j,y,"category")));var P=hS(d);if(!E||0===E.length){var A,E,T,_,M,C=null!==(M=h.domain)&&void 0!==M?M:P;if(y){if(E=uZ(j,y,d),"category"===d&&p){var D=k(E);m&&D?(T=E,E=tb()(0,S)):m||(E=lb(C,E,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(hf(t),[e])},[]))}else if("category"===d)E=m?E.filter(function(t){return""!==t&&!N()(t)}):lb(C,E,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||N()(e)?t:[].concat(hf(t),[e])},[]);else if("number"===d){var I=u6(j,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===w&&(x||!o)}),y,o,l);I&&(E=I)}p&&("number"===d||"auto"!==b)&&(_=uZ(j,y,"category"))}else E=p?tb()(0,S):a&&a[w]&&a[w].hasStack&&"number"===d?"expand"===f?[0,1]:lh(a[w].stackGroups,c,u):u5(j,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===w&&(x||!r)}),d,l,!0);"number"===d?(E=ph(s,E,w,o,g),C&&(E=lv(C,E,v))):"category"===d&&C&&E.every(function(t){return C.indexOf(t)>=0})&&(E=C)}return hy(hy({},e),{},hv({},w,hy(hy({},h),{},{axisType:o,domain:E,categoricalDomain:_,duplicateDomain:T,originalDomain:null!==(A=h.domain)&&void 0!==A?A:P,isCategorical:p,layout:l})))},{})},hE=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=hj(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=u4(l,o),d=-1;return r.reduce(function(t,e){var y,m=(void 0!==e.type.defaultProps?hy(hy({},e.type.defaultProps),e.props):e.props)[i],b=hS("number");return t[m]?t:(d++,y=h?tb()(0,p):a&&a[m]&&a[m].hasStack?ph(s,y=lh(a[m].stackGroups,c,u),m,o):ph(s,y=lv(b,u5(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===m&&!o}),"number",l),n.defaultProps.allowDataOverflow),m,o),hy(hy({},t),{},hv({},m,hy(hy({axisType:o},n.defaultProps),{},{hide:!0,orientation:v()(hb,"".concat(o,".").concat(d%2),null),domain:y,originalDomain:b,isCategorical:h,layout:l}))))},{})},hT=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=tr(l,o),p={};return f&&f.length?p=hk(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=hE(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},h_=function(t){var e=A(t),r=u8(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tx()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lm(e,r)}},hM=function(t){var e=t.children,r=t.defaultShowTooltip,n=tn(e,lL),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},hC=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},hN=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=tn(s,lL),h=tn(s,e1),d=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:hy(hy({},t),{},hv({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:hy(hy({},t),{},hv({},n,v()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),m=hy(hy({},y),d),b=m.bottom;p&&(m.bottom+=p.props.height||lL.defaultProps.height),h&&e&&(m=u2(m,n,r,e));var g=u-m.left-m.right,x=l-m.top-m.bottom;return hy(hy({brushBottom:b},m),{},{width:Math.max(g,0),height:Math.max(x,0)})},hD=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,o=void 0===n?"axis":n,c=t.validateTooltipEventTypes,l=void 0===c?["axis"]:c,f=t.axisComponents,p=t.legendContent,h=t.formatAxisMap,d=t.defaultProps,y=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,l=t.layout,s=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=hC(l),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=J(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=hj(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?hy(hy({},r.type.defaultProps),r.props):r.props,O=x.dataKey,w=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],P=f.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||tO(!1);var i=n[o];return hy(hy({},t),{},hv(hv({},r.axisType,i),"".concat(r.axisType,"Ticks"),u8(i)))},{}),A=P[v],k=P["".concat(v,"Ticks")],E=n&&n[j]&&n[j].hasStack&&lp(r,n[j].stackGroups),T=J(r.type).indexOf("Bar")>=0,_=lm(A,k),M=[],C=m&&u0({barSize:u,stackGroups:n,totalSize:"xAxis"===v?P[v].width:"yAxis"===v?P[v].height:void 0});if(T){var D,I,B=N()(w)?h:w,R=null!==(D=null!==(I=lm(A,k,!0))&&void 0!==I?I:B)&&void 0!==D?D:0;M=u1({barGap:s,barCategoryGap:p,bandSize:R!==_?R:_,sizeList:C[S],maxBarSize:B}),R!==_&&(M=M.map(function(t){return hy(hy({},t),{},{position:hy(hy({},t.position),{},{offset:t.position.offset-R/2})})}))}var L=r&&r.type&&r.type.getComposedData;L&&b.push({props:hy(hy({},L(hy(hy({},P),{},{displayedData:g,props:t,dataKey:O,item:r,bandSize:_,barPosition:M,offset:o,stackedData:E,layout:l,dataStartIndex:a,dataEndIndex:c}))),{},hv(hv(hv({key:r.key||"item-".concat(d)},y,P[y]),v,P[v]),"animationId",i)),childIndex:te(t.children).indexOf(r),item:r})}),b},m=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!to({props:o}))return null;var u=o.children,l=o.layout,s=o.stackOffset,p=o.data,d=o.reverseStackOrder,v=hC(l),m=v.numericAxisName,b=v.cateAxisName,g=tr(u,r),x=lc(p,g,"".concat(m,"Id"),"".concat(b,"Id"),s,d),O=f.reduce(function(t,e){var r="".concat(e.axisType,"Map");return hy(hy({},t),{},hv({},r,hT(o,hy(hy({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),w=hN(hy(hy({},O),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(O).forEach(function(t){O[t]=h(o,O[t],w,t.replace("Map",""),e)});var j=h_(O["".concat(b,"Map")]),S=y(o,hy(hy({},O),{},{dataStartIndex:i,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:w}));return hy(hy({formattedGraphicalItems:S,graphicalItems:g,offset:w,stackGroups:x},j),O)},b=function(t){var r,n;function c(t){var r,n,o,l,f;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,c),l=c,f=[t],l=hl(l),hv(o=function(t,e){if(e&&("object"===hn(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,hu()?Reflect.construct(l,f||[],hl(this).constructor):l.apply(this,f)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hv(o,"accessibilityManager",new pO),hv(o,"handleLegendBBoxUpdate",function(t){if(t){var e=o.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;o.setState(hy({legendBBox:t},m({props:o.props,dataStartIndex:r,dataEndIndex:n,updateId:i},hy(hy({},o.state),{},{legendBBox:t}))))}}),hv(o,"handleReceiveSyncEvent",function(t,e,r){o.props.syncId===t&&(r!==o.eventEmitterSymbol||"function"==typeof o.props.syncMethod)&&o.applySyncEvent(e)}),hv(o,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==o.state.dataStartIndex||r!==o.state.dataEndIndex){var n=o.state.updateId;o.setState(function(){return hy({dataStartIndex:e,dataEndIndex:r},m({props:o.props,dataStartIndex:e,dataEndIndex:r,updateId:n},o.state))}),o.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),hv(o,"handleMouseEnter",function(t){var e=o.getMouseInfo(t);if(e){var r=hy(hy({},e),{},{isTooltipActive:!0});o.setState(r),o.triggerSyncEvent(r);var n=o.props.onMouseEnter;I()(n)&&n(r,t)}}),hv(o,"triggeredAfterMouseMove",function(t){var e=o.getMouseInfo(t),r=e?hy(hy({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};o.setState(r),o.triggerSyncEvent(r);var n=o.props.onMouseMove;I()(n)&&n(r,t)}),hv(o,"handleItemMouseEnter",function(t){o.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),hv(o,"handleItemMouseLeave",function(){o.setState(function(){return{isTooltipActive:!1}})}),hv(o,"handleMouseMove",function(t){t.persist(),o.throttleTriggeredAfterMouseMove(t)}),hv(o,"handleMouseLeave",function(t){o.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};o.setState(e),o.triggerSyncEvent(e);var r=o.props.onMouseLeave;I()(r)&&r(e,t)}),hv(o,"handleOuterEvent",function(t){var e,r=tf(t),n=v()(o.props,"".concat(r));r&&I()(n)&&n(null!==(e=/.*touch.*/i.test(r)?o.getMouseInfo(t.changedTouches[0]):o.getMouseInfo(t))&&void 0!==e?e:{},t)}),hv(o,"handleClick",function(t){var e=o.getMouseInfo(t);if(e){var r=hy(hy({},e),{},{isTooltipActive:!0});o.setState(r),o.triggerSyncEvent(r);var n=o.props.onClick;I()(n)&&n(r,t)}}),hv(o,"handleMouseDown",function(t){var e=o.props.onMouseDown;I()(e)&&e(o.getMouseInfo(t),t)}),hv(o,"handleMouseUp",function(t){var e=o.props.onMouseUp;I()(e)&&e(o.getMouseInfo(t),t)}),hv(o,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),hv(o,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.handleMouseDown(t.changedTouches[0])}),hv(o,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.handleMouseUp(t.changedTouches[0])}),hv(o,"handleDoubleClick",function(t){var e=o.props.onDoubleClick;I()(e)&&e(o.getMouseInfo(t),t)}),hv(o,"handleContextMenu",function(t){var e=o.props.onContextMenu;I()(e)&&e(o.getMouseInfo(t),t)}),hv(o,"triggerSyncEvent",function(t){void 0!==o.props.syncId&&py.emit(pv,o.props.syncId,t,o.eventEmitterSymbol)}),hv(o,"applySyncEvent",function(t){var e=o.props,r=e.layout,n=e.syncMethod,i=o.state.updateId,a=t.dataStartIndex,c=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)o.setState(hy({dataStartIndex:a,dataEndIndex:c},m({props:o.props,dataStartIndex:a,dataEndIndex:c,updateId:i},o.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=o.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){s=d;break}}var y=hy(hy({},p),{},{x:p.left,y:p.top}),v=Math.min(u,y.x+y.width),b=Math.min(l,y.y+y.height),g=h[s]&&h[s].value,x=hP(o.state,o.props.data,s),O=h[s]?{x:"horizontal"===r?h[s].coordinate:v,y:"horizontal"===r?b:h[s].coordinate}:hx;o.setState(hy(hy({},t),{},{activeLabel:g,activeCoordinate:O,activePayload:x,activeTooltipIndex:s}))}else o.setState(t)}),hv(o,"renderCursor",function(t){var r,n=o.state,i=n.isTooltipActive,c=n.activeCoordinate,u=n.activePayload,l=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=o.getTooltipEventType(),h=null!==(r=t.props.active)&&void 0!==r?r:i,d=o.props.layout,y=t.key||"_recharts-cursor";return a().createElement(ht,{key:y,activeCoordinate:c,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),hv(o,"renderPolarAxis",function(t,e,r){var n=v()(t,"type.axisType"),a=v()(o.state,"".concat(n,"Map")),c=t.type.defaultProps,l=void 0!==c?hy(hy({},c),t.props):t.props,s=a&&a[l["".concat(n,"Id")]];return(0,i.cloneElement)(t,hy(hy({},s),{},{className:u(n,s.className),key:t.key||"".concat(e,"-").concat(r),ticks:u8(s,!0)}))}),hv(o,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,a=e.polarRadius,c=o.state,u=c.radiusAxisMap,l=c.angleAxisMap,s=A(u),f=A(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,i.cloneElement)(t,{polarAngles:Array.isArray(n)?n:u8(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(a)?a:u8(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),hv(o,"renderLegend",function(){var t=o.state.formattedGraphicalItems,e=o.props,r=e.children,n=e.width,a=e.height,c=o.props.margin||{},u=uq({children:r,formattedGraphicalItems:t,legendWidth:n-(c.left||0)-(c.right||0),legendContent:p});if(!u)return null;var l=u.item,s=ha(u,he);return(0,i.cloneElement)(l,hy(hy({},s),{},{chartWidth:n,chartHeight:a,margin:c,onBBoxUpdate:o.handleLegendBBoxUpdate}))}),hv(o,"renderTooltip",function(){var t,e=o.props,r=e.children,n=e.accessibilityLayer,a=tn(r,et);if(!a)return null;var c=o.state,u=c.isTooltipActive,l=c.activeCoordinate,s=c.activePayload,f=c.activeLabel,p=c.offset,h=null!==(t=a.props.active)&&void 0!==t?t:u;return(0,i.cloneElement)(a,{viewBox:hy(hy({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:n})}),hv(o,"renderBrush",function(t){var e=o.props,r=e.margin,n=e.data,a=o.state,c=a.offset,u=a.dataStartIndex,l=a.dataEndIndex,s=a.updateId;return(0,i.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:lt(o.handleBrushChange,t.props.onChange),data:n,x:O(t.props.x)?t.props.x:c.left,y:O(t.props.y)?t.props.y:c.top+c.height+c.brushBottom-(r.bottom||0),width:O(t.props.width)?t.props.width:c.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),hv(o,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=o.clipPathId,a=o.state,c=a.xAxisMap,u=a.yAxisMap,l=a.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,i.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:c[h],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),hv(o,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,n=t.basePoint,o=t.childIndex,i=t.isRange,a=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?hy(hy({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=hy(hy({index:o,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:uQ(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tc(s,!1)),X(s));return a.push(c.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(o))),n?a.push(c.renderActiveDot(s,hy(hy({},f),{},{cx:n.x,cy:n.y}),"".concat(u,"-basePoint-").concat(o))):i&&a.push(null),a}),hv(o,"renderGraphicChild",function(t,e,r){var n=o.filterFormatItem(t,e,r);if(!n)return null;var a=o.getTooltipEventType(),c=o.state,u=c.isTooltipActive,l=c.tooltipAxis,s=c.activeTooltipIndex,f=c.activeLabel,p=tn(o.props.children,et),h=n.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==n.item.type.defaultProps?hy(hy({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,O=m.activeShape,w={};"axis"!==a&&p&&"click"===p.props.trigger?w={onClick:lt(o.handleItemMouseEnter,t.props.onClick)}:"axis"!==a&&(w={onMouseLeave:lt(o.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:lt(o.handleItemMouseEnter,t.props.onMouseEnter)});var j=(0,i.cloneElement)(t,hy(hy({},n.props),w));if(!g&&u&&p&&(b||x||O)){if(s>=0){if(l.dataKey&&!l.allowDuplicatedCategory){var S="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());A=T(d,S,f),k=y&&v&&T(v,S,f)}else A=null==d?void 0:d[s],k=y&&v&&v[s];if(O||x){var P=void 0!==t.props.activeIndex?t.props.activeIndex:s;return[(0,i.cloneElement)(t,hy(hy(hy({},n.props),w),{},{activeIndex:P})),null,null]}if(!N()(A))return[j].concat(hf(o.renderActivePoints({item:n,activePoint:A,basePoint:k,childIndex:s,isRange:y})))}else{var A,k,E,_=(null!==(E=o.getItemByXY(o.state.activeCoordinate))&&void 0!==E?E:{graphicalItem:j}).graphicalItem,M=_.item,C=void 0===M?t:M,D=_.childIndex,I=hy(hy(hy({},n.props),w),{},{activeIndex:D});return[(0,i.cloneElement)(C,I),null,null]}}return y?[j,null,null]:[j,null]}),hv(o,"renderCustomized",function(t,e,r){return(0,i.cloneElement)(t,hy(hy({key:"recharts-customized-".concat(r)},o.props),o.state))}),hv(o,"renderMap",{CartesianGrid:{handler:hO,once:!0},ReferenceArea:{handler:o.renderReferenceElement},ReferenceLine:{handler:hO},ReferenceDot:{handler:o.renderReferenceElement},XAxis:{handler:hO},YAxis:{handler:hO},Brush:{handler:o.renderBrush,once:!0},Bar:{handler:o.renderGraphicChild},Line:{handler:o.renderGraphicChild},Area:{handler:o.renderGraphicChild},Radar:{handler:o.renderGraphicChild},RadialBar:{handler:o.renderGraphicChild},Scatter:{handler:o.renderGraphicChild},Pie:{handler:o.renderGraphicChild},Funnel:{handler:o.renderGraphicChild},Tooltip:{handler:o.renderCursor,once:!0},PolarGrid:{handler:o.renderPolarGrid,once:!0},PolarAngleAxis:{handler:o.renderPolarAxis},PolarRadiusAxis:{handler:o.renderPolarAxis},Customized:{handler:o.renderCustomized}}),o.clipPathId="".concat(null!==(r=t.id)&&void 0!==r?r:S("recharts"),"-clip"),o.throttleTriggeredAfterMouseMove=s()(o.triggeredAfterMouseMove,null!==(n=t.throttleDelay)&&void 0!==n?n:1e3/60),o.state={},o}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&hs(t,e)}(c,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=tn(e,et);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=hP(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=hy(hy({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tu([tn(t.children,et)],[tn(this.props.children,et)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tn(this.props.children,et);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return l.indexOf(e)>=0?e:o}return o}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=hA(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=A(u).scale,h=A(l).scale,d=p&&p.invert?p.invert(o.chartX):null,y=h&&h.invert?h.invert(o.chartY):null;return hy(hy({},o),{},{xValue:d,yValue:y},f)}return f?hy(hy({},o),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?lY({x:o,y:i},A(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tn(t,et),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),hy(hy({},X(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){py.on(pv,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){py.removeListener(pv,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===J(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,i=e.width;return a().createElement("defs",null,a().createElement("clipPath",{id:t},a().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hi(e,2),n=r[0],o=r[1];return hy(hy({},t),{},hv({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hi(e,2),n=r[0],o=r[1];return hy(hy({},t),{},hv({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?hy(hy({},u.type.defaultProps),u.props):u.props,s=J(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return ns(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return lY(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(sX(a,n)||sH(a,n)||sV(a,n)){var h=function(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,c=(sX(i,o)?e="trapezoids":sH(i,o)?e="sectors":sV(i,o)&&(e="points"),e),u=sX(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:sH(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:sV(i,o)?o.payload:{},l=a.filter(function(t,e){var r=cH()(u,t),n=i.props[c].filter(function(t){var e;return(sX(i,o)?e=sG:sH(i,o)?e=sK:sV(i,o)&&(e=sY),e)(t,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:hy(hy({},a),{},{childIndex:d}),payload:sV(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!to(this))return null;var n=this.props,o=n.children,i=n.className,c=n.width,l=n.height,s=n.style,f=n.compact,p=n.title,h=n.desc,d=tc(ha(n,hr),!1);if(f)return a().createElement(fq,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(tS,ho({},d,{width:c,height:l,title:p,desc:h}),this.renderClipPath(),ts(o,this.renderMap)));this.props.accessibilityLayer&&(d.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,d.role=null!==(e=this.props.role)&&void 0!==e?e:"application",d.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},d.onFocus=function(){r.accessibilityManager.focus()});var y=this.parseEventsOfWrapper();return a().createElement(fq,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",ho({className:u("recharts-wrapper",i),style:hy({position:"relative",cursor:"default",width:c,height:l},s)},y,{ref:function(t){r.container=t}}),a().createElement(tS,ho({},d,{width:c,height:l,title:p,desc:h,style:hg}),this.renderClipPath(),ts(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],hc(c.prototype,r),n&&hc(c,n),Object.defineProperty(c,"prototype",{writable:!1}),c}(i.Component);hv(b,"displayName",e),hv(b,"defaultProps",hy({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},d)),hv(b,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=hM(t);return hy(hy(hy({},p),{},{updateId:0},m(hy(hy({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!z(l,e.prevMargin)){var h=hM(t),d={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},y=hy(hy({},hA(e,n,c)),{},{updateId:e.updateId+1}),v=hy(hy(hy({},h),d),y);return hy(hy(hy({},v),m(hy({props:t},v),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!tu(o,e.prevChildren)){var b,g,x,O,w=tn(o,lL),j=w&&null!==(b=null===(g=w.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:s,S=w&&null!==(x=null===(O=w.props)||void 0===O?void 0:O.endIndex)&&void 0!==x?x:f,P=N()(n)||j!==s||S!==f?e.updateId+1:e.updateId;return hy(hy({updateId:P},m(hy(hy({props:t},e),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:S})}return null}),hv(b,"renderActiveDot",function(t,e,r){var n;return n=(0,i.isValidElement)(t)?(0,i.cloneElement)(t,e):I()(t)?t(e):a().createElement(e3,e),a().createElement(tk,{className:"recharts-active-dot",key:r},n)});var g=(0,i.forwardRef)(function(t,e){return a().createElement(b,ho({},t,{ref:e}))});return g.displayName=b.displayName,g},hI=["type","layout","connectNulls","ref"],hB=["key"];function hR(t){return(hR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hL(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function hz(){return(hz=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hF(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hU(Object(r),!0).forEach(function(e){hG(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h$(t){return function(t){if(Array.isArray(t))return hq(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return hq(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hq(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hq(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hW(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hK(n.key),n)}}function hX(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hX=function(){return!!t})()}function hH(t){return(hH=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hV(t,e){return(hV=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hG(t,e,r){return(e=hK(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hK(t){var e=function(t,e){if("object"!=hR(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hR(e)?e:e+""}var hY=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=hH(e),hG(t=function(t,e){if(e&&("object"===hR(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,hX()?Reflect.construct(e,r||[],hH(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),hG(t,"generateSimpleStrokeDasharray",function(t,e){return"".concat(e,"px ").concat(t-e,"px")}),hG(t,"getStrokeDasharray",function(e,r,o){var i=o.reduce(function(t,e){return t+e});if(!i)return t.generateSimpleStrokeDasharray(r,e);for(var a=e%i,c=r-e,u=[],l=0,s=0;l<o.length;s+=o[l],++l)if(s+o[l]>a){u=[].concat(h$(o.slice(0,l)),[a-s]);break}var f=u.length%2==0?[0,c]:[c];return[].concat(h$(n.repeat(o,Math.floor(e/i))),h$(u),f).map(function(t){return"".concat(t,"px")}).join(", ")}),hG(t,"id",S("recharts-line-")),hG(t,"pathRef",function(e){t.mainCurve=e}),hG(t,"handleAnimationEnd",function(){t.setState({isAnimationFinished:!0}),t.props.onAnimationEnd&&t.props.onAnimationEnd()}),hG(t,"handleAnimationStart",function(){t.setState({isAnimationFinished:!1}),t.props.onAnimationStart&&t.props.onAnimationStart()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&hV(t,e)}(n,t),e=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();this.setState({totalLength:t})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var t=this.getTotalLength();t!==this.state.totalLength&&this.setState({totalLength:t})}}},{key:"getTotalLength",value:function(){var t=this.mainCurve;try{return t&&t.getTotalLength&&t.getTotalLength()||0}catch(t){return 0}}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,o=r.xAxis,i=r.yAxis,c=r.layout,u=tr(r.children,uz);if(!u)return null;var l=function(t,e){return{x:t.x,y:t.y,value:t.value,errorVal:uY(t.payload,e)}};return a().createElement(tk,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"bar-".concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:c,dataPointFormatter:l})}))}},{key:"renderDots",value:function(t,e,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var o=this.props,i=o.dot,c=o.points,u=o.dataKey,l=tc(this.props,!1),s=tc(i,!0),f=c.map(function(t,e){var r=hF(hF(hF({key:"dot-".concat(e),r:3},l),s),{},{index:e,cx:t.x,cy:t.y,value:t.value,dataKey:u,payload:t.payload,points:c});return n.renderDotItem(i,r)}),p={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return a().createElement(tk,hz({className:"recharts-line-dots",key:"dots"},p),f)}},{key:"renderCurveStatically",value:function(t,e,r,n){var o=this.props,i=o.type,c=o.layout,u=o.connectNulls,l=hF(hF(hF({},tc((o.ref,hL(o,hI)),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:e?"url(#clipPath-".concat(r,")"):null,points:t},n),{},{type:i,layout:c,connectNulls:u});return a().createElement(p0,hz({},l,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(t,e){var r=this,n=this.props,o=n.points,i=n.strokeDasharray,c=n.isAnimationActive,u=n.animationBegin,l=n.animationDuration,s=n.animationEasing,f=n.animationId,p=n.animateNewValues,h=n.width,d=n.height,y=this.state,v=y.prevPoints,m=y.totalLength;return a().createElement(nn,{begin:u,duration:l,isActive:c,easing:s,from:{t:0},to:{t:1},key:"line-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a,c=n.t;if(v){var u=v.length/o.length,l=o.map(function(t,e){var r=Math.floor(e*u);if(v[r]){var n=v[r],o=E(n.x,t.x),i=E(n.y,t.y);return hF(hF({},t),{},{x:o(c),y:i(c)})}if(p){var a=E(2*h,t.x),l=E(d/2,t.y);return hF(hF({},t),{},{x:a(c),y:l(c)})}return hF(hF({},t),{},{x:t.x,y:t.y})});return r.renderCurveStatically(l,t,e)}var s=E(0,m)(c);if(i){var f="".concat(i).split(/[,\s]+/gim).map(function(t){return parseFloat(t)});a=r.getStrokeDasharray(s,m,f)}else a=r.generateSimpleStrokeDasharray(m,s);return r.renderCurveStatically(o,t,e,{strokeDasharray:a})})}},{key:"renderCurve",value:function(t,e){var r=this.props,n=r.points,o=r.isAnimationActive,i=this.state,a=i.prevPoints,c=i.totalLength;return o&&n&&n.length&&(!a&&c>0||!cH()(a,n))?this.renderCurveWithAnimation(t,e):this.renderCurveStatically(n,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,o=e.points,i=e.className,c=e.xAxis,l=e.yAxis,s=e.top,f=e.left,p=e.width,h=e.height,d=e.isAnimationActive,y=e.id;if(r||!o||!o.length)return null;var v=this.state.isAnimationFinished,m=1===o.length,b=u("recharts-line",i),g=c&&c.allowDataOverflow,x=l&&l.allowDataOverflow,O=g||x,w=N()(y)?this.id:y,j=null!==(t=tc(n,!1))&&void 0!==t?t:{r:3,strokeWidth:2},S=j.r,P=j.strokeWidth,A=(n&&"object"===Y(n)&&"clipDot"in n?n:{}).clipDot,k=void 0===A||A,E=2*(void 0===S?3:S)+(void 0===P?2:P);return a().createElement(tk,{className:b},g||x?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(w)},a().createElement("rect",{x:g?f:f-p/2,y:x?s:s-h/2,width:g?p:2*p,height:x?h:2*h})),!k&&a().createElement("clipPath",{id:"clipPath-dots-".concat(w)},a().createElement("rect",{x:f-E/2,y:s-E/2,width:p+E,height:h+E}))):null,!m&&this.renderCurve(O,w),this.renderErrorBar(O,w),(m||n)&&this.renderDots(O,k,w),(!d||v)&&sm.renderCallByParent(this.props,o))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,prevPoints:e.curPoints}:t.points!==e.curPoints?{curPoints:t.points}:null}},{key:"repeat",value:function(t,e){for(var r=t.length%2!=0?[].concat(h$(t),[0]):t,n=[],o=0;o<e;++o)n=[].concat(h$(n),h$(r));return n}},{key:"renderDotItem",value:function(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(I()(t))r=t(e);else{var n=e.key,o=hL(e,hB),i=u("recharts-line-dot","boolean"!=typeof t?t.className:"");r=a().createElement(e3,hz({key:n},o,{className:i}))}return r}}],e&&hW(n.prototype,e),r&&hW(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function hZ(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function hJ(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function hQ(t){return(hQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h0(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=hQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hQ(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h2(t,e,r){var n,o,i,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if(O(h)||tY.isSsr)return hZ(l,("number"==typeof h&&O(h)?h:0)+1);var m="top"===p||"bottom"===p?"width":"height",b=y&&"width"===m?nE(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var o,i=I()(d)?d(t.value,n):t.value;return"width"===m?fm({width:(o=nE(i,{fontSize:e,letterSpacing:r})).width+b.width,height:o.height+b.height},v):nE(i,{fontSize:e,letterSpacing:r})[m]},w=l.length>=2?g(l[1].coordinate-l[0].coordinate):1,j=(n="width"===m,o=s.x,i=s.y,a=s.width,c=s.height,1===w?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===h?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(i=function(){var e,i=null==n?void 0:n[l];if(void 0===i)return{v:hZ(n,s)};var a=l,p=function(){return void 0===e&&(e=r(i,a)),e},h=i.coordinate,d=0===l||hJ(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+o),l+=s)}())return i.v;return[]}(w,j,x,l,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=h1(h1({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),hJ(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=h1(h1({},s),{},{isShow:!0}))}for(var h=i?c-1:c,d=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=h1(h1({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=h1(h1({},i),{},{tickCoord:i.coordinate});hJ(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=h1(h1({},i),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(w,j,x,l,f,"preserveStartEnd"===h):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=h1(h1({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=h1(h1({},l),{},{tickCoord:l.coordinate});hJ(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=h1(h1({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(w,j,x,l,f)).filter(function(t){return t.isShow})}hG(hY,"displayName","Line"),hG(hY,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!tY.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),hG(hY,"getComposedData",function(t){var e=t.props,r=t.xAxis,n=t.yAxis,o=t.xAxisTicks,i=t.yAxisTicks,a=t.dataKey,c=t.bandSize,u=t.displayedData,l=t.offset,s=e.layout;return hF({points:u.map(function(t,e){var u=uY(t,a);return"horizontal"===s?{x:ll({axis:r,ticks:o,bandSize:c,entry:t,index:e}),y:N()(u)?null:n.scale(u),value:u,payload:t}:{x:N()(u)?null:r.scale(u),y:ll({axis:n,ticks:i,bandSize:c,entry:t,index:e}),value:u,payload:t}}),layout:s},l)});var h3=["viewBox"],h6=["viewBox"],h5=["ticks"];function h4(t){return(h4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function h7(){return(h7=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h8(Object(r),!0).forEach(function(e){da(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dt(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function de(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dc(n.key),n)}}function dr(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dr=function(){return!!t})()}function dn(t){return(dn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function di(t,e){return(di=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function da(t,e,r){return(e=dc(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dc(t){var e=function(t,e){if("object"!=h4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h4(e)?e:e+""}var du=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=dn(r),(e=function(t,e){if(e&&("object"===h4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,dr()?Reflect.construct(r,o||[],dn(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&di(t,e)}(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=dt(t,h3),o=this.props,i=o.viewBox,a=dt(o,h6);return!z(r,i)||!z(n,a)||!z(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,b=O(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!d*f)-v*m)-v*y,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+ +!d*s)-v*m)-v*y,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+ +d*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+ +d*f)+v*m)+v*y,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.orientation,c=t.mirror,l=t.axisLine,s=h9(h9(h9({},tc(this.props,!1)),tc(l,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var f=+("top"===i&&!c||"bottom"===i&&c);s=h9(h9({},s),{},{x1:e,y1:r+f*o,x2:e+n,y2:r+f*o})}else{var p=+("left"===i&&!c||"right"===i&&c);s=h9(h9({},s),{},{x1:e+p*n,y1:r,x2:e+p*n,y2:r+o})}return a().createElement("line",h7({},s,{className:u("recharts-cartesian-axis-line",v()(l,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,i=this.props,c=i.tickLine,l=i.stroke,s=i.tick,f=i.tickFormatter,p=i.unit,h=h2(h9(h9({},this.props),{},{ticks:t}),e,r),d=this.getTickTextAnchor(),y=this.getTickVerticalAnchor(),m=tc(this.props,!1),b=tc(s,!1),g=h9(h9({},m),{},{fill:"none"},tc(c,!1)),x=h.map(function(t,e){var r=o.getTickLineCoord(t),i=r.line,x=r.tick,O=h9(h9(h9(h9({textAnchor:d,verticalAnchor:y},m),{},{stroke:"none",fill:l},b),x),{},{index:e,payload:t,visibleTicksCount:h.length,tickFormatter:f});return a().createElement(tk,h7({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},H(o.props,t,e)),c&&a().createElement("line",h7({},g,i,{className:u("recharts-cartesian-axis-tick-line",v()(c,"className"))})),s&&n.renderTickItem(s,O,"".concat(I()(f)?f(t.value,e):t.value).concat(p||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},x)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,i=e.ticksGenerator,c=e.className;if(e.hide)return null;var l=this.props,s=l.ticks,f=dt(l,h5),p=s;return(I()(i)&&(p=i(s&&s.length>0?this.props:f)),n<=0||o<=0||!p||!p.length)?null:a().createElement(tk,{className:u("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(p,this.state.fontSize,this.state.letterSpacing),l8.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){return a().isValidElement(t)?a().cloneElement(t,e):I()(t)?t(e):a().createElement(n1,h7({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&de(n.prototype,e),r&&de(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.Component);function dl(t){return(dl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ds(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dy(n.key),n)}}function df(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(df=function(){return!!t})()}function dp(t){return(dp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dh(t,e){return(dh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dd(t,e,r){return(e=dy(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dy(t){var e=function(t,e){if("object"!=dl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dl(e)?e:e+""}function dv(){return(dv=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dm(t){var e=t.xAxisId,r=fV(),n=fG(),o=fW(e);return null==o?null:a().createElement(du,dv({},o,{className:u("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return u8(t,!0)}}))}da(du,"displayName","CartesianAxis"),da(du,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var db=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=dp(t),function(t,e){if(e&&("object"===dl(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,df()?Reflect.construct(t,e||[],dp(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&dh(t,e)}(n,t),e=[{key:"render",value:function(){return a().createElement(dm,this.props)}}],ds(n.prototype,e),r&&ds(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a().Component);function dg(t){return(dg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dx(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dP(n.key),n)}}function dO(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dO=function(){return!!t})()}function dw(t){return(dw=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dj(t,e){return(dj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dS(t,e,r){return(e=dP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dP(t){var e=function(t,e){if("object"!=dg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dg(e)?e:e+""}function dA(){return(dA=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}dd(db,"displayName","XAxis"),dd(db,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var dk=function(t){var e=t.yAxisId,r=fV(),n=fG(),o=fH(e);return null==o?null:a().createElement(du,dA({},o,{className:u("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return u8(t,!0)}}))},dE=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=dw(t),function(t,e){if(e&&("object"===dg(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,dO()?Reflect.construct(t,e||[],dw(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&dj(t,e)}(n,t),e=[{key:"render",value:function(){return a().createElement(dk,this.props)}}],dx(n.prototype,e),r&&dx(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(a().Component);dS(dE,"displayName","YAxis"),dS(dE,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var dT=hD({chartName:"LineChart",GraphicalChild:hY,axisComponents:[{axisType:"xAxis",AxisComp:db},{axisType:"yAxis",AxisComp:dE}],formatAxisMap:fh}),d_=["x1","y1","x2","y2","key"],dM=["offset"];function dC(t){return(dC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dN(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dD(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dN(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=dC(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dC(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dC(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dN(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dI(){return(dI=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dB(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var dR=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,o=t.y,i=t.width,c=t.height,u=t.ry;return a().createElement("rect",{x:n,y:o,ry:u,width:i,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function dL(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(I()(t))r=t(e);else{var n=e.x1,o=e.y1,i=e.x2,c=e.y2,u=e.key,l=tc(dB(e,d_),!1),s=(l.offset,dB(l,dM));r=a().createElement("line",dI({},s,{x1:n,y1:o,x2:i,y2:c,fill:"none",key:u}))}return r}function dz(t){var e=t.x,r=t.width,n=t.horizontal,o=void 0===n||n,i=t.horizontalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return dL(o,dD(dD({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function dU(t){var e=t.y,r=t.height,n=t.vertical,o=void 0===n||n,i=t.verticalPoints;if(!o||!i||!i.length)return null;var c=i.map(function(n,i){return dL(o,dD(dD({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(i),index:i}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function dF(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,o=t.y,i=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=s[u+1]?s[u+1]-t:o+c-t;if(l<=0)return null;var f=u%e.length;return a().createElement("rect",{key:"react-".concat(u),y:t,x:n,height:l,width:i,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function d$(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,o=t.x,i=t.y,c=t.width,u=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+o-o)}).sort(function(t,e){return t-e});o!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:o+c-t;if(l<=0)return null;var f=e%r.length;return a().createElement("rect",{key:"react-".concat(e),x:t,y:i,width:l,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var dq=function(t,e){var r=t.xAxis,n=t.width,o=t.height,i=t.offset;return u7(h2(dD(dD(dD({},du.defaultProps),r),{},{ticks:u8(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.left,i.left+i.width,e)},dW=function(t,e){var r=t.yAxis,n=t.width,o=t.height,i=t.offset;return u7(h2(dD(dD(dD({},du.defaultProps),r),{},{ticks:u8(r,!0),viewBox:{x:0,y:0,width:n,height:o}})),i.top,i.top+i.height,e)},dX={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function dH(t){var e,r,n,o,c,u,l=fV(),s=fG(),f=(0,i.useContext)(fz),p=dD(dD({},t),{},{stroke:null!==(e=t.stroke)&&void 0!==e?e:dX.stroke,fill:null!==(r=t.fill)&&void 0!==r?r:dX.fill,horizontal:null!==(n=t.horizontal)&&void 0!==n?n:dX.horizontal,horizontalFill:null!==(o=t.horizontalFill)&&void 0!==o?o:dX.horizontalFill,vertical:null!==(c=t.vertical)&&void 0!==c?c:dX.vertical,verticalFill:null!==(u=t.verticalFill)&&void 0!==u?u:dX.verticalFill,x:O(t.x)?t.x:f.left,y:O(t.y)?t.y:f.top,width:O(t.width)?t.width:f.width,height:O(t.height)?t.height:f.height}),h=p.x,d=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=A((0,i.useContext)(fB)),w=fX();if(!O(y)||y<=0||!O(v)||v<=0||!O(h)||h!==+h||!O(d)||d!==+d)return null;var j=p.verticalCoordinatesGenerator||dq,S=p.horizontalCoordinatesGenerator||dW,P=p.horizontalPoints,k=p.verticalPoints;if((!P||!P.length)&&I()(S)){var E=b&&b.length,T=S({yAxis:w?dD(dD({},w),{},{ticks:E?b:w.ticks}):void 0,width:l,height:s,offset:f},!!E||m);M(Array.isArray(T),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(dC(T),"]")),Array.isArray(T)&&(P=T)}if((!k||!k.length)&&I()(j)){var _=g&&g.length,C=j({xAxis:x?dD(dD({},x),{},{ticks:_?g:x.ticks}):void 0,width:l,height:s,offset:f},!!_||m);M(Array.isArray(C),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(dC(C),"]")),Array.isArray(C)&&(k=C)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(dR,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(dz,dI({},p,{offset:f,horizontalPoints:P,xAxis:x,yAxis:w})),a().createElement(dU,dI({},p,{offset:f,verticalPoints:k,xAxis:x,yAxis:w})),a().createElement(dF,dI({},p,{horizontalPoints:P})),a().createElement(d$,dI({},p,{verticalPoints:k})))}dH.displayName="CartesianGrid";var dV=hD({chartName:"BarChart",GraphicalChild:fa,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:db},{axisType:"yAxis",AxisComp:dE}],formatAxisMap:fh}),dG=["points","className","baseLinePoints","connectNulls"];function dK(){return(dK=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dY(t){return function(t){if(Array.isArray(t))return dZ(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return dZ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dZ(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dZ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var dJ=function(t){return t&&t.x===+t.x&&t.y===+t.y},dQ=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=[[]];return t.forEach(function(t){dJ(t)?e[e.length-1].push(t):e[e.length-1].length>0&&e.push([])}),dJ(t[0])&&e[e.length-1].push(t[0]),e[e.length-1].length<=0&&(e=e.slice(0,-1)),e},d0=function(t,e){var r=dQ(t);e&&(r=[r.reduce(function(t,e){return[].concat(dY(t),dY(e))},[])]);var n=r.map(function(t){return t.reduce(function(t,e,r){return"".concat(t).concat(0===r?"M":"L").concat(e.x,",").concat(e.y)},"")}).join("");return 1===r.length?"".concat(n,"Z"):n},d1=function(t,e,r){var n=d0(t,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(d0(e.reverse(),r).slice(1))},d2=function(t){var e=t.points,r=t.className,n=t.baseLinePoints,o=t.connectNulls,i=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,dG);if(!e||!e.length)return null;var c=u("recharts-polygon",r);if(n&&n.length){var l=i.stroke&&"none"!==i.stroke,s=d1(e,n,o);return a().createElement("g",{className:c},a().createElement("path",dK({},tc(i,!0),{fill:"Z"===s.slice(-1)?i.fill:"none",stroke:"none",d:s})),l?a().createElement("path",dK({},tc(i,!0),{fill:"none",d:d0(e,o)})):null,l?a().createElement("path",dK({},tc(i,!0),{fill:"none",d:d0(n,o)})):null)}var f=d0(e,o);return a().createElement("path",dK({},tc(i,!0),{fill:"Z"===f.slice(-1)?i.fill:"none",className:c,d:f}))};function d3(t){return(d3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d6(){return(d6=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d4(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d5(Object(r),!0).forEach(function(e){ye(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d7(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yr(n.key),n)}}function d8(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d8=function(){return!!t})()}function d9(t){return(d9=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function yt(t,e){return(yt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ye(t,e,r){return(e=yr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yr(t){var e=function(t,e){if("object"!=d3(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d3(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d3(e)?e:e+""}var yn=Math.PI/180,yo=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=d9(t),function(t,e){if(e&&("object"===d3(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,d8()?Reflect.construct(t,e||[],d9(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&yt(t,e)}(n,t),e=[{key:"getTickLineCoord",value:function(t){var e=this.props,r=e.cx,n=e.cy,o=e.radius,i=e.orientation,a=e.tickSize,c=lX(r,n,o,t.coordinate),u=lX(r,n,o+("inner"===i?-1:1)*(a||8),t.coordinate);return{x1:c.x,y1:c.y,x2:u.x,y2:u.y}}},{key:"getTickTextAnchor",value:function(t){var e=this.props.orientation,r=Math.cos(-t.coordinate*yn);return r>1e-5?"outer"===e?"start":"end":r<-.00001?"outer"===e?"end":"start":"middle"}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.radius,o=t.axisLine,i=t.axisLineType,c=d4(d4({},tc(this.props,!1)),{},{fill:"none"},tc(o,!1));if("circle"===i)return a().createElement(e3,d6({className:"recharts-polar-angle-axis-line"},c,{cx:e,cy:r,r:n}));var u=this.props.ticks.map(function(t){return lX(e,r,n,t.coordinate)});return a().createElement(d2,d6({className:"recharts-polar-angle-axis-line"},c,{points:u}))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,o=e.tick,i=e.tickLine,c=e.tickFormatter,l=e.stroke,s=tc(this.props,!1),f=tc(o,!1),p=d4(d4({},s),{},{fill:"none"},tc(i,!1)),h=r.map(function(e,r){var h=t.getTickLineCoord(e),d=d4(d4(d4({textAnchor:t.getTickTextAnchor(e)},s),{},{stroke:"none",fill:l},f),{},{index:r,payload:e,x:h.x2,y:h.y2});return a().createElement(tk,d6({className:u("recharts-polar-angle-axis-tick",lZ(o)),key:"tick-".concat(e.coordinate)},H(t.props,e,r)),i&&a().createElement("line",d6({className:"recharts-polar-angle-axis-tick-line"},p,h)),o&&n.renderTickItem(o,d,c?c(e.value,r):e.value))});return a().createElement(tk,{className:"recharts-polar-angle-axis-ticks"},h)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.radius,n=t.axisLine;return!(r<=0)&&e&&e.length?a().createElement(tk,{className:u("recharts-polar-angle-axis",this.props.className)},n&&this.renderAxisLine(),this.renderTicks()):null}}],r=[{key:"renderTickItem",value:function(t,e,r){return a().isValidElement(t)?a().cloneElement(t,e):I()(t)?t(e):a().createElement(n1,d6({},e,{className:"recharts-polar-angle-axis-tick-value"}),r)}}],e&&d7(n.prototype,e),r&&d7(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);ye(yo,"displayName","PolarAngleAxis"),ye(yo,"axisType","angleAxis"),ye(yo,"defaultProps",{type:"category",angleAxisId:0,scale:"auto",cx:0,cy:0,orientation:"outer",axisLine:!0,tickLine:!0,tickSize:8,tick:!0,hide:!1,allowDuplicatedCategory:!0});var yi=r(77333),ya=r.n(yi),yc=r(67568),yu=r.n(yc),yl=["cx","cy","angle","ticks","axisLine"],ys=["ticks","tick","angle","tickFormatter","stroke"];function yf(t){return(yf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yp(){return(yp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function yh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function yd(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yh(Object(r),!0).forEach(function(e){yx(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yh(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function yy(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function yv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yO(n.key),n)}}function ym(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ym=function(){return!!t})()}function yb(t){return(yb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function yg(t,e){return(yg=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function yx(t,e,r){return(e=yO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yO(t){var e=function(t,e){if("object"!=yf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=yf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==yf(e)?e:e+""}var yw=function(t){var e,r;function n(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),t=n,e=arguments,t=yb(t),function(t,e){if(e&&("object"===yf(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ym()?Reflect.construct(t,e||[],yb(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&yg(t,e)}(n,t),e=[{key:"getTickValueCoord",value:function(t){var e=t.coordinate,r=this.props,n=r.angle;return lX(r.cx,r.cy,e,n)}},{key:"getTickTextAnchor",value:function(){var t;switch(this.props.orientation){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t}},{key:"getViewBox",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=ya()(o,function(t){return t.coordinate||0});return{cx:e,cy:r,startAngle:n,endAngle:n,innerRadius:yu()(o,function(t){return t.coordinate||0}).coordinate||0,outerRadius:i.coordinate||0}}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.cx,r=t.cy,n=t.angle,o=t.ticks,i=t.axisLine,c=yy(t,yl),u=o.reduce(function(t,e){return[Math.min(t[0],e.coordinate),Math.max(t[1],e.coordinate)]},[1/0,-1/0]),l=lX(e,r,u[0],n),s=lX(e,r,u[1],n),f=yd(yd(yd({},tc(c,!1)),{},{fill:"none"},tc(i,!1)),{},{x1:l.x,y1:l.y,x2:s.x,y2:s.y});return a().createElement("line",yp({className:"recharts-polar-radius-axis-line"},f))}},{key:"renderTicks",value:function(){var t=this,e=this.props,r=e.ticks,o=e.tick,i=e.angle,c=e.tickFormatter,l=e.stroke,s=yy(e,ys),f=this.getTickTextAnchor(),p=tc(s,!1),h=tc(o,!1),d=r.map(function(e,r){var s=t.getTickValueCoord(e),d=yd(yd(yd(yd({textAnchor:f,transform:"rotate(".concat(90-i,", ").concat(s.x,", ").concat(s.y,")")},p),{},{stroke:"none",fill:l},h),{},{index:r},s),{},{payload:e});return a().createElement(tk,yp({className:u("recharts-polar-radius-axis-tick",lZ(o)),key:"tick-".concat(e.coordinate)},H(t.props,e,r)),n.renderTickItem(o,d,c?c(e.value,r):e.value))});return a().createElement(tk,{className:"recharts-polar-radius-axis-ticks"},d)}},{key:"render",value:function(){var t=this.props,e=t.ticks,r=t.axisLine,n=t.tick;return e&&e.length?a().createElement(tk,{className:u("recharts-polar-radius-axis",this.props.className)},r&&this.renderAxisLine(),n&&this.renderTicks(),l8.renderCallByParent(this.props,this.getViewBox())):null}}],r=[{key:"renderTickItem",value:function(t,e,r){return a().isValidElement(t)?a().cloneElement(t,e):I()(t)?t(e):a().createElement(n1,yp({},e,{className:"recharts-polar-radius-axis-tick-value"}),r)}}],e&&yv(n.prototype,e),r&&yv(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);function yj(t){return(yj="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function yS(){return(yS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function yP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function yA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yP(Object(r),!0).forEach(function(e){yM(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function yk(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,yC(n.key),n)}}function yE(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(yE=function(){return!!t})()}function yT(t){return(yT=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function y_(t,e){return(y_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function yM(t,e,r){return(e=yC(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function yC(t){var e=function(t,e){if("object"!=yj(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=yj(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==yj(e)?e:e+""}yx(yw,"displayName","PolarRadiusAxis"),yx(yw,"axisType","radiusAxis"),yx(yw,"defaultProps",{type:"number",radiusAxisId:0,cx:0,cy:0,angle:0,orientation:"right",stroke:"#ccc",axisLine:!0,tick:!0,tickCount:5,allowDataOverflow:!1,scale:"auto",allowDuplicatedCategory:!0});var yN=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=yT(r),yM(e=function(t,e){if(e&&("object"===yj(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,yE()?Reflect.construct(r,o||[],yT(this).constructor):r.apply(this,o)),"pieRef",null),yM(e,"sectorRefs",[]),yM(e,"id",S("recharts-pie-")),yM(e,"handleAnimationEnd",function(){var t=e.props.onAnimationEnd;e.setState({isAnimationFinished:!0}),I()(t)&&t()}),yM(e,"handleAnimationStart",function(){var t=e.props.onAnimationStart;e.setState({isAnimationFinished:!1}),I()(t)&&t()}),e.state={isAnimationFinished:!t.isAnimationActive,prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,sectorToFocus:0},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&y_(t,e)}(n,t),e=[{key:"isActiveIndex",value:function(t){var e=this.props.activeIndex;return Array.isArray(e)?-1!==e.indexOf(t):t===e}},{key:"hasActiveIndex",value:function(){var t=this.props.activeIndex;return Array.isArray(t)?0!==t.length:t||0===t}},{key:"renderLabels",value:function(t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var e=this.props,r=e.label,o=e.labelLine,i=e.dataKey,c=e.valueKey,u=tc(this.props,!1),l=tc(r,!1),s=tc(o,!1),f=r&&r.offsetRadius||20,p=t.map(function(t,e){var p=(t.startAngle+t.endAngle)/2,h=lX(t.cx,t.cy,t.outerRadius+f,p),d=yA(yA(yA(yA({},u),t),{},{stroke:"none"},l),{},{index:e,textAnchor:n.getTextAnchor(h.x,t.cx)},h),y=yA(yA(yA(yA({},u),t),{},{fill:"none",stroke:t.fill},s),{},{index:e,points:[lX(t.cx,t.cy,t.outerRadius,p),h]}),v=i;return N()(i)&&N()(c)?v="value":N()(i)&&(v=c),a().createElement(tk,{key:"label-".concat(t.startAngle,"-").concat(t.endAngle,"-").concat(t.midAngle,"-").concat(e)},o&&n.renderLabelLineItem(o,y,"line"),n.renderLabelItem(r,d,uY(t,v)))});return a().createElement(tk,{className:"recharts-pie-labels"},p)}},{key:"renderSectorsStatically",value:function(t){var e=this,r=this.props,n=r.activeShape,o=r.blendStroke,i=r.inactiveShape;return t.map(function(r,c){if((null==r?void 0:r.startAngle)===0&&(null==r?void 0:r.endAngle)===0&&1!==t.length)return null;var u=e.isActiveIndex(c),l=i&&e.hasActiveIndex()?i:null,s=yA(yA({},r),{},{stroke:o?r.fill:r.stroke,tabIndex:-1});return a().createElement(tk,yS({ref:function(t){t&&!e.sectorRefs.includes(t)&&e.sectorRefs.push(t)},tabIndex:-1,className:"recharts-pie-sector"},H(e.props,r,c),{key:"sector-".concat(null==r?void 0:r.startAngle,"-").concat(null==r?void 0:r.endAngle,"-").concat(r.midAngle,"-").concat(c)}),a().createElement(sW,yS({option:u?n:l,isActive:u,shapeType:"sector"},s)))})}},{key:"renderSectorsWithAnimation",value:function(){var t=this,e=this.props,r=e.sectors,n=e.isAnimationActive,o=e.animationBegin,i=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state,s=l.prevSectors,f=l.prevIsAnimationActive;return a().createElement(nn,{begin:o,duration:i,isActive:n,easing:c,from:{t:0},to:{t:1},key:"pie-".concat(u,"-").concat(f),onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},function(e){var n=e.t,o=[],i=(r&&r[0]).startAngle;return r.forEach(function(t,e){var r=s&&s[e],a=e>0?v()(t,"paddingAngle",0):0;if(r){var c=E(r.endAngle-r.startAngle,t.endAngle-t.startAngle),u=yA(yA({},t),{},{startAngle:i+a,endAngle:i+c(n)+a});o.push(u),i=u.endAngle}else{var l=E(0,t.endAngle-t.startAngle)(n),f=yA(yA({},t),{},{startAngle:i+a,endAngle:i+l+a});o.push(f),i=f.endAngle}}),a().createElement(tk,null,t.renderSectorsStatically(o))})}},{key:"attachKeyboardHandlers",value:function(t){var e=this;t.onkeydown=function(t){if(!t.altKey)switch(t.key){case"ArrowLeft":var r=++e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[r].focus(),e.setState({sectorToFocus:r});break;case"ArrowRight":var n=--e.state.sectorToFocus<0?e.sectorRefs.length-1:e.state.sectorToFocus%e.sectorRefs.length;e.sectorRefs[n].focus(),e.setState({sectorToFocus:n});break;case"Escape":e.sectorRefs[e.state.sectorToFocus].blur(),e.setState({sectorToFocus:0})}}}},{key:"renderSectors",value:function(){var t=this.props,e=t.sectors,r=t.isAnimationActive,n=this.state.prevSectors;return r&&e&&e.length&&(!n||!cH()(n,e))?this.renderSectorsWithAnimation():this.renderSectorsStatically(e)}},{key:"componentDidMount",value:function(){this.pieRef&&this.attachKeyboardHandlers(this.pieRef)}},{key:"render",value:function(){var t=this,e=this.props,r=e.hide,n=e.sectors,o=e.className,i=e.label,c=e.cx,l=e.cy,s=e.innerRadius,f=e.outerRadius,p=e.isAnimationActive,h=this.state.isAnimationFinished;if(r||!n||!n.length||!O(c)||!O(l)||!O(s)||!O(f))return null;var d=u("recharts-pie",o);return a().createElement(tk,{tabIndex:this.props.rootTabIndex,className:d,ref:function(e){t.pieRef=e}},this.renderSectors(),i&&this.renderLabels(n),l8.renderCallByParent(this.props,null,!1),(!p||h)&&sm.renderCallByParent(this.props,n,!1))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return e.prevIsAnimationActive!==t.isAnimationActive?{prevIsAnimationActive:t.isAnimationActive,prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:[],isAnimationFinished:!0}:t.isAnimationActive&&t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curSectors:t.sectors,prevSectors:e.curSectors,isAnimationFinished:!0}:t.sectors!==e.curSectors?{curSectors:t.sectors,isAnimationFinished:!0}:null}},{key:"getTextAnchor",value:function(t,e){return t>e?"start":t<e?"end":"middle"}},{key:"renderLabelLineItem",value:function(t,e,r){if(a().isValidElement(t))return a().cloneElement(t,e);if(I()(t))return t(e);var n=u("recharts-pie-label-line","boolean"!=typeof t?t.className:"");return a().createElement(p0,yS({},e,{key:r,type:"linear",className:n}))}},{key:"renderLabelItem",value:function(t,e,r){if(a().isValidElement(t))return a().cloneElement(t,e);var n=r;if(I()(t)&&(n=t(e),a().isValidElement(n)))return n;var o=u("recharts-pie-label-text","boolean"==typeof t||I()(t)?"":t.className);return a().createElement(n1,yS({},e,{alignmentBaseline:"middle",className:o}),n)}}],e&&yk(n.prototype,e),r&&yk(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);yM(yN,"displayName","Pie"),yM(yN,"defaultProps",{stroke:"#fff",fill:"#808080",legendType:"rect",cx:"50%",cy:"50%",startAngle:0,endAngle:360,innerRadius:0,outerRadius:"80%",paddingAngle:0,labelLine:!0,hide:!1,minAngle:0,isAnimationActive:!tY.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",blendStroke:!1,rootTabIndex:0}),yM(yN,"parseDeltaAngle",function(t,e){return g(e-t)*Math.min(Math.abs(e-t),360)}),yM(yN,"getRealPieData",function(t){var e=t.data,r=t.children,n=tc(t,!1),o=tr(r,si);return e&&e.length?e.map(function(t,e){return yA(yA(yA({payload:t},n),t),o&&o[e]&&o[e].props)}):o&&o.length?o.map(function(t){return yA(yA({},n),t.props)}):[]}),yM(yN,"parseCoordinateOfPie",function(t,e){var r=e.top,n=e.left,o=e.width,i=e.height,a=lH(o,i);return{cx:n+P(t.cx,o,o/2),cy:r+P(t.cy,i,i/2),innerRadius:P(t.innerRadius,a,0),outerRadius:P(t.outerRadius,a,.8*a),maxRadius:t.maxRadius||Math.sqrt(o*o+i*i)/2}}),yM(yN,"getComposedData",function(t){var e,r,n=t.item,o=t.offset,i=void 0!==n.type.defaultProps?yA(yA({},n.type.defaultProps),n.props):n.props,a=yN.getRealPieData(i);if(!a||!a.length)return null;var c=i.cornerRadius,u=i.startAngle,l=i.endAngle,s=i.paddingAngle,f=i.dataKey,p=i.nameKey,h=i.valueKey,d=i.tooltipType,y=Math.abs(i.minAngle),v=yN.parseCoordinateOfPie(i,o),m=yN.parseDeltaAngle(u,l),b=Math.abs(m),x=f;N()(f)&&N()(h)?(M(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),x="value"):N()(f)&&(M(!1,'Use "dataKey" to specify the value of pie,\n      the props "valueKey" will be deprecated in 1.1.0'),x=h);var w=a.filter(function(t){return 0!==uY(t,x,0)}).length,j=b-w*y-(b>=360?w:w-1)*s,S=a.reduce(function(t,e){var r=uY(e,x,0);return t+(O(r)?r:0)},0);return S>0&&(e=a.map(function(t,e){var n,o=uY(t,x,0),i=uY(t,p,e),a=(O(o)?o:0)/S,l=(n=e?r.endAngle+g(m)*s*(0!==o?1:0):u)+g(m)*((0!==o?y:0)+a*j),f=(n+l)/2,h=(v.innerRadius+v.outerRadius)/2,b=[{name:i,value:o,payload:t,dataKey:x,type:d}],w=lX(v.cx,v.cy,h,f);return r=yA(yA(yA({percent:a,cornerRadius:c,name:i,tooltipPayload:b,midAngle:f,middleRadius:h,tooltipPosition:w},t),v),{},{value:uY(t,x),startAngle:n,endAngle:l,payload:t,paddingAngle:g(m)*s})})),yA(yA({},v),{},{sectors:e,data:a})});var yD=hD({chartName:"PieChart",GraphicalChild:yN,validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",legendContent:"children",axisComponents:[{axisType:"angleAxis",AxisComp:yo},{axisType:"radiusAxis",AxisComp:yw}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,c=t.startAngle,u=t.endAngle,l=P(t.cx,i,i/2),s=P(t.cy,a,a/2),f=lH(i,a,r),p=P(t.innerRadius,f,0),h=P(t.outerRadius,f,.8*f);return Object.keys(e).reduce(function(t,r){var i,a=e[r],f=a.domain,d=a.reversed;if(N()(a.range))"angleAxis"===n?i=[c,u]:"radiusAxis"===n&&(i=[p,h]),d&&(i=[i[1],i[0]]);else{var y,v=function(t){if(Array.isArray(t))return t}(y=i=a.range)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(y,2)||function(t,e){if(t){if("string"==typeof t)return lq(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lq(t,e)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=v[0],u=v[1]}var m=le(a,o),b=m.realScaleType,g=m.scale;g.domain(f).range(i),lr(g);var x=lu(g,lF(lF({},a),{},{realScaleType:b})),O=lF(lF(lF({},a),x),{},{range:i,radius:h,realScaleType:b,scale:g,cx:l,cy:s,innerRadius:p,outerRadius:h,startAngle:c,endAngle:u});return lF(lF({},t),{},l$({},r,O))},{})},defaultProps:{layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"}});let yI=({title:t,value:e,icon:r,change:n,isPositive:i})=>o.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,o.jsxs)("div",{className:"flex justify-between items-start",children:[(0,o.jsxs)("div",{children:[o.jsx("p",{className:"text-gray-500 text-sm",children:t}),o.jsx("h3",{className:"text-2xl font-bold mt-1",children:e}),n&&(0,o.jsxs)("p",{className:`text-sm mt-2 flex items-center ${i?"text-green-500":"text-red-500"}`,children:[o.jsx(c.QqI,{className:`mr-1 ${i?"":"transform rotate-180"}`}),n," ",i?"增长":"下降"]})]}),o.jsx("div",{className:"p-3 bg-primary-50 rounded-full",children:r})]})}),yB=[{name:"1月",访问量:4e3},{name:"2月",访问量:3e3},{name:"3月",访问量:2e3},{name:"4月",访问量:2780},{name:"5月",访问量:1890},{name:"6月",访问量:2390},{name:"7月",访问量:3490},{name:"8月",访问量:4200},{name:"9月",访问量:5e3},{name:"10月",访问量:4300},{name:"11月",访问量:4800},{name:"12月",访问量:5200}],yR=[{name:"1月",咨询量:400},{name:"2月",咨询量:300},{name:"3月",咨询量:200},{name:"4月",咨询量:278},{name:"5月",咨询量:189},{name:"6月",咨询量:239},{name:"7月",咨询量:349},{name:"8月",咨询量:420},{name:"9月",咨询量:500},{name:"10月",咨询量:430},{name:"11月",咨询量:480},{name:"12月",咨询量:520}],yL=[{name:"留学申请",value:40},{name:"考研保研",value:30},{name:"职业规划",value:20},{name:"职业转型",value:10}],yz=["#0088FE","#00C49F","#FFBB28","#FF8042"],yU=[{id:1,name:"张先生",service:"留学申请",date:"2023-12-01",status:"待回复"},{id:2,name:"李女士",service:"职业规划",date:"2023-11-30",status:"已回复"},{id:3,name:"王同学",service:"考研保研",date:"2023-11-29",status:"已回复"},{id:4,name:"赵女士",service:"职业转型",date:"2023-11-28",status:"已回复"},{id:5,name:"刘同学",service:"留学申请",date:"2023-11-27",status:"待回复"}];function yF(){let[t,e]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{e(!0)},[]),(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsxs)("div",{className:"mb-6",children:[o.jsx("h1",{className:"text-2xl font-bold",children:"仪表盘"}),o.jsx("p",{className:"text-gray-600",children:"欢迎回来，查看网站的最新数据和统计信息"})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[o.jsx(yI,{title:"总访问量",value:"24,532",icon:o.jsx(c.rDJ,{className:"text-primary-600 text-xl"}),change:"12%",isPositive:!0}),o.jsx(yI,{title:"咨询数量",value:"1,423",icon:o.jsx(c.IC0,{className:"text-primary-600 text-xl"}),change:"8%",isPositive:!0}),o.jsx(yI,{title:"文章数量",value:"48",icon:o.jsx(c.NOg,{className:"text-primary-600 text-xl"}),change:"5%",isPositive:!0}),o.jsx(yI,{title:"注册用户",value:"256",icon:o.jsx(c.WY8,{className:"text-primary-600 text-xl"}),change:"15%",isPositive:!0})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[o.jsx("h2",{className:"text-lg font-medium mb-4",children:"访问量趋势"}),t&&o.jsx("div",{className:"h-80",children:o.jsx(tv,{width:"100%",height:"100%",children:(0,o.jsxs)(dT,{data:yB,margin:{top:5,right:30,left:20,bottom:5},children:[o.jsx(dH,{strokeDasharray:"3 3"}),o.jsx(db,{dataKey:"name"}),o.jsx(dE,{}),o.jsx(et,{}),o.jsx(hY,{type:"monotone",dataKey:"访问量",stroke:"#8884d8",activeDot:{r:8}})]})})})]}),(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[o.jsx("h2",{className:"text-lg font-medium mb-4",children:"咨询量趋势"}),t&&o.jsx("div",{className:"h-80",children:o.jsx(tv,{width:"100%",height:"100%",children:(0,o.jsxs)(dV,{data:yR,margin:{top:5,right:30,left:20,bottom:5},children:[o.jsx(dH,{strokeDasharray:"3 3"}),o.jsx(db,{dataKey:"name"}),o.jsx(dE,{}),o.jsx(et,{}),o.jsx(fa,{dataKey:"咨询量",fill:"#82ca9d"})]})})})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[o.jsx("h2",{className:"text-lg font-medium mb-4",children:"服务咨询分布"}),t&&o.jsx("div",{className:"h-64",children:o.jsx(tv,{width:"100%",height:"100%",children:(0,o.jsxs)(yD,{children:[o.jsx(yN,{data:yL,cx:"50%",cy:"50%",labelLine:!1,outerRadius:80,fill:"#8884d8",dataKey:"value",label:({name:t,percent:e})=>`${t} ${(100*e).toFixed(0)}%`,children:yL.map((t,e)=>o.jsx(si,{fill:yz[e%yz.length]},`cell-${e}`))}),o.jsx(et,{})]})})})]}),(0,o.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 lg:col-span-2",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[o.jsx("h2",{className:"text-lg font-medium",children:"最近咨询"}),o.jsx("button",{className:"text-primary-600 text-sm hover:underline",children:"查看全部"})]}),o.jsx("div",{className:"overflow-x-auto",children:(0,o.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[o.jsx("thead",{className:"bg-gray-50",children:(0,o.jsxs)("tr",{children:[o.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"咨询人"}),o.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"服务类型"}),o.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"日期"}),o.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"})]})}),o.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:yU.map(t=>(0,o.jsxs)("tr",{className:"hover:bg-gray-50",children:[o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t.name}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.service}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,o.jsxs)("div",{className:"flex items-center",children:[o.jsx(c.vlc,{className:"mr-2 text-gray-400"}),t.date]})}),o.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm",children:o.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"待回复"===t.status?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:t.status})})]},t.id))})]})})]})]})]})}},67329:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>u});var n=r(95344);r(3729),r(4047);var o=r(99847),i=r(44669),a=r(22254);function c({children:t}){let{user:e,logout:r,isAuthenticated:i,loading:c}=(0,o.a)(),u=(0,a.usePathname)();return"/login"===u?n.jsx(n.Fragment,{children:t}):c?n.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:n.jsx("div",{className:"text-lg",children:"加载中..."})}):i?(0,n.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[n.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[n.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsxs)("span",{className:"text-sm",children:["欢迎，",e?.name]}),n.jsx("button",{onClick:r,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,n.jsxs)("div",{className:"flex pt-16",children:[n.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,n.jsxs)("ul",{className:"space-y-2 mt-4",children:[n.jsx("li",{children:n.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===u?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),n.jsx("li",{children:n.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),n.jsx("li",{children:n.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),n.jsx("li",{children:n.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),n.jsx("li",{children:n.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),n.jsx("li",{children:n.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),n.jsx("li",{children:n.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),n.jsx("li",{children:n.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),n.jsx("li",{children:n.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),n.jsx("li",{children:n.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),n.jsx("li",{children:n.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),n.jsx("li",{children:n.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${u.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),n.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:t})]})]}):n.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,n.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[n.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),n.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),n.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function u({children:t}){return n.jsx("html",{lang:"zh",children:n.jsx("body",{children:(0,n.jsxs)(o.H,{children:[n.jsx(c,{children:t}),n.jsx(i.x7,{position:"top-right"})]})})})}},43932:(t,e,r)=>{"use strict";r.d(e,{Z:()=>o,h:()=>n});let n=r(47665).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});n.interceptors.request.use(t=>t,t=>Promise.reject(t)),n.interceptors.response.use(t=>t,t=>(t.response&&t.response.status,Promise.reject(t)));let o=n},14149:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},22392:(t,e,r)=>{var n=r(84032)(r(67598),"DataView");t.exports=n},75216:(t,e,r)=>{var n=r(67931),o=r(48514),i=r(79633),a=r(6125),c=r(78410);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},32230:(t,e,r)=>{var n=r(31547),o=r(30543),i=r(75046),a=r(45341),c=r(93205);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},67597:(t,e,r)=>{var n=r(84032)(r(67598),"Map");t.exports=n},67830:(t,e,r)=>{var n=r(61609),o=r(44135),i=r(52460),a=r(76269),c=r(81762);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},89840:(t,e,r)=>{var n=r(84032)(r(67598),"Promise");t.exports=n},27542:(t,e,r)=>{var n=r(84032)(r(67598),"Set");t.exports=n},35935:(t,e,r)=>{var n=r(67830),o=r(34666),i=r(68760);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},92369:(t,e,r)=>{var n=r(32230),o=r(36366),i=r(53102),a=r(4406),c=r(63311),u=r(46215);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},46377:(t,e,r)=>{var n=r(67598).Symbol;t.exports=n},34728:(t,e,r)=>{var n=r(67598).Uint8Array;t.exports=n},2082:(t,e,r)=>{var n=r(84032)(r(67598),"WeakMap");t.exports=n},90717:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},62303:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},85376:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},43254:(t,e,r)=>{var n=r(39727);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},80265:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},63612:(t,e,r)=>{var n=r(17385),o=r(55095),i=r(71166),a=r(84458),c=r(44570),u=r(55789),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},65068:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},74409:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},62976:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},87258:t=>{t.exports=function(t){return t.split("")}},12708:(t,e,r)=>{var n=r(22249);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},50421:(t,e,r)=>{var n=r(11248);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},59064:(t,e,r)=>{var n=r(17210),o=r(65470)(n);t.exports=o},33815:(t,e,r)=>{var n=r(59064);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},7369:(t,e,r)=>{var n=r(56381);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},35241:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},2572:(t,e,r)=>{var n=r(74409),o=r(12183);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},69077:(t,e,r)=>{var n=r(80792)();t.exports=n},17210:(t,e,r)=>{var n=r(69077),o=r(1290);t.exports=function(t,e){return t&&n(t,e,o)}},35915:(t,e,r)=>{var n=r(96601),o=r(14711);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},30380:(t,e,r)=>{var n=r(74409),o=r(71166);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},65945:(t,e,r)=>{var n=r(46377),o=r(51370),i=r(33517),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},20222:t=>{t.exports=function(t,e){return t>e}},91414:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},39727:(t,e,r)=>{var n=r(35241),o=r(17501),i=r(73048);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},10534:(t,e,r)=>{var n=r(65945),o=r(12111);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},29511:(t,e,r)=>{var n=r(3049),o=r(12111);t.exports=function t(e,r,i,a,c){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,c):e!=e&&r!=r)}},3049:(t,e,r)=>{var n=r(92369),o=r(28053),i=r(65926),a=r(43136),c=r(50752),u=r(71166),l=r(84458),s=r(55789),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),O=g?p:c(e);x=x==f?h:x,O=O==f?h:O;var w=x==h,j=O==h,S=x==O;if(S&&l(t)){if(!l(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var P=w&&d.call(t,"__wrapped__"),A=j&&d.call(e,"__wrapped__");if(P||A){var k=P?t.value():t,E=A?e.value():e;return m||(m=new n),v(k,E,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},94828:(t,e,r)=>{var n=r(92369),o=r(29511);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(i)var d=i(f,p,s,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},17501:t=>{t.exports=function(t){return t!=t}},98471:(t,e,r)=>{var n=r(51282),o=r(33297),i=r(60986),a=r(13318),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:c).test(a(t))}},60077:(t,e,r)=>{var n=r(65945),o=r(19723),i=r(12111),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},69373:(t,e,r)=>{var n=r(10950),o=r(33002),i=r(87517),a=r(71166),c=r(14906);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},3425:(t,e,r)=>{var n=r(45782),o=r(35793),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},1479:t=>{t.exports=function(t,e){return t<e}},72466:(t,e,r)=>{var n=r(59064),o=r(40567);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},10950:(t,e,r)=>{var n=r(94828),o=r(39257),i=r(30273);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},33002:(t,e,r)=>{var n=r(29511),o=r(41365),i=r(95600),a=r(997),c=r(33954),u=r(30273),l=r(14711);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},55335:(t,e,r)=>{var n=r(65068),o=r(35915),i=r(69373),a=r(72466),c=r(66065),u=r(42454),l=r(38824),s=r(87517),f=r(71166);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(i)),c(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},97787:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},67242:(t,e,r)=>{var n=r(35915);t.exports=function(t){return function(e){return n(e,t)}}},77728:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},85590:(t,e,r)=>{var n=r(87517),o=r(71194),i=r(43394);t.exports=function(t,e){return i(o(t,e,n),t+"")}},36984:(t,e,r)=>{var n=r(56853),o=r(11248),i=r(87517),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i;t.exports=a},26784:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},35010:(t,e,r)=>{var n=r(59064);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},66065:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},17385:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},46584:(t,e,r)=>{var n=r(46377),o=r(65068),i=r(71166),a=r(56381),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},62894:(t,e,r)=>{var n=r(69575),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},42454:t=>{t.exports=function(t){return function(e){return t(e)}}},62634:(t,e,r)=>{var n=r(35935),o=r(43254),i=r(80265),a=r(66173),c=r(86513),u=r(80185);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},66173:t=>{t.exports=function(t,e){return t.has(e)}},96601:(t,e,r)=>{var n=r(71166),o=r(997),i=r(31110),a=r(64294);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},60706:(t,e,r)=>{var n=r(26784);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},83214:(t,e,r)=>{var n=r(56381);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return -1}return 0}},38824:(t,e,r)=>{var n=r(83214);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l){if(o>=u)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},88640:(t,e,r)=>{var n=r(67598)["__core-js_shared__"];t.exports=n},65470:(t,e,r)=>{var n=r(40567);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},80792:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},88053:(t,e,r)=>{var n=r(60706),o=r(20794),i=r(66503),a=r(64294);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},31572:(t,e,r)=>{var n=r(69373),o=r(40567),i=r(1290);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},29560:(t,e,r)=>{var n=r(77728),o=r(7647),i=r(79964);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},86513:(t,e,r)=>{var n=r(27542),o=r(25296),i=r(80185),a=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o;t.exports=a},11248:(t,e,r)=>{var n=r(84032),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},28053:(t,e,r)=>{var n=r(35935),o=r(62976),i=r(66173);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},65926:(t,e,r)=>{var n=r(46377),o=r(34728),i=r(22249),a=r(28053),c=r(69299),u=r(80185),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},43136:(t,e,r)=>{var n=r(65397),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return c.delete(t),c.delete(e),y}},22335:t=>{var e="object"==typeof global&&global&&global.Object===Object&&global;t.exports=e},65397:(t,e,r)=>{var n=r(30380),o=r(53420),i=r(1290);t.exports=function(t){return n(t,i,o)}},41373:(t,e,r)=>{var n=r(22157);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},39257:(t,e,r)=>{var n=r(33954),o=r(1290);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},84032:(t,e,r)=>{var n=r(98471),o=r(87868);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},38167:(t,e,r)=>{var n=r(1600)(Object.getPrototypeOf,Object);t.exports=n},51370:(t,e,r)=>{var n=r(46377),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},53420:(t,e,r)=>{var n=r(85376),o=r(59276),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,c=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o;t.exports=c},50752:(t,e,r)=>{var n=r(22392),o=r(67597),i=r(89840),a=r(27542),c=r(2082),u=r(65945),l=r(13318),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},87868:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},8854:(t,e,r)=>{var n=r(96601),o=r(55095),i=r(71166),a=r(44570),c=r(19723),u=r(14711);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},20794:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},67931:(t,e,r)=>{var n=r(65714);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},48514:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},79633:(t,e,r)=>{var n=r(65714),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},6125:(t,e,r)=>{var n=r(65714),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},78410:(t,e,r)=>{var n=r(65714);t.exports=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},12183:(t,e,r)=>{var n=r(46377),o=r(55095),i=r(71166),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},44570:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},7647:(t,e,r)=>{var n=r(22249),o=r(40567),i=r(44570),a=r(60986);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(o(r)&&i(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},997:(t,e,r)=>{var n=r(71166),o=r(56381),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},22157:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},33297:(t,e,r)=>{var n=r(88640),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},45782:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},33954:(t,e,r)=>{var n=r(60986);t.exports=function(t){return t==t&&!n(t)}},31547:t=>{t.exports=function(){this.__data__=[],this.size=0}},30543:(t,e,r)=>{var n=r(12708),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},75046:(t,e,r)=>{var n=r(12708);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},45341:(t,e,r)=>{var n=r(12708);t.exports=function(t){return n(this.__data__,t)>-1}},93205:(t,e,r)=>{var n=r(12708);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},61609:(t,e,r)=>{var n=r(75216),o=r(32230),i=r(67597);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},44135:(t,e,r)=>{var n=r(41373);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},52460:(t,e,r)=>{var n=r(41373);t.exports=function(t){return n(this,t).get(t)}},76269:(t,e,r)=>{var n=r(41373);t.exports=function(t){return n(this,t).has(t)}},81762:(t,e,r)=>{var n=r(41373);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this}},69299:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},30273:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},53207:(t,e,r)=>{var n=r(84722);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},65714:(t,e,r)=>{var n=r(84032)(Object,"create");t.exports=n},35793:(t,e,r)=>{var n=r(1600)(Object.keys,Object);t.exports=n},63203:(t,e,r)=>{t=r.nmd(t);var n=r(22335),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},33517:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},1600:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},71194:(t,e,r)=>{var n=r(90717),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},67598:(t,e,r)=>{var n=r(22335),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},34666:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},68760:t=>{t.exports=function(t){return this.__data__.has(t)}},80185:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},43394:(t,e,r)=>{var n=r(36984),o=r(60123)(n);t.exports=o},60123:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},36366:(t,e,r)=>{var n=r(32230);t.exports=function(){this.__data__=new n,this.size=0}},53102:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},4406:t=>{t.exports=function(t){return this.__data__.get(t)}},63311:t=>{t.exports=function(t){return this.__data__.has(t)}},46215:(t,e,r)=>{var n=r(32230),o=r(67597),i=r(67830);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},73048:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},66503:(t,e,r)=>{var n=r(87258),o=r(20794),i=r(6602);t.exports=function(t){return o(t)?i(t):n(t)}},31110:(t,e,r)=>{var n=r(53207),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e});t.exports=a},14711:(t,e,r)=>{var n=r(56381),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},13318:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},69575:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6602:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|")+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},56853:t=>{t.exports=function(t){return function(){return t}}},63908:(t,e,r)=>{var n=r(60986),o=r(120),i=r(29155),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-h,r=i-d,n=e-t,v?c(n,s-r):n))}function O(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function w(){var t,r=o(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},22249:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},28611:(t,e,r)=>{var n=r(62303),o=r(33815),i=r(69373),a=r(71166),c=r(7647);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},94784:(t,e,r)=>{var n=r(31572)(r(76361));t.exports=n},76361:(t,e,r)=>{var n=r(35241),o=r(69373),i=r(4296),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},40927:(t,e,r)=>{var n=r(2572),o=r(68114);t.exports=function(t,e){return n(o(t,e),1)}},41365:(t,e,r)=>{var n=r(35915);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},95600:(t,e,r)=>{var n=r(91414),o=r(8854);t.exports=function(t,e){return null!=t&&o(t,e,n)}},87517:t=>{t.exports=function(t){return t}},55095:(t,e,r)=>{var n=r(10534),o=r(12111),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable,u=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")};t.exports=u},71166:t=>{var e=Array.isArray;t.exports=e},40567:(t,e,r)=>{var n=r(51282),o=r(19723);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},84228:(t,e,r)=>{var n=r(65945),o=r(12111);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},84458:(t,e,r)=>{t=r.nmd(t);var n=r(67598),o=r(66012),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||o},1149:(t,e,r)=>{var n=r(29511);t.exports=function(t,e){return n(t,e)}},51282:(t,e,r)=>{var n=r(65945),o=r(60986);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},19723:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},44505:(t,e,r)=>{var n=r(73791);t.exports=function(t){return n(t)&&t!=+t}},56995:t=>{t.exports=function(t){return null==t}},73791:(t,e,r)=>{var n=r(65945),o=r(12111);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},60986:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},12111:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},24319:(t,e,r)=>{var n=r(65945),o=r(38167),i=r(12111),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},62055:(t,e,r)=>{var n=r(65945),o=r(71166),i=r(12111);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},56381:(t,e,r)=>{var n=r(65945),o=r(12111);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},55789:(t,e,r)=>{var n=r(60077),o=r(42454),i=r(63203),a=i&&i.isTypedArray,c=a?o(a):n;t.exports=c},1290:(t,e,r)=>{var n=r(63612),o=r(3425),i=r(40567);t.exports=function(t){return i(t)?n(t):o(t)}},10182:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},68114:(t,e,r)=>{var n=r(65068),o=r(69373),i=r(72466),a=r(71166);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},69167:(t,e,r)=>{var n=r(50421),o=r(17210),i=r(69373);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},55409:(t,e,r)=>{var n=r(7369),o=r(20222),i=r(87517);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},77333:(t,e,r)=>{var n=r(7369),o=r(20222),i=r(69373);t.exports=function(t,e){return t&&t.length?n(t,i(e,2),o):void 0}},84722:(t,e,r)=>{var n=r(67830);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},35534:(t,e,r)=>{var n=r(7369),o=r(1479),i=r(87517);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},67568:(t,e,r)=>{var n=r(7369),o=r(69373),i=r(1479);t.exports=function(t,e){return t&&t.length?n(t,o(e,2),i):void 0}},25296:t=>{t.exports=function(){}},120:(t,e,r)=>{var n=r(67598);t.exports=function(){return n.Date.now()}},14906:(t,e,r)=>{var n=r(97787),o=r(67242),i=r(997),a=r(14711);t.exports=function(t){return i(t)?n(a(t)):o(t)}},18590:(t,e,r)=>{var n=r(29560)();t.exports=n},94137:(t,e,r)=>{var n=r(62976),o=r(69373),i=r(35010),a=r(71166),c=r(7647);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},16476:(t,e,r)=>{var n=r(2572),o=r(55335),i=r(85590),a=r(7647),c=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])});t.exports=c},59276:t=>{t.exports=function(){return[]}},66012:t=>{t.exports=function(){return!1}},4011:(t,e,r)=>{var n=r(63908),o=r(60986);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},79964:(t,e,r)=>{var n=r(29155),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},4296:(t,e,r)=>{var n=r(79964);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},29155:(t,e,r)=>{var n=r(62894),o=r(60986),i=r(56381),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},64294:(t,e,r)=>{var n=r(46584);t.exports=function(t){return null==t?"":n(t)}},99541:(t,e,r)=>{var n=r(69373),o=r(62634);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},88523:(t,e,r)=>{var n=r(88053)("toUpperCase");t.exports=n},21541:(t,e,r)=>{"use strict";var n=r(40378);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},7470:(t,e,r)=>{t.exports=r(21541)()},40378:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},64456:(t,e)=>{"use strict";/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),c=Symbol.for("react.provider"),u=Symbol.for("react.context"),l=Symbol.for("react.server_context"),s=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),d=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case o:case a:case i:case f:case p:return t;default:switch(t=t&&t.$$typeof){case l:case u:case s:case d:case h:case c:return t;default:return e}}case n:return e}}}(t)===o}},99046:(t,e,r)=>{"use strict";t.exports=r(64456)},79865:(t,e,r)=>{"use strict";r.r(e),r.d(e,{$$typeof:()=>i,__esModule:()=>o,default:()=>a});let n=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\dashboard\page.tsx`),{__esModule:o,$$typeof:i}=n,a=n.default},82917:(t,e,r)=>{"use strict";r.r(e),r.d(e,{$$typeof:()=>i,__esModule:()=>o,default:()=>a});let n=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:o,$$typeof:i}=n,a=n.default},4047:()=>{}};var e=require("../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[638,606,456],()=>r(11925));module.exports=n})();