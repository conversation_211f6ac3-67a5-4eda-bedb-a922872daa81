(()=>{var e={};e.id=370,e.ids=[370],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},6691:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(50482),a=s(69108),i=s(62563),l=s.n(i),n=s(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c=["",{children:["inquiries",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36114)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\inquiries\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\inquiries\\page.tsx"],u="/inquiries/page",h={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/inquiries/page",pathname:"/inquiries",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79585:(e,t,s)=>{Promise.resolve().then(s.bind(s,67169))},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>o,a:()=>c});var r=s(95344),a=s(3729),i=s(22254),l=s(43932);let n=(0,a.createContext)(void 0);function o({children:e}){let[t,s]=(0,a.useState)(null),[o,c]=(0,a.useState)(!0),d=(0,i.useRouter)(),u=(0,i.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(l.Z.defaults.headers.common.Authorization=`Bearer ${e}`,s(JSON.parse(t))):"/login"!==u&&d.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{c(!1)}})()},[u,d]);let h=async(e,t)=>{try{let{user:r,token:a}=(await l.Z.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),l.Z.defaults.headers.common.Authorization=`Bearer ${a}`,s(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(n.Provider,{value:{user:t,loading:o,login:h,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete l.Z.defaults.headers.common.Authorization,s(null),d.push("/login")},updateUserInfo:e=>{if(t){let r={...t,...e};s(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!t},children:e})}function c(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67169:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95344),a=s(3729),i=s(99847),l=s(22254),n=s(44669),o=s(43932);function c(){let{user:e}=(0,i.a)();(0,l.useRouter)();let[t,s]=(0,a.useState)([]),[c,d]=(0,a.useState)(!0),[u,h]=(0,a.useState)(""),[x,p]=(0,a.useState)(null),[m,g]=(0,a.useState)(!1),[b,y]=(0,a.useState)("all"),[j,f]=(0,a.useState)("");(0,a.useEffect)(()=>{(async()=>{try{d(!0);let e=await o.h.get("/inquiries");s(e.data)}catch(e){console.error("获取咨询列表失败:",e),n.ZP.error("获取咨询列表失败")}finally{d(!1)}})()},[]);let v=t.filter(e=>{if("all"!==b&&e.status!==b)return!1;if(j){let t=j.toLowerCase();return e.name.toLowerCase().includes(t)||e.email.toLowerCase().includes(t)||e.subject.toLowerCase().includes(t)||e.message.toLowerCase().includes(t)}return!0}),N=async t=>{if(!u.trim()){n.ZP.error("回复内容不能为空");return}try{g(!0),await o.h.post(`/inquiries/${t}/reply`,{reply:u,replied_by_user_id:e?.id}),s(s=>s.map(s=>s.id===t?{...s,status:"replied",reply:u,replied_at:new Date().toISOString(),replied_by_user:{id:e?.id||0,name:e?.name||""}}:s)),h(""),p(null),n.ZP.success("回复成功")}catch(e){console.error("回复咨询失败:",e),n.ZP.error("回复咨询失败")}finally{g(!1)}},w=async e=>{try{await o.h.patch(`/inquiries/${e}/status`,{status:"closed"}),s(t=>t.map(t=>t.id===e?{...t,status:"closed"}:t)),n.ZP.success("咨询已关闭")}catch(e){console.error("关闭咨询失败:",e),n.ZP.error("关闭咨询失败")}},q=async e=>{try{await o.h.patch(`/inquiries/${e}/status`,{status:"pending"}),s(t=>t.map(t=>t.id===e?{...t,status:"pending"}:t)),n.ZP.success("咨询已重新打开")}catch(e){console.error("重新打开咨询失败:",e),n.ZP.error("重新打开咨询失败")}},P=e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),k=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"replied":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},_=e=>{switch(e){case"pending":return"待处理";case"replied":return"已回复";case"closed":return"已关闭";default:return"未知状态"}};return(0,r.jsxs)("div",{className:"p-6",children:[r.jsx("h1",{className:"text-2xl font-bold mb-6",children:"咨询管理"}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between mb-6 gap-4",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>y("all"),className:`px-3 py-1 rounded ${"all"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"全部"}),r.jsx("button",{onClick:()=>y("pending"),className:`px-3 py-1 rounded ${"pending"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"待处理"}),r.jsx("button",{onClick:()=>y("replied"),className:`px-3 py-1 rounded ${"replied"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"已回复"}),r.jsx("button",{onClick:()=>y("closed"),className:`px-3 py-1 rounded ${"closed"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"已关闭"})]}),r.jsx("div",{className:"relative",children:r.jsx("input",{type:"text",placeholder:"搜索咨询...",className:"border rounded px-3 py-1 w-full md:w-64",value:j,onChange:e=>f(e.target.value)})})]}),c?r.jsx("div",{className:"text-center py-10",children:"加载中..."}):0===v.length?r.jsx("div",{className:"text-center py-10 text-gray-500",children:"暂无咨询数据"}):r.jsx("div",{className:"space-y-6",children:v.map(e=>(0,r.jsxs)("div",{className:"border rounded-lg overflow-hidden bg-white shadow-sm",children:[r.jsx("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-bold text-lg",children:e.subject}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.name," (",e.email,")",e.phone&&` \xb7 ${e.phone}`]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:`text-xs px-2 py-1 rounded ${k(e.status)}`,children:_(e.status)}),r.jsx("span",{className:"text-xs text-gray-500",children:P(e.created_at)})]})]})}),r.jsx("div",{className:"p-4 bg-gray-50",children:r.jsx("p",{className:"whitespace-pre-wrap",children:e.message})}),e.reply&&(0,r.jsxs)("div",{className:"p-4 border-t bg-blue-50",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[r.jsx("h4",{className:"font-medium",children:"回复"}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:[e.replied_at&&P(e.replied_at),e.replied_by_user&&` \xb7 ${e.replied_by_user.name}`]})]}),r.jsx("p",{className:"whitespace-pre-wrap",children:e.reply})]}),(0,r.jsxs)("div",{className:"p-4 border-t bg-gray-100 flex justify-end space-x-2",children:["pending"===e.status&&r.jsx("button",{onClick:()=>p(e.id),className:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600",children:"回复"}),"pending"===e.status&&r.jsx("button",{onClick:()=>w(e.id),className:"px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600",children:"关闭"}),"replied"===e.status&&r.jsx("button",{onClick:()=>w(e.id),className:"px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600",children:"关闭"}),"closed"===e.status&&r.jsx("button",{onClick:()=>q(e.id),className:"px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600",children:"重新打开"})]}),x===e.id&&(0,r.jsxs)("div",{className:"p-4 border-t",children:[r.jsx("textarea",{className:"w-full border rounded p-2 mb-2",rows:4,placeholder:"输入回复内容...",value:u,onChange:e=>h(e.target.value)}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[r.jsx("button",{onClick:()=>{p(null),h("")},className:"px-3 py-1 bg-gray-300 rounded hover:bg-gray-400",children:"取消"}),r.jsx("button",{onClick:()=>N(e.id),disabled:m,className:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300",children:m?"发送中...":"发送回复"})]})]})]},e.id))}),r.jsx(n.x7,{})]})}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(95344);s(3729),s(4047);var a=s(99847),i=s(44669),l=s(22254);function n({children:e}){let{user:t,logout:s,isAuthenticated:i,loading:n}=(0,a.a)(),o=(0,l.usePathname)();return"/login"===o?r.jsx(r.Fragment,{children:e}):n?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):i?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===o?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function o({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(n,{children:e}),r.jsx(i.x7,{position:"top-right"})]})})})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},36114:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\inquiries\page.tsx`),{__esModule:a,$$typeof:i}=r,l=r.default},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:i}=r,l=r.default},4047:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606],()=>s(6691));module.exports=r})();