(()=>{var e={};e.id=370,e.ids=[370],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},6691:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>o,routeModule:()=>x,tree:()=>d});var r=t(50482),a=t(69108),l=t(62563),i=t.n(l),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["inquiries",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36114)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\inquiries\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\inquiries\\page.tsx"],u="/inquiries/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/inquiries/page",pathname:"/inquiries",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79585:(e,s,t)=>{Promise.resolve().then(t.bind(t,67169))},89747:(e,s,t)=>{Promise.resolve().then(t.bind(t,67329))},95444:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},99847:(e,s,t)=>{"use strict";t.d(s,{H:()=>c,a:()=>d});var r=t(95344),a=t(3729),l=t(22254),i=t(43932);let n=(0,a.createContext)(void 0);function c({children:e}){let[s,t]=(0,a.useState)(null),[c,d]=(0,a.useState)(!0),o=(0,l.useRouter)(),u=(0,l.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),s=localStorage.getItem("adminUser");e&&s?(i.h.defaults.headers.common.Authorization=`Bearer ${e}`,t(JSON.parse(s))):"/login"!==u&&o.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[u,o]);let p=async(e,s)=>{try{let{user:r,token:a}=(await i.h.post("/auth/login",{username:e,password:s})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),i.h.defaults.headers.common.Authorization=`Bearer ${a}`,t(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(n.Provider,{value:{user:s,loading:c,login:p,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.h.defaults.headers.common.Authorization,t(null),o.push("/login")},updateUserInfo:e=>{if(s){let r={...s,...e};t(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!s},children:e})}function d(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67169:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(95344),a=t(3729),l=t(99847),i=t(22254),n=t(44669),c=t(43932);function d(){let{user:e}=(0,l.a)();(0,i.useRouter)();let[s,t]=(0,a.useState)([]),[d,o]=(0,a.useState)(!0),[u,p]=(0,a.useState)(""),[x,h]=(0,a.useState)(null),[m,g]=(0,a.useState)(!1),[b,j]=(0,a.useState)("all"),[y,f]=(0,a.useState)("all"),[v,N]=(0,a.useState)("");(0,a.useEffect)(()=>{(async()=>{try{o(!0);let e=await c.Z.get("/inquiries");if(console.log("咨询API响应:",e.data),e.data.success){let s=e.data.data.items||e.data.data||[];t(s)}else console.error("API返回失败状态:",e.data),t([]),n.ZP.error("获取咨询列表失败")}catch(e){console.error("获取咨询列表失败:",e),t([]),n.ZP.error("获取咨询列表失败")}finally{o(!1)}})()},[]);let w=s.filter(e=>{if("all"!==b&&e.status!==b)return!1;if("all"!==y){let s="在线预约申请"===e.subject;if("appointment"===y&&!s||"contact"===y&&s)return!1}if(v){let s=v.toLowerCase();return e.name.toLowerCase().includes(s)||e.email.toLowerCase().includes(s)||e.subject.toLowerCase().includes(s)||e.message.toLowerCase().includes(s)}return!0}),_=async s=>{if(!u.trim()){n.ZP.error("回复内容不能为空");return}try{g(!0),await c.Z.post(`/inquiries/${s}/reply`,{reply:u,replied_by_user_id:e?.id}),t(t=>t.map(t=>t.id===s?{...t,status:"replied",reply:u,replied_at:new Date().toISOString(),replied_by_user:{id:e?.id||0,name:e?.name||""}}:t)),p(""),h(null),n.ZP.success("回复成功")}catch(e){console.error("回复咨询失败:",e),n.ZP.error("回复咨询失败")}finally{g(!1)}},P=async e=>{try{await c.Z.patch(`/inquiries/${e}/status`,{status:"closed"}),t(s=>s.map(s=>s.id===e?{...s,status:"closed"}:s)),n.ZP.success("咨询已关闭")}catch(e){console.error("关闭咨询失败:",e),n.ZP.error("关闭咨询失败")}},q=async e=>{try{await c.Z.patch(`/inquiries/${e}/status`,{status:"pending"}),t(s=>s.map(s=>s.id===e?{...s,status:"pending"}:s)),n.ZP.success("咨询已重新打开")}catch(e){console.error("重新打开咨询失败:",e),n.ZP.error("重新打开咨询失败")}},k=e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),C=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"replied":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},$=e=>{switch(e){case"pending":return"待处理";case"replied":return"已回复";case"closed":return"已关闭";default:return"未知状态"}};return(0,r.jsxs)("div",{className:"p-6",children:[r.jsx("h1",{className:"text-2xl font-bold mb-6",children:"咨询管理"}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row justify-between mb-6 gap-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>j("all"),className:`px-3 py-1 rounded ${"all"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"全部"}),r.jsx("button",{onClick:()=>j("pending"),className:`px-3 py-1 rounded ${"pending"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"待处理"}),r.jsx("button",{onClick:()=>j("replied"),className:`px-3 py-1 rounded ${"replied"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"已回复"}),r.jsx("button",{onClick:()=>j("closed"),className:`px-3 py-1 rounded ${"closed"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"已关闭"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>f("all"),className:`px-3 py-1 rounded ${"all"===y?"bg-green-500 text-white":"bg-gray-200"}`,children:"全部来源"}),r.jsx("button",{onClick:()=>f("contact"),className:`px-3 py-1 rounded ${"contact"===y?"bg-green-500 text-white":"bg-gray-200"}`,children:"联系咨询"}),r.jsx("button",{onClick:()=>f("appointment"),className:`px-3 py-1 rounded ${"appointment"===y?"bg-purple-500 text-white":"bg-gray-200"}`,children:"预约申请"})]})]}),r.jsx("div",{className:"relative",children:r.jsx("input",{type:"text",placeholder:"搜索咨询...",className:"border rounded px-3 py-1 w-full md:w-64",value:v,onChange:e=>N(e.target.value)})})]}),d?r.jsx("div",{className:"text-center py-10",children:"加载中..."}):0===w.length?r.jsx("div",{className:"text-center py-10 text-gray-500",children:"暂无咨询数据"}):r.jsx("div",{className:"space-y-6",children:w.map(e=>(0,r.jsxs)("div",{className:"border rounded-lg overflow-hidden bg-white shadow-sm",children:[r.jsx("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-bold text-lg",children:e.subject}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.name," (",e.email,")",e.phone&&` \xb7 ${e.phone}`]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:`text-xs px-2 py-1 rounded ${"在线预约申请"===e.subject?"bg-purple-100 text-purple-800":"bg-green-100 text-green-800"}`,children:"在线预约申请"===e.subject?"预约申请":"联系咨询"}),r.jsx("span",{className:`text-xs px-2 py-1 rounded ${C(e.status)}`,children:$(e.status)}),r.jsx("span",{className:"text-xs text-gray-500",children:k(e.created_at)})]})]})}),(0,r.jsxs)("div",{className:"p-4 bg-gray-50",children:[r.jsx("p",{className:"whitespace-pre-wrap",children:e.message}),"在线预约申请"===e.subject&&(e.preferred_date||e.preferred_time||e.service_type)&&(0,r.jsxs)("div",{className:"mt-4 p-3 bg-purple-50 border border-purple-200 rounded",children:[r.jsx("h5",{className:"font-medium text-purple-800 mb-2",children:"预约详情"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-2 text-sm",children:[e.service_type&&(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-gray-600",children:"服务类型："}),r.jsx("span",{className:"text-purple-700",children:e.service_type})]}),e.preferred_date&&(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-gray-600",children:"期望日期："}),r.jsx("span",{className:"text-purple-700",children:e.preferred_date})]}),e.preferred_time&&(0,r.jsxs)("div",{children:[r.jsx("span",{className:"text-gray-600",children:"期望时间："}),r.jsx("span",{className:"text-purple-700",children:e.preferred_time})]})]})]})]}),e.reply&&(0,r.jsxs)("div",{className:"p-4 border-t bg-blue-50",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[r.jsx("h4",{className:"font-medium",children:"回复"}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:[e.replied_at&&k(e.replied_at),e.replied_by_user&&` \xb7 ${e.replied_by_user.name}`]})]}),r.jsx("p",{className:"whitespace-pre-wrap",children:e.reply})]}),(0,r.jsxs)("div",{className:"p-4 border-t bg-gray-100 flex justify-end space-x-2",children:["pending"===e.status&&r.jsx("button",{onClick:()=>h(e.id),className:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600",children:"回复"}),"pending"===e.status&&r.jsx("button",{onClick:()=>P(e.id),className:"px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600",children:"关闭"}),"replied"===e.status&&r.jsx("button",{onClick:()=>P(e.id),className:"px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600",children:"关闭"}),"closed"===e.status&&r.jsx("button",{onClick:()=>q(e.id),className:"px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600",children:"重新打开"})]}),x===e.id&&(0,r.jsxs)("div",{className:"p-4 border-t",children:[r.jsx("textarea",{className:"w-full border rounded p-2 mb-2",rows:4,placeholder:"输入回复内容...",value:u,onChange:e=>p(e.target.value)}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[r.jsx("button",{onClick:()=>{h(null),p("")},className:"px-3 py-1 bg-gray-300 rounded hover:bg-gray-400",children:"取消"}),r.jsx("button",{onClick:()=>_(e.id),disabled:m,className:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300",children:m?"发送中...":"发送回复"})]})]})]},e.id))}),r.jsx(n.x7,{})]})}},67329:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(95344);t(3729),t(4047);var a=t(99847),l=t(44669),i=t(20783),n=t.n(i),c=t(22254);function d({children:e}){let{user:s,logout:t,isAuthenticated:l,loading:i}=(0,a.a)(),d=(0,c.usePathname)();return"/login"===d?r.jsx(r.Fragment,{children:e}):i?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):l?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",s?.name]}),r.jsx("button",{onClick:t,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx(n(),{href:"/dashboard",className:`block p-2 hover:bg-gray-200 rounded ${"/dashboard"===d?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx(n(),{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx(n(),{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx(n(),{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx(n(),{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function o({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(d,{children:e}),r.jsx(l.x7,{position:"top-right"})]})})})}},43932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a,h:()=>r});let r=t(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},36114:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\inquiries\page.tsx`),{__esModule:a,$$typeof:l}=r,i=r.default},82917:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:l}=r,i=r.default},4047:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,300],()=>t(6691));module.exports=r})();