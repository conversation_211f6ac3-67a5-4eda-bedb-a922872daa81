(()=>{var e={};e.id=370,e.ids=[370],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},6691:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o});var r=t(50482),a=t(69108),i=t(62563),n=t.n(i),l=t(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(s,d);let o=["",{children:["inquiries",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,36114)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\inquiries\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\inquiries\\page.tsx"],u="/inquiries/page",p={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/inquiries/page",pathname:"/inquiries",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79585:(e,s,t)=>{Promise.resolve().then(t.bind(t,67169))},67169:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(95344),a=t(3729),i=t(99847),n=t(22254),l=t(44669),d=t(43932);function o(){let{user:e}=(0,i.a)();(0,n.useRouter)();let[s,t]=(0,a.useState)([]),[o,c]=(0,a.useState)(!0),[u,p]=(0,a.useState)(""),[x,g]=(0,a.useState)(null),[h,m]=(0,a.useState)(!1),[b,y]=(0,a.useState)("all"),[j,w]=(0,a.useState)("");(0,a.useEffect)(()=>{(async()=>{try{c(!0);let e=await d.Z.get("/inquiries");if(console.log("咨询API响应:",e.data),e.data.success){let s=e.data.data.items||e.data.data||[];t(s)}else console.error("API返回失败状态:",e.data),t([]),l.ZP.error("获取咨询列表失败")}catch(e){console.error("获取咨询列表失败:",e),t([]),l.ZP.error("获取咨询列表失败")}finally{c(!1)}})()},[]);let f=s.filter(e=>{if("all"!==b&&e.status!==b)return!1;if(j){let s=j.toLowerCase();return e.name.toLowerCase().includes(s)||e.email.toLowerCase().includes(s)||e.subject.toLowerCase().includes(s)||e.message.toLowerCase().includes(s)}return!0}),v=async s=>{if(!u.trim()){l.ZP.error("回复内容不能为空");return}try{m(!0),await d.Z.post(`/inquiries/${s}/reply`,{reply:u,replied_by_user_id:e?.id}),t(t=>t.map(t=>t.id===s?{...t,status:"replied",reply:u,replied_at:new Date().toISOString(),replied_by_user:{id:e?.id||0,name:e?.name||""}}:t)),p(""),g(null),l.ZP.success("回复成功")}catch(e){console.error("回复咨询失败:",e),l.ZP.error("回复咨询失败")}finally{m(!1)}},N=async e=>{try{await d.Z.patch(`/inquiries/${e}/status`,{status:"closed"}),t(s=>s.map(s=>s.id===e?{...s,status:"closed"}:s)),l.ZP.success("咨询已关闭")}catch(e){console.error("关闭咨询失败:",e),l.ZP.error("关闭咨询失败")}},q=async e=>{try{await d.Z.patch(`/inquiries/${e}/status`,{status:"pending"}),t(s=>s.map(s=>s.id===e?{...s,status:"pending"}:s)),l.ZP.success("咨询已重新打开")}catch(e){console.error("重新打开咨询失败:",e),l.ZP.error("重新打开咨询失败")}},_=e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),P=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"replied":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},C=e=>{switch(e){case"pending":return"待处理";case"replied":return"已回复";case"closed":return"已关闭";default:return"未知状态"}};return(0,r.jsxs)("div",{className:"p-6",children:[r.jsx("h1",{className:"text-2xl font-bold mb-6",children:"咨询管理"}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between mb-6 gap-4",children:[(0,r.jsxs)("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>y("all"),className:`px-3 py-1 rounded ${"all"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"全部"}),r.jsx("button",{onClick:()=>y("pending"),className:`px-3 py-1 rounded ${"pending"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"待处理"}),r.jsx("button",{onClick:()=>y("replied"),className:`px-3 py-1 rounded ${"replied"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"已回复"}),r.jsx("button",{onClick:()=>y("closed"),className:`px-3 py-1 rounded ${"closed"===b?"bg-blue-500 text-white":"bg-gray-200"}`,children:"已关闭"})]}),r.jsx("div",{className:"relative",children:r.jsx("input",{type:"text",placeholder:"搜索咨询...",className:"border rounded px-3 py-1 w-full md:w-64",value:j,onChange:e=>w(e.target.value)})})]}),o?r.jsx("div",{className:"text-center py-10",children:"加载中..."}):0===f.length?r.jsx("div",{className:"text-center py-10 text-gray-500",children:"暂无咨询数据"}):r.jsx("div",{className:"space-y-6",children:f.map(e=>(0,r.jsxs)("div",{className:"border rounded-lg overflow-hidden bg-white shadow-sm",children:[r.jsx("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-bold text-lg",children:e.subject}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[e.name," (",e.email,")",e.phone&&` \xb7 ${e.phone}`]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:`text-xs px-2 py-1 rounded ${P(e.status)}`,children:C(e.status)}),r.jsx("span",{className:"text-xs text-gray-500",children:_(e.created_at)})]})]})}),r.jsx("div",{className:"p-4 bg-gray-50",children:r.jsx("p",{className:"whitespace-pre-wrap",children:e.message})}),e.reply&&(0,r.jsxs)("div",{className:"p-4 border-t bg-blue-50",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[r.jsx("h4",{className:"font-medium",children:"回复"}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:[e.replied_at&&_(e.replied_at),e.replied_by_user&&` \xb7 ${e.replied_by_user.name}`]})]}),r.jsx("p",{className:"whitespace-pre-wrap",children:e.reply})]}),(0,r.jsxs)("div",{className:"p-4 border-t bg-gray-100 flex justify-end space-x-2",children:["pending"===e.status&&r.jsx("button",{onClick:()=>g(e.id),className:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600",children:"回复"}),"pending"===e.status&&r.jsx("button",{onClick:()=>N(e.id),className:"px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600",children:"关闭"}),"replied"===e.status&&r.jsx("button",{onClick:()=>N(e.id),className:"px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600",children:"关闭"}),"closed"===e.status&&r.jsx("button",{onClick:()=>q(e.id),className:"px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600",children:"重新打开"})]}),x===e.id&&(0,r.jsxs)("div",{className:"p-4 border-t",children:[r.jsx("textarea",{className:"w-full border rounded p-2 mb-2",rows:4,placeholder:"输入回复内容...",value:u,onChange:e=>p(e.target.value)}),(0,r.jsxs)("div",{className:"flex justify-end space-x-2",children:[r.jsx("button",{onClick:()=>{g(null),p("")},className:"px-3 py-1 bg-gray-300 rounded hover:bg-gray-400",children:"取消"}),r.jsx("button",{onClick:()=>v(e.id),disabled:h,className:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300",children:h?"发送中...":"发送回复"})]})]})]},e.id))}),r.jsx(l.x7,{})]})}},36114:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\inquiries\page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,300,238],()=>t(6691));module.exports=r})();