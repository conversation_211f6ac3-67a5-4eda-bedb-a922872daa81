(()=>{var e={};e.id=626,e.ids=[626],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},8029:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>d,routeModule:()=>x,tree:()=>u});var t=s(50482),o=s(69108),n=s(62563),a=s.n(n),i=s(68300),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(r,l);let u=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,43015)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\login\\page.tsx"],c="/login/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},59209:(e,r,s)=>{Promise.resolve().then(s.bind(s,89928))},89928:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var t=s(95344),o=s(3729),n=s(22254),a=s(44669),i=s(99847);function l(){let[e,r]=(0,o.useState)(""),[s,l]=(0,o.useState)(""),[u,d]=(0,o.useState)(""),[c,p]=(0,o.useState)(!1),x=(0,n.useRouter)(),{login:m}=(0,i.a)(),g=async r=>{r.preventDefault(),d(""),p(!0);try{console.log("开始登录...",{username:e,password:s}),await m(e,s),console.log("登录成功，跳转到仪表板"),a.ZP.success("登录成功！"),x.push("/dashboard")}catch(r){console.error("登录失败:",r);let e=r.response?.data?.message||r.message||"登录失败，请重试";d(e),a.ZP.error(e)}finally{p(!1)}};return(0,t.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"max-w-md w-full space-y-8 bg-white p-10 rounded-lg shadow-md",children:[t.jsx("div",{children:t.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"管理系统登录"})}),u&&t.jsx("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 mb-4",children:t.jsx("p",{className:"text-red-700",children:u})}),(0,t.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:g,children:[(0,t.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"username",className:"sr-only",children:"用户名"}),t.jsx("input",{id:"username",name:"username",type:"text",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"用户名",value:e,onChange:e=>r(e.target.value)})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"password",className:"sr-only",children:"密码"}),t.jsx("input",{id:"password",name:"password",type:"password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"密码",value:s,onChange:e=>l(e.target.value)})]})]}),t.jsx("div",{children:t.jsx("button",{type:"submit",disabled:c,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300",children:c?"登录中...":"登录"})})]})]}),t.jsx(a.x7,{position:"top-right"})]})}},43015:(e,r,s)=>{"use strict";s.r(r),s.d(r,{$$typeof:()=>n,__esModule:()=>o,default:()=>a});let t=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\login\page.tsx`),{__esModule:o,$$typeof:n}=t,a=t.default}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,606,238],()=>s(8029));module.exports=t})();