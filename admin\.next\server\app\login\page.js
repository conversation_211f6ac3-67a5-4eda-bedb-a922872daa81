(()=>{var e={};e.id=626,e.ids=[626],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},8029:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=t(50482),n=t(69108),l=t(62563),a=t.n(l),i=t(68300),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(s,o);let c=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,43015)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\login\\page.tsx"],u="/login/page",h={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89747:(e,s,t)=>{Promise.resolve().then(t.bind(t,67329))},59209:(e,s,t)=>{Promise.resolve().then(t.bind(t,89928))},95444:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},99847:(e,s,t)=>{"use strict";t.d(s,{H:()=>o,a:()=>c});var r=t(95344),n=t(3729),l=t(22254),a=t(43932);let i=(0,n.createContext)(void 0);function o({children:e}){let[s,t]=(0,n.useState)(null),[o,c]=(0,n.useState)(!0),d=(0,l.useRouter)(),u=(0,l.usePathname)();(0,n.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),s=localStorage.getItem("adminUser");e&&s?(a.h.defaults.headers.common.Authorization=`Bearer ${e}`,t(JSON.parse(s))):"/login"!==u&&d.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{c(!1)}})()},[u,d]);let h=async(e,s)=>{try{let{user:r,token:n}=(await a.h.post("/auth/login",{username:e,password:s})).data;return localStorage.setItem("adminToken",n),localStorage.setItem("adminUser",JSON.stringify(r)),a.h.defaults.headers.common.Authorization=`Bearer ${n}`,t(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(i.Provider,{value:{user:s,loading:o,login:h,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete a.h.defaults.headers.common.Authorization,t(null),d.push("/login")},isAuthenticated:!!s},children:e})}function c(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(95344);t(3729),t(4047);var n=t(99847),l=t(44669),a=t(20783),i=t.n(a),o=t(22254);function c({children:e}){let{user:s,logout:t,isAuthenticated:l,loading:a}=(0,n.a)(),c=(0,o.usePathname)();return"/login"===c?r.jsx(r.Fragment,{children:e}):a?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):l?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",s?.name]}),r.jsx("button",{onClick:t,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx(i(),{href:"/dashboard",className:`block p-2 hover:bg-gray-200 rounded ${"/dashboard"===c?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx(i(),{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx(i(),{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx(i(),{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx(i(),{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function d({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(n.H,{children:[r.jsx(c,{children:e}),r.jsx(l.x7,{position:"top-right"})]})})})}},89928:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(95344),n=t(3729),l=t(22254),a=t(44669),i=t(99847);function o(){let[e,s]=(0,n.useState)(""),[t,o]=(0,n.useState)(""),[c,d]=(0,n.useState)(""),[u,h]=(0,n.useState)(!1),x=(0,l.useRouter)(),{login:p}=(0,i.a)(),m=async s=>{s.preventDefault(),d(""),h(!0);try{console.log("开始登录...",{username:e,password:t}),await p(e,t),console.log("登录成功，跳转到仪表板"),a.ZP.success("登录成功！"),x.push("/dashboard")}catch(s){console.error("登录失败:",s);let e=s.response?.data?.message||s.message||"登录失败，请重试";d(e),a.ZP.error(e)}finally{h(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8 bg-white p-10 rounded-lg shadow-md",children:[r.jsx("div",{children:r.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"管理系统登录"})}),c&&r.jsx("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 mb-4",children:r.jsx("p",{className:"text-red-700",children:c})}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:m,children:[(0,r.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"username",className:"sr-only",children:"用户名"}),r.jsx("input",{id:"username",name:"username",type:"text",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"用户名",value:e,onChange:e=>s(e.target.value)})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"password",className:"sr-only",children:"密码"}),r.jsx("input",{id:"password",name:"password",type:"password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"密码",value:t,onChange:e=>o(e.target.value)})]})]}),r.jsx("div",{children:r.jsx("button",{type:"submit",disabled:u,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300",children:u?"登录中...":"登录"})})]})]}),r.jsx(a.x7,{position:"top-right"})]})}},43932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n,h:()=>r});let r=t(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let n=r},82917:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>n,default:()=>a});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:n,$$typeof:l}=r,a=r.default},43015:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>n,default:()=>a});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\login\page.tsx`),{__esModule:n,$$typeof:l}=r,a=r.default},4047:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,300],()=>t(8029));module.exports=r})();