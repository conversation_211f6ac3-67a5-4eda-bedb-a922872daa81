(()=>{var e={};e.id=626,e.ids=[626],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},8029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(50482),n=s(69108),a=s(62563),l=s.n(a),i=s(68300),o={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let c=["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,43015)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\login\\page.tsx"],u="/login/page",h={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},59209:(e,t,s)=>{Promise.resolve().then(s.bind(s,89928))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>o,a:()=>c});var r=s(95344),n=s(3729),a=s(22254),l=s(43932);let i=(0,n.createContext)(void 0);function o({children:e}){let[t,s]=(0,n.useState)(null),[o,c]=(0,n.useState)(!0),d=(0,a.useRouter)(),u=(0,a.usePathname)();(0,n.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(l.h.defaults.headers.common.Authorization=`Bearer ${e}`,s(JSON.parse(t))):"/login"!==u&&d.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{c(!1)}})()},[u,d]);let h=async(e,t)=>{try{let{user:r,token:n}=(await l.h.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",n),localStorage.setItem("adminUser",JSON.stringify(r)),l.h.defaults.headers.common.Authorization=`Bearer ${n}`,s(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(i.Provider,{value:{user:t,loading:o,login:h,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete l.h.defaults.headers.common.Authorization,s(null),d.push("/login")},isAuthenticated:!!t},children:e})}function c(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(95344);s(3729),s(4047);var n=s(99847),a=s(44669),l=s(22254);function i({children:e}){let{user:t,logout:s,isAuthenticated:a,loading:i}=(0,n.a)(),o=(0,l.usePathname)();return"/login"===o?r.jsx(r.Fragment,{children:e}):i?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):a?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===o?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function o({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(n.H,{children:[r.jsx(i,{children:e}),r.jsx(a.x7,{position:"top-right"})]})})})}},89928:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(95344),n=s(3729),a=s(22254),l=s(43932);function i(){let[e,t]=(0,n.useState)(""),[s,i]=(0,n.useState)(""),[o,c]=(0,n.useState)(""),[d,u]=(0,n.useState)(!1),h=(0,a.useRouter)(),x=async t=>{t.preventDefault(),c(""),u(!0);try{let t=await l.h.post("/auth/login",{username:e,password:s});localStorage.setItem("adminToken",t.data.token),localStorage.setItem("adminUser",JSON.stringify(t.data.user)),h.push("/")}catch(e){c(e.response?.data?.error||"登录失败，请重试")}finally{u(!1)}};return r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8 bg-white p-10 rounded-lg shadow-md",children:[r.jsx("div",{children:r.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"管理系统登录"})}),o&&r.jsx("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 mb-4",children:r.jsx("p",{className:"text-red-700",children:o})}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:x,children:[(0,r.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"username",className:"sr-only",children:"用户名"}),r.jsx("input",{id:"username",name:"username",type:"text",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"用户名",value:e,onChange:e=>t(e.target.value)})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"password",className:"sr-only",children:"密码"}),r.jsx("input",{id:"password",name:"password",type:"password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"密码",value:s,onChange:e=>i(e.target.value)})]})]}),r.jsx("div",{children:r.jsx("button",{type:"submit",disabled:d,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300",children:d?"登录中...":"登录"})})]})]})})}},43932:(e,t,s)=>{"use strict";s.d(t,{h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)))},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:n,$$typeof:a}=r,l=r.default},43015:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\login\page.tsx`),{__esModule:n,$$typeof:a}=r,l=r.default},4047:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606],()=>s(8029));module.exports=r})();