(()=>{var e={};e.id=745,e.ids=[745],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},93328:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=t(50482),l=t(69108),a=t(62563),i=t.n(a),n=t(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d=["",{children:["logs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,20471)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\logs\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\logs\\page.tsx"],x="/logs/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/logs/page",pathname:"/logs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},1429:(e,s,t)=>{Promise.resolve().then(t.bind(t,87380))},87380:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(95344),l=t(3729),a=t(32456);let i=[{id:1,action:"用户登录",user:"管理员",userRole:"超级管理员",timestamp:"2023-12-01 14:32:45",ip:"*************",details:"管理员成功登录系统",module:"auth",level:"info"},{id:2,action:"创建文章",user:"内容编辑",userRole:"编辑",timestamp:"2023-12-01 15:10:22",ip:"*************",details:"创建了新文章「留学申请流程详解」",module:"content",level:"info"},{id:3,action:"修改用户",user:"管理员",userRole:"超级管理员",timestamp:"2023-12-01 16:05:18",ip:"*************",details:"修改了用户「张编辑」的权限",module:"user",level:"warning"},{id:4,action:"删除文章",user:"内容编辑",userRole:"编辑",timestamp:"2023-12-01 16:45:33",ip:"*************",details:"删除了文章「过期内容」",module:"content",level:"warning"},{id:5,action:"系统设置修改",user:"管理员",userRole:"超级管理员",timestamp:"2023-12-01 17:20:11",ip:"*************",details:"更新了网站SEO设置",module:"system",level:"info"},{id:6,action:"登录失败",user:"未知用户",userRole:"未知",timestamp:"2023-12-01 18:05:42",ip:"************",details:"多次尝试登录失败，账户暂时锁定",module:"auth",level:"error"},{id:7,action:"创建用户",user:"管理员",userRole:"超级管理员",timestamp:"2023-12-02 09:15:30",ip:"*************",details:"创建了新用户「李编辑」",module:"user",level:"info"},{id:8,action:"回复咨询",user:"客服人员",userRole:"客服",timestamp:"2023-12-02 10:30:25",ip:"*************",details:"回复了用户「王先生」的咨询",module:"content",level:"info"},{id:9,action:"系统备份",user:"系统",userRole:"系统",timestamp:"2023-12-02 12:00:00",ip:"127.0.0.1",details:"系统自动备份完成",module:"system",level:"info"},{id:10,action:"数据库错误",user:"系统",userRole:"系统",timestamp:"2023-12-02 14:22:18",ip:"127.0.0.1",details:"数据库连接超时，自动重连成功",module:"system",level:"error"},{id:11,action:"更新服务",user:"内容编辑",userRole:"编辑",timestamp:"2023-12-02 15:40:12",ip:"*************",details:"更新了服务「职业规划咨询」的详情",module:"content",level:"info"},{id:12,action:"用户登出",user:"客服人员",userRole:"客服",timestamp:"2023-12-02 17:30:45",ip:"*************",details:"用户主动登出系统",module:"auth",level:"info"}],n=e=>{switch(e){case"info":return"bg-blue-100 text-blue-800";case"warning":return"bg-yellow-100 text-yellow-800";case"error":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},o=e=>{switch(e){case"content":return r.jsx(a.bAx,{className:"text-blue-500"});case"user":return r.jsx(a.fzv,{className:"text-green-500"});case"system":return r.jsx(a.bAx,{className:"text-purple-500"});case"auth":return r.jsx(a.fzv,{className:"text-orange-500"});default:return r.jsx(a.bAx,{className:"text-gray-500"})}};function d(){let[e,s]=(0,l.useState)(""),[t,d]=(0,l.useState)("all"),[c,x]=(0,l.useState)("all"),[u,p]=(0,l.useState)(1),[m]=(0,l.useState)(10),h=i.filter(s=>{let r=s.action.toLowerCase().includes(e.toLowerCase())||s.user.toLowerCase().includes(e.toLowerCase())||s.details.toLowerCase().includes(e.toLowerCase()),l="all"===t||s.module===t,a="all"===c||s.level===c;return r&&l&&a}),g=u*m,v=g-m,f=h.slice(v,g),y=Math.ceil(h.length/m);return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"系统日志"}),r.jsx("p",{className:"text-gray-600",children:"查看和管理系统操作日志"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4",children:[(0,r.jsxs)("div",{className:"flex-1 relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(a.jRj,{className:"text-gray-400"})}),r.jsx("input",{type:"text",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"搜索日志...",value:e,onChange:e=>s(e.target.value)})]}),r.jsx("div",{className:"w-full md:w-48",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(a.Ihx,{className:"text-gray-400"})}),(0,r.jsxs)("select",{className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none",value:t,onChange:e=>d(e.target.value),children:[r.jsx("option",{value:"all",children:"所有模块"}),r.jsx("option",{value:"content",children:"内容管理"}),r.jsx("option",{value:"user",children:"用户管理"}),r.jsx("option",{value:"system",children:"系统管理"}),r.jsx("option",{value:"auth",children:"认证授权"})]}),r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx("svg",{className:"h-5 w-5 text-gray-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:r.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})}),r.jsx("div",{className:"w-full md:w-48",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(a.Ihx,{className:"text-gray-400"})}),(0,r.jsxs)("select",{className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none",value:c,onChange:e=>x(e.target.value),children:[r.jsx("option",{value:"all",children:"所有级别"}),r.jsx("option",{value:"info",children:"信息"}),r.jsx("option",{value:"warning",children:"警告"}),r.jsx("option",{value:"error",children:"错误"})]}),r.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:r.jsx("svg",{className:"h-5 w-5 text-gray-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:r.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})}),(0,r.jsxs)("button",{onClick:()=>{alert("日志导出功能将在实际环境中实现")},className:"w-full md:w-auto px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 flex items-center justify-center space-x-2",children:[r.jsx(a._hL,{}),r.jsx("span",{children:"导出日志"})]})]})}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[r.jsx("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户"}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"时间"}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"IP地址"}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"级别"}),r.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"详情"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:f.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx("div",{className:"flex-shrink-0 h-8 w-8 flex items-center justify-center",children:o(e.module)}),(0,r.jsxs)("div",{className:"ml-4",children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.action}),r.jsx("div",{className:"text-xs text-gray-500",children:e.module})]})]})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[r.jsx("div",{className:"text-sm text-gray-900",children:e.user}),r.jsx("div",{className:"text-xs text-gray-500",children:e.userRole})]}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsxs)("div",{className:"flex items-center text-sm text-gray-500",children:[r.jsx(a.TCC,{className:"mr-1"}),e.timestamp]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.ip}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${n(e.level)}`,children:"info"===e.level?"信息":"warning"===e.level?"警告":"错误"})}),r.jsx("td",{className:"px-6 py-4 text-sm text-gray-500 max-w-xs truncate",children:e.details})]},e.id))})]})}),y>1&&r.jsx("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:(0,r.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[r.jsx("div",{children:(0,r.jsxs)("p",{className:"text-sm text-gray-700",children:["显示第 ",r.jsx("span",{className:"font-medium",children:v+1})," 到 ",r.jsx("span",{className:"font-medium",children:Math.min(g,h.length)})," 条，共 ",r.jsx("span",{className:"font-medium",children:h.length})," 条记录"]})}),r.jsx("div",{children:(0,r.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,r.jsxs)("button",{onClick:()=>p(e=>Math.max(e-1,1)),disabled:1===u,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[r.jsx("span",{className:"sr-only",children:"上一页"}),r.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:r.jsx("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),Array.from({length:y},(e,s)=>s+1).map(e=>r.jsx("button",{onClick:()=>p(e),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${u===e?"z-10 bg-primary-50 border-primary-500 text-primary-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"}`,children:e},e)),(0,r.jsxs)("button",{onClick:()=>p(e=>Math.min(e+1,y)),disabled:u===y,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[r.jsx("span",{className:"sr-only",children:"下一页"}),r.jsx("svg",{className:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:r.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})})]})]})}},20471:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>a,__esModule:()=>l,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\logs\page.tsx`),{__esModule:l,$$typeof:a}=r,i=r.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,606,456,238],()=>t(93328));module.exports=r})();