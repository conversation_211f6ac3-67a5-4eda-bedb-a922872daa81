(()=>{var e={};e.id=931,e.ids=[931],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},81415:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>h,pages:()=>d,routeModule:()=>u,tree:()=>o});var r=t(50482),a=t(69108),l=t(62563),i=t.n(l),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,70751)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\page.tsx"],h="/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},89747:(e,s,t)=>{Promise.resolve().then(t.bind(t,67329))},18788:(e,s,t)=>{Promise.resolve().then(t.bind(t,14302))},95444:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},99847:(e,s,t)=>{"use strict";t.d(s,{H:()=>c,a:()=>o});var r=t(95344),a=t(3729),l=t(22254),i=t(43932);let n=(0,a.createContext)(void 0);function c({children:e}){let[s,t]=(0,a.useState)(null),[c,o]=(0,a.useState)(!0),d=(0,l.useRouter)(),h=(0,l.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),s=localStorage.getItem("adminUser");e&&s?(i.h.defaults.headers.common.Authorization=`Bearer ${e}`,t(JSON.parse(s))):"/login"!==h&&d.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{o(!1)}})()},[h,d]);let x=async(e,s)=>{try{let{user:r,token:a}=(await i.h.post("/auth/login",{username:e,password:s})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),i.h.defaults.headers.common.Authorization=`Bearer ${a}`,t(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(n.Provider,{value:{user:s,loading:c,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.h.defaults.headers.common.Authorization,t(null),d.push("/login")},isAuthenticated:!!s},children:e})}function o(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(95344);t(3729),t(4047);var a=t(99847),l=t(44669),i=t(22254);function n({children:e}){let{user:s,logout:t,isAuthenticated:l,loading:n}=(0,a.a)(),c=(0,i.usePathname)();return"/login"===c?r.jsx(r.Fragment,{children:e}):n?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):l?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",s?.name]}),r.jsx("button",{onClick:t,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===c?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${c.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function c({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(n,{children:e}),r.jsx(l.x7,{position:"top-right"})]})})})}},14302:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(95344),a=t(3729),l=t(22254),i=t(99847),n=t(43932);function c(){let{user:e,loading:s,isAuthenticated:t}=(0,i.a)(),c=(0,l.useRouter)(),[o,d]=(0,a.useState)({totalUsers:0,totalArticles:0,totalInquiries:0,pendingInquiries:0});(0,a.useEffect)(()=>{if(!s&&!t){c.push("/login");return}t&&h()},[s,t,c]);let h=async()=>{try{let e=await n.h.get("/dashboard/stats");e.data.success&&d(e.data.data.overview)}catch(e){console.error("获取统计数据失败:",e)}};return s?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):t?(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-2xl font-semibold mb-6",children:"仪表盘"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-700",children:"用户总数"}),r.jsx("p",{className:"text-3xl font-bold text-blue-600 mt-2",children:o.totalUsers}),r.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"系统用户"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-700",children:"文章总数"}),r.jsx("p",{className:"text-3xl font-bold text-green-600 mt-2",children:o.totalArticles}),r.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"已发布文章"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-700",children:"待处理咨询"}),r.jsx("p",{className:"text-3xl font-bold text-yellow-500 mt-2",children:o.pendingInquiries}),r.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"当前积压"})]})]}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h3",{className:"text-xl font-semibold mb-4",children:"快速操作"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[r.jsx("a",{href:"/content/articles/new",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:r.jsx("span",{className:"block text-blue-600 font-medium",children:"发布文章"})}),r.jsx("a",{href:"/inquiries",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:r.jsx("span",{className:"block text-blue-600 font-medium",children:"处理咨询"})}),r.jsx("a",{href:"/content/services",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:r.jsx("span",{className:"block text-blue-600 font-medium",children:"管理服务"})}),r.jsx("a",{href:"/content/cases",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:r.jsx("span",{className:"block text-blue-600 font-medium",children:"管理案例"})})]})]}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h3",{className:"text-xl font-semibold mb-4",children:"最近活动"}),r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"时间"})]})}),(0,r.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"更新了首页Banner"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"管理员"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-15 14:30"})]}),(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"添加了新服务"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"编辑"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-15 11:20"})]}),(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"回复了客户咨询"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"客服"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-14 16:45"})]})]})]})})]})]}):null}},43932:(e,s,t)=>{"use strict";t.d(s,{h:()=>r});let r=t(47665).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)))},82917:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:l}=r,i=r.default},70751:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\page.tsx`),{__esModule:a,$$typeof:l}=r,i=r.default},4047:()=>{}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,606],()=>t(81415));module.exports=r})();