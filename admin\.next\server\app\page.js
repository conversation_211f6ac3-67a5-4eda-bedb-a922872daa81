(()=>{var e={};e.id=931,e.ids=[931],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},81415:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d});var r=t(50482),a=t(69108),i=t(62563),l=t.n(i),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,70751)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\page.tsx"],x="/page",p={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},18788:(e,s,t)=>{Promise.resolve().then(t.bind(t,14302))},14302:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(95344),a=t(3729),i=t(22254),l=t(99847),n=t(43932);function c(){let{user:e,loading:s,isAuthenticated:t}=(0,l.a)(),c=(0,i.useRouter)(),[d,o]=(0,a.useState)({totalUsers:0,totalArticles:0,totalInquiries:0,pendingInquiries:0});(0,a.useEffect)(()=>{if(!s&&!t){c.push("/login");return}t&&x()},[s,t,c]);let x=async()=>{try{let e=await n.h.get("/dashboard/stats");e.data.success&&o(e.data.data.overview)}catch(e){console.error("获取统计数据失败:",e)}};return s?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):t?(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-2xl font-semibold mb-6",children:"仪表盘"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-700",children:"用户总数"}),r.jsx("p",{className:"text-3xl font-bold text-blue-600 mt-2",children:d.totalUsers}),r.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"系统用户"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-700",children:"文章总数"}),r.jsx("p",{className:"text-3xl font-bold text-green-600 mt-2",children:d.totalArticles}),r.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"已发布文章"})]}),(0,r.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-700",children:"待处理咨询"}),r.jsx("p",{className:"text-3xl font-bold text-yellow-500 mt-2",children:d.pendingInquiries}),r.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"当前积压"})]})]}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h3",{className:"text-xl font-semibold mb-4",children:"快速操作"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[r.jsx("a",{href:"/content/articles/new",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:r.jsx("span",{className:"block text-blue-600 font-medium",children:"发布文章"})}),r.jsx("a",{href:"/inquiries",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:r.jsx("span",{className:"block text-blue-600 font-medium",children:"处理咨询"})}),r.jsx("a",{href:"/content/services",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:r.jsx("span",{className:"block text-blue-600 font-medium",children:"管理服务"})}),r.jsx("a",{href:"/content/cases",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:r.jsx("span",{className:"block text-blue-600 font-medium",children:"管理案例"})})]})]}),(0,r.jsxs)("div",{className:"mt-8",children:[r.jsx("h3",{className:"text-xl font-semibold mb-4",children:"最近活动"}),r.jsx("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"时间"})]})}),(0,r.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"更新了首页Banner"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"管理员"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-15 14:30"})]}),(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"添加了新服务"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"编辑"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-15 11:20"})]}),(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"回复了客户咨询"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"客服"}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-14 16:45"})]})]})]})})]})]}):null}},70751:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\page.tsx`),{__esModule:a,$$typeof:i}=r,l=r.default}};var s=require("../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,300,238],()=>t(81415));module.exports=r})();