(()=>{var e={};e.id=178,e.ids=[178],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},26706:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var t=r(50482),a=r(69108),l=r(62563),i=r.n(l),o=r(68300),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(s,n);let d=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,48654)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\profile\\page.tsx"],m="/profile/page",x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89747:(e,s,r)=>{Promise.resolve().then(r.bind(r,67329))},95939:(e,s,r)=>{Promise.resolve().then(r.bind(r,51115))},95444:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},99847:(e,s,r)=>{"use strict";r.d(s,{H:()=>n,a:()=>d});var t=r(95344),a=r(3729),l=r(22254),i=r(43932);let o=(0,a.createContext)(void 0);function n({children:e}){let[s,r]=(0,a.useState)(null),[n,d]=(0,a.useState)(!0),c=(0,l.useRouter)(),m=(0,l.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),s=localStorage.getItem("adminUser");e&&s?(i.h.defaults.headers.common.Authorization=`Bearer ${e}`,r(JSON.parse(s))):"/login"!==m&&c.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[m,c]);let x=async(e,s)=>{try{let{user:t,token:a}=(await i.h.post("/auth/login",{username:e,password:s})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(t)),i.h.defaults.headers.common.Authorization=`Bearer ${a}`,r(t),t}catch(e){throw console.error("登录失败:",e),e}};return t.jsx(o.Provider,{value:{user:s,loading:n,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.h.defaults.headers.common.Authorization,r(null),c.push("/login")},isAuthenticated:!!s},children:e})}function d(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(95344);r(3729),r(4047);var a=r(99847),l=r(44669),i=r(20783),o=r.n(i),n=r(22254);function d({children:e}){let{user:s,logout:r,isAuthenticated:l,loading:i}=(0,a.a)(),d=(0,n.usePathname)();return"/login"===d?t.jsx(t.Fragment,{children:e}):i?t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:t.jsx("div",{className:"text-lg",children:"加载中..."})}):l?(0,t.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[t.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[t.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"text-sm",children:["欢迎，",s?.name]}),t.jsx("button",{onClick:r,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,t.jsxs)("div",{className:"flex pt-16",children:[t.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,t.jsxs)("ul",{className:"space-y-2 mt-4",children:[t.jsx("li",{children:t.jsx(o(),{href:"/dashboard",className:`block p-2 hover:bg-gray-200 rounded ${"/dashboard"===d?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),t.jsx("li",{children:t.jsx(o(),{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),t.jsx("li",{children:t.jsx(o(),{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),t.jsx("li",{children:t.jsx(o(),{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),t.jsx("li",{children:t.jsx(o(),{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),t.jsx("li",{children:t.jsx(o(),{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),t.jsx("li",{children:t.jsx(o(),{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),t.jsx("li",{children:t.jsx(o(),{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),t.jsx("li",{children:t.jsx(o(),{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),t.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,t.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[t.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),t.jsx(o(),{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function c({children:e}){return t.jsx("html",{lang:"zh",children:t.jsx("body",{children:(0,t.jsxs)(a.H,{children:[t.jsx(d,{children:e}),t.jsx(l.x7,{position:"top-right"})]})})})}},51115:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(95344),a=r(3729),l=r(60708),i=r(44669),o=r(99847);function n(){let{user:e,updateUserInfo:s}=(0,o.a)(),[r,n]=(0,a.useState)("profile"),[d,c]=(0,a.useState)(!1),[m,x]=(0,a.useState)(null),{register:u,handleSubmit:p,formState:{errors:h},reset:b}=(0,l.cI)({defaultValues:{name:e?.name||"",email:e?.email||"",avatar:e?.avatar||"",phone:e?.phone||"",position:e?.position||"",bio:e?.bio||""}}),{register:g,handleSubmit:y,formState:{errors:j},reset:f,watch:v}=(0,l.cI)(),N=v("newPassword"),w=async r=>{c(!0);try{await new Promise(e=>setTimeout(e,1e3)),s({...e,name:r.name,email:r.email,avatar:m||r.avatar,phone:r.phone,position:r.position,bio:r.bio}),i.ZP.success("个人资料已成功更新"),c(!1)}catch(e){console.error("更新个人资料失败:",e),i.ZP.error("更新个人资料失败，请重试"),c(!1)}},P=async e=>{c(!0);try{await new Promise(e=>setTimeout(e,1e3)),i.ZP.success("密码已成功修改"),f(),c(!1)}catch(e){console.error("修改密码失败:",e),i.ZP.error("修改密码失败，请确认当前密码是否正确"),c(!1)}};return(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold",children:"个人资料"}),t.jsx("p",{className:"text-gray-600",children:"管理您的账户信息和安全设置"})]}),t.jsx("div",{className:"mb-6 border-b border-gray-200",children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[t.jsx("button",{onClick:()=>n("profile"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"profile"===r?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"个人信息"}),t.jsx("button",{onClick:()=>n("password"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"password"===r?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"修改密码"})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:["profile"===r&&(0,t.jsxs)("form",{onSubmit:p(w),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-6",children:[t.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full overflow-hidden flex items-center justify-center",children:m?t.jsx("img",{src:m,alt:"头像预览",className:"h-full w-full object-cover"}):e?.avatar?t.jsx("img",{src:e.avatar,alt:"当前头像",className:"h-full w-full object-cover"}):t.jsx("span",{className:"text-gray-500 text-xs",children:"无头像"})}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[t.jsx("input",{id:"avatar",type:"file",accept:"image/*",className:"hidden",onChange:e=>{let s=e.target.files?.[0];s&&x(URL.createObjectURL(s))}}),t.jsx("button",{type:"button",onClick:()=>document.getElementById("avatar")?.click(),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm",children:"更换头像"}),t.jsx("p",{className:"text-xs text-gray-500",children:"推荐使用正方形图片，最大2MB"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:["姓名 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{id:"name",type:"text",className:`w-full px-3 py-2 border rounded-md ${h.name?"border-red-500":"border-gray-300"}`,...u("name",{required:"请输入姓名"})}),h.name&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.name.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["邮箱 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{id:"email",type:"email",className:`w-full px-3 py-2 border rounded-md ${h.email?"border-red-500":"border-gray-300"}`,...u("email",{required:"请输入邮箱",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"请输入有效的邮箱地址"}})}),h.email&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.email.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"电话"}),t.jsx("input",{id:"phone",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...u("phone")})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-1",children:"职位"}),t.jsx("input",{id:"position",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...u("position")})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-1",children:"个人简介"}),t.jsx("textarea",{id:"bio",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"简单介绍一下自己...",...u("bio")})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[t.jsx("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>b(),disabled:d,children:"取消"}),t.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:d,children:d?"保存中...":"保存修改"})]})]}),"password"===r&&(0,t.jsxs)("form",{onSubmit:y(P),className:"space-y-6 max-w-md",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["当前密码 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{id:"currentPassword",type:"password",className:`w-full px-3 py-2 border rounded-md ${j.currentPassword?"border-red-500":"border-gray-300"}`,...g("currentPassword",{required:"请输入当前密码"})}),j.currentPassword&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:j.currentPassword.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["新密码 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{id:"newPassword",type:"password",className:`w-full px-3 py-2 border rounded-md ${j.newPassword?"border-red-500":"border-gray-300"}`,...g("newPassword",{required:"请输入新密码",minLength:{value:8,message:"密码长度至少为8个字符"},pattern:{value:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,message:"密码必须包含大小写字母和数字"}})}),j.newPassword&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:j.newPassword.message}),t.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"密码必须至少包含8个字符，包括大小写字母和数字"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["确认新密码 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{id:"confirmPassword",type:"password",className:`w-full px-3 py-2 border rounded-md ${j.confirmPassword?"border-red-500":"border-gray-300"}`,...g("confirmPassword",{required:"请确认新密码",validate:e=>e===N||"两次输入的密码不一致"})}),j.confirmPassword&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:j.confirmPassword.message})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[t.jsx("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>f(),disabled:d,children:"取消"}),t.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:d,children:d?"更新中...":"更新密码"})]})]})]}),t.jsx(i.x7,{position:"top-right"})]})}},43932:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a,h:()=>t});let t=r(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});t.interceptors.request.use(e=>e,e=>Promise.reject(e)),t.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=t},82917:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let t=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:l}=t,i=t.default},48654:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let t=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\profile\page.tsx`),{__esModule:a,$$typeof:l}=t,i=t.default},4047:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,300,708],()=>r(26706));module.exports=t})();