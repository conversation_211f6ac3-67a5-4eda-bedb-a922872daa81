(()=>{var e={};e.id=178,e.ids=[178],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},26706:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>n});var t=s(50482),a=s(69108),i=s(62563),o=s.n(i),l=s(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(r,d);let n=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,48654)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\profile\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\profile\\page.tsx"],m="/profile/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},95939:(e,r,s)=>{Promise.resolve().then(s.bind(s,51115))},51115:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d});var t=s(95344),a=s(3729),i=s(60708),o=s(44669),l=s(99847);function d(){let{user:e,updateUserInfo:r}=(0,l.a)(),[s,d]=(0,a.useState)("profile"),[n,c]=(0,a.useState)(!1),[m,p]=(0,a.useState)(null),{register:x,handleSubmit:u,formState:{errors:b},reset:h}=(0,i.cI)({defaultValues:{name:e?.name||"",email:e?.email||"",avatar:e?.avatar||"",phone:e?.phone||"",position:e?.position||"",bio:e?.bio||""}}),{register:y,handleSubmit:g,formState:{errors:f},reset:j,watch:w}=(0,i.cI)(),v=w("newPassword"),N=async s=>{c(!0);try{await new Promise(e=>setTimeout(e,1e3)),r({...e,name:s.name,email:s.email,avatar:m||s.avatar,phone:s.phone,position:s.position,bio:s.bio}),o.ZP.success("个人资料已成功更新"),c(!1)}catch(e){console.error("更新个人资料失败:",e),o.ZP.error("更新个人资料失败，请重试"),c(!1)}},P=async e=>{c(!0);try{await new Promise(e=>setTimeout(e,1e3)),o.ZP.success("密码已成功修改"),j(),c(!1)}catch(e){console.error("修改密码失败:",e),o.ZP.error("修改密码失败，请确认当前密码是否正确"),c(!1)}};return(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold",children:"个人资料"}),t.jsx("p",{className:"text-gray-600",children:"管理您的账户信息和安全设置"})]}),t.jsx("div",{className:"mb-6 border-b border-gray-200",children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[t.jsx("button",{onClick:()=>d("profile"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"profile"===s?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"个人信息"}),t.jsx("button",{onClick:()=>d("password"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"password"===s?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"修改密码"})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:["profile"===s&&(0,t.jsxs)("form",{onSubmit:u(N),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-6",children:[t.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full overflow-hidden flex items-center justify-center",children:m?t.jsx("img",{src:m,alt:"头像预览",className:"h-full w-full object-cover"}):e?.avatar?t.jsx("img",{src:e.avatar,alt:"当前头像",className:"h-full w-full object-cover"}):t.jsx("span",{className:"text-gray-500 text-xs",children:"无头像"})}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[t.jsx("input",{id:"avatar",type:"file",accept:"image/*",className:"hidden",onChange:e=>{let r=e.target.files?.[0];r&&p(URL.createObjectURL(r))}}),t.jsx("button",{type:"button",onClick:()=>document.getElementById("avatar")?.click(),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm",children:"更换头像"}),t.jsx("p",{className:"text-xs text-gray-500",children:"推荐使用正方形图片，最大2MB"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:["姓名 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{id:"name",type:"text",className:`w-full px-3 py-2 border rounded-md ${b.name?"border-red-500":"border-gray-300"}`,...x("name",{required:"请输入姓名"})}),b.name&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:b.name.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["邮箱 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{id:"email",type:"email",className:`w-full px-3 py-2 border rounded-md ${b.email?"border-red-500":"border-gray-300"}`,...x("email",{required:"请输入邮箱",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"请输入有效的邮箱地址"}})}),b.email&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:b.email.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"电话"}),t.jsx("input",{id:"phone",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...x("phone")})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-1",children:"职位"}),t.jsx("input",{id:"position",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...x("position")})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-1",children:"个人简介"}),t.jsx("textarea",{id:"bio",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"简单介绍一下自己...",...x("bio")})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[t.jsx("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>h(),disabled:n,children:"取消"}),t.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:n,children:n?"保存中...":"保存修改"})]})]}),"password"===s&&(0,t.jsxs)("form",{onSubmit:g(P),className:"space-y-6 max-w-md",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["当前密码 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{id:"currentPassword",type:"password",className:`w-full px-3 py-2 border rounded-md ${f.currentPassword?"border-red-500":"border-gray-300"}`,...y("currentPassword",{required:"请输入当前密码"})}),f.currentPassword&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:f.currentPassword.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["新密码 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{id:"newPassword",type:"password",className:`w-full px-3 py-2 border rounded-md ${f.newPassword?"border-red-500":"border-gray-300"}`,...y("newPassword",{required:"请输入新密码",minLength:{value:8,message:"密码长度至少为8个字符"},pattern:{value:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,message:"密码必须包含大小写字母和数字"}})}),f.newPassword&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:f.newPassword.message}),t.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"密码必须至少包含8个字符，包括大小写字母和数字"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["确认新密码 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{id:"confirmPassword",type:"password",className:`w-full px-3 py-2 border rounded-md ${f.confirmPassword?"border-red-500":"border-gray-300"}`,...y("confirmPassword",{required:"请确认新密码",validate:e=>e===v||"两次输入的密码不一致"})}),f.confirmPassword&&t.jsx("p",{className:"mt-1 text-sm text-red-500",children:f.confirmPassword.message})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[t.jsx("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>j(),disabled:n,children:"取消"}),t.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:n,children:n?"更新中...":"更新密码"})]})]})]}),t.jsx(o.x7,{position:"top-right"})]})}},48654:(e,r,s)=>{"use strict";s.r(r),s.d(r,{$$typeof:()=>i,__esModule:()=>a,default:()=>o});let t=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\profile\page.tsx`),{__esModule:a,$$typeof:i}=t,o=t.default}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,300,708,238],()=>s(26706));module.exports=t})();