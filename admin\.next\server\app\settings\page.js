(()=>{var e={};e.id=938,e.ids=[938],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},25494:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=t(50482),a=t(69108),i=t(62563),l=t.n(i),n=t(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let d=["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,78826)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\settings\\page.tsx"],m="/settings/page",x={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89747:(e,s,t)=>{Promise.resolve().then(t.bind(t,67329))},10438:(e,s,t)=>{Promise.resolve().then(t.bind(t,48917))},95444:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},99847:(e,s,t)=>{"use strict";t.d(s,{H:()=>o,a:()=>d});var r=t(95344),a=t(3729),i=t(22254),l=t(43932);let n=(0,a.createContext)(void 0);function o({children:e}){let[s,t]=(0,a.useState)(null),[o,d]=(0,a.useState)(!0),c=(0,i.useRouter)(),m=(0,i.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),s=localStorage.getItem("adminUser");e&&s?(l.Z.defaults.headers.common.Authorization=`Bearer ${e}`,t(JSON.parse(s))):"/login"!==m&&c.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[m,c]);let x=async(e,s)=>{try{let{user:r,token:a}=(await l.Z.post("/auth/login",{username:e,password:s})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),l.Z.defaults.headers.common.Authorization=`Bearer ${a}`,t(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(n.Provider,{value:{user:s,loading:o,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete l.Z.defaults.headers.common.Authorization,t(null),c.push("/login")},updateUserInfo:e=>{if(s){let r={...s,...e};t(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!s},children:e})}function d(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(95344);t(3729),t(4047);var a=t(99847),i=t(44669),l=t(22254);function n({children:e}){let{user:s,logout:t,isAuthenticated:i,loading:n}=(0,a.a)(),o=(0,l.usePathname)();return"/login"===o?r.jsx(r.Fragment,{children:e}):n?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):i?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",s?.name]}),r.jsx("button",{onClick:t,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===o?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function o({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(n,{children:e}),r.jsx(i.x7,{position:"top-right"})]})})})}},48917:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(95344),a=t(3729),i=t(60708),l=t(44669);let n={siteName:"上海留学顾问",siteDescription:"专业的留学与职业规划咨询服务",contactEmail:"<EMAIL>",contactPhone:"021-12345678",address:"上海市浦东新区张江高科技园区博云路2号",icp:"沪ICP备12345678号",seoKeywords:"留学,职业规划,考研,保研,职业转型,上海留学顾问",seoDescription:"上海留学顾问提供专业的留学申请、考研保研规划、职业发展咨询等服务，助力学生和职场人士实现人生目标。",logoUrl:"/images/logo.png",faviconUrl:"/favicon.ico",footerCopyright:"\xa9 2023 上海留学顾问 版权所有",socialMedia:{weixin:"slhgw_weixin",weibo:"slhgw_weibo",zhihu:"slhgw_zhihu"}};function o(){let[e,s]=(0,a.useState)(!1),[t,o]=(0,a.useState)(null),[d,c]=(0,a.useState)(null),{register:m,handleSubmit:x,formState:{errors:h},reset:u}=(0,i.cI)({defaultValues:n}),p=async e=>{s(!0);try{await new Promise(e=>setTimeout(e,1e3)),l.ZP.success("系统设置已成功保存"),s(!1)}catch(e){console.error("保存系统设置失败:",e),l.ZP.error("保存系统设置失败，请重试"),s(!1)}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[r.jsx("h1",{className:"text-2xl font-bold",children:"系统设置"}),r.jsx("p",{className:"text-gray-600",children:"管理网站基本信息和配置"})]}),r.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("form",{onSubmit:x(p),className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:"基本信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"siteName",className:"block text-sm font-medium text-gray-700 mb-1",children:["网站名称 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"siteName",type:"text",className:`w-full px-3 py-2 border rounded-md ${h.siteName?"border-red-500":"border-gray-300"}`,...m("siteName",{required:"请输入网站名称"})}),h.siteName&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.siteName.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"siteDescription",className:"block text-sm font-medium text-gray-700 mb-1",children:["网站描述 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"siteDescription",type:"text",className:`w-full px-3 py-2 border rounded-md ${h.siteDescription?"border-red-500":"border-gray-300"}`,...m("siteDescription",{required:"请输入网站描述"})}),h.siteDescription&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.siteDescription.message})]})]})]}),(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:"联系信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"contactEmail",className:"block text-sm font-medium text-gray-700 mb-1",children:["联系邮箱 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"contactEmail",type:"email",className:`w-full px-3 py-2 border rounded-md ${h.contactEmail?"border-red-500":"border-gray-300"}`,...m("contactEmail",{required:"请输入联系邮箱",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"请输入有效的邮箱地址"}})}),h.contactEmail&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.contactEmail.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"contactPhone",className:"block text-sm font-medium text-gray-700 mb-1",children:["联系电话 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"contactPhone",type:"text",className:`w-full px-3 py-2 border rounded-md ${h.contactPhone?"border-red-500":"border-gray-300"}`,...m("contactPhone",{required:"请输入联系电话"})}),h.contactPhone&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.contactPhone.message})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsxs)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700 mb-1",children:["公司地址 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"address",type:"text",className:`w-full px-3 py-2 border rounded-md ${h.address?"border-red-500":"border-gray-300"}`,...m("address",{required:"请输入公司地址"})}),h.address&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.address.message})]})]})]}),(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:"SEO设置"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"seoKeywords",className:"block text-sm font-medium text-gray-700 mb-1",children:["SEO关键词 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"seoKeywords",type:"text",className:`w-full px-3 py-2 border rounded-md ${h.seoKeywords?"border-red-500":"border-gray-300"}`,...m("seoKeywords",{required:"请输入SEO关键词"})}),h.seoKeywords&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.seoKeywords.message}),r.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"多个关键词用英文逗号分隔"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"seoDescription",className:"block text-sm font-medium text-gray-700 mb-1",children:["SEO描述 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"seoDescription",rows:3,className:`w-full px-3 py-2 border rounded-md ${h.seoDescription?"border-red-500":"border-gray-300"}`,...m("seoDescription",{required:"请输入SEO描述"})}),h.seoDescription&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.seoDescription.message})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"icp",className:"block text-sm font-medium text-gray-700 mb-1",children:"ICP备案号"}),r.jsx("input",{id:"icp",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...m("icp")})]})]})]}),(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:"网站资源"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"网站Logo"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("input",{id:"logo",type:"file",accept:"image/*",className:"hidden",onChange:e=>{let s=e.target.files?.[0];s&&o(URL.createObjectURL(s))}}),r.jsx("button",{type:"button",onClick:()=>document.getElementById("logo")?.click(),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"选择Logo"}),r.jsx("div",{className:"h-12 w-24 bg-gray-200 rounded overflow-hidden flex items-center justify-center",children:t?r.jsx("img",{src:t,alt:"Logo预览",className:"h-full w-full object-contain"}):r.jsx("span",{className:"text-gray-500 text-xs",children:"Logo预览"})})]}),r.jsx("input",{type:"hidden",...m("logoUrl")})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"网站图标 (Favicon)"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[r.jsx("input",{id:"favicon",type:"file",accept:"image/x-icon,image/png",className:"hidden",onChange:e=>{let s=e.target.files?.[0];s&&c(URL.createObjectURL(s))}}),r.jsx("button",{type:"button",onClick:()=>document.getElementById("favicon")?.click(),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"选择图标"}),r.jsx("div",{className:"h-8 w-8 bg-gray-200 rounded overflow-hidden flex items-center justify-center",children:d?r.jsx("img",{src:d,alt:"Favicon预览",className:"h-full w-full object-contain"}):r.jsx("span",{className:"text-gray-500 text-xs",children:"图标"})})]}),r.jsx("input",{type:"hidden",...m("faviconUrl")})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsxs)("label",{htmlFor:"footerCopyright",className:"block text-sm font-medium text-gray-700 mb-1",children:["页脚版权信息 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"footerCopyright",type:"text",className:`w-full px-3 py-2 border rounded-md ${h.footerCopyright?"border-red-500":"border-gray-300"}`,...m("footerCopyright",{required:"请输入页脚版权信息"})}),h.footerCopyright&&r.jsx("p",{className:"mt-1 text-sm text-red-500",children:h.footerCopyright.message})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:"社交媒体"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"weixin",className:"block text-sm font-medium text-gray-700 mb-1",children:"微信公众号"}),r.jsx("input",{id:"weixin",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...m("socialMedia.weixin")})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"weibo",className:"block text-sm font-medium text-gray-700 mb-1",children:"微博"}),r.jsx("input",{id:"weibo",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...m("socialMedia.weibo")})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"zhihu",className:"block text-sm font-medium text-gray-700 mb-1",children:"知乎"}),r.jsx("input",{id:"zhihu",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...m("socialMedia.zhihu")})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[r.jsx("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>u(n),disabled:e,children:"重置"}),r.jsx("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:e,children:e?"保存中...":"保存设置"})]})]})}),r.jsx(l.x7,{position:"top-right"})]})}},43932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a,h:()=>r});let r=t(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},82917:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:i}=r,l=r.default},78826:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\settings\page.tsx`),{__esModule:a,$$typeof:i}=r,l=r.default},4047:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,606,708],()=>t(25494));module.exports=r})();