(()=>{var e={};e.id=360,e.ids=[360],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},34299:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(50482),a=s(69108),l=s(62563),i=s.n(l),n=s(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d=["",{children:["team",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,41052)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\team\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\team\\new\\page.tsx"],m="/team/new/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/team/new/page",pathname:"/team/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},75375:(e,t,s)=>{Promise.resolve().then(s.bind(s,11029))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>o,a:()=>d});var r=s(95344),a=s(3729),l=s(22254),i=s(43932);let n=(0,a.createContext)(void 0);function o({children:e}){let[t,s]=(0,a.useState)(null),[o,d]=(0,a.useState)(!0),c=(0,l.useRouter)(),m=(0,l.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(i.Z.defaults.headers.common.Authorization=`Bearer ${e}`,s(JSON.parse(t))):"/login"!==m&&c.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[m,c]);let u=async(e,t)=>{try{let{user:r,token:a}=(await i.Z.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),i.Z.defaults.headers.common.Authorization=`Bearer ${a}`,s(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(n.Provider,{value:{user:t,loading:o,login:u,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.Z.defaults.headers.common.Authorization,s(null),c.push("/login")},updateUserInfo:e=>{if(t){let r={...t,...e};s(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!t},children:e})}function d(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(95344);s(3729),s(4047);var a=s(99847),l=s(44669),i=s(22254);function n({children:e}){let{user:t,logout:s,isAuthenticated:l,loading:n}=(0,a.a)(),o=(0,i.usePathname)();return"/login"===o?r.jsx(r.Fragment,{children:e}):n?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):l?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===o?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${o.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function o({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(a.H,{children:[r.jsx(n,{children:e}),r.jsx(l.x7,{position:"top-right"})]})})})}},11029:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(95344),a=s(3729),l=s(22254),i=s(60708),n=s(44669),o=s(32456),d=s(20783),c=s.n(d),m=s(43932);function u(){let e=(0,l.useRouter)(),{register:t,handleSubmit:s,formState:{errors:d},setValue:u}=(0,i.cI)(),[x,h]=(0,a.useState)(!1),[p,b]=(0,a.useState)(null),g=async t=>{h(!0);let s=new FormData;s.append("name",t.name),s.append("title",t.title),t.avatar&&t.avatar[0]&&s.append("avatar",t.avatar[0]),s.append("department",t.department),s.append("order",t.order.toString()),s.append("bio",t.bio),s.append("status",t.status);try{await m.h.post("/team",s,{headers:{"Content-Type":"multipart/form-data"}}),n.ZP.success("团队成员创建成功！"),e.push("/team")}catch(e){console.error("创建团队成员失败:",e),n.ZP.error("创建团队成员失败，请稍后再试。")}finally{h(!1)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[r.jsx(n.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-800",children:"添加新团队成员"}),r.jsx(c(),{href:"/team",children:(0,r.jsxs)("button",{className:"bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(o.Ao2,{className:"mr-2"}),"返回列表"]})})]}),(0,r.jsxs)("form",{onSubmit:s(g),className:"bg-white p-8 rounded-xl shadow-xl space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:["姓名 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"text",id:"name",...t("name",{required:"姓名不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.name?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),d.name&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.name.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["职位 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"text",id:"title",...t("title",{required:"职位不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.title?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),d.title&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.title.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"avatar",className:"block text-sm font-medium text-gray-700 mb-1",children:["头像 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"mt-1 flex items-center space-x-4",children:[r.jsx("span",{className:"inline-block h-20 w-20 rounded-full overflow-hidden bg-gray-100",children:p?r.jsx("img",{src:p,alt:"头像预览",className:"h-full w-full object-cover"}):r.jsx("div",{className:"h-full w-full flex items-center justify-center text-gray-400",children:r.jsx(o.Yjd,{className:"h-8 w-8"})})}),r.jsx("label",{htmlFor:"avatar-upload",className:"cursor-pointer bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out text-sm",children:"选择头像"}),r.jsx("input",{id:"avatar-upload",type:"file",accept:"image/*",...t("avatar",{required:"头像不能为空"}),className:"sr-only",onChange:e=>{if(e.target.files&&e.target.files[0]){let t=e.target.files[0];b(URL.createObjectURL(t)),u("avatar",e.target.files)}}})]}),d.avatar&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.avatar.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"department",className:"block text-sm font-medium text-gray-700 mb-1",children:["部门 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"text",id:"department",...t("department",{required:"部门不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.department?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`,placeholder:"例如: 留学规划部"}),d.department&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.department.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"order",className:"block text-sm font-medium text-gray-700 mb-1",children:["排序 (数字越小越靠前) ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"number",id:"order",...t("order",{required:"排序不能为空",valueAsNumber:!0,min:{value:0,message:"排序值不能小于0"}}),className:`mt-1 block w-full px-4 py-2 border ${d.order?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`,defaultValue:0}),d.order&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.order.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-1",children:["个人简介 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("textarea",{id:"bio",rows:5,...t("bio",{required:"个人简介不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.bio?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}),d.bio&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.bio.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-1",children:["状态 ",r.jsx("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"status",...t("status",{required:"状态不能为空"}),className:`mt-1 block w-full px-4 py-2 border ${d.status?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`,defaultValue:"active",children:[r.jsx("option",{value:"active",children:"启用"}),r.jsx("option",{value:"inactive",children:"禁用"})]}),d.status&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:d.status.message})]}),r.jsx("div",{className:"flex justify-end pt-4",children:(0,r.jsxs)("button",{type:"submit",disabled:x,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[x?(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):r.jsx(o.mW3,{className:"mr-2"}),x?"正在添加...":"添加成员"]})})]})]})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=r},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:l}=r,i=r.default},41052:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\team\new\page.tsx`),{__esModule:a,$$typeof:l}=r,i=r.default},4047:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606,783,708,456],()=>s(34299));module.exports=r})();