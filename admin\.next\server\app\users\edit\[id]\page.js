(()=>{var e={};e.id=95,e.ids=[95],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},91862:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=r(50482),a=r(69108),i=r(62563),l=r.n(i),n=r(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(s,o);let d=["",{children:["users",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3468)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\users\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\users\\edit\\[id]\\page.tsx"],u="/users/edit/[id]/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/users/edit/[id]/page",pathname:"/users/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89747:(e,s,r)=>{Promise.resolve().then(r.bind(r,67329))},22552:(e,s,r)=>{Promise.resolve().then(r.bind(r,90665))},95444:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},99847:(e,s,r)=>{"use strict";r.d(s,{H:()=>o,a:()=>d});var t=r(95344),a=r(3729),i=r(22254),l=r(43932);let n=(0,a.createContext)(void 0);function o({children:e}){let[s,r]=(0,a.useState)(null),[o,d]=(0,a.useState)(!0),c=(0,i.useRouter)(),u=(0,i.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),s=localStorage.getItem("adminUser");e&&s?(l.h.defaults.headers.common.Authorization=`Bearer ${e}`,r(JSON.parse(s))):"/login"!==u&&c.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[u,c]);let m=async(e,s)=>{try{let{user:t,token:a}=(await l.h.post("/auth/login",{username:e,password:s})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(t)),l.h.defaults.headers.common.Authorization=`Bearer ${a}`,r(t),t}catch(e){throw console.error("登录失败:",e),e}};return t.jsx(n.Provider,{value:{user:s,loading:o,login:m,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete l.h.defaults.headers.common.Authorization,r(null),c.push("/login")},updateUserInfo:e=>{if(s){let t={...s,...e};r(t),localStorage.setItem("adminUser",JSON.stringify(t))}},isAuthenticated:!!s},children:e})}function d(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(95344);r(3729),r(4047);var a=r(99847),i=r(44669),l=r(20783),n=r.n(l),o=r(22254);function d({children:e}){let{user:s,logout:r,isAuthenticated:i,loading:l}=(0,a.a)(),d=(0,o.usePathname)();return"/login"===d?t.jsx(t.Fragment,{children:e}):l?t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:t.jsx("div",{className:"text-lg",children:"加载中..."})}):i?(0,t.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[t.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[t.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{className:"text-sm",children:["欢迎，",s?.name]}),t.jsx("button",{onClick:r,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,t.jsxs)("div",{className:"flex pt-16",children:[t.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,t.jsxs)("ul",{className:"space-y-2 mt-4",children:[t.jsx("li",{children:t.jsx(n(),{href:"/dashboard",className:`block p-2 hover:bg-gray-200 rounded ${"/dashboard"===d?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),t.jsx("li",{children:t.jsx(n(),{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),t.jsx("li",{children:t.jsx(n(),{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),t.jsx("li",{children:t.jsx(n(),{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),t.jsx("li",{children:t.jsx(n(),{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),t.jsx("li",{children:t.jsx(n(),{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),t.jsx("li",{children:t.jsx(n(),{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),t.jsx("li",{children:t.jsx(n(),{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),t.jsx("li",{children:t.jsx(n(),{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),t.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,t.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[t.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),t.jsx(n(),{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function c({children:e}){return t.jsx("html",{lang:"zh",children:t.jsx("body",{children:(0,t.jsxs)(a.H,{children:[t.jsx(d,{children:e}),t.jsx(i.x7,{position:"top-right"})]})})})}},90665:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var t=r(95344),a=r(3729),i=r(22254),l=r(20783),n=r.n(l),o=r(44669),d=r(99847),c=r(43932),u=r(32456);function m(){let e=(0,i.useRouter)(),s=(0,i.useParams)(),{user:r}=(0,d.a)(),[l,m]=(0,a.useState)(!0),[x,h]=(0,a.useState)(!1),[p,g]=(0,a.useState)(null),[b,f]=(0,a.useState)({username:"",email:"",name:"",role:"editor",status:"active",password:"",confirmPassword:""});(0,a.useEffect)(()=>{s.id&&j()},[s.id]);let j=async()=>{try{let e=(await c.Z.get(`/users/${s.id}`)).data.data;g(e),f({username:e.username,email:e.email,name:e.name,role:e.role,status:e.status,password:"",confirmPassword:""})}catch(s){o.ZP.error("获取用户信息失败"),e.push("/users")}finally{m(!1)}},v=e=>{let{name:s,value:r}=e.target;f(e=>({...e,[s]:r}))},y=()=>b.username.trim()?b.email.trim()?b.name.trim()?b.password&&b.password!==b.confirmPassword?(o.ZP.error("两次输入的密码不一致"),!1):!b.password||!(b.password.length<6)||(o.ZP.error("密码长度至少6位"),!1):(o.ZP.error("请输入姓名"),!1):(o.ZP.error("请输入邮箱"),!1):(o.ZP.error("请输入用户名"),!1),N=async r=>{if(r.preventDefault(),y()){h(!0);try{let{confirmPassword:r,...t}=b;t.password||delete t.password,await c.Z.put(`/users/${s.id}`,t),o.ZP.success("用户更新成功"),e.push("/users")}catch(e){o.ZP.error(e.response?.data?.message||"更新用户失败")}finally{h(!1)}}};return l?t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsx("div",{className:"text-center py-10",children:t.jsx("p",{className:"text-lg text-gray-500",children:"正在加载用户信息..."})})}):p?(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[t.jsx(o.x7,{position:"top-center"}),t.jsx("div",{className:"flex items-center justify-between mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx(n(),{href:"/users",className:"mr-4 p-2 hover:bg-gray-100 rounded-lg transition-colors",children:t.jsx(u.Ao2,{className:"text-gray-600",size:20})}),(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 flex items-center",children:[t.jsx(u.fzv,{className:"mr-3 text-indigo-600"})," 编辑用户"]})]})}),t.jsx("div",{className:"bg-white rounded-lg shadow-lg p-6",children:(0,t.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:["用户名 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx(u.fzv,{className:"text-gray-400"})}),t.jsx("input",{type:"text",id:"username",name:"username",value:b.username,onChange:v,className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",placeholder:"请输入用户名",required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:["邮箱 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx(u.Imn,{className:"text-gray-400"})}),t.jsx("input",{type:"email",id:"email",name:"email",value:b.email,onChange:v,className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",placeholder:"请输入邮箱",required:!0})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:["姓名 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx("input",{type:"text",id:"name",name:"name",value:b.name,onChange:v,className:"block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",placeholder:"请输入姓名",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700 mb-2",children:["角色 ",t.jsx("span",{className:"text-red-500",children:"*"})]}),(0,t.jsxs)("select",{id:"role",name:"role",value:b.role,onChange:v,className:"block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",required:!0,disabled:r?.role!=="admin",children:[t.jsx("option",{value:"viewer",children:"查看者"}),t.jsx("option",{value:"editor",children:"编辑"}),r?.role==="admin"&&t.jsx("option",{value:"admin",children:"管理员"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-2",children:"状态"}),(0,t.jsxs)("select",{id:"status",name:"status",value:b.status,onChange:v,className:"block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",disabled:r?.role!=="admin",children:[t.jsx("option",{value:"active",children:"激活"}),t.jsx("option",{value:"inactive",children:"禁用"})]})]})]}),(0,t.jsxs)("div",{className:"border-t pt-6",children:[t.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"修改密码（可选）"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"新密码"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx(u.UIZ,{className:"text-gray-400"})}),t.jsx("input",{type:"password",id:"password",name:"password",value:b.password,onChange:v,className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",placeholder:"留空则不修改密码"})]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"确认新密码"}),(0,t.jsxs)("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx(u.UIZ,{className:"text-gray-400"})}),t.jsx("input",{type:"password",id:"confirmPassword",name:"confirmPassword",value:b.confirmPassword,onChange:v,className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500",placeholder:"请再次输入新密码"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4",children:[t.jsx(n(),{href:"/users",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors",children:"取消"}),t.jsx("button",{type:"submit",disabled:x,className:"px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center",children:x?(0,t.jsxs)(t.Fragment,{children:[t.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"保存中..."]}):(0,t.jsxs)(t.Fragment,{children:[t.jsx(u.mW3,{className:"mr-2"}),"保存更改"]})})]})]})})]}):t.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"text-center py-10",children:[t.jsx("p",{className:"text-lg text-gray-500",children:"用户不存在"}),t.jsx(n(),{href:"/users",className:"text-indigo-600 hover:underline",children:"返回用户列表"})]})})}},43932:(e,s,r)=>{"use strict";r.d(s,{Z:()=>a,h:()=>t});let t=r(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});t.interceptors.request.use(e=>e,e=>Promise.reject(e)),t.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let a=t},82917:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let t=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:a,$$typeof:i}=t,l=t.default},3468:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>i,__esModule:()=>a,default:()=>l});let t=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\users\edit\[id]\page.tsx`),{__esModule:a,$$typeof:i}=t,l=t.default},4047:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,300,456],()=>r(91862));module.exports=t})();