(()=>{var e={};e.id=110,e.ids=[110],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},69165:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(50482),i=t(69108),a=t(62563),n=t.n(a),l=t(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["users",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69604)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\users\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\users\\new\\page.tsx"],u="/users/new/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/users/new/page",pathname:"/users/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},89747:(e,s,t)=>{Promise.resolve().then(t.bind(t,67329))},90025:(e,s,t)=>{Promise.resolve().then(t.bind(t,37054))},95444:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},99847:(e,s,t)=>{"use strict";t.d(s,{H:()=>o,a:()=>d});var r=t(95344),i=t(3729),a=t(22254),n=t(43932);let l=(0,i.createContext)(void 0);function o({children:e}){let[s,t]=(0,i.useState)(null),[o,d]=(0,i.useState)(!0),c=(0,a.useRouter)(),u=(0,a.usePathname)();(0,i.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),s=localStorage.getItem("adminUser");e&&s?(n.h.defaults.headers.common.Authorization=`Bearer ${e}`,t(JSON.parse(s))):"/login"!==u&&c.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[u,c]);let m=async(e,s)=>{try{let{user:r,token:i}=(await n.h.post("/auth/login",{username:e,password:s})).data;return localStorage.setItem("adminToken",i),localStorage.setItem("adminUser",JSON.stringify(r)),n.h.defaults.headers.common.Authorization=`Bearer ${i}`,t(r),r}catch(e){throw console.error("登录失败:",e),e}};return r.jsx(l.Provider,{value:{user:s,loading:o,login:m,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete n.h.defaults.headers.common.Authorization,t(null),c.push("/login")},updateUserInfo:e=>{if(s){let r={...s,...e};t(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!s},children:e})}function d(){let e=(0,i.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var r=t(95344);t(3729),t(4047);var i=t(99847),a=t(44669),n=t(20783),l=t.n(n),o=t(22254);function d({children:e}){let{user:s,logout:t,isAuthenticated:a,loading:n}=(0,i.a)(),d=(0,o.usePathname)();return"/login"===d?r.jsx(r.Fragment,{children:e}):n?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):a?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",s?.name]}),r.jsx("button",{onClick:t,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx(l(),{href:"/dashboard",className:`block p-2 hover:bg-gray-200 rounded ${"/dashboard"===d?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx(l(),{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx(l(),{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx(l(),{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${d.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx(l(),{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function c({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(i.H,{children:[r.jsx(d,{children:e}),r.jsx(a.x7,{position:"top-right"})]})})})}},37054:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var r=t(95344),i=t(3729),a=t(60708),n=t(44669),l=t(20783),o=t.n(l),d=t(22254),c=t(32456),u=t(43932);let m=()=>{let{register:e,handleSubmit:s,formState:{errors:t},reset:l}=(0,a.cI)(),m=(0,d.useRouter)(),[x,h]=(0,i.useState)(!1),p=async e=>{h(!0);try{await u.h.post("/users",e),n.ZP.success("用户添加成功！"),l(),setTimeout(()=>{m.push("/users")},1500)}catch(e){e.response&&e.response.data&&e.response.data.message?n.ZP.error(`添加用户失败: ${e.response.data.message}`):n.ZP.error("添加用户失败，请稍后再试。"),console.error("添加用户失败:",e)}h(!1)};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[r.jsx(n.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 flex items-center",children:[r.jsx(c.zy7,{className:"mr-3 text-indigo-600"})," 添加新用户"]}),r.jsx(o(),{href:"/users",children:(0,r.jsxs)("button",{className:"bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(c.Ao2,{className:"mr-2"})," 返回用户列表"]})})]}),(0,r.jsxs)("form",{onSubmit:s(p),className:"bg-white p-8 rounded-lg shadow-xl space-y-6",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-1",children:"用户名"}),r.jsx("input",{id:"username",type:"text",...e("username",{required:"用户名为必填项"}),className:`w-full px-4 py-2 border ${t.username?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}),t.username&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.username.message})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"姓名"}),r.jsx("input",{id:"name",type:"text",...e("name",{required:"姓名为必填项"}),className:`w-full px-4 py-2 border ${t.name?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}),t.name&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.name.message})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"邮箱"}),r.jsx("input",{id:"email",type:"email",...e("email",{required:"邮箱为必填项",pattern:{value:/^\S+@\S+$/i,message:"请输入有效的邮箱地址"}}),className:`w-full px-4 py-2 border ${t.email?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}),t.email&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.email.message})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700 mb-1",children:"角色"}),(0,r.jsxs)("select",{id:"role",...e("role",{required:"角色为必选项"}),className:`w-full px-4 py-2 border ${t.role?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white`,children:[r.jsx("option",{value:"viewer",children:"查看者"}),r.jsx("option",{value:"editor",children:"编辑"}),r.jsx("option",{value:"admin",children:"管理员"})]}),t.role&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.role.message})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"初始密码 (可选)"}),r.jsx("input",{id:"password",type:"password",...e("password",{minLength:{value:6,message:"密码至少需要6个字符"}}),className:`w-full px-4 py-2 border ${t.password?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`,placeholder:"留空则由系统生成"}),t.password&&r.jsx("p",{className:"mt-1 text-xs text-red-500",children:t.password.message})]}),r.jsx("div",{className:"flex justify-end pt-4",children:(0,r.jsxs)("button",{type:"submit",disabled:x,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[x?r.jsx(c.dAq,{className:"animate-spin mr-2"}):r.jsx(c.mW3,{className:"mr-2"}),x?"正在保存...":"保存用户"]})})]})]})}},43932:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i,h:()=>r});let r=t(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let i=r},82917:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>a,__esModule:()=>i,default:()=>n});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:i,$$typeof:a}=r,n=r.default},69604:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>a,__esModule:()=>i,default:()=>n});let r=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\users\new\page.tsx`),{__esModule:i,$$typeof:a}=r,n=r.default},4047:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,300,708,456],()=>t(69165));module.exports=r})();