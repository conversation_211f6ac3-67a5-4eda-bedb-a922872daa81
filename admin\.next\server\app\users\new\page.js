(()=>{var e={};e.id=110,e.ids=[110],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},69165:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>o.a,__next_app__:()=>c,originalPathname:()=>m,pages:()=>u,routeModule:()=>x,tree:()=>l});var t=r(50482),i=r(69108),a=r(62563),o=r.n(a),n=r(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let l=["",{children:["users",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69604)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\users\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],u=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\users\\new\\page.tsx"],m="/users/new/page",c={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/users/new/page",pathname:"/users/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},90025:(e,s,r)=>{Promise.resolve().then(r.bind(r,37054))},37054:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(95344),i=r(3729),a=r(60708),o=r(44669),n=r(20783),d=r.n(n),l=r(22254),u=r(32456),m=r(43932);let c=()=>{let{register:e,handleSubmit:s,formState:{errors:r},reset:n}=(0,a.cI)(),c=(0,l.useRouter)(),[x,p]=(0,i.useState)(!1),g=async e=>{p(!0);try{await m.h.post("/users",e),o.ZP.success("用户添加成功！"),n(),setTimeout(()=>{c.push("/users")},1500)}catch(e){e.response&&e.response.data&&e.response.data.message?o.ZP.error(`添加用户失败: ${e.response.data.message}`):o.ZP.error("添加用户失败，请稍后再试。"),console.error("添加用户失败:",e)}p(!1)};return(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[t.jsx(o.x7,{position:"top-center"}),(0,t.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 flex items-center",children:[t.jsx(u.zy7,{className:"mr-3 text-indigo-600"})," 添加新用户"]}),t.jsx(d(),{href:"/users",children:(0,t.jsxs)("button",{className:"bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[t.jsx(u.Ao2,{className:"mr-2"})," 返回用户列表"]})})]}),(0,t.jsxs)("form",{onSubmit:s(g),className:"bg-white p-8 rounded-lg shadow-xl space-y-6",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-1",children:"用户名"}),t.jsx("input",{id:"username",type:"text",...e("username",{required:"用户名为必填项"}),className:`w-full px-4 py-2 border ${r.username?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}),r.username&&t.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.username.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"姓名"}),t.jsx("input",{id:"name",type:"text",...e("name",{required:"姓名为必填项"}),className:`w-full px-4 py-2 border ${r.name?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}),r.name&&t.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.name.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"邮箱"}),t.jsx("input",{id:"email",type:"email",...e("email",{required:"邮箱为必填项",pattern:{value:/^\S+@\S+$/i,message:"请输入有效的邮箱地址"}}),className:`w-full px-4 py-2 border ${r.email?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}),r.email&&t.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.email.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700 mb-1",children:"角色"}),(0,t.jsxs)("select",{id:"role",...e("role",{required:"角色为必选项"}),className:`w-full px-4 py-2 border ${r.role?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white`,children:[t.jsx("option",{value:"viewer",children:"查看者"}),t.jsx("option",{value:"editor",children:"编辑"}),t.jsx("option",{value:"admin",children:"管理员"})]}),r.role&&t.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.role.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"初始密码 (可选)"}),t.jsx("input",{id:"password",type:"password",...e("password",{minLength:{value:6,message:"密码至少需要6个字符"}}),className:`w-full px-4 py-2 border ${r.password?"border-red-500":"border-gray-300"} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`,placeholder:"留空则由系统生成"}),r.password&&t.jsx("p",{className:"mt-1 text-xs text-red-500",children:r.password.message})]}),t.jsx("div",{className:"flex justify-end pt-4",children:(0,t.jsxs)("button",{type:"submit",disabled:x,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[x?t.jsx(u.dAq,{className:"animate-spin mr-2"}):t.jsx(u.mW3,{className:"mr-2"}),x?"正在保存...":"保存用户"]})})]})]})}},69604:(e,s,r)=>{"use strict";r.r(s),r.d(s,{$$typeof:()=>a,__esModule:()=>i,default:()=>o});let t=(0,r(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\users\new\page.tsx`),{__esModule:i,$$typeof:a}=t,o=t.default}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,300,708,456,238],()=>r(69165));module.exports=t})();