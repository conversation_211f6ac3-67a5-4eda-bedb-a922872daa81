(()=>{var e={};e.id=240,e.ids=[240],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},43698:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(50482),i=s(69108),a=s(62563),n=s.n(a),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,87412)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\users\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,82917)),"C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\Desktop\\slhgw\\admin\\app\\users\\page.tsx"],x="/users/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/users/page",pathname:"/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},17316:(e,t,s)=>{Promise.resolve().then(s.bind(s,93774))},93774:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(95344),i=s(3729),a=s(20783),n=s.n(a),l=s(44669),o=s(43932),d=s(32456);function c(){let[e,t]=(0,i.useState)([]),[s,a]=(0,i.useState)(!0),[c,x]=(0,i.useState)(""),[u,p]=(0,i.useState)(""),m=async()=>{a(!0);try{let e=await o.Z.get("/users",{params:{search:c,role:u}});t(e.data)}catch(e){l.ZP.error("获取用户列表失败"),console.error("获取用户列表失败:",e)}a(!1)};(0,i.useEffect)(()=>{m()},[]),(0,i.useEffect)(()=>{let e=setTimeout(()=>{m()},300);return()=>clearTimeout(e)},[c,u]);let h=async(s,r)=>{try{let i="active"===r?"inactive":"active";await o.Z.patch(`/users/${s}/status`,{status:i}),t(e.map(e=>e.id===s?{...e,status:i}:e)),l.ZP.success(`用户状态已更新为${"active"===i?"激活":"禁用"}`)}catch(e){l.ZP.error("更新失败，请重试"),console.error("更新用户状态失败:",e)}},g=async s=>{if(window.confirm("确定要删除此用户吗？此操作不可恢复。"))try{await o.Z.delete(`/users/${s}`),t(e.filter(e=>e.id!==s)),l.ZP.success("用户删除成功")}catch(e){l.ZP.error("删除用户失败"),console.error("删除用户失败:",e)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[r.jsx(l.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 flex items-center",children:[r.jsx(d.WY8,{className:"mr-3 text-indigo-600"})," 用户管理"]}),r.jsx(n(),{href:"/users/new",children:(0,r.jsxs)("button",{className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[r.jsx(d.O9D,{className:"mr-2"})," 添加用户"]})})]}),r.jsx("div",{className:"mb-6 p-4 bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700",children:"搜索用户"}),(0,r.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:r.jsx(d.jRj,{className:"text-gray-400"})}),r.jsx("input",{type:"text",id:"search",className:"focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-2",placeholder:"按用户名、姓名或邮箱搜索...",value:c,onChange:e=>x(e.target.value)})]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{htmlFor:"roleFilter",className:"block text-sm font-medium text-gray-700",children:"按角色筛选"}),(0,r.jsxs)("select",{id:"roleFilter",className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",value:u,onChange:e=>p(e.target.value),children:[r.jsx("option",{value:"",children:"所有角色"}),r.jsx("option",{value:"admin",children:"管理员"}),r.jsx("option",{value:"editor",children:"编辑"}),r.jsx("option",{value:"viewer",children:"查看者"})]})]})]})}),s?r.jsx("div",{className:"text-center py-10",children:r.jsx("p",{className:"text-lg text-gray-500",children:"正在加载用户列表..."})}):0===e.length?(0,r.jsxs)("div",{className:"text-center py-10 bg-white rounded-lg shadow",children:[r.jsx(d.$Rx,{className:"mx-auto text-gray-400 text-5xl mb-4"}),r.jsx("p",{className:"text-lg text-gray-500",children:"未找到用户。"}),(0,r.jsxs)("p",{className:"text-sm text-gray-400",children:["尝试调整搜索词或筛选条件，或",r.jsx(n(),{href:"/users/new",className:"text-indigo-600 hover:underline",children:"添加新用户"}),"。"]})]}):r.jsx("div",{className:"bg-white shadow-xl rounded-lg overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"角色"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"最后登录"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-center",children:"操作"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("div",{className:"flex items-center",children:(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),r.jsx("div",{className:"text-sm text-gray-500",children:e.email})]})})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"admin"===e.role?"bg-purple-100 text-purple-800":"editor"===e.role?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:"admin"===e.role?"管理员":"editor"===e.role?"编辑":"查看者"})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.lastLogin}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsx("span",{className:`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${"active"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:"active"===e.status?"激活":"禁用"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-center",children:[r.jsx(n(),{href:`/users/edit/${e.id}`,className:"text-indigo-600 hover:text-indigo-800 transition duration-150 p-1 rounded-full hover:bg-indigo-100 inline-block mr-2",title:"编辑用户",children:r.jsx(d.vPQ,{size:18})}),r.jsx("button",{onClick:()=>h(e.id,e.status),className:`p-1 rounded-full transition duration-150 ${"active"===e.status?"text-red-600 hover:text-red-800 hover:bg-red-100":"text-green-600 hover:text-green-800 hover:bg-green-100"}`,title:"active"===e.status?"禁用用户":"激活用户",children:"active"===e.status?r.jsx(d.X$5,{size:20}):r.jsx(d.gU9,{size:20})}),r.jsx("button",{onClick:()=>g(e.id),className:"text-red-600 hover:text-red-800 transition duration-150 p-1 rounded-full hover:bg-red-100 ml-2",title:"删除用户",children:r.jsx(d.Ybf,{size:18})})]})]},e.id))})]})})]})}},87412:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>i,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\users\page.tsx`),{__esModule:i,$$typeof:a}=r,n=r.default}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,606,783,456,238],()=>s(43698));module.exports=r})();