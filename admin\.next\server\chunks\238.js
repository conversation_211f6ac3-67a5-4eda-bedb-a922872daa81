exports.id=238,exports.ids=[238],exports.modules={89747:(e,t,s)=>{Promise.resolve().then(s.bind(s,67329))},95444:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,2583,23)),Promise.resolve().then(s.t.bind(s,26840,23)),Promise.resolve().then(s.t.bind(s,38771,23)),Promise.resolve().then(s.t.bind(s,13225,23)),Promise.resolve().then(s.t.bind(s,9295,23)),Promise.resolve().then(s.t.bind(s,43982,23))},99847:(e,t,s)=>{"use strict";s.d(t,{H:()=>i,a:()=>c});var r=s(95344),l=s(3729),a=s(22254),n=s(43932);let o=(0,l.createContext)(void 0);function i({children:e}){let[t,s]=(0,l.useState)(null),[i,c]=(0,l.useState)(!0),[d,h]=(0,l.useState)(!1),u=(0,a.useRouter)(),m=(0,a.usePathname)();(0,l.useEffect)(()=>{let e=async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(e&&t&&"undefined"!==t&&"null"!==t)try{n.Z.defaults.headers.common.Authorization=`Bearer ${e}`;try{let e=await n.Z.get("/auth/me");if(e.data.success&&e.data.data.user)s(e.data.data.user),localStorage.setItem("adminUser",JSON.stringify(e.data.data.user));else throw Error("Token验证失败")}catch(e){console.error("Token验证失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete n.Z.defaults.headers.common.Authorization,s(null)}}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),s(null)}}catch(e){console.error("认证检查失败:",e),s(null)}finally{c(!1),h(!0)}};d||e()},[d]),(0,l.useEffect)(()=>{!d||i||t||"/login"===m||u.push("/login")},[d,i,t,m,u]);let x=async(e,t)=>{try{console.log("AuthContext: 发送登录请求",{username:e});let r=await n.Z.post("/auth/login",{username:e,password:t});if(console.log("AuthContext: 收到响应",r.data),!r.data||!r.data.data)throw Error("API响应格式错误");let{user:l,token:a}=r.data.data;if(!l||!a)throw Error("响应中缺少用户信息或令牌");return console.log("AuthContext: 解析的用户数据",{user:l,token:a}),localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(l)),n.Z.defaults.headers.common.Authorization=`Bearer ${a}`,s(l),console.log("AuthContext: 登录成功，用户状态已更新"),l}catch(e){throw console.error("AuthContext: 登录失败",e),e}};return r.jsx(o.Provider,{value:{user:t,loading:i,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete n.Z.defaults.headers.common.Authorization,s(null),u.push("/login")},updateUserInfo:e=>{if(t){let r={...t,...e};s(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!t},children:e})}function c(){let e=(0,l.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},67329:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(95344);s(3729),s(4047);var l=s(99847),a=s(44669),n=s(22254);function o({children:e}){let{user:t,logout:s,isAuthenticated:a,loading:o}=(0,l.a)(),i=(0,n.usePathname)();return"/login"===i?r.jsx(r.Fragment,{children:e}):o?r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:r.jsx("div",{className:"text-lg",children:"加载中..."})}):a?(0,r.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[r.jsx("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("span",{className:"text-sm",children:["欢迎，",t?.name]}),r.jsx("button",{onClick:s,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,r.jsxs)("div",{className:"flex pt-16",children:[r.jsx("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,r.jsxs)("ul",{className:"space-y-2 mt-4",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:`block p-2 hover:bg-gray-200 rounded ${"/"===i?"bg-blue-100 text-blue-600":""}`,children:"仪表盘"})}),r.jsx("li",{children:r.jsx("a",{href:"/users",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/users")?"bg-blue-100 text-blue-600":""}`,children:"用户管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/articles",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""}`,children:"文章管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/services",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/content/services")?"bg-blue-100 text-blue-600":""}`,children:"服务管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/cases",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""}`,children:"案例管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/banners",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""}`,children:"Banner管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/content/faq",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""}`,children:"FAQ管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/consultants",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/consultants")?"bg-blue-100 text-blue-600":""}`,children:"咨询师管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/appointments",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/appointments")?"bg-blue-100 text-blue-600":""}`,children:"预约管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/inquiries",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""}`,children:"客户咨询"})}),r.jsx("li",{children:r.jsx("a",{href:"/team",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/team")?"bg-blue-100 text-blue-600":""}`,children:"团队管理"})}),r.jsx("li",{children:r.jsx("a",{href:"/settings",className:`block p-2 hover:bg-gray-200 rounded ${i.startsWith("/settings")?"bg-blue-100 text-blue-600":""}`,children:"系统设置"})})]})}),r.jsx("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:e})]})]}):r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[r.jsx("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),r.jsx("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),r.jsx("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function i({children:e}){return r.jsx("html",{lang:"zh",children:r.jsx("body",{children:(0,r.jsxs)(l.H,{children:[r.jsx(o,{children:e}),r.jsx(a.x7,{position:"top-right"})]})})})}},43932:(e,t,s)=>{"use strict";s.d(t,{Z:()=>l,h:()=>r});let r=s(47665).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>e,e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&e.response.status,Promise.reject(e)));let l=r},82917:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>a,__esModule:()=>l,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\Desktop\slhgw\admin\app\layout.tsx`),{__esModule:l,$$typeof:a}=r,n=r.default},4047:()=>{}};