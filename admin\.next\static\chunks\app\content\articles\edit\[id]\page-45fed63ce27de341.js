(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[410],{64187:function(e,t,r){Promise.resolve().then(r.bind(r,49508))},31584:function(e,t,r){"use strict";r.d(t,{H:function(){return l},a:function(){return c}});var a=r(57437),s=r(2265),o=r(24033),i=r(30540);let n=(0,s.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(!0),u=(0,o.useRouter)(),m=(0,o.usePathname)();(0,s.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(i.h.defaults.headers.common.Authorization="Bearer ".concat(e),l(JSON.parse(t))):"/login"!==m&&u.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[m,u]);let p=async(e,t)=>{try{let{user:r,token:a}=(await i.h.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),i.h.defaults.headers.common.Authorization="Bearer ".concat(a),l(r),r}catch(e){throw console.error("登录失败:",e),e}};return(0,a.jsx)(n.Provider,{value:{user:r,loading:c,login:p,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.h.defaults.headers.common.Authorization,l(null),u.push("/login")},updateUserInfo:e=>{if(r){let t={...r,...e};l(t),localStorage.setItem("adminUser",JSON.stringify(t))}},isAuthenticated:!!r},children:t})}function c(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},49508:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return m}});var a=r(57437),s=r(2265),o=r(24033),i=r(61396),n=r.n(i),l=r(5925),c=r(31584),d=r(30540),u=r(6834);function m(){let e=(0,o.useRouter)(),t=(0,o.useParams)(),{user:r}=(0,c.a)(),[i,m]=(0,s.useState)(!0),[p,f]=(0,s.useState)(!1),[h,g]=(0,s.useState)([]),[x,y]=(0,s.useState)(null),[b,v]=(0,s.useState)({title:"",slug:"",summary:"",content:"",category_id:"",status:"draft",cover_image_url:""});(0,s.useEffect)(()=>{t.id&&(j(),w())},[t.id]);let j=async()=>{try{let e=await d.Z.get("/articles/".concat(t.id));if(e.data.success){let t=e.data.data;y(t),v({title:t.title,slug:t.slug,summary:t.summary||"",content:t.content,category_id:t.category_id||"",status:t.status,cover_image_url:t.cover_image_url||""})}}catch(t){l.ZP.error("获取文章信息失败"),e.push("/content/articles")}finally{m(!1)}},w=async()=>{try{let e=await d.Z.get("/categories");e.data.success&&g(e.data.data)}catch(e){console.error("获取分类失败:",e)}},N=e=>{let{name:t,value:r}=e.target;v(e=>({...e,[t]:r}))},k=()=>b.title.trim()?!!b.content.trim()||(l.ZP.error("请输入文章内容"),!1):(l.ZP.error("请输入文章标题"),!1),_=async r=>{if(r.preventDefault(),k()){f(!0);try{let r={...b,category_id:b.category_id||null};await d.Z.put("/articles/".concat(t.id),r),l.ZP.success("文章更新成功"),e.push("/content/articles")}catch(e){var a,s;l.ZP.error((null===(s=e.response)||void 0===s?void 0:null===(a=s.data)||void 0===a?void 0:a.message)||"更新文章失败")}finally{f(!1)}}};return i?(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsx)("div",{className:"text-center py-10",children:(0,a.jsx)("p",{className:"text-lg text-gray-500",children:"正在加载文章信息..."})})}):x?(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)(l.x7,{position:"top-center"}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(n(),{href:"/content/articles",className:"mr-4 p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(u.Ao2,{className:"text-gray-600",size:20})}),(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 flex items-center",children:[(0,a.jsx)(u.NOg,{className:"mr-3 text-blue-600"})," 编辑文章"]})]}),(0,a.jsxs)("button",{onClick:()=>{let e=window.open("","_blank");e&&(e.document.write("\n        <html>\n          <head>\n            <title>".concat(b.title,"</title>\n            <style>\n              body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }\n              h1 { color: #333; }\n              .summary { color: #666; font-style: italic; margin-bottom: 20px; }\n              .content { line-height: 1.6; }\n            </style>\n          </head>\n          <body>\n            <h1>").concat(b.title,'</h1>\n            <div class="summary">').concat(b.summary,'</div>\n            <div class="content">').concat(b.content,"</div>\n          </body>\n        </html>\n      ")),e.document.close())},className:"px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors flex items-center",children:[(0,a.jsx)(u.rDJ,{className:"mr-2"}),"预览"]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:(0,a.jsxs)("form",{onSubmit:_,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-2",children:["文章标题 ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",id:"title",name:"title",value:b.title,onChange:N,className:"block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"请输入文章标题",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"slug",className:"block text-sm font-medium text-gray-700 mb-2",children:"URL别名"}),(0,a.jsx)("input",{type:"text",id:"slug",name:"slug",value:b.slug,onChange:N,className:"block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"URL别名"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"category_id",className:"block text-sm font-medium text-gray-700 mb-2",children:"文章分类"}),(0,a.jsxs)("select",{id:"category_id",name:"category_id",value:b.category_id,onChange:N,className:"block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"",children:"请选择分类"}),h.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"cover_image_url",className:"block text-sm font-medium text-gray-700 mb-2",children:"封面图片URL"}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("input",{type:"url",id:"cover_image_url",name:"cover_image_url",value:b.cover_image_url,onChange:N,className:"flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"请输入图片URL"}),(0,a.jsx)("button",{type:"button",className:"px-4 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 transition-colors",children:(0,a.jsx)(u.LFN,{})})]})]}),(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsx)("label",{htmlFor:"summary",className:"block text-sm font-medium text-gray-700 mb-2",children:"文章摘要"}),(0,a.jsx)("textarea",{id:"summary",name:"summary",value:b.summary,onChange:N,rows:3,className:"block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"请输入文章摘要"})]}),(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-2",children:["文章内容 ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("textarea",{id:"content",name:"content",value:b.content,onChange:N,rows:15,className:"block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"请输入文章内容（支持HTML格式）",required:!0}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"支持HTML标签，如 <h2>、<p>、<ul>、<li> 等"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-2",children:"发布状态"}),(0,a.jsxs)("select",{id:"status",name:"status",value:b.status,onChange:N,className:"block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"draft",children:"草稿"}),(0,a.jsx)("option",{value:"published",children:"已发布"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"文章信息"}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsxs)("p",{children:["浏览量：",x.views]}),(0,a.jsxs)("p",{children:["创建时间：",new Date(x.created_at).toLocaleString()]}),(0,a.jsxs)("p",{children:["更新时间：",new Date(x.updated_at).toLocaleString()]})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[(0,a.jsx)(n(),{href:"/content/articles",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors",children:"取消"}),(0,a.jsx)("button",{type:"submit",disabled:p,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center",children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"保存中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.mW3,{className:"mr-2"}),"保存更改"]})})]})]})})]}):(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"text-center py-10",children:[(0,a.jsx)("p",{className:"text-lg text-gray-500",children:"文章不存在"}),(0,a.jsx)(n(),{href:"/content/articles",className:"text-blue-600 hover:underline",children:"返回文章列表"})]})})}},30540:function(e,t,r){"use strict";r.d(t,{h:function(){return a}});let a=r(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=a},24033:function(e,t,r){e.exports=r(15313)},5925:function(e,t,r){"use strict";let a,s;r.d(t,{x7:function(){return eu},ZP:function(){return em},Am:function(){return z}});var o,i=r(2265);let n={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||n,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,m=(e,t)=>{let r="",a="",s="";for(let o in e){let i=e[o];"@"==o[0]?"i"==o[1]?r=o+" "+i+";":a+="f"==o[1]?m(i,o):o+"{"+m(i,"k"==o[1]?"":t)+"}":"object"==typeof i?a+=m(i,t?t.replace(/([^,])+/g,e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):o):null!=i&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=m.p?m.p(o,i):o+":"+i+";")}return r+(t&&s?t+"{"+s+"}":s)+a},p={},f=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+f(e[r]);return t}return e},h=(e,t,r,a,s)=>{var o;let i=f(e),n=p[i]||(p[i]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(i));if(!p[n]){let t=i!==e?e:(e=>{let t,r,a=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?a.shift():t[3]?(r=t[3].replace(u," ").trim(),a.unshift(a[0][r]=a[0][r]||{})):a[0][t[1]]=t[2].replace(u," ").trim();return a[0]})(e);p[n]=m(s?{["@keyframes "+n]:t}:t,r?"":"."+n)}let l=r&&p.g?p.g:null;return r&&(p.g=p[n]),o=p[n],l?t.data=t.data.replace(l,o):-1===t.data.indexOf(o)&&(t.data=a?o+t.data:t.data+o),n},g=(e,t,r)=>e.reduce((e,a,s)=>{let o=t[s];if(o&&o.call){let e=o(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":m(e,""):!1===e?"":e}return e+a+(null==o?"":o)},"");function x(e){let t=this||{},r=e.call?e(t.p):e;return h(r.unshift?r.raw?g(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}x.bind({g:1});let y,b,v,j=x.bind({k:1});function w(e,t){let r=this||{};return function(){let a=arguments;function s(o,i){let n=Object.assign({},o),l=n.className||s.className;r.p=Object.assign({theme:b&&b()},n),r.o=/ *go\d+/.test(l),n.className=x.apply(r,a)+(l?" "+l:""),t&&(n.ref=i);let c=e;return e[0]&&(c=n.as||e,delete n.as),v&&c[0]&&v(n),y(c,n)}return t?t(s):s}}var N=e=>"function"==typeof e,k=(e,t)=>N(e)?e(t):e,_=(a=0,()=>(++a).toString()),E=()=>{if(void 0===s&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");s=!e||e.matches}return s},S=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return S(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let s=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+s}))}}},C=[],P={toasts:[],pausedAt:void 0},A=e=>{P=S(P,e),C.forEach(e=>{e(P)})},I={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},O=(e={})=>{let[t,r]=(0,i.useState)(P),a=(0,i.useRef)(P);(0,i.useEffect)(()=>(a.current!==P&&r(P),C.push(r),()=>{let e=C.indexOf(r);e>-1&&C.splice(e,1)}),[]);let s=t.toasts.map(t=>{var r,a,s;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||I[t.type],style:{...e.style,...null==(s=e[t.type])?void 0:s.style,...t.style}}});return{...t,toasts:s}},$=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||_()}),D=e=>(t,r)=>{let a=$(t,e,r);return A({type:2,toast:a}),a.id},z=(e,t)=>D("blank")(e,t);z.error=D("error"),z.success=D("success"),z.loading=D("loading"),z.custom=D("custom"),z.dismiss=e=>{A({type:3,toastId:e})},z.remove=e=>A({type:4,toastId:e}),z.promise=(e,t,r)=>{let a=z.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let s=t.success?k(t.success,e):void 0;return s?z.success(s,{id:a,...r,...null==r?void 0:r.success}):z.dismiss(a),e}).catch(e=>{let s=t.error?k(t.error,e):void 0;s?z.error(s,{id:a,...r,...null==r?void 0:r.error}):z.dismiss(a)}),e};var F=(e,t)=>{A({type:1,toast:{id:e,height:t}})},L=()=>{A({type:5,time:Date.now()})},T=new Map,U=1e3,Z=(e,t=U)=>{if(T.has(e))return;let r=setTimeout(()=>{T.delete(e),A({type:4,toastId:e})},t);T.set(e,r)},R=e=>{let{toasts:t,pausedAt:r}=O(e);(0,i.useEffect)(()=>{if(r)return;let e=Date.now(),a=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&z.dismiss(t.id);return}return setTimeout(()=>z.dismiss(t.id),r)});return()=>{a.forEach(e=>e&&clearTimeout(e))}},[t,r]);let a=(0,i.useCallback)(()=>{r&&A({type:6,time:Date.now()})},[r]),s=(0,i.useCallback)((e,r)=>{let{reverseOrder:a=!1,gutter:s=8,defaultPosition:o}=r||{},i=t.filter(t=>(t.position||o)===(e.position||o)&&t.height),n=i.findIndex(t=>t.id===e.id),l=i.filter((e,t)=>t<n&&e.visible).length;return i.filter(e=>e.visible).slice(...a?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+s,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)Z(e.id,e.removeDelay);else{let t=T.get(e.id);t&&(clearTimeout(t),T.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:F,startPause:L,endPause:a,calculateOffset:s}}},H=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,M=j`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,q=j`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=w("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${H} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${M} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${q} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,J=j`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,W=w("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${J} 1s linear infinite;
`,Y=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,G=j`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,K=w("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Y} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${G} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Q=w("div")`
  position: absolute;
`,V=w("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=j`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=w("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:a}=e;return void 0!==t?"string"==typeof t?i.createElement(ee,null,t):t:"blank"===r?null:i.createElement(V,null,i.createElement(W,{...a}),"loading"!==r&&i.createElement(Q,null,"error"===r?i.createElement(B,{...a}):i.createElement(K,{...a})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ea=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,es=w("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,eo=w("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let r=e.includes("top")?1:-1,[a,s]=E()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),ea(r)];return{animation:t?`${j(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${j(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},en=i.memo(({toast:e,position:t,style:r,children:a})=>{let s=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},o=i.createElement(et,{toast:e}),n=i.createElement(eo,{...e.ariaProps},k(e.message,e));return i.createElement(es,{className:e.className,style:{...s,...r,...e.style}},"function"==typeof a?a({icon:o,message:n}):i.createElement(i.Fragment,null,o,n))});o=i.createElement,m.p=void 0,y=o,b=void 0,v=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:a,children:s})=>{let o=i.useCallback(t=>{if(t){let r=()=>{a(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,a]);return i.createElement("div",{ref:o,className:t,style:r},s)},ec=(e,t)=>{let r=e.includes("top"),a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:E()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...a}},ed=x`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:a,children:s,containerStyle:o,containerClassName:n})=>{let{toasts:l,handlers:c}=R(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:n,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(r=>{let o=r.position||t,n=ec(o,c.calculateOffset(r,{reverseOrder:e,gutter:a,defaultPosition:t}));return i.createElement(el,{id:r.id,key:r.id,onHeightUpdate:c.updateHeight,className:r.visible?ed:"",style:n},"custom"===r.type?k(r.message,r):s?s(r):i.createElement(en,{toast:r,position:o}))}))},em=z}},function(e){e.O(0,[737,396,61,971,458,744],function(){return e(e.s=64187)}),_N_E=e.O()}]);