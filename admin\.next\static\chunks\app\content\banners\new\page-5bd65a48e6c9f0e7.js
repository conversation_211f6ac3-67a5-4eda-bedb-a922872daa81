(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[886],{99287:function(e,s,t){Promise.resolve().then(t.bind(t,44063))},44063:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return x}});var r=t(57437),a=t(2265),n=t(24033),i=t(61865),o=t(5925),l=t(6834),d=t(61396),c=t.n(d),m=t(30540);function x(){let e=(0,n.useRouter)(),{register:s,handleSubmit:t,formState:{errors:d},watch:x,setValue:u}=(0,i.cI)(),[p,h]=(0,a.useState)(!1),[b,g]=(0,a.useState)(null),f=async s=>{h(!0);let t=new FormData;t.append("title",s.title),s.image&&s.image[0]&&t.append("image",s.image[0]),t.append("link",s.link),t.append("position",s.position),t.append("order",s.order.toString()),t.append("status",s.status),s.startDate&&t.append("startDate",s.startDate),s.endDate&&t.append("endDate",s.endDate);try{await m.h.post("/content/banners",t,{headers:{"Content-Type":"multipart/form-data"}}),o.ZP.success("Banner创建成功！"),e.push("/content/banners")}catch(e){console.error("创建Banner失败:",e),o.ZP.error("创建Banner失败，请稍后再试。")}finally{h(!1)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)(o.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"创建新Banner"}),(0,r.jsx)(c(),{href:"/content/banners",children:(0,r.jsxs)("button",{className:"bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[(0,r.jsx)(l.Ao2,{className:"mr-2"}),"返回列表"]})})]}),(0,r.jsxs)("form",{onSubmit:t(f),className:"bg-white p-8 rounded-xl shadow-xl space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["Banner标题 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",id:"title",...s("title",{required:"Banner标题不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(d.title?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),d.title&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:d.title.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"image",className:"block text-sm font-medium text-gray-700 mb-1",children:["Banner图片 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"mt-1 flex items-center space-x-4",children:[(0,r.jsx)("span",{className:"inline-block h-24 w-48 rounded-md overflow-hidden bg-gray-100",children:b?(0,r.jsx)("img",{src:b,alt:"Banner预览",className:"h-full w-full object-cover"}):(0,r.jsx)("div",{className:"h-full w-full flex items-center justify-center text-gray-400",children:(0,r.jsx)(l.Yjd,{className:"h-8 w-8"})})}),(0,r.jsx)("label",{htmlFor:"image-upload",className:"cursor-pointer bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out text-sm",children:"选择图片"}),(0,r.jsx)("input",{id:"image-upload",type:"file",accept:"image/*",...s("image",{required:"Banner图片不能为空"}),className:"sr-only",onChange:e=>{if(e.target.files&&e.target.files[0]){let s=e.target.files[0];g(URL.createObjectURL(s)),u("image",e.target.files)}}})]}),d.image&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:d.image.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"link",className:"block text-sm font-medium text-gray-700 mb-1",children:["链接地址 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",id:"link",...s("link",{required:"链接地址不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(d.link?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"),placeholder:"例如: /services/study-abroad"}),d.link&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:d.link.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-1",children:["显示位置 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"position",...s("position",{required:"显示位置不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(d.position?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"),children:[(0,r.jsx)("option",{value:"",children:"选择位置"}),(0,r.jsx)("option",{value:"home_top",children:"首页顶部"}),(0,r.jsx)("option",{value:"home_middle",children:"首页中部"}),(0,r.jsx)("option",{value:"services_page",children:"服务列表页"}),(0,r.jsx)("option",{value:"cases_page",children:"案例列表页"})]}),d.position&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:d.position.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"order",className:"block text-sm font-medium text-gray-700 mb-1",children:["排序 (数字越小越靠前) ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"number",id:"order",...s("order",{required:"排序不能为空",valueAsNumber:!0,min:{value:0,message:"排序不能小于0"}}),className:"mt-1 block w-full px-4 py-2 border ".concat(d.order?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"),defaultValue:0}),d.order&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:d.order.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-1",children:["状态 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"status",...s("status",{required:"状态不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(d.status?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"),defaultValue:"active",children:[(0,r.jsx)("option",{value:"active",children:"启用"}),(0,r.jsx)("option",{value:"inactive",children:"禁用"})]}),d.status&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:d.status.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"startDate",className:"block text-sm font-medium text-gray-700 mb-1",children:"开始日期 (可选)"}),(0,r.jsx)("input",{type:"date",id:"startDate",...s("startDate"),className:"mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"endDate",className:"block text-sm font-medium text-gray-700 mb-1",children:"结束日期 (可选)"}),(0,r.jsx)("input",{type:"date",id:"endDate",...s("endDate"),className:"mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"})]})]}),(0,r.jsx)("div",{className:"flex justify-end pt-4",children:(0,r.jsxs)("button",{type:"submit",disabled:p,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[p?(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,r.jsx)(l.mW3,{className:"mr-2"}),p?"正在保存...":"保存Banner"]})})]})]})}},30540:function(e,s,t){"use strict";t.d(s,{h:function(){return r}});let r=t(54829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let s=localStorage.getItem("adminToken");return s&&(e.headers.Authorization="Bearer ".concat(s)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e)))},24033:function(e,s,t){e.exports=t(15313)}},function(e){e.O(0,[737,892,865,61,971,458,744],function(){return e(e.s=99287)}),_N_E=e.O()}]);