(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[100],{31317:function(e,t,s){Promise.resolve().then(s.bind(s,52669))},52669:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return d}});var r=s(57437),a=s(2265),n=s(61396),i=s.n(n),c=s(5925),l=s(30540);function d(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!0);(0,a.useEffect)(()=>{(async()=>{n(!0);try{let e=await l.h.get("/content/banners");t(e.data.sort((e,t)=>e.order-t.order))}catch(e){c.ZP.error("获取Banner列表失败"),console.error("获取Banner列表失败:",e)}n(!1)})()},[]);let[d,o]=(0,a.useState)(!1),[x,p]=(0,a.useState)(null),h=async s=>{o(!0),p(s);try{await l.h.delete("/content/banners/".concat(s)),t(e.filter(e=>e.id!==s)),c.ZP.success("Banner已成功删除")}catch(e){console.error("删除Banner失败:",e),c.ZP.error("删除Banner失败，请重试")}finally{o(!1),p(null)}},m=async(s,r)=>{let a="active"===r?"inactive":"active";try{await l.h.patch("/content/banners/".concat(s),{status:a}),t(e.map(e=>e.id===s?{...e,status:a}:e)),c.ZP.success("Banner状态已更改为".concat("active"===a?"启用":"禁用"))}catch(e){console.error("更改Banner状态失败:",e),c.ZP.error("更改Banner状态失败，请重试")}},u=async(s,r)=>{let a=e.findIndex(e=>e.id===s);if(-1===a||"up"===r&&0===a||"down"===r&&a===e.length-1)return;let n=[...e],i="up"===r?a-1:a+1;[n[a],n[i]]=[n[i],n[a]],n[a].order=a+1,n[i].order=i+1;try{let e=n.map(e=>({id:e.id,order:e.order}));await l.h.patch("/content/banners/reorder",{banners:e}),t(n.sort((e,t)=>e.order-t.order)),c.ZP.success("Banner排序已更新")}catch(e){console.error("更新Banner排序失败:",e),c.ZP.error("更新Banner排序失败，请重试")}},f=e=>({home_top:"首页顶部",home_middle:"首页中部",home_bottom:"首页底部",services_page:"服务页面",about_page:"关于我们页面",contact_page:"联系我们页面"})[e]||e;return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"Banner管理"}),(0,r.jsx)(i(),{href:"/content/banners/new",className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"添加Banner"})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"排序"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"预览"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"标题"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"位置"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"链接"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"有效期"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(t=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{children:t.order}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("button",{onClick:()=>u(t.id,"up"),className:"text-gray-500 hover:text-gray-700 focus:outline-none",disabled:0===e.indexOf(t),children:"▲"}),(0,r.jsx)("button",{onClick:()=>u(t.id,"down"),className:"text-gray-500 hover:text-gray-700 focus:outline-none",disabled:e.indexOf(t)===e.length-1,children:"▼"})]})]})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"h-12 w-20 relative bg-gray-200 rounded overflow-hidden",children:(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center text-gray-500 text-xs",children:"Banner预览"})})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t.title}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:f(t.position)}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate",children:t.link}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("active"===t.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:"active"===t.status?"启用":"禁用"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[t.startDate," 至 ",t.endDate]}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>m(t.id,t.status),className:"text-indigo-600 hover:text-indigo-900",children:"active"===t.status?"禁用":"启用"}),(0,r.jsx)(i(),{href:"/content/banners/edit/".concat(t.id),className:"text-blue-600 hover:text-blue-900",children:"编辑"}),(0,r.jsx)("button",{onClick:()=>h(t.id),disabled:d&&x===t.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:d&&x===t.id?"删除中...":"删除"})]})})]},t.id))})]})}),(0,r.jsx)(c.x7,{position:"top-right"})]})}},30540:function(e,t,s){"use strict";s.d(t,{h:function(){return r}});let r=s(54829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=r}},function(e){e.O(0,[737,892,971,458,744],function(){return e(e.s=31317)}),_N_E=e.O()}]);