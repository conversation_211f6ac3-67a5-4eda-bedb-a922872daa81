(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[875],{97026:function(e,s,t){Promise.resolve().then(t.bind(t,50562))},50562:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return i}});var r=t(57437),a=t(2265),l=t(24033),c=t(61865),d=t(5925),n=t(30540);function i(){let e=(0,l.useRouter)(),[s,t]=(0,a.useState)(!1),[i,o]=(0,a.useState)(null),m=(0,a.useRef)(null),{register:x,handleSubmit:u,formState:{errors:h},watch:p}=(0,c.cI)({defaultValues:{status:"draft",featured:!1,category:"留学案例"}}),b=p("title"),j=e=>e.toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g,"-").replace(/^-+|-+$/g,""),y=async s=>{t(!0);try{var r,a;s.slug||(s.slug=j(s.title));let t=null===(a=m.current)||void 0===a?void 0:null===(r=a.files)||void 0===r?void 0:r[0],l=new FormData;Object.entries(s).forEach(e=>{let[s,t]=e;"boolean"==typeof t?l.append(s,String(t)):l.append(s,t)}),t&&l.append("thumbnail",t),await n.h.post("/content/cases",l,{headers:{"Content-Type":"multipart/form-data"}}),d.ZP.success("案例创建成功"),e.push("/content/cases")}catch(e){console.error("创建案例失败:",e),d.ZP.error("创建案例失败，请重试"),t(!1)}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"添加新案例"}),(0,r.jsx)("p",{className:"text-gray-600",children:"创建新的成功案例"})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("form",{onSubmit:u(y),className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例标题 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{id:"title",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(h.title?"border-red-500":"border-gray-300"),placeholder:"输入案例标题",...x("title",{required:"请输入案例标题"})}),h.title&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.title.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"slug",className:"block text-sm font-medium text-gray-700 mb-1",children:"URL别名"}),(0,r.jsx)("input",{id:"slug",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:b?j(b):"自动生成或手动输入",...x("slug")}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"留空将根据标题自动生成"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例分类 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"category",className:"w-full px-3 py-2 border rounded-md ".concat(h.category?"border-red-500":"border-gray-300"),...x("category",{required:"请选择案例分类"}),children:[(0,r.jsx)("option",{value:"留学案例",children:"留学案例"}),(0,r.jsx)("option",{value:"保研案例",children:"保研案例"}),(0,r.jsx)("option",{value:"考研案例",children:"考研案例"}),(0,r.jsx)("option",{value:"职业转型",children:"职业转型"}),(0,r.jsx)("option",{value:"职场晋升",children:"职场晋升"}),(0,r.jsx)("option",{value:"其他案例",children:"其他案例"})]}),h.category&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.category.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"thumbnail",className:"block text-sm font-medium text-gray-700 mb-1",children:["缩略图 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("input",{id:"thumbnail",type:"file",accept:"image/*",ref:m,onChange:e=>{var s;let t=null===(s=e.target.files)||void 0===s?void 0:s[0];t&&o(URL.createObjectURL(t))},className:"hidden"}),(0,r.jsx)("button",{type:"button",onClick:()=>{var e;return null===(e=m.current)||void 0===e?void 0:e.click()},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"选择图片"}),i&&(0,r.jsx)("div",{className:"relative h-20 w-32 bg-gray-200 rounded overflow-hidden",children:(0,r.jsx)("img",{src:i,alt:"缩略图预览",className:"h-full w-full object-cover"})}),!i&&(0,r.jsx)("div",{className:"h-20 w-32 bg-gray-200 rounded flex items-center justify-center text-gray-500 text-sm",children:"无预览"})]}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"建议尺寸: 800x600px, 最大文件大小: 2MB"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"summary",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例摘要 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"summary",rows:3,className:"w-full px-3 py-2 border rounded-md ".concat(h.summary?"border-red-500":"border-gray-300"),placeholder:"简要描述案例的主要内容和亮点",...x("summary",{required:"请输入案例摘要"})}),h.summary&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.summary.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"client",className:"block text-sm font-medium text-gray-700 mb-1",children:["客户信息 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"client",rows:3,className:"w-full px-3 py-2 border rounded-md ".concat(h.client?"border-red-500":"border-gray-300"),placeholder:"描述客户的背景、需求和挑战",...x("client",{required:"请输入客户信息"})}),h.client&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.client.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例详情 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"content",rows:10,className:"w-full px-3 py-2 border rounded-md ".concat(h.content?"border-red-500":"border-gray-300"),placeholder:"详细描述案例的过程、方法和策略",...x("content",{required:"请输入案例详情"})}),h.content&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.content.message}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"支持Markdown格式"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"result",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例结果 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"result",rows:3,className:"w-full px-3 py-2 border rounded-md ".concat(h.result?"border-red-500":"border-gray-300"),placeholder:"描述案例的最终结果和成果",...x("result",{required:"请输入案例结果"})}),h.result&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.result.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("label",{className:"inline-flex items-center",children:[(0,r.jsx)("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"draft",...x("status")}),(0,r.jsx)("span",{className:"ml-2",children:"草稿"})]}),(0,r.jsxs)("label",{className:"inline-flex items-center",children:[(0,r.jsx)("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"published",...x("status")}),(0,r.jsx)("span",{className:"ml-2",children:"发布"})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"精选案例"}),(0,r.jsxs)("label",{className:"inline-flex items-center",children:[(0,r.jsx)("input",{type:"checkbox",className:"form-checkbox h-4 w-4 text-primary-600",...x("featured")}),(0,r.jsx)("span",{className:"ml-2",children:"设为精选案例（将在首页展示）"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>e.back(),disabled:s,children:"取消"}),(0,r.jsx)("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:s,children:s?"保存中...":"保存"})]})]})}),(0,r.jsx)(d.x7,{position:"top-right"})]})}},30540:function(e,s,t){"use strict";t.d(s,{h:function(){return r}});let r=t(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let s=localStorage.getItem("adminToken");return s&&(e.headers.Authorization="Bearer ".concat(s)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),s.Z=r},24033:function(e,s,t){e.exports=t(15313)}},function(e){e.O(0,[737,279,971,458,744],function(){return e(e.s=97026)}),_N_E=e.O()}]);