(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[875],{97026:function(e,t,r){Promise.resolve().then(r.bind(r,50562))},50562:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return d}});var s=r(57437),a=r(2265),i=r(24033),o=r(61865),l=r(5925),n=r(30540);function d(){let e=(0,i.useRouter)(),[t,r]=(0,a.useState)(!1),[d,c]=(0,a.useState)(null),m=(0,a.useRef)(null),{register:u,handleSubmit:p,formState:{errors:x},watch:h}=(0,o.cI)({defaultValues:{status:"draft",featured:!1,category:"留学案例"}}),f=h("title"),b=e=>e.toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g,"-").replace(/^-+|-+$/g,""),y=async t=>{r(!0);try{var s,a;t.slug||(t.slug=b(t.title));let r=null===(a=m.current)||void 0===a?void 0:null===(s=a.files)||void 0===s?void 0:s[0],i=new FormData;Object.entries(t).forEach(e=>{let[t,r]=e;"boolean"==typeof r?i.append(t,String(r)):i.append(t,r)}),r&&i.append("thumbnail",r),await n.h.post("/content/cases",i,{headers:{"Content-Type":"multipart/form-data"}}),l.ZP.success("案例创建成功"),e.push("/content/cases")}catch(e){console.error("创建案例失败:",e),l.ZP.error("创建案例失败，请重试"),r(!1)}};return(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"添加新案例"}),(0,s.jsx)("p",{className:"text-gray-600",children:"创建新的成功案例"})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("form",{onSubmit:p(y),className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例标题 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"title",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(x.title?"border-red-500":"border-gray-300"),placeholder:"输入案例标题",...u("title",{required:"请输入案例标题"})}),x.title&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.title.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"slug",className:"block text-sm font-medium text-gray-700 mb-1",children:"URL别名"}),(0,s.jsx)("input",{id:"slug",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:f?b(f):"自动生成或手动输入",...u("slug")}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"留空将根据标题自动生成"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例分类 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("select",{id:"category",className:"w-full px-3 py-2 border rounded-md ".concat(x.category?"border-red-500":"border-gray-300"),...u("category",{required:"请选择案例分类"}),children:[(0,s.jsx)("option",{value:"留学案例",children:"留学案例"}),(0,s.jsx)("option",{value:"保研案例",children:"保研案例"}),(0,s.jsx)("option",{value:"考研案例",children:"考研案例"}),(0,s.jsx)("option",{value:"职业转型",children:"职业转型"}),(0,s.jsx)("option",{value:"职场晋升",children:"职场晋升"}),(0,s.jsx)("option",{value:"其他案例",children:"其他案例"})]}),x.category&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.category.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"thumbnail",className:"block text-sm font-medium text-gray-700 mb-1",children:["缩略图 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("input",{id:"thumbnail",type:"file",accept:"image/*",ref:m,onChange:e=>{var t;let r=null===(t=e.target.files)||void 0===t?void 0:t[0];r&&c(URL.createObjectURL(r))},className:"hidden"}),(0,s.jsx)("button",{type:"button",onClick:()=>{var e;return null===(e=m.current)||void 0===e?void 0:e.click()},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"选择图片"}),d&&(0,s.jsx)("div",{className:"relative h-20 w-32 bg-gray-200 rounded overflow-hidden",children:(0,s.jsx)("img",{src:d,alt:"缩略图预览",className:"h-full w-full object-cover"})}),!d&&(0,s.jsx)("div",{className:"h-20 w-32 bg-gray-200 rounded flex items-center justify-center text-gray-500 text-sm",children:"无预览"})]}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"建议尺寸: 800x600px, 最大文件大小: 2MB"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"summary",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例摘要 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("textarea",{id:"summary",rows:3,className:"w-full px-3 py-2 border rounded-md ".concat(x.summary?"border-red-500":"border-gray-300"),placeholder:"简要描述案例的主要内容和亮点",...u("summary",{required:"请输入案例摘要"})}),x.summary&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.summary.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"client",className:"block text-sm font-medium text-gray-700 mb-1",children:["客户信息 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("textarea",{id:"client",rows:3,className:"w-full px-3 py-2 border rounded-md ".concat(x.client?"border-red-500":"border-gray-300"),placeholder:"描述客户的背景、需求和挑战",...u("client",{required:"请输入客户信息"})}),x.client&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.client.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例详情 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("textarea",{id:"content",rows:10,className:"w-full px-3 py-2 border rounded-md ".concat(x.content?"border-red-500":"border-gray-300"),placeholder:"详细描述案例的过程、方法和策略",...u("content",{required:"请输入案例详情"})}),x.content&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.content.message}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"支持Markdown格式"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"result",className:"block text-sm font-medium text-gray-700 mb-1",children:["案例结果 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("textarea",{id:"result",rows:3,className:"w-full px-3 py-2 border rounded-md ".concat(x.result?"border-red-500":"border-gray-300"),placeholder:"描述案例的最终结果和成果",...u("result",{required:"请输入案例结果"})}),x.result&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:x.result.message})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("label",{className:"inline-flex items-center",children:[(0,s.jsx)("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"draft",...u("status")}),(0,s.jsx)("span",{className:"ml-2",children:"草稿"})]}),(0,s.jsxs)("label",{className:"inline-flex items-center",children:[(0,s.jsx)("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"published",...u("status")}),(0,s.jsx)("span",{className:"ml-2",children:"发布"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"精选案例"}),(0,s.jsxs)("label",{className:"inline-flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",className:"form-checkbox h-4 w-4 text-primary-600",...u("featured")}),(0,s.jsx)("span",{className:"ml-2",children:"设为精选案例（将在首页展示）"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,s.jsx)("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>e.back(),disabled:t,children:"取消"}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:t,children:t?"保存中...":"保存"})]})]})}),(0,s.jsx)(l.x7,{position:"top-right"})]})}},30540:function(e,t,r){"use strict";r.d(t,{h:function(){return s}});let s=r(54829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=s},24033:function(e,t,r){e.exports=r(15313)},5925:function(e,t,r){"use strict";let s,a;r.d(t,{x7:function(){return em},ZP:function(){return eu},Am:function(){return A}});var i,o=r(2265);let l={data:""},n=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||l,d=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,m=/\n+/g,u=(e,t)=>{let r="",s="",a="";for(let i in e){let o=e[i];"@"==i[0]?"i"==i[1]?r=i+" "+o+";":s+="f"==i[1]?u(o,i):i+"{"+u(o,"k"==i[1]?"":t)+"}":"object"==typeof o?s+=u(o,t?t.replace(/([^,])+/g,e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):i):null!=o&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=u.p?u.p(i,o):i+":"+o+";")}return r+(t&&a?t+"{"+a+"}":a)+s},p={},x=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+x(e[r]);return t}return e},h=(e,t,r,s,a)=>{var i;let o=x(e),l=p[o]||(p[o]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(o));if(!p[l]){let t=o!==e?e:(e=>{let t,r,s=[{}];for(;t=d.exec(e.replace(c,""));)t[4]?s.shift():t[3]?(r=t[3].replace(m," ").trim(),s.unshift(s[0][r]=s[0][r]||{})):s[0][t[1]]=t[2].replace(m," ").trim();return s[0]})(e);p[l]=u(a?{["@keyframes "+l]:t}:t,r?"":"."+l)}let n=r&&p.g?p.g:null;return r&&(p.g=p[l]),i=p[l],n?t.data=t.data.replace(n,i):-1===t.data.indexOf(i)&&(t.data=s?i+t.data:t.data+i),l},f=(e,t,r)=>e.reduce((e,s,a)=>{let i=t[a];if(i&&i.call){let e=i(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=t?"."+t:e&&"object"==typeof e?e.props?"":u(e,""):!1===e?"":e}return e+s+(null==i?"":i)},"");function b(e){let t=this||{},r=e.call?e(t.p):e;return h(r.unshift?r.raw?f(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,n(t.target),t.g,t.o,t.k)}b.bind({g:1});let y,g,v,j=b.bind({k:1});function N(e,t){let r=this||{};return function(){let s=arguments;function a(i,o){let l=Object.assign({},i),n=l.className||a.className;r.p=Object.assign({theme:g&&g()},l),r.o=/ *go\d+/.test(n),l.className=b.apply(r,s)+(n?" "+n:""),t&&(l.ref=o);let d=e;return e[0]&&(d=l.as||e,delete l.as),v&&d[0]&&v(l),y(d,l)}return t?t(a):a}}var w=e=>"function"==typeof e,k=(e,t)=>w(e)?e(t):e,E=(s=0,()=>(++s).toString()),$=()=>{if(void 0===a&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");a=!e||e.matches}return a},C=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return C(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},O=[],D={toasts:[],pausedAt:void 0},F=e=>{D=C(D,e),O.forEach(e=>{e(D)})},P={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},S=(e={})=>{let[t,r]=(0,o.useState)(D),s=(0,o.useRef)(D);(0,o.useEffect)(()=>(s.current!==D&&r(D),O.push(r),()=>{let e=O.indexOf(r);e>-1&&O.splice(e,1)}),[]);let a=t.toasts.map(t=>{var r,s,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(s=e[t.type])?void 0:s.duration)||(null==e?void 0:e.duration)||P[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},I=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||E()}),z=e=>(t,r)=>{let s=I(t,e,r);return F({type:2,toast:s}),s.id},A=(e,t)=>z("blank")(e,t);A.error=z("error"),A.success=z("success"),A.loading=z("loading"),A.custom=z("custom"),A.dismiss=e=>{F({type:3,toastId:e})},A.remove=e=>F({type:4,toastId:e}),A.promise=(e,t,r)=>{let s=A.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?k(t.success,e):void 0;return a?A.success(a,{id:s,...r,...null==r?void 0:r.success}):A.dismiss(s),e}).catch(e=>{let a=t.error?k(t.error,e):void 0;a?A.error(a,{id:s,...r,...null==r?void 0:r.error}):A.dismiss(s)}),e};var _=(e,t)=>{F({type:1,toast:{id:e,height:t}})},T=()=>{F({type:5,time:Date.now()})},L=new Map,q=1e3,M=(e,t=q)=>{if(L.has(e))return;let r=setTimeout(()=>{L.delete(e),F({type:4,toastId:e})},t);L.set(e,r)},R=e=>{let{toasts:t,pausedAt:r}=S(e);(0,o.useEffect)(()=>{if(r)return;let e=Date.now(),s=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&A.dismiss(t.id);return}return setTimeout(()=>A.dismiss(t.id),r)});return()=>{s.forEach(e=>e&&clearTimeout(e))}},[t,r]);let s=(0,o.useCallback)(()=>{r&&F({type:6,time:Date.now()})},[r]),a=(0,o.useCallback)((e,r)=>{let{reverseOrder:s=!1,gutter:a=8,defaultPosition:i}=r||{},o=t.filter(t=>(t.position||i)===(e.position||i)&&t.height),l=o.findIndex(t=>t.id===e.id),n=o.filter((e,t)=>t<l&&e.visible).length;return o.filter(e=>e.visible).slice(...s?[n+1]:[0,n]).reduce((e,t)=>e+(t.height||0)+a,0)},[t]);return(0,o.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)M(e.id,e.removeDelay);else{let t=L.get(e.id);t&&(clearTimeout(t),L.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:_,startPause:T,endPause:s,calculateOffset:a}}},U=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Z=j`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,H=j`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${U} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Z} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${H} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,V=j`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y=N("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${V} 1s linear infinite;
`,G=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,J=j`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,K=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${G} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${J} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Q=N("div")`
  position: absolute;
`,W=N("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=j`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=N("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:s}=e;return void 0!==t?"string"==typeof t?o.createElement(ee,null,t):t:"blank"===r?null:o.createElement(W,null,o.createElement(Y,{...s}),"loading"!==r&&o.createElement(Q,null,"error"===r?o.createElement(B,{...s}):o.createElement(K,{...s})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,es=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=N("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,ei=N("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,eo=(e,t)=>{let r=e.includes("top")?1:-1,[s,a]=$()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),es(r)];return{animation:t?`${j(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${j(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},el=o.memo(({toast:e,position:t,style:r,children:s})=>{let a=e.height?eo(e.position||t||"top-center",e.visible):{opacity:0},i=o.createElement(et,{toast:e}),l=o.createElement(ei,{...e.ariaProps},k(e.message,e));return o.createElement(ea,{className:e.className,style:{...a,...r,...e.style}},"function"==typeof s?s({icon:i,message:l}):o.createElement(o.Fragment,null,i,l))});i=o.createElement,u.p=void 0,y=i,g=void 0,v=void 0;var en=({id:e,className:t,style:r,onHeightUpdate:s,children:a})=>{let i=o.useCallback(t=>{if(t){let r=()=>{s(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return o.createElement("div",{ref:i,className:t,style:r},a)},ed=(e,t)=>{let r=e.includes("top"),s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:$()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...s}},ec=b`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,em=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:s,children:a,containerStyle:i,containerClassName:l})=>{let{toasts:n,handlers:d}=R(r);return o.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...i},className:l,onMouseEnter:d.startPause,onMouseLeave:d.endPause},n.map(r=>{let i=r.position||t,l=ed(i,d.calculateOffset(r,{reverseOrder:e,gutter:s,defaultPosition:t}));return o.createElement(en,{id:r.id,key:r.id,onHeightUpdate:d.updateHeight,className:r.visible?ec:"",style:l},"custom"===r.type?k(r.message,r):a?a(r):o.createElement(el,{toast:r,position:i}))}))},eu=A}},function(e){e.O(0,[737,865,971,458,744],function(){return e(e.s=97026)}),_N_E=e.O()}]);