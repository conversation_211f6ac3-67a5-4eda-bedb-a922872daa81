(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[734],{91318:function(e,t,a){Promise.resolve().then(a.bind(a,69650))},69650:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return c}});var s=a(57437),r=a(2265),i=a(61396),o=a.n(i),n=a(5925),l=a(30540);function c(){let[e,t]=(0,r.useState)([]),[a,i]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(async()=>{i(!0);try{let e=await l.Z.get("/cases");if(e.data.success&&e.data.data.items){let a=e.data.data.items.map(e=>({id:e.id,title:e.title,category:e.category||"general",status:e.status,featured:e.is_featured||!1,updatedAt:e.updated_at}));t(a)}else t([])}catch(e){n.ZP.error("获取案例列表失败"),console.error("获取案例列表失败:",e)}i(!1)})()},[]);let[c,d]=(0,r.useState)(!1),[p,u]=(0,r.useState)(null),m=async a=>{d(!0),u(a);try{await l.Z.delete("/cases/".concat(a)),t(e.filter(e=>e.id!==a)),n.ZP.success("案例已成功删除")}catch(e){console.error("删除案例失败:",e),n.ZP.error("删除案例失败，请重试")}finally{d(!1),u(null)}},f=async(a,s)=>{let r="published"===s?"draft":"published";try{await l.Z.patch("/cases/".concat(a),{status:r}),t(e.map(e=>e.id===a?{...e,status:r}:e)),n.ZP.success("案例状态已更改为".concat("published"===r?"已发布":"草稿"))}catch(e){console.error("更改案例状态失败:",e),n.ZP.error("更改案例状态失败，请重试")}},x=async(a,s)=>{try{await l.Z.patch("/cases/".concat(a),{featured:!s}),t(e.map(e=>e.id===a?{...e,featured:!s}:e)),n.ZP.success("案例已".concat(s?"取消":"设为","精选"))}catch(e){console.error("更改精选状态失败:",e),n.ZP.error("更改精选状态失败，请重试")}};return(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"案例管理"}),(0,s.jsx)(o(),{href:"/content/cases/new",className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"添加案例"})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"缩略图"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"标题"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"分类"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"精选"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"更新日期"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"h-12 w-16 relative bg-gray-200 rounded overflow-hidden",children:(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center text-gray-500 text-xs",children:"缩略图"})})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.title}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.category}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"published"===e.status?"已发布":"草稿"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat(e.featured?"bg-purple-100 text-purple-800":"bg-gray-100 text-gray-800"),children:e.featured?"是":"否"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.updatedAt}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>f(e.id,e.status),className:"text-indigo-600 hover:text-indigo-900",children:"published"===e.status?"设为草稿":"发布"}),(0,s.jsx)("button",{onClick:()=>x(e.id,e.featured),className:"text-purple-600 hover:text-purple-900",children:e.featured?"取消精选":"设为精选"}),(0,s.jsx)(o(),{href:"/content/cases/edit/".concat(e.id),className:"text-blue-600 hover:text-blue-900",children:"编辑"}),(0,s.jsx)("button",{onClick:()=>m(e.id),disabled:c&&p===e.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:c&&p===e.id?"删除中...":"删除"})]})})]},e.id))})]})}),(0,s.jsx)(n.x7,{position:"top-right"})]})}},30540:function(e,t,a){"use strict";a.d(t,{h:function(){return s}});let s=a(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=s},5925:function(e,t,a){"use strict";let s,r;a.d(t,{x7:function(){return ep},ZP:function(){return eu},Am:function(){return S}});var i,o=a(2265);let n={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||n,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,p=/\n+/g,u=(e,t)=>{let a="",s="",r="";for(let i in e){let o=e[i];"@"==i[0]?"i"==i[1]?a=i+" "+o+";":s+="f"==i[1]?u(o,i):i+"{"+u(o,"k"==i[1]?"":t)+"}":"object"==typeof o?s+=u(o,t?t.replace(/([^,])+/g,e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):i):null!=o&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),r+=u.p?u.p(i,o):i+":"+o+";")}return a+(t&&r?t+"{"+r+"}":r)+s},m={},f=e=>{if("object"==typeof e){let t="";for(let a in e)t+=a+f(e[a]);return t}return e},x=(e,t,a,s,r)=>{var i;let o=f(e),n=m[o]||(m[o]=(e=>{let t=0,a=11;for(;t<e.length;)a=101*a+e.charCodeAt(t++)>>>0;return"go"+a})(o));if(!m[n]){let t=o!==e?e:(e=>{let t,a,s=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?s.shift():t[3]?(a=t[3].replace(p," ").trim(),s.unshift(s[0][a]=s[0][a]||{})):s[0][t[1]]=t[2].replace(p," ").trim();return s[0]})(e);m[n]=u(r?{["@keyframes "+n]:t}:t,a?"":"."+n)}let l=a&&m.g?m.g:null;return a&&(m.g=m[n]),i=m[n],l?t.data=t.data.replace(l,i):-1===t.data.indexOf(i)&&(t.data=s?i+t.data:t.data+i),n},h=(e,t,a)=>e.reduce((e,s,r)=>{let i=t[r];if(i&&i.call){let e=i(a),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=t?"."+t:e&&"object"==typeof e?e.props?"":u(e,""):!1===e?"":e}return e+s+(null==i?"":i)},"");function y(e){let t=this||{},a=e.call?e(t.p):e;return x(a.unshift?a.raw?h(a,[].slice.call(arguments,1),t.p):a.reduce((e,a)=>Object.assign(e,a&&a.call?a(t.p):a),{}):a,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let g,b,v,w=y.bind({k:1});function j(e,t){let a=this||{};return function(){let s=arguments;function r(i,o){let n=Object.assign({},i),l=n.className||r.className;a.p=Object.assign({theme:b&&b()},n),a.o=/ *go\d+/.test(l),n.className=y.apply(a,s)+(l?" "+l:""),t&&(n.ref=o);let c=e;return e[0]&&(c=n.as||e,delete n.as),v&&c[0]&&v(n),g(c,n)}return t?t(r):r}}var N=e=>"function"==typeof e,k=(e,t)=>N(e)?e(t):e,E=(s=0,()=>(++s).toString()),P=()=>{if(void 0===r&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");r=!e||e.matches}return r},$=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:a}=t;return $(e,{type:e.toasts.find(e=>e.id===a.id)?1:0,toast:a});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let r=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+r}))}}},C=[],D={toasts:[],pausedAt:void 0},O=e=>{D=$(D,e),C.forEach(e=>{e(D)})},Z={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},A=(e={})=>{let[t,a]=(0,o.useState)(D),s=(0,o.useRef)(D);(0,o.useEffect)(()=>(s.current!==D&&a(D),C.push(a),()=>{let e=C.indexOf(a);e>-1&&C.splice(e,1)}),[]);let r=t.toasts.map(t=>{var a,s,r;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(a=e[t.type])?void 0:a.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(s=e[t.type])?void 0:s.duration)||(null==e?void 0:e.duration)||Z[t.type],style:{...e.style,...null==(r=e[t.type])?void 0:r.style,...t.style}}});return{...t,toasts:r}},_=(e,t="blank",a)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...a,id:(null==a?void 0:a.id)||E()}),I=e=>(t,a)=>{let s=_(t,e,a);return O({type:2,toast:s}),s.id},S=(e,t)=>I("blank")(e,t);S.error=I("error"),S.success=I("success"),S.loading=I("loading"),S.custom=I("custom"),S.dismiss=e=>{O({type:3,toastId:e})},S.remove=e=>O({type:4,toastId:e}),S.promise=(e,t,a)=>{let s=S.loading(t.loading,{...a,...null==a?void 0:a.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let r=t.success?k(t.success,e):void 0;return r?S.success(r,{id:s,...a,...null==a?void 0:a.success}):S.dismiss(s),e}).catch(e=>{let r=t.error?k(t.error,e):void 0;r?S.error(r,{id:s,...a,...null==a?void 0:a.error}):S.dismiss(s)}),e};var z=(e,t)=>{O({type:1,toast:{id:e,height:t}})},T=()=>{O({type:5,time:Date.now()})},M=new Map,F=1e3,H=(e,t=F)=>{if(M.has(e))return;let a=setTimeout(()=>{M.delete(e),O({type:4,toastId:e})},t);M.set(e,a)},L=e=>{let{toasts:t,pausedAt:a}=A(e);(0,o.useEffect)(()=>{if(a)return;let e=Date.now(),s=t.map(t=>{if(t.duration===1/0)return;let a=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(a<0){t.visible&&S.dismiss(t.id);return}return setTimeout(()=>S.dismiss(t.id),a)});return()=>{s.forEach(e=>e&&clearTimeout(e))}},[t,a]);let s=(0,o.useCallback)(()=>{a&&O({type:6,time:Date.now()})},[a]),r=(0,o.useCallback)((e,a)=>{let{reverseOrder:s=!1,gutter:r=8,defaultPosition:i}=a||{},o=t.filter(t=>(t.position||i)===(e.position||i)&&t.height),n=o.findIndex(t=>t.id===e.id),l=o.filter((e,t)=>t<n&&e.visible).length;return o.filter(e=>e.visible).slice(...s?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+r,0)},[t]);return(0,o.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)H(e.id,e.removeDelay);else{let t=M.get(e.id);t&&(clearTimeout(t),M.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:z,startPause:T,endPause:s,calculateOffset:r}}},U=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,R=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,q=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${U} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${R} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${q} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Y=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,G=j("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Y} 1s linear infinite;
`,J=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,K=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Q=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${J} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${K} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,V=j("div")`
  position: absolute;
`,W=j("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=j("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:a,iconTheme:s}=e;return void 0!==t?"string"==typeof t?o.createElement(ee,null,t):t:"blank"===a?null:o.createElement(W,null,o.createElement(G,{...s}),"loading"!==a&&o.createElement(V,null,"error"===a?o.createElement(B,{...s}):o.createElement(Q,{...s})))},ea=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,es=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,er=j("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,ei=j("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,eo=(e,t)=>{let a=e.includes("top")?1:-1,[s,r]=P()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[ea(a),es(a)];return{animation:t?`${w(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(r)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},en=o.memo(({toast:e,position:t,style:a,children:s})=>{let r=e.height?eo(e.position||t||"top-center",e.visible):{opacity:0},i=o.createElement(et,{toast:e}),n=o.createElement(ei,{...e.ariaProps},k(e.message,e));return o.createElement(er,{className:e.className,style:{...r,...a,...e.style}},"function"==typeof s?s({icon:i,message:n}):o.createElement(o.Fragment,null,i,n))});i=o.createElement,u.p=void 0,g=i,b=void 0,v=void 0;var el=({id:e,className:t,style:a,onHeightUpdate:s,children:r})=>{let i=o.useCallback(t=>{if(t){let a=()=>{s(e,t.getBoundingClientRect().height)};a(),new MutationObserver(a).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return o.createElement("div",{ref:i,className:t,style:a},r)},ec=(e,t)=>{let a=e.includes("top"),s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:P()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(a?1:-1)}px)`,...a?{top:0}:{bottom:0},...s}},ed=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ep=({reverseOrder:e,position:t="top-center",toastOptions:a,gutter:s,children:r,containerStyle:i,containerClassName:n})=>{let{toasts:l,handlers:c}=L(a);return o.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...i},className:n,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(a=>{let i=a.position||t,n=ec(i,c.calculateOffset(a,{reverseOrder:e,gutter:s,defaultPosition:t}));return o.createElement(el,{id:a.id,key:a.id,onHeightUpdate:c.updateHeight,className:a.visible?ed:"",style:n},"custom"===a.type?k(a.message,a):r?r(a):o.createElement(en,{toast:a,position:i}))}))},eu=S}},function(e){e.O(0,[737,396,971,458,744],function(){return e(e.s=91318)}),_N_E=e.O()}]);