(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[770],{80719:function(e,t,r){Promise.resolve().then(r.bind(r,5027))},5027:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return d}});var s=r(57437),a=r(2265),i=r(61396),o=r.n(i),n=r(5925);let l=[{id:1,question:"申请留学需要准备哪些材料？",answer:"申请留学通常需要准备以下材料：个人陈述、推荐信、成绩单、语言成绩证明（如托福、雅思）、简历、作品集（视专业而定）等。具体要求因学校和专业而异，建议提前查询目标院校的官方要求。",category:"留学申请",order:1,status:"published",createdAt:"2023-10-15",updatedAt:"2023-11-01"},{id:2,question:"保研和考研有什么区别？",answer:"保研是免试推荐攻读研究生的方式，通常需要本科期间成绩优异，且获得学校的推荐资格。考研则是通过全国统一的研究生入学考试，参加初试和复试后被录取。保研免去了考试环节，但名额有限且竞争激烈；考研则机会更加公平，但需要付出更多备考努力。",category:"考研保研",order:1,status:"published",createdAt:"2023-10-16",updatedAt:"2023-10-30"},{id:3,question:"如何确定适合自己的职业方向？",answer:"确定适合自己的职业方向可以从以下几个方面考虑：1）自我评估：了解自己的兴趣、能力、价值观和性格特点；2）市场调研：了解不同行业和职位的发展前景、薪资水平和工作内容；3）尝试实习或项目：通过实际体验来验证自己的兴趣和适应性；4）寻求专业指导：可以咨询职业规划师或行业内的专业人士。",category:"职业规划",order:1,status:"published",createdAt:"2023-10-17",updatedAt:"2023-10-29"},{id:4,question:"职业转型需要具备哪些条件？",answer:"成功的职业转型通常需要：1）明确的目标和规划；2）相关的技能储备或学习能力；3）对新领域的了解和研究；4）良好的人际网络；5）足够的财务准备以应对过渡期；6）积极的心态和抗压能力。根据个人情况不同，可能还需要额外的教育背景或证书。",category:"职业转型",order:1,status:"published",createdAt:"2023-10-18",updatedAt:"2023-10-28"},{id:5,question:"贵公司的服务收费标准是怎样的？",answer:"我们的服务收费根据不同的服务类型和客户需求而定，没有统一的标准价格。我们会根据您的具体情况和需求提供定制化的服务方案和相应的报价。您可以通过预约咨询，与我们的顾问详细沟通，获取适合您的服务方案和价格信息。",category:"服务咨询",order:1,status:"published",createdAt:"2023-10-19",updatedAt:"2023-10-27"}];function d(){let[e,t]=(0,a.useState)(l),[r,i]=(0,a.useState)(!1),[d,c]=(0,a.useState)(null),[p,u]=(0,a.useState)(null),m=async r=>{i(!0),c(r);try{await new Promise(e=>setTimeout(e,500)),t(e.filter(e=>e.id!==r)),n.ZP.success("FAQ已成功删除")}catch(e){console.error("删除FAQ失败:",e),n.ZP.error("删除FAQ失败，请重试")}finally{i(!1),c(null)}},f=async(r,s)=>{let a="published"===s?"draft":"published";try{await new Promise(e=>setTimeout(e,500)),t(e.map(e=>e.id===r?{...e,status:a}:e)),n.ZP.success("FAQ状态已更改为".concat("published"===a?"已发布":"草稿"))}catch(e){console.error("更改FAQ状态失败:",e),n.ZP.error("更改FAQ状态失败，请重试")}},x=e=>{u(p===e?null:e)};return(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"FAQ管理"}),(0,s.jsx)(o(),{href:"/content/faq/new",className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"添加FAQ"})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"问题"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"分类"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"更新日期"}),(0,s.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("tr",{className:"hover:bg-gray-50 cursor-pointer",onClick:()=>x(e.id),children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),(0,s.jsx)("td",{className:"px-6 py-4 text-sm font-medium text-gray-900",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"mr-2",children:p===e.id?"▼":"▶"}),e.question]})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.category}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"published"===e.status?"已发布":"草稿"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.updatedAt}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex space-x-2",onClick:e=>e.stopPropagation(),children:[(0,s.jsx)("button",{onClick:()=>f(e.id,e.status),className:"text-indigo-600 hover:text-indigo-900",children:"published"===e.status?"设为草稿":"发布"}),(0,s.jsx)(o(),{href:"/content/faq/edit/".concat(e.id),className:"text-blue-600 hover:text-blue-900",children:"编辑"}),(0,s.jsx)("button",{onClick:()=>m(e.id),disabled:r&&d===e.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:r&&d===e.id?"删除中...":"删除"})]})})]},e.id),p===e.id&&(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:6,className:"px-6 py-4 bg-gray-50",children:(0,s.jsxs)("div",{className:"text-sm text-gray-700 whitespace-pre-wrap",children:[(0,s.jsx)("p",{className:"font-semibold mb-2",children:"答案："}),e.answer]})})})]}))})]})}),(0,s.jsx)(n.x7,{position:"top-right"})]})}},30622:function(e,t,r){"use strict";var s=r(2265),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,n=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function d(e,t,r){var s,i={},d=null,c=null;for(s in void 0!==r&&(d=""+r),void 0!==t.key&&(d=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,s)&&!l.hasOwnProperty(s)&&(i[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===i[s]&&(i[s]=t[s]);return{$$typeof:a,type:e,key:d,ref:c,props:i,_owner:n.current}}t.Fragment=i,t.jsx=d,t.jsxs=d},57437:function(e,t,r){"use strict";e.exports=r(30622)},5925:function(e,t,r){"use strict";let s,a;r.d(t,{x7:function(){return ep},ZP:function(){return eu},Am:function(){return I}});var i,o=r(2265);let n={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||n,d=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,p=/\n+/g,u=(e,t)=>{let r="",s="",a="";for(let i in e){let o=e[i];"@"==i[0]?"i"==i[1]?r=i+" "+o+";":s+="f"==i[1]?u(o,i):i+"{"+u(o,"k"==i[1]?"":t)+"}":"object"==typeof o?s+=u(o,t?t.replace(/([^,])+/g,e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):i):null!=o&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=u.p?u.p(i,o):i+":"+o+";")}return r+(t&&a?t+"{"+a+"}":a)+s},m={},f=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+f(e[r]);return t}return e},x=(e,t,r,s,a)=>{var i;let o=f(e),n=m[o]||(m[o]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(o));if(!m[n]){let t=o!==e?e:(e=>{let t,r,s=[{}];for(;t=d.exec(e.replace(c,""));)t[4]?s.shift():t[3]?(r=t[3].replace(p," ").trim(),s.unshift(s[0][r]=s[0][r]||{})):s[0][t[1]]=t[2].replace(p," ").trim();return s[0]})(e);m[n]=u(a?{["@keyframes "+n]:t}:t,r?"":"."+n)}let l=r&&m.g?m.g:null;return r&&(m.g=m[n]),i=m[n],l?t.data=t.data.replace(l,i):-1===t.data.indexOf(i)&&(t.data=s?i+t.data:t.data+i),n},h=(e,t,r)=>e.reduce((e,s,a)=>{let i=t[a];if(i&&i.call){let e=i(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=t?"."+t:e&&"object"==typeof e?e.props?"":u(e,""):!1===e?"":e}return e+s+(null==i?"":i)},"");function y(e){let t=this||{},r=e.call?e(t.p):e;return x(r.unshift?r.raw?h(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let g,b,v,w=y.bind({k:1});function j(e,t){let r=this||{};return function(){let s=arguments;function a(i,o){let n=Object.assign({},i),l=n.className||a.className;r.p=Object.assign({theme:b&&b()},n),r.o=/ *go\d+/.test(l),n.className=y.apply(r,s)+(l?" "+l:""),t&&(n.ref=o);let d=e;return e[0]&&(d=n.as||e,delete n.as),v&&d[0]&&v(n),g(d,n)}return t?t(a):a}}var N=e=>"function"==typeof e,k=(e,t)=>N(e)?e(t):e,E=(s=0,()=>(++s).toString()),A=()=>{if(void 0===a&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");a=!e||e.matches}return a},_=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return _(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},O=[],P={toasts:[],pausedAt:void 0},$=e=>{P=_(P,e),O.forEach(e=>{e(P)})},C={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},D=(e={})=>{let[t,r]=(0,o.useState)(P),s=(0,o.useRef)(P);(0,o.useEffect)(()=>(s.current!==P&&r(P),O.push(r),()=>{let e=O.indexOf(r);e>-1&&O.splice(e,1)}),[]);let a=t.toasts.map(t=>{var r,s,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(s=e[t.type])?void 0:s.duration)||(null==e?void 0:e.duration)||C[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},F=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||E()}),S=e=>(t,r)=>{let s=F(t,e,r);return $({type:2,toast:s}),s.id},I=(e,t)=>S("blank")(e,t);I.error=S("error"),I.success=S("success"),I.loading=S("loading"),I.custom=S("custom"),I.dismiss=e=>{$({type:3,toastId:e})},I.remove=e=>$({type:4,toastId:e}),I.promise=(e,t,r)=>{let s=I.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?k(t.success,e):void 0;return a?I.success(a,{id:s,...r,...null==r?void 0:r.success}):I.dismiss(s),e}).catch(e=>{let a=t.error?k(t.error,e):void 0;a?I.error(a,{id:s,...r,...null==r?void 0:r.error}):I.dismiss(s)}),e};var T=(e,t)=>{$({type:1,toast:{id:e,height:t}})},z=()=>{$({type:5,time:Date.now()})},q=new Map,Q=1e3,L=(e,t=Q)=>{if(q.has(e))return;let r=setTimeout(()=>{q.delete(e),$({type:4,toastId:e})},t);q.set(e,r)},R=e=>{let{toasts:t,pausedAt:r}=D(e);(0,o.useEffect)(()=>{if(r)return;let e=Date.now(),s=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&I.dismiss(t.id);return}return setTimeout(()=>I.dismiss(t.id),r)});return()=>{s.forEach(e=>e&&clearTimeout(e))}},[t,r]);let s=(0,o.useCallback)(()=>{r&&$({type:6,time:Date.now()})},[r]),a=(0,o.useCallback)((e,r)=>{let{reverseOrder:s=!1,gutter:a=8,defaultPosition:i}=r||{},o=t.filter(t=>(t.position||i)===(e.position||i)&&t.height),n=o.findIndex(t=>t.id===e.id),l=o.filter((e,t)=>t<n&&e.visible).length;return o.filter(e=>e.visible).slice(...s?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+a,0)},[t]);return(0,o.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)L(e.id,e.removeDelay);else{let t=q.get(e.id);t&&(clearTimeout(t),q.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:T,startPause:z,endPause:s,calculateOffset:a}}},M=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Z=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,H=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,U=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${M} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Z} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${H} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,B=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y=j("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${B} 1s linear infinite;
`,W=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,G=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,J=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${W} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${G} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,K=j("div")`
  position: absolute;
`,V=j("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=j("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:s}=e;return void 0!==t?"string"==typeof t?o.createElement(ee,null,t):t:"blank"===r?null:o.createElement(V,null,o.createElement(Y,{...s}),"loading"!==r&&o.createElement(K,null,"error"===r?o.createElement(U,{...s}):o.createElement(J,{...s})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,es=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=j("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,ei=j("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,eo=(e,t)=>{let r=e.includes("top")?1:-1,[s,a]=A()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),es(r)];return{animation:t?`${w(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},en=o.memo(({toast:e,position:t,style:r,children:s})=>{let a=e.height?eo(e.position||t||"top-center",e.visible):{opacity:0},i=o.createElement(et,{toast:e}),n=o.createElement(ei,{...e.ariaProps},k(e.message,e));return o.createElement(ea,{className:e.className,style:{...a,...r,...e.style}},"function"==typeof s?s({icon:i,message:n}):o.createElement(o.Fragment,null,i,n))});i=o.createElement,u.p=void 0,g=i,b=void 0,v=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:s,children:a})=>{let i=o.useCallback(t=>{if(t){let r=()=>{s(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return o.createElement("div",{ref:i,className:t,style:r},a)},ed=(e,t)=>{let r=e.includes("top"),s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:A()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...s}},ec=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ep=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:s,children:a,containerStyle:i,containerClassName:n})=>{let{toasts:l,handlers:d}=R(r);return o.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...i},className:n,onMouseEnter:d.startPause,onMouseLeave:d.endPause},l.map(r=>{let i=r.position||t,n=ed(i,d.calculateOffset(r,{reverseOrder:e,gutter:s,defaultPosition:t}));return o.createElement(el,{id:r.id,key:r.id,onHeightUpdate:d.updateHeight,className:r.visible?ec:"",style:n},"custom"===r.type?k(r.message,r):a?a(r):o.createElement(en,{toast:r,position:i}))}))},eu=I}},function(e){e.O(0,[396,971,458,744],function(){return e(e.s=80719)}),_N_E=e.O()}]);