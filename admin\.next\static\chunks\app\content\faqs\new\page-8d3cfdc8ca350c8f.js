(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[127],{7283:function(e,s,t){Promise.resolve().then(t.bind(t,63776))},63776:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return u}});var r=t(57437),o=t(2265),a=t(24033),n=t(61865),i=t(5925),d=t(6834),c=t(61396),l=t.n(c),m=t(30540);function u(){let e=(0,a.useRouter)(),{register:s,handleSubmit:t,formState:{errors:c},setValue:u}=(0,n.cI)(),[x,h]=(0,o.useState)(!1),[p,b]=(0,o.useState)([]);(0,o.useEffect)(()=>{(async()=>{try{let e=await m.h.get("/content/faqs/categories");b(e.data)}catch(e){i.ZP.error("获取分类列表失败"),console.error("获取分类列表失败:",e)}})()},[]);let g=async s=>{h(!0);let t={...s,categoryId:s.categoryId?Number(s.categoryId):null};try{await m.h.post("/content/faqs",t),i.ZP.success("FAQ创建成功！"),e.push("/content/faqs")}catch(e){console.error("创建FAQ失败:",e),i.ZP.error("创建FAQ失败，请稍后再试。")}finally{h(!1)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)(i.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"创建新FAQ"}),(0,r.jsx)(l(),{href:"/content/faqs",children:(0,r.jsxs)("button",{className:"bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[(0,r.jsx)(d.Ao2,{className:"mr-2"}),"返回列表"]})})]}),(0,r.jsxs)("form",{onSubmit:t(g),className:"bg-white p-8 rounded-xl shadow-xl space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"question",className:"block text-sm font-medium text-gray-700 mb-1",children:["问题 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",id:"question",...s("question",{required:"问题不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(c.question?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),c.question&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:c.question.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"answer",className:"block text-sm font-medium text-gray-700 mb-1",children:["答案 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"answer",rows:6,...s("answer",{required:"答案不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(c.answer?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),c.answer&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:c.answer.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"categoryId",className:"block text-sm font-medium text-gray-700 mb-1",children:"分类"}),(0,r.jsxs)("select",{id:"categoryId",...s("categoryId"),className:"mt-1 block w-full px-4 py-2 border ".concat(c.categoryId?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"),defaultValue:"",children:[(0,r.jsx)("option",{value:"",children:"选择分类 (可选)"}),p.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]}),c.categoryId&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:c.categoryId.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-1",children:["状态 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"status",...s("status",{required:"状态不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(c.status?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"),defaultValue:"draft",children:[(0,r.jsx)("option",{value:"draft",children:"草稿"}),(0,r.jsx)("option",{value:"published",children:"已发布"})]}),c.status&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:c.status.message})]}),(0,r.jsx)("div",{className:"flex justify-end pt-4",children:(0,r.jsxs)("button",{type:"submit",disabled:x,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[x?(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,r.jsx)(d.mW3,{className:"mr-2"}),x?"正在创建...":"创建FAQ"]})})]})]})}},30540:function(e,s,t){"use strict";t.d(s,{h:function(){return r}});let r=t(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let s=localStorage.getItem("adminToken");return s&&(e.headers.Authorization="Bearer ".concat(s)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),s.Z=r},24033:function(e,s,t){e.exports=t(15313)}},function(e){e.O(0,[737,892,865,61,971,458,744],function(){return e(e.s=7283)}),_N_E=e.O()}]);