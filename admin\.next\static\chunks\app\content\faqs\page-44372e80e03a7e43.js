(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[344],{6956:function(e,t,s){Promise.resolve().then(s.bind(s,22975))},22975:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return c}});var r=s(57437),a=s(2265),n=s(61396),l=s.n(n),i=s(5925),o=s(6834),d=s(30540);function c(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)([]),[c,m]=(0,a.useState)(!0),[x,u]=(0,a.useState)(""),[h,g]=(0,a.useState)(""),[f,b]=(0,a.useState)(null),[p,j]=(0,a.useState)(!1),[y,v]=(0,a.useState)(""),[N,w]=(0,a.useState)(null),k=async()=>{m(!0);try{let e=await d.h.get("/content/faqs",{params:{search:x,category:h}});t(e.data)}catch(e){i.ZP.error("获取FAQ列表失败"),console.error("获取FAQ列表失败:",e)}m(!1)},F=async()=>{try{let e=await d.h.get("/content/faqs/categories");n(e.data)}catch(e){i.ZP.error("获取分类列表失败"),console.error("获取分类列表失败:",e)}};(0,a.useEffect)(()=>{k(),F()},[]),(0,a.useEffect)(()=>{let e=setTimeout(()=>{k()},300);return()=>clearTimeout(e)},[x,h]);let C=async s=>{if(window.confirm("确定要删除此FAQ吗？"))try{await d.h.delete("/content/faqs/".concat(s)),t(e.filter(e=>e.id!==s)),i.ZP.success("FAQ删除成功")}catch(e){i.ZP.error("删除FAQ失败"),console.error("删除FAQ失败:",e)}},A=e=>{b(e)},Q=async s=>{if(s.preventDefault(),f)try{let s=await d.h.put("/content/faqs/".concat(f.id),f);t(e.map(e=>e.id===f.id?s.data:e)),i.ZP.success("FAQ更新成功"),b(null)}catch(e){i.ZP.error("更新FAQ失败"),console.error("更新FAQ失败:",e)}},P=async()=>{if(!y.trim()){i.ZP.error("分类名称不能为空");return}try{let e=await d.h.post("/content/faqs/categories",{name:y});n([...s,e.data]),v(""),i.ZP.success("分类添加成功")}catch(e){i.ZP.error("添加分类失败"),console.error("添加分类失败:",e)}},S=e=>{w(e),v(e.name)},q=async()=>{if(!N||!y.trim()){i.ZP.error("分类名称不能为空");return}try{let e=await d.h.put("/content/faqs/categories/".concat(N.id),{name:y});n(s.map(t=>t.id===N.id?e.data:t)),v(""),w(null),i.ZP.success("分类更新成功")}catch(e){i.ZP.error("更新分类失败"),console.error("更新分类失败:",e)}},Z=async e=>{if(window.confirm("确定要删除此分类吗？删除分类将导致该分类下的FAQ变为未分类。"))try{await d.h.delete("/content/faqs/categories/".concat(e)),n(s.filter(t=>t.id!==e)),k(),i.ZP.success("分类删除成功")}catch(e){i.ZP.error("删除分类失败"),console.error("删除分类失败:",e)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)(i.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"FAQ管理"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>j(!0),className:"bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[(0,r.jsx)(o.Ihx,{className:"mr-2"})," 管理分类"]}),(0,r.jsx)(l(),{href:"/content/faqs/new",children:(0,r.jsxs)("button",{className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[(0,r.jsx)(o.O9D,{className:"mr-2"})," 添加新FAQ"]})})]})]}),(0,r.jsx)("div",{className:"mb-6 p-4 bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700",children:"搜索问题"}),(0,r.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(o.jRj,{className:"text-gray-400"})}),(0,r.jsx)("input",{type:"text",id:"search",className:"focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-2",placeholder:"输入关键词...",value:x,onChange:e=>u(e.target.value)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"categoryFilter",className:"block text-sm font-medium text-gray-700",children:"按分类筛选"}),(0,r.jsxs)("select",{id:"categoryFilter",className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",value:h,onChange:e=>g(e.target.value),children:[(0,r.jsx)("option",{value:"",children:"所有分类"}),s.map(e=>(0,r.jsx)("option",{value:e.name,children:e.name},e.id))]})]})]})}),c?(0,r.jsx)("div",{className:"text-center py-10",children:(0,r.jsx)("p",{className:"text-lg text-gray-500",children:"正在加载FAQ..."})}):0===e.length?(0,r.jsxs)("div",{className:"text-center py-10 bg-white rounded-lg shadow",children:[(0,r.jsx)(o.$Rx,{className:"mx-auto text-gray-400 text-5xl mb-4"}),(0,r.jsx)("p",{className:"text-lg text-gray-500",children:"未找到FAQ。"}),(0,r.jsxs)("p",{className:"text-sm text-gray-400",children:["尝试调整搜索词或筛选条件，或",(0,r.jsx)(l(),{href:"/content/faqs/new",className:"text-indigo-600 hover:underline",children:"添加新的FAQ"}),"。"]})]}):(0,r.jsx)("div",{className:"bg-white shadow-xl rounded-lg overflow-hidden",children:(0,r.jsx)("ul",{className:"divide-y divide-gray-200",children:e.map(e=>(0,r.jsx)("li",{className:"p-4 hover:bg-gray-50 transition duration-150",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-indigo-700",children:e.question}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-600 whitespace-pre-wrap",children:e.answer}),(0,r.jsxs)("div",{className:"mt-2 text-xs text-gray-500",children:[(0,r.jsxs)("span",{children:["分类: ",e.category||"未分类"]})," |",(0,r.jsxs)("span",{children:["状态: ",(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"published"===e.status?"已发布":"草稿"})]})," |",(0,r.jsxs)("span",{children:["最后更新: ",new Date(e.updatedAt).toLocaleDateString()]})]})]}),(0,r.jsxs)("div",{className:"flex-shrink-0 flex space-x-2 ml-4",children:[(0,r.jsx)("button",{onClick:()=>A(e),className:"text-blue-600 hover:text-blue-800 transition duration-150 p-1 rounded-full hover:bg-blue-100",title:"编辑",children:(0,r.jsx)(o.vPQ,{size:18})}),(0,r.jsx)("button",{onClick:()=>C(e.id),className:"text-red-600 hover:text-red-800 transition duration-150 p-1 rounded-full hover:bg-red-100",title:"删除",children:(0,r.jsx)(o.Ybf,{size:18})})]})]})},e.id))})}),f&&(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-xl w-full max-w-2xl transform transition-all",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6 text-gray-800",children:"编辑FAQ"}),(0,r.jsxs)("form",{onSubmit:Q,className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"editQuestion",className:"block text-sm font-medium text-gray-700",children:"问题"}),(0,r.jsx)("input",{type:"text",id:"editQuestion",value:f.question,onChange:e=>b({...f,question:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"editAnswer",className:"block text-sm font-medium text-gray-700",children:"答案"}),(0,r.jsx)("textarea",{id:"editAnswer",rows:5,value:f.answer,onChange:e=>b({...f,answer:e.target.value}),className:"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"editCategory",className:"block text-sm font-medium text-gray-700",children:"分类"}),(0,r.jsxs)("select",{id:"editCategory",value:f.category,onChange:e=>b({...f,category:e.target.value}),className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",children:[(0,r.jsx)("option",{value:"",children:"选择分类"}),s.map(e=>(0,r.jsx)("option",{value:e.name,children:e.name},e.id))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"editStatus",className:"block text-sm font-medium text-gray-700",children:"状态"}),(0,r.jsxs)("select",{id:"editStatus",value:f.status,onChange:e=>b({...f,status:e.target.value}),className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",children:[(0,r.jsx)("option",{value:"published",children:"已发布"}),(0,r.jsx)("option",{value:"draft",children:"草稿"})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)("button",{type:"button",onClick:()=>b(null),className:"bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out",children:"取消"}),(0,r.jsxs)("button",{type:"submit",className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[(0,r.jsx)(o.mW3,{className:"mr-2"})," 保存更改"]})]})]})]})}),p&&(0,r.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-xl w-full max-w-lg transform transition-all",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"管理分类"}),(0,r.jsx)("button",{onClick:()=>{j(!1),v(""),w(null)},className:"text-gray-500 hover:text-gray-700",children:(0,r.jsx)(o.$Rx,{size:24})})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{htmlFor:"newCategoryName",className:"block text-sm font-medium text-gray-700",children:N?"编辑分类名称":"添加新分类"}),(0,r.jsxs)("div",{className:"mt-1 flex rounded-md shadow-sm",children:[(0,r.jsx)("input",{type:"text",id:"newCategoryName",value:y,onChange:e=>v(e.target.value),className:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-l-md sm:text-sm border-gray-300 px-3 py-2",placeholder:"例如：留学申请"}),(0,r.jsxs)("button",{type:"button",onClick:N?q:P,className:"".concat(N?"bg-green-500 hover:bg-green-600":"bg-blue-500 hover:bg-blue-600"," inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150"),children:[(0,r.jsx)(o.mW3,{className:"mr-2"})," ",N?"更新":"添加"]})]}),N&&(0,r.jsx)("button",{type:"button",onClick:()=>{w(null),v("")},className:"mt-2 text-sm text-gray-600 hover:text-gray-800",children:"取消编辑"})]}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"现有分类"}),0===s.length?(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"暂无分类。"}):(0,r.jsx)("ul",{className:"divide-y divide-gray-200 max-h-60 overflow-y-auto border rounded-md",children:s.map(e=>(0,r.jsxs)("li",{className:"px-4 py-3 flex justify-between items-center hover:bg-gray-50",children:[(0,r.jsx)("span",{className:"text-sm text-gray-800",children:e.name}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>S(e),className:"text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-100",title:"编辑分类",children:(0,r.jsx)(o.vPQ,{size:16})}),(0,r.jsx)("button",{onClick:()=>Z(e.id),className:"text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-100",title:"删除分类",children:(0,r.jsx)(o.Ybf,{size:16})})]})]},e.id))})]})})]})}},30540:function(e,t,s){"use strict";s.d(t,{h:function(){return r}});let r=s(54829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e)))}},function(e){e.O(0,[737,892,61,971,458,744],function(){return e(e.s=6956)}),_N_E=e.O()}]);