(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[834],{22141:function(e,t,s){Promise.resolve().then(s.bind(s,51777))},51777:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return o}});var r=s(57437),a=s(2265),l=s(24033),d=s(61865),c=s(5925),n=s(30540);function o(){let e=(0,l.useRouter)(),[t,s]=(0,a.useState)(!1),{register:o,handleSubmit:i,formState:{errors:m},watch:x}=(0,d.cI)({defaultValues:{status:"draft",category:"学业规划"}}),u=x("title"),p=e=>e.toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g,"-").replace(/^-+|-+$/g,""),h=async t=>{s(!0);try{t.slug||(t.slug=p(t.title)),await n.h.post("/content/services",t),c.ZP.success("服务创建成功"),e.push("/content/services")}catch(e){console.error("创建服务失败:",e),c.ZP.error("创建服务失败，请重试"),s(!1)}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"添加新服务"}),(0,r.jsx)("p",{className:"text-gray-600",children:"创建新的服务项目"})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("form",{onSubmit:i(h),className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务标题 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{id:"title",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(m.title?"border-red-500":"border-gray-300"),placeholder:"输入服务标题",...o("title",{required:"请输入服务标题"})}),m.title&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:m.title.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"slug",className:"block text-sm font-medium text-gray-700 mb-1",children:"URL别名"}),(0,r.jsx)("input",{id:"slug",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:u?p(u):"自动生成或手动输入",...o("slug")}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"留空将根据标题自动生成"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务分类 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"category",className:"w-full px-3 py-2 border rounded-md ".concat(m.category?"border-red-500":"border-gray-300"),...o("category",{required:"请选择服务分类"}),children:[(0,r.jsx)("option",{value:"学业规划",children:"学业规划"}),(0,r.jsx)("option",{value:"职业发展",children:"职业发展"}),(0,r.jsx)("option",{value:"留学服务",children:"留学服务"}),(0,r.jsx)("option",{value:"其他服务",children:"其他服务"})]}),m.category&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:m.category.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务简介 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"description",rows:3,className:"w-full px-3 py-2 border rounded-md ".concat(m.description?"border-red-500":"border-gray-300"),placeholder:"简要描述服务内容和特点",...o("description",{required:"请输入服务简介"})}),m.description&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:m.description.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务详情 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"content",rows:10,className:"w-full px-3 py-2 border rounded-md ".concat(m.content?"border-red-500":"border-gray-300"),placeholder:"详细描述服务内容、流程、特色等",...o("content",{required:"请输入服务详情"})}),m.content&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:m.content.message}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"支持Markdown格式"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"icon",className:"block text-sm font-medium text-gray-700 mb-1",children:"服务图标"}),(0,r.jsx)("input",{id:"icon",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"输入图标名称或URL",...o("icon")}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"可以是图标名称或图片URL"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"order",className:"block text-sm font-medium text-gray-700 mb-1",children:"排序"}),(0,r.jsx)("input",{id:"order",type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"数字越小排序越靠前",...o("order",{valueAsNumber:!0})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态"}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsxs)("label",{className:"inline-flex items-center",children:[(0,r.jsx)("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"draft",...o("status")}),(0,r.jsx)("span",{className:"ml-2",children:"草稿"})]}),(0,r.jsxs)("label",{className:"inline-flex items-center",children:[(0,r.jsx)("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"published",...o("status")}),(0,r.jsx)("span",{className:"ml-2",children:"发布"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,r.jsx)("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>e.back(),disabled:t,children:"取消"}),(0,r.jsx)("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:t,children:t?"保存中...":"保存"})]})]})}),(0,r.jsx)(c.x7,{position:"top-right"})]})}},30540:function(e,t,s){"use strict";s.d(t,{h:function(){return r}});let r=s(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=r},24033:function(e,t,s){e.exports=s(15313)}},function(e){e.O(0,[737,279,971,458,744],function(){return e(e.s=22141)}),_N_E=e.O()}]);