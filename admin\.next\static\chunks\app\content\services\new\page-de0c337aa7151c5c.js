(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[834],{22141:function(e,t,r){Promise.resolve().then(r.bind(r,51777))},51777:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return d}});var s=r(57437),a=r(2265),o=r(24033),i=r(61865),n=r(5925),l=r(30540);function d(){let e=(0,o.useRouter)(),[t,r]=(0,a.useState)(!1),{register:d,handleSubmit:c,formState:{errors:m},watch:u}=(0,i.cI)({defaultValues:{status:"draft",category:"学业规划"}}),p=u("title"),f=e=>e.toLowerCase().replace(/[^\w\u4e00-\u9fa5]+/g,"-").replace(/^-+|-+$/g,""),x=async t=>{r(!0);try{t.slug||(t.slug=f(t.title)),await l.h.post("/content/services",t),n.ZP.success("服务创建成功"),e.push("/content/services")}catch(e){console.error("创建服务失败:",e),n.ZP.error("创建服务失败，请重试"),r(!1)}};return(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"添加新服务"}),(0,s.jsx)("p",{className:"text-gray-600",children:"创建新的服务项目"})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsxs)("form",{onSubmit:c(x),className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务标题 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"title",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(m.title?"border-red-500":"border-gray-300"),placeholder:"输入服务标题",...d("title",{required:"请输入服务标题"})}),m.title&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:m.title.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"slug",className:"block text-sm font-medium text-gray-700 mb-1",children:"URL别名"}),(0,s.jsx)("input",{id:"slug",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:p?f(p):"自动生成或手动输入",...d("slug")}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"留空将根据标题自动生成"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务分类 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("select",{id:"category",className:"w-full px-3 py-2 border rounded-md ".concat(m.category?"border-red-500":"border-gray-300"),...d("category",{required:"请选择服务分类"}),children:[(0,s.jsx)("option",{value:"学业规划",children:"学业规划"}),(0,s.jsx)("option",{value:"职业发展",children:"职业发展"}),(0,s.jsx)("option",{value:"留学服务",children:"留学服务"}),(0,s.jsx)("option",{value:"其他服务",children:"其他服务"})]}),m.category&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:m.category.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务简介 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("textarea",{id:"description",rows:3,className:"w-full px-3 py-2 border rounded-md ".concat(m.description?"border-red-500":"border-gray-300"),placeholder:"简要描述服务内容和特点",...d("description",{required:"请输入服务简介"})}),m.description&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:m.description.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"content",className:"block text-sm font-medium text-gray-700 mb-1",children:["服务详情 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("textarea",{id:"content",rows:10,className:"w-full px-3 py-2 border rounded-md ".concat(m.content?"border-red-500":"border-gray-300"),placeholder:"详细描述服务内容、流程、特色等",...d("content",{required:"请输入服务详情"})}),m.content&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:m.content.message}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"支持Markdown格式"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"icon",className:"block text-sm font-medium text-gray-700 mb-1",children:"服务图标"}),(0,s.jsx)("input",{id:"icon",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"输入图标名称或URL",...d("icon")}),(0,s.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"可以是图标名称或图片URL"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"order",className:"block text-sm font-medium text-gray-700 mb-1",children:"排序"}),(0,s.jsx)("input",{id:"order",type:"number",className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"数字越小排序越靠前",...d("order",{valueAsNumber:!0})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"状态"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsxs)("label",{className:"inline-flex items-center",children:[(0,s.jsx)("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"draft",...d("status")}),(0,s.jsx)("span",{className:"ml-2",children:"草稿"})]}),(0,s.jsxs)("label",{className:"inline-flex items-center",children:[(0,s.jsx)("input",{type:"radio",className:"form-radio h-4 w-4 text-primary-600",value:"published",...d("status")}),(0,s.jsx)("span",{className:"ml-2",children:"发布"})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,s.jsx)("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>e.back(),disabled:t,children:"取消"}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:t,children:t?"保存中...":"保存"})]})]})}),(0,s.jsx)(n.x7,{position:"top-right"})]})}},30540:function(e,t,r){"use strict";r.d(t,{h:function(){return s}});let s=r(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=s},24033:function(e,t,r){e.exports=r(15313)},5925:function(e,t,r){"use strict";let s,a;r.d(t,{x7:function(){return em},ZP:function(){return eu},Am:function(){return S}});var o,i=r(2265);let n={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||n,d=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,m=/\n+/g,u=(e,t)=>{let r="",s="",a="";for(let o in e){let i=e[o];"@"==o[0]?"i"==o[1]?r=o+" "+i+";":s+="f"==o[1]?u(i,o):o+"{"+u(i,"k"==o[1]?"":t)+"}":"object"==typeof i?s+=u(i,t?t.replace(/([^,])+/g,e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):o):null!=i&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=u.p?u.p(o,i):o+":"+i+";")}return r+(t&&a?t+"{"+a+"}":a)+s},p={},f=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+f(e[r]);return t}return e},x=(e,t,r,s,a)=>{var o;let i=f(e),n=p[i]||(p[i]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(i));if(!p[n]){let t=i!==e?e:(e=>{let t,r,s=[{}];for(;t=d.exec(e.replace(c,""));)t[4]?s.shift():t[3]?(r=t[3].replace(m," ").trim(),s.unshift(s[0][r]=s[0][r]||{})):s[0][t[1]]=t[2].replace(m," ").trim();return s[0]})(e);p[n]=u(a?{["@keyframes "+n]:t}:t,r?"":"."+n)}let l=r&&p.g?p.g:null;return r&&(p.g=p[n]),o=p[n],l?t.data=t.data.replace(l,o):-1===t.data.indexOf(o)&&(t.data=s?o+t.data:t.data+o),n},h=(e,t,r)=>e.reduce((e,s,a)=>{let o=t[a];if(o&&o.call){let e=o(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":u(e,""):!1===e?"":e}return e+s+(null==o?"":o)},"");function y(e){let t=this||{},r=e.call?e(t.p):e;return x(r.unshift?r.raw?h(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let b,g,v,j=y.bind({k:1});function w(e,t){let r=this||{};return function(){let s=arguments;function a(o,i){let n=Object.assign({},o),l=n.className||a.className;r.p=Object.assign({theme:g&&g()},n),r.o=/ *go\d+/.test(l),n.className=y.apply(r,s)+(l?" "+l:""),t&&(n.ref=i);let d=e;return e[0]&&(d=n.as||e,delete n.as),v&&d[0]&&v(n),b(d,n)}return t?t(a):a}}var N=e=>"function"==typeof e,k=(e,t)=>N(e)?e(t):e,E=(s=0,()=>(++s).toString()),$=()=>{if(void 0===a&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");a=!e||e.matches}return a},C=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return C(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},O=[],D={toasts:[],pausedAt:void 0},P=e=>{D=C(D,e),O.forEach(e=>{e(D)})},A={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},F=(e={})=>{let[t,r]=(0,i.useState)(D),s=(0,i.useRef)(D);(0,i.useEffect)(()=>(s.current!==D&&r(D),O.push(r),()=>{let e=O.indexOf(r);e>-1&&O.splice(e,1)}),[]);let a=t.toasts.map(t=>{var r,s,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(s=e[t.type])?void 0:s.duration)||(null==e?void 0:e.duration)||A[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},I=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||E()}),z=e=>(t,r)=>{let s=I(t,e,r);return P({type:2,toast:s}),s.id},S=(e,t)=>z("blank")(e,t);S.error=z("error"),S.success=z("success"),S.loading=z("loading"),S.custom=z("custom"),S.dismiss=e=>{P({type:3,toastId:e})},S.remove=e=>P({type:4,toastId:e}),S.promise=(e,t,r)=>{let s=S.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?k(t.success,e):void 0;return a?S.success(a,{id:s,...r,...null==r?void 0:r.success}):S.dismiss(s),e}).catch(e=>{let a=t.error?k(t.error,e):void 0;a?S.error(a,{id:s,...r,...null==r?void 0:r.error}):S.dismiss(s)}),e};var _=(e,t)=>{P({type:1,toast:{id:e,height:t}})},L=()=>{P({type:5,time:Date.now()})},T=new Map,M=1e3,R=(e,t=M)=>{if(T.has(e))return;let r=setTimeout(()=>{T.delete(e),P({type:4,toastId:e})},t);T.set(e,r)},U=e=>{let{toasts:t,pausedAt:r}=F(e);(0,i.useEffect)(()=>{if(r)return;let e=Date.now(),s=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&S.dismiss(t.id);return}return setTimeout(()=>S.dismiss(t.id),r)});return()=>{s.forEach(e=>e&&clearTimeout(e))}},[t,r]);let s=(0,i.useCallback)(()=>{r&&P({type:6,time:Date.now()})},[r]),a=(0,i.useCallback)((e,r)=>{let{reverseOrder:s=!1,gutter:a=8,defaultPosition:o}=r||{},i=t.filter(t=>(t.position||o)===(e.position||o)&&t.height),n=i.findIndex(t=>t.id===e.id),l=i.filter((e,t)=>t<n&&e.visible).length;return i.filter(e=>e.visible).slice(...s?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+a,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)R(e.id,e.removeDelay);else{let t=T.get(e.id);t&&(clearTimeout(t),T.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:_,startPause:L,endPause:s,calculateOffset:a}}},q=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Z=j`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,H=j`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=w("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${q} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Z} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${H} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,V=j`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y=w("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${V} 1s linear infinite;
`,G=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,J=j`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,K=w("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${G} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${J} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Q=w("div")`
  position: absolute;
`,W=w("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=j`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=w("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:s}=e;return void 0!==t?"string"==typeof t?i.createElement(ee,null,t):t:"blank"===r?null:i.createElement(W,null,i.createElement(Y,{...s}),"loading"!==r&&i.createElement(Q,null,"error"===r?i.createElement(B,{...s}):i.createElement(K,{...s})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,es=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=w("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,eo=w("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let r=e.includes("top")?1:-1,[s,a]=$()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),es(r)];return{animation:t?`${j(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${j(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},en=i.memo(({toast:e,position:t,style:r,children:s})=>{let a=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},o=i.createElement(et,{toast:e}),n=i.createElement(eo,{...e.ariaProps},k(e.message,e));return i.createElement(ea,{className:e.className,style:{...a,...r,...e.style}},"function"==typeof s?s({icon:o,message:n}):i.createElement(i.Fragment,null,o,n))});o=i.createElement,u.p=void 0,b=o,g=void 0,v=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:s,children:a})=>{let o=i.useCallback(t=>{if(t){let r=()=>{s(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return i.createElement("div",{ref:o,className:t,style:r},a)},ed=(e,t)=>{let r=e.includes("top"),s=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:$()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...s}},ec=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,em=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:s,children:a,containerStyle:o,containerClassName:n})=>{let{toasts:l,handlers:d}=U(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:n,onMouseEnter:d.startPause,onMouseLeave:d.endPause},l.map(r=>{let o=r.position||t,n=ed(o,d.calculateOffset(r,{reverseOrder:e,gutter:s,defaultPosition:t}));return i.createElement(el,{id:r.id,key:r.id,onHeightUpdate:d.updateHeight,className:r.visible?ec:"",style:n},"custom"===r.type?k(r.message,r):a?a(r):i.createElement(en,{toast:r,position:o}))}))},eu=S}},function(e){e.O(0,[737,865,971,458,744],function(){return e(e.s=22141)}),_N_E=e.O()}]);