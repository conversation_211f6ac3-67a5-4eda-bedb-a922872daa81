(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[443],{26980:function(e,t,s){Promise.resolve().then(s.bind(s,97924))},97924:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return d}});var a=s(57437),r=s(2265),i=s(61396),c=s.n(i),n=s(5925),l=s(30540);function d(){let[e,t]=(0,r.useState)([]),[s,i]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(async()=>{i(!0);try{let e=await l.h.get("/content/services");t(e.data)}catch(e){n.ZP.error("获取服务列表失败"),console.error("获取服务列表失败:",e)}i(!1)})()},[]);let[d,x]=(0,r.useState)(!1),[o,p]=(0,r.useState)(null),h=async s=>{x(!0),p(s);try{await l.h.delete("/content/services/".concat(s)),t(e.filter(e=>e.id!==s)),n.ZP.success("服务已成功删除")}catch(e){console.error("删除服务失败:",e),n.ZP.error("删除服务失败，请重试")}finally{x(!1),p(null)}},m=async(s,a)=>{let r="published"===a?"draft":"published";try{await l.h.patch("/content/services/".concat(s),{status:r}),t(e.map(e=>e.id===s?{...e,status:r}:e)),n.ZP.success("服务状态已更改为".concat("published"===r?"已发布":"草稿"))}catch(e){console.error("更改服务状态失败:",e),n.ZP.error("更改服务状态失败，请重试")}};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold",children:"服务管理"}),(0,a.jsx)(c(),{href:"/content/services/new",className:"px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors",children:"添加服务"})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"ID"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"标题"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"别名"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"分类"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"创建日期"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"更新日期"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.id}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:e.title}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.slug}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.category}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"),children:"published"===e.status?"已发布":"草稿"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.createdAt}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.updatedAt}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>m(e.id,e.status),className:"text-indigo-600 hover:text-indigo-900",children:"published"===e.status?"设为草稿":"发布"}),(0,a.jsx)(c(),{href:"/content/services/edit/".concat(e.id),className:"text-blue-600 hover:text-blue-900",children:"编辑"}),(0,a.jsx)("button",{onClick:()=>h(e.id),disabled:d&&o===e.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:d&&o===e.id?"删除中...":"删除"})]})})]},e.id))})]})}),(0,a.jsx)(n.x7,{position:"top-right"})]})}},30540:function(e,t,s){"use strict";s.d(t,{h:function(){return a}});let a=s(54829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e)))}},function(e){e.O(0,[737,892,971,458,744],function(){return e(e.s=26980)}),_N_E=e.O()}]);