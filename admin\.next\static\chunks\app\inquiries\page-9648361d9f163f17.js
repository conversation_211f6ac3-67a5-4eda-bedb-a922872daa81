(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[370],{30403:function(e,t,r){Promise.resolve().then(r.bind(r,76820))},31584:function(e,t,r){"use strict";r.d(t,{H:function(){return l},a:function(){return c}});var a=r(57437),s=r(2265),i=r(24033),o=r(30540);let n=(0,s.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(!0),u=(0,i.useRouter)(),p=(0,i.usePathname)();(0,s.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(o.h.defaults.headers.common.Authorization="Bearer ".concat(e),l(JSON.parse(t))):"/login"!==p&&u.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[p,u]);let m=async(e,t)=>{try{let{user:r,token:a}=(await o.h.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),o.h.defaults.headers.common.Authorization="Bearer ".concat(a),l(r),r}catch(e){throw console.error("登录失败:",e),e}};return(0,a.jsx)(n.Provider,{value:{user:r,loading:c,login:m,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete o.h.defaults.headers.common.Authorization,l(null),u.push("/login")},isAuthenticated:!!r},children:t})}function c(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},76820:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return c}});var a=r(57437),s=r(2265),i=r(31584),o=r(24033),n=r(5925),l=r(30540);function c(){let{user:e}=(0,i.a)();(0,o.useRouter)();let[t,r]=(0,s.useState)([]),[c,d]=(0,s.useState)(!0),[u,p]=(0,s.useState)(""),[m,h]=(0,s.useState)(null),[f,g]=(0,s.useState)(!1),[y,x]=(0,s.useState)("all"),[b,v]=(0,s.useState)("");(0,s.useEffect)(()=>{(async()=>{try{d(!0);let e=await l.Z.get("/inquiries");if(console.log("咨询API响应:",e.data),e.data.success){let t=e.data.data.items||e.data.data||[];r(t)}else console.error("API返回失败状态:",e.data),r([]),n.ZP.error("获取咨询列表失败")}catch(e){console.error("获取咨询列表失败:",e),r([]),n.ZP.error("获取咨询列表失败")}finally{d(!1)}})()},[]);let w=t.filter(e=>{if("all"!==y&&e.status!==y)return!1;if(b){let t=b.toLowerCase();return e.name.toLowerCase().includes(t)||e.email.toLowerCase().includes(t)||e.subject.toLowerCase().includes(t)||e.message.toLowerCase().includes(t)}return!0}),j=async t=>{if(!u.trim()){n.ZP.error("回复内容不能为空");return}try{g(!0),await l.Z.post("/inquiries/".concat(t,"/reply"),{reply:u,replied_by_user_id:null==e?void 0:e.id}),r(r=>r.map(r=>r.id===t?{...r,status:"replied",reply:u,replied_at:new Date().toISOString(),replied_by_user:{id:(null==e?void 0:e.id)||0,name:(null==e?void 0:e.name)||""}}:r)),p(""),h(null),n.ZP.success("回复成功")}catch(e){console.error("回复咨询失败:",e),n.ZP.error("回复咨询失败")}finally{g(!1)}},N=async e=>{try{await l.Z.patch("/inquiries/".concat(e,"/status"),{status:"closed"}),r(t=>t.map(t=>t.id===e?{...t,status:"closed"}:t)),n.ZP.success("咨询已关闭")}catch(e){console.error("关闭咨询失败:",e),n.ZP.error("关闭咨询失败")}},k=async e=>{try{await l.Z.patch("/inquiries/".concat(e,"/status"),{status:"pending"}),r(t=>t.map(t=>t.id===e?{...t,status:"pending"}:t)),n.ZP.success("咨询已重新打开")}catch(e){console.error("重新打开咨询失败:",e),n.ZP.error("重新打开咨询失败")}},C=e=>new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),E=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"replied":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},S=e=>{switch(e){case"pending":return"待处理";case"replied":return"已回复";case"closed":return"已关闭";default:return"未知状态"}};return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"咨询管理"}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between mb-6 gap-4",children:[(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>x("all"),className:"px-3 py-1 rounded ".concat("all"===y?"bg-blue-500 text-white":"bg-gray-200"),children:"全部"}),(0,a.jsx)("button",{onClick:()=>x("pending"),className:"px-3 py-1 rounded ".concat("pending"===y?"bg-blue-500 text-white":"bg-gray-200"),children:"待处理"}),(0,a.jsx)("button",{onClick:()=>x("replied"),className:"px-3 py-1 rounded ".concat("replied"===y?"bg-blue-500 text-white":"bg-gray-200"),children:"已回复"}),(0,a.jsx)("button",{onClick:()=>x("closed"),className:"px-3 py-1 rounded ".concat("closed"===y?"bg-blue-500 text-white":"bg-gray-200"),children:"已关闭"})]}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("input",{type:"text",placeholder:"搜索咨询...",className:"border rounded px-3 py-1 w-full md:w-64",value:b,onChange:e=>v(e.target.value)})})]}),c?(0,a.jsx)("div",{className:"text-center py-10",children:"加载中..."}):0===w.length?(0,a.jsx)("div",{className:"text-center py-10 text-gray-500",children:"暂无咨询数据"}):(0,a.jsx)("div",{className:"space-y-6",children:w.map(e=>(0,a.jsxs)("div",{className:"border rounded-lg overflow-hidden bg-white shadow-sm",children:[(0,a.jsx)("div",{className:"p-4 border-b",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-bold text-lg",children:e.subject}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[e.name," (",e.email,")",e.phone&&" \xb7 ".concat(e.phone)]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat(E(e.status)),children:S(e.status)}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:C(e.created_at)})]})]})}),(0,a.jsx)("div",{className:"p-4 bg-gray-50",children:(0,a.jsx)("p",{className:"whitespace-pre-wrap",children:e.message})}),e.reply&&(0,a.jsxs)("div",{className:"p-4 border-t bg-blue-50",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h4",{className:"font-medium",children:"回复"}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[e.replied_at&&C(e.replied_at),e.replied_by_user&&" \xb7 ".concat(e.replied_by_user.name)]})]}),(0,a.jsx)("p",{className:"whitespace-pre-wrap",children:e.reply})]}),(0,a.jsxs)("div",{className:"p-4 border-t bg-gray-100 flex justify-end space-x-2",children:["pending"===e.status&&(0,a.jsx)("button",{onClick:()=>h(e.id),className:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600",children:"回复"}),"pending"===e.status&&(0,a.jsx)("button",{onClick:()=>N(e.id),className:"px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600",children:"关闭"}),"replied"===e.status&&(0,a.jsx)("button",{onClick:()=>N(e.id),className:"px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600",children:"关闭"}),"closed"===e.status&&(0,a.jsx)("button",{onClick:()=>k(e.id),className:"px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600",children:"重新打开"})]}),m===e.id&&(0,a.jsxs)("div",{className:"p-4 border-t",children:[(0,a.jsx)("textarea",{className:"w-full border rounded p-2 mb-2",rows:4,placeholder:"输入回复内容...",value:u,onChange:e=>p(e.target.value)}),(0,a.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{h(null),p("")},className:"px-3 py-1 bg-gray-300 rounded hover:bg-gray-400",children:"取消"}),(0,a.jsx)("button",{onClick:()=>j(e.id),disabled:f,className:"px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300",children:f?"发送中...":"发送回复"})]})]})]},e.id))}),(0,a.jsx)(n.x7,{})]})}},30540:function(e,t,r){"use strict";r.d(t,{h:function(){return a}});let a=r(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=a},24033:function(e,t,r){e.exports=r(15313)},5925:function(e,t,r){"use strict";let a,s;r.d(t,{x7:function(){return eu},ZP:function(){return ep},Am:function(){return D}});var i,o=r(2265);let n={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||n,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,p=(e,t)=>{let r="",a="",s="";for(let i in e){let o=e[i];"@"==i[0]?"i"==i[1]?r=i+" "+o+";":a+="f"==i[1]?p(o,i):i+"{"+p(o,"k"==i[1]?"":t)+"}":"object"==typeof o?a+=p(o,t?t.replace(/([^,])+/g,e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):i):null!=o&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=p.p?p.p(i,o):i+":"+o+";")}return r+(t&&s?t+"{"+s+"}":s)+a},m={},h=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+h(e[r]);return t}return e},f=(e,t,r,a,s)=>{var i;let o=h(e),n=m[o]||(m[o]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(o));if(!m[n]){let t=o!==e?e:(e=>{let t,r,a=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?a.shift():t[3]?(r=t[3].replace(u," ").trim(),a.unshift(a[0][r]=a[0][r]||{})):a[0][t[1]]=t[2].replace(u," ").trim();return a[0]})(e);m[n]=p(s?{["@keyframes "+n]:t}:t,r?"":"."+n)}let l=r&&m.g?m.g:null;return r&&(m.g=m[n]),i=m[n],l?t.data=t.data.replace(l,i):-1===t.data.indexOf(i)&&(t.data=a?i+t.data:t.data+i),n},g=(e,t,r)=>e.reduce((e,a,s)=>{let i=t[s];if(i&&i.call){let e=i(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=t?"."+t:e&&"object"==typeof e?e.props?"":p(e,""):!1===e?"":e}return e+a+(null==i?"":i)},"");function y(e){let t=this||{},r=e.call?e(t.p):e;return f(r.unshift?r.raw?g(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let x,b,v,w=y.bind({k:1});function j(e,t){let r=this||{};return function(){let a=arguments;function s(i,o){let n=Object.assign({},i),l=n.className||s.className;r.p=Object.assign({theme:b&&b()},n),r.o=/ *go\d+/.test(l),n.className=y.apply(r,a)+(l?" "+l:""),t&&(n.ref=o);let c=e;return e[0]&&(c=n.as||e,delete n.as),v&&c[0]&&v(n),x(c,n)}return t?t(s):s}}var N=e=>"function"==typeof e,k=(e,t)=>N(e)?e(t):e,C=(a=0,()=>(++a).toString()),E=()=>{if(void 0===s&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");s=!e||e.matches}return s},S=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return S(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let s=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+s}))}}},P=[],_={toasts:[],pausedAt:void 0},I=e=>{_=S(_,e),P.forEach(e=>{e(_)})},A={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},$=(e={})=>{let[t,r]=(0,o.useState)(_),a=(0,o.useRef)(_);(0,o.useEffect)(()=>(a.current!==_&&r(_),P.push(r),()=>{let e=P.indexOf(r);e>-1&&P.splice(e,1)}),[]);let s=t.toasts.map(t=>{var r,a,s;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||A[t.type],style:{...e.style,...null==(s=e[t.type])?void 0:s.style,...t.style}}});return{...t,toasts:s}},O=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||C()}),Z=e=>(t,r)=>{let a=O(t,e,r);return I({type:2,toast:a}),a.id},D=(e,t)=>Z("blank")(e,t);D.error=Z("error"),D.success=Z("success"),D.loading=Z("loading"),D.custom=Z("custom"),D.dismiss=e=>{I({type:3,toastId:e})},D.remove=e=>I({type:4,toastId:e}),D.promise=(e,t,r)=>{let a=D.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let s=t.success?k(t.success,e):void 0;return s?D.success(s,{id:a,...r,...null==r?void 0:r.success}):D.dismiss(a),e}).catch(e=>{let s=t.error?k(t.error,e):void 0;s?D.error(s,{id:a,...r,...null==r?void 0:r.error}):D.dismiss(a)}),e};var z=(e,t)=>{I({type:1,toast:{id:e,height:t}})},T=()=>{I({type:5,time:Date.now()})},L=new Map,U=1e3,q=(e,t=U)=>{if(L.has(e))return;let r=setTimeout(()=>{L.delete(e),I({type:4,toastId:e})},t);L.set(e,r)},H=e=>{let{toasts:t,pausedAt:r}=$(e);(0,o.useEffect)(()=>{if(r)return;let e=Date.now(),a=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&D.dismiss(t.id);return}return setTimeout(()=>D.dismiss(t.id),r)});return()=>{a.forEach(e=>e&&clearTimeout(e))}},[t,r]);let a=(0,o.useCallback)(()=>{r&&I({type:6,time:Date.now()})},[r]),s=(0,o.useCallback)((e,r)=>{let{reverseOrder:a=!1,gutter:s=8,defaultPosition:i}=r||{},o=t.filter(t=>(t.position||i)===(e.position||i)&&t.height),n=o.findIndex(t=>t.id===e.id),l=o.filter((e,t)=>t<n&&e.visible).length;return o.filter(e=>e.visible).slice(...a?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+s,0)},[t]);return(0,o.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)q(e.id,e.removeDelay);else{let t=L.get(e.id);t&&(clearTimeout(t),L.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:z,startPause:T,endPause:a,calculateOffset:s}}},M=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,F=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,R=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${M} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${F} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${R} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,J=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y=j("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${J} 1s linear infinite;
`,G=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,K=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Q=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${G} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${K} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,V=j("div")`
  position: absolute;
`,W=j("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=j("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:a}=e;return void 0!==t?"string"==typeof t?o.createElement(ee,null,t):t:"blank"===r?null:o.createElement(W,null,o.createElement(Y,{...a}),"loading"!==r&&o.createElement(V,null,"error"===r?o.createElement(B,{...a}):o.createElement(Q,{...a})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ea=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,es=j("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,ei=j("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,eo=(e,t)=>{let r=e.includes("top")?1:-1,[a,s]=E()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),ea(r)];return{animation:t?`${w(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},en=o.memo(({toast:e,position:t,style:r,children:a})=>{let s=e.height?eo(e.position||t||"top-center",e.visible):{opacity:0},i=o.createElement(et,{toast:e}),n=o.createElement(ei,{...e.ariaProps},k(e.message,e));return o.createElement(es,{className:e.className,style:{...s,...r,...e.style}},"function"==typeof a?a({icon:i,message:n}):o.createElement(o.Fragment,null,i,n))});i=o.createElement,p.p=void 0,x=i,b=void 0,v=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:a,children:s})=>{let i=o.useCallback(t=>{if(t){let r=()=>{a(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,a]);return o.createElement("div",{ref:i,className:t,style:r},s)},ec=(e,t)=>{let r=e.includes("top"),a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:E()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...a}},ed=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:a,children:s,containerStyle:i,containerClassName:n})=>{let{toasts:l,handlers:c}=H(r);return o.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...i},className:n,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(r=>{let i=r.position||t,n=ec(i,c.calculateOffset(r,{reverseOrder:e,gutter:a,defaultPosition:t}));return o.createElement(el,{id:r.id,key:r.id,onHeightUpdate:c.updateHeight,className:r.visible?ed:"",style:n},"custom"===r.type?k(r.message,r):s?s(r):o.createElement(en,{toast:r,position:i}))}))},ep=D}},function(e){e.O(0,[737,971,458,744],function(){return e(e.s=30403)}),_N_E=e.O()}]);