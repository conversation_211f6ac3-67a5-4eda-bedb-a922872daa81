(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{38770:function(e,t,n){Promise.resolve().then(n.bind(n,56377))},31584:function(e,t,n){"use strict";n.d(t,{H:function(){return c},a:function(){return i}});var s=n(57437),r=n(2265),l=n(24033),o=n(30540);let a=(0,r.createContext)(void 0);function c(e){let{children:t}=e,[n,c]=(0,r.useState)(null),[i,d]=(0,r.useState)(!0),[h,u]=(0,r.useState)(!1),m=(0,l.useRouter)(),g=(0,l.usePathname)();(0,r.useEffect)(()=>{h||(()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(e&&t&&"undefined"!==t&&"null"!==t)try{o.Z.defaults.headers.common.Authorization="Bearer ".concat(e);let n=JSON.parse(t);c(n),console.log("从本地存储恢复用户状态:",n)}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),c(null)}}catch(e){console.error("认证检查失败:",e),c(null)}finally{d(!1),u(!0)}})()},[h]),(0,r.useEffect)(()=>{!h||i||n||"/login"===g||m.push("/login")},[h,i,n,g,m]),(0,r.useEffect)(()=>{let e=()=>{console.log("收到401错误，自动登出"),c(null),"/login"!==g&&m.push("/login")};return window.addEventListener("auth:logout",e),()=>{window.removeEventListener("auth:logout",e)}},[g,m]);let b=async(e,t)=>{try{console.log("AuthContext: 发送登录请求",{username:e});let n=await o.Z.post("/auth/login",{username:e,password:t});if(console.log("AuthContext: 收到响应",n.data),!n.data||!n.data.data)throw Error("API响应格式错误");let{user:s,token:r}=n.data.data;if(!s||!r)throw Error("响应中缺少用户信息或令牌");console.log("AuthContext: 解析的用户数据",{user:s,token:r}),localStorage.setItem("adminToken",r),localStorage.setItem("adminUser",JSON.stringify(s)),o.Z.defaults.headers.common.Authorization="Bearer ".concat(r),c(s),console.log("AuthContext: 登录成功，用户状态已更新")}catch(e){throw console.error("AuthContext: 登录失败",e),e}};return(0,s.jsx)(a.Provider,{value:{user:n,loading:i,login:b,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete o.Z.defaults.headers.common.Authorization,c(null),m.push("/login")},updateUserInfo:e=>{if(n){let t={...n,...e};c(t),localStorage.setItem("adminUser",JSON.stringify(t))}},isAuthenticated:!!n},children:t})}function i(){let e=(0,r.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},56377:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return d}});var s=n(57437);n(2265),n(52445);var r=n(31584),l=n(5925),o=n(61396),a=n.n(o),c=n(24033);function i(e){let{children:t}=e,{user:n,logout:l,isAuthenticated:o,loading:i}=(0,r.a)(),d=(0,c.usePathname)();return"/login"===d?(0,s.jsx)(s.Fragment,{children:t}):i?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsx)("div",{className:"text-lg",children:"加载中..."})}):o?(0,s.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[(0,s.jsx)("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("span",{className:"text-sm",children:["欢迎，",null==n?void 0:n.name]}),(0,s.jsx)("button",{onClick:l,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,s.jsxs)("div",{className:"flex pt-16",children:[(0,s.jsx)("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,s.jsxs)("ul",{className:"space-y-2 mt-4",children:[(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/dashboard",className:"block p-2 hover:bg-gray-200 rounded ".concat("/dashboard"===d?"bg-blue-100 text-blue-600":""),children:"仪表盘"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/users",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/users")?"bg-blue-100 text-blue-600":""),children:"用户管理"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/content/articles",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""),children:"文章管理"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/content/services",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/content/services")?"bg-blue-100 text-blue-600":""),children:"服务管理"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/content/cases",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""),children:"案例管理"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/content/banners",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""),children:"Banner管理"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/content/faqs",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/content/faqs")?"bg-blue-100 text-blue-600":""),children:"FAQ管理"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/consultants",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/consultants")?"bg-blue-100 text-blue-600":""),children:"咨询师管理"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/appointments",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/appointments")?"bg-blue-100 text-blue-600":""),children:"预约管理"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/inquiries",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""),children:"客户咨询"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/team",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/team")?"bg-blue-100 text-blue-600":""),children:"团队管理"})}),(0,s.jsx)("li",{children:(0,s.jsx)(a(),{href:"/settings",className:"block p-2 hover:bg-gray-200 rounded ".concat(d.startsWith("/settings")?"bg-blue-100 text-blue-600":""),children:"系统设置"})})]})}),(0,s.jsx)("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:t})]})]}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),(0,s.jsx)(a(),{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function d(e){let{children:t}=e;return(0,s.jsx)("html",{lang:"zh",children:(0,s.jsx)("body",{children:(0,s.jsxs)(r.H,{children:[(0,s.jsx)(i,{children:t}),(0,s.jsx)(l.x7,{position:"top-right"})]})})})}},30540:function(e,t,n){"use strict";n.d(t,{h:function(){return s}});let s=n(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete s.defaults.headers.common.Authorization,window.dispatchEvent(new CustomEvent("auth:logout"))),Promise.reject(e))),t.Z=s},52445:function(){},24033:function(e,t,n){e.exports=n(15313)}},function(e){e.O(0,[737,892,971,458,744],function(){return e(e.s=38770)}),_N_E=e.O()}]);