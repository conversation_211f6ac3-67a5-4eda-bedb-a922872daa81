(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{38770:function(e,t,r){Promise.resolve().then(r.bind(r,56377))},31584:function(e,t,r){"use strict";r.d(t,{H:function(){return l},a:function(){return c}});var a=r(57437),s=r(2265),o=r(24033),i=r(30540);let n=(0,s.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(!0),u=(0,o.useRouter)(),m=(0,o.usePathname)();(0,s.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(i.h.defaults.headers.common.Authorization="Bearer ".concat(e),l(JSON.parse(t))):"/login"!==m&&u.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[m,u]);let p=async(e,t)=>{try{let{user:r,token:a}=(await i.h.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),i.h.defaults.headers.common.Authorization="Bearer ".concat(a),l(r),r}catch(e){throw console.error("登录失败:",e),e}};return(0,a.jsx)(n.Provider,{value:{user:r,loading:c,login:p,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.h.defaults.headers.common.Authorization,l(null),u.push("/login")},isAuthenticated:!!r},children:t})}function c(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},56377:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return l}});var a=r(57437);r(2265),r(52445);var s=r(31584),o=r(5925),i=r(24033);function n(e){let{children:t}=e,{user:r,logout:o,isAuthenticated:n,loading:l}=(0,s.a)(),c=(0,i.usePathname)();return"/login"===c?(0,a.jsx)(a.Fragment,{children:t}):l?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsx)("div",{className:"text-lg",children:"加载中..."})}):n?(0,a.jsxs)("div",{className:"admin-layout min-h-screen bg-gray-100",children:[(0,a.jsx)("header",{className:"bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h1",{className:"text-xl font-semibold",children:"后台管理系统"}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("span",{className:"text-sm",children:["欢迎，",null==r?void 0:r.name]}),(0,a.jsx)("button",{onClick:o,className:"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm",children:"登出"})]})]})}),(0,a.jsxs)("div",{className:"flex pt-16",children:[(0,a.jsx)("nav",{className:"bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40",children:(0,a.jsxs)("ul",{className:"space-y-2 mt-4",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/",className:"block p-2 hover:bg-gray-200 rounded ".concat("/"===c?"bg-blue-100 text-blue-600":""),children:"仪表盘"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/users",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/users")?"bg-blue-100 text-blue-600":""),children:"用户管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/content/articles",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/content/articles")?"bg-blue-100 text-blue-600":""),children:"文章管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/content/services",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/content/services")?"bg-blue-100 text-blue-600":""),children:"服务管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/content/cases",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/content/cases")?"bg-blue-100 text-blue-600":""),children:"案例管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/content/banners",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/content/banners")?"bg-blue-100 text-blue-600":""),children:"Banner管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/content/faq",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/content/faq")?"bg-blue-100 text-blue-600":""),children:"FAQ管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/consultants",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/consultants")?"bg-blue-100 text-blue-600":""),children:"咨询师管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/appointments",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/appointments")?"bg-blue-100 text-blue-600":""),children:"预约管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/inquiries",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/inquiries")?"bg-blue-100 text-blue-600":""),children:"客户咨询"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/team",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/team")?"bg-blue-100 text-blue-600":""),children:"团队管理"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/settings",className:"block p-2 hover:bg-gray-200 rounded ".concat(c.startsWith("/settings")?"bg-blue-100 text-blue-600":""),children:"系统设置"})})]})}),(0,a.jsx)("main",{className:"flex-1 p-6 md:ml-64 bg-gray-100",children:t})]})]}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:(0,a.jsxs)("div",{className:"max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"需要登录"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"请先登录以访问管理系统"}),(0,a.jsx)("a",{href:"/login",className:"bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700",children:"前往登录"})]})})}function l(e){let{children:t}=e;return(0,a.jsx)("html",{lang:"zh",children:(0,a.jsx)("body",{children:(0,a.jsxs)(s.H,{children:[(0,a.jsx)(n,{children:t}),(0,a.jsx)(o.x7,{position:"top-right"})]})})})}},30540:function(e,t,r){"use strict";r.d(t,{h:function(){return a}});let a=r(54829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e)))},52445:function(){},24033:function(e,t,r){e.exports=r(15313)},5925:function(e,t,r){"use strict";let a,s;r.d(t,{x7:function(){return eu},ZP:function(){return em},Am:function(){return T}});var o,i=r(2265);let n={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||n,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,m=(e,t)=>{let r="",a="",s="";for(let o in e){let i=e[o];"@"==o[0]?"i"==o[1]?r=o+" "+i+";":a+="f"==o[1]?m(i,o):o+"{"+m(i,"k"==o[1]?"":t)+"}":"object"==typeof i?a+=m(i,t?t.replace(/([^,])+/g,e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):o):null!=i&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=m.p?m.p(o,i):o+":"+i+";")}return r+(t&&s?t+"{"+s+"}":s)+a},p={},h=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+h(e[r]);return t}return e},f=(e,t,r,a,s)=>{var o;let i=h(e),n=p[i]||(p[i]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(i));if(!p[n]){let t=i!==e?e:(e=>{let t,r,a=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?a.shift():t[3]?(r=t[3].replace(u," ").trim(),a.unshift(a[0][r]=a[0][r]||{})):a[0][t[1]]=t[2].replace(u," ").trim();return a[0]})(e);p[n]=m(s?{["@keyframes "+n]:t}:t,r?"":"."+n)}let l=r&&p.g?p.g:null;return r&&(p.g=p[n]),o=p[n],l?t.data=t.data.replace(l,o):-1===t.data.indexOf(o)&&(t.data=a?o+t.data:t.data+o),n},g=(e,t,r)=>e.reduce((e,a,s)=>{let o=t[s];if(o&&o.call){let e=o(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":m(e,""):!1===e?"":e}return e+a+(null==o?"":o)},"");function b(e){let t=this||{},r=e.call?e(t.p):e;return f(r.unshift?r.raw?g(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}b.bind({g:1});let x,y,v,j=b.bind({k:1});function w(e,t){let r=this||{};return function(){let a=arguments;function s(o,i){let n=Object.assign({},o),l=n.className||s.className;r.p=Object.assign({theme:y&&y()},n),r.o=/ *go\d+/.test(l),n.className=b.apply(r,a)+(l?" "+l:""),t&&(n.ref=i);let c=e;return e[0]&&(c=n.as||e,delete n.as),v&&c[0]&&v(n),x(c,n)}return t?t(s):s}}var N=e=>"function"==typeof e,k=(e,t)=>N(e)?e(t):e,E=(a=0,()=>(++a).toString()),$=()=>{if(void 0===s&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");s=!e||e.matches}return s},A=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return A(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let s=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+s}))}}},S=[],z={toasts:[],pausedAt:void 0},C=e=>{z=A(z,e),S.forEach(e=>{e(z)})},I={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},O=(e={})=>{let[t,r]=(0,i.useState)(z),a=(0,i.useRef)(z);(0,i.useEffect)(()=>(a.current!==z&&r(z),S.push(r),()=>{let e=S.indexOf(r);e>-1&&S.splice(e,1)}),[]);let s=t.toasts.map(t=>{var r,a,s;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||I[t.type],style:{...e.style,...null==(s=e[t.type])?void 0:s.style,...t.style}}});return{...t,toasts:s}},P=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||E()}),D=e=>(t,r)=>{let a=P(t,e,r);return C({type:2,toast:a}),a.id},T=(e,t)=>D("blank")(e,t);T.error=D("error"),T.success=D("success"),T.loading=D("loading"),T.custom=D("custom"),T.dismiss=e=>{C({type:3,toastId:e})},T.remove=e=>C({type:4,toastId:e}),T.promise=(e,t,r)=>{let a=T.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let s=t.success?k(t.success,e):void 0;return s?T.success(s,{id:a,...r,...null==r?void 0:r.success}):T.dismiss(a),e}).catch(e=>{let s=t.error?k(t.error,e):void 0;s?T.error(s,{id:a,...r,...null==r?void 0:r.error}):T.dismiss(a)}),e};var W=(e,t)=>{C({type:1,toast:{id:e,height:t}})},_=()=>{C({type:5,time:Date.now()})},F=new Map,H=1e3,U=(e,t=H)=>{if(F.has(e))return;let r=setTimeout(()=>{F.delete(e),C({type:4,toastId:e})},t);F.set(e,r)},q=e=>{let{toasts:t,pausedAt:r}=O(e);(0,i.useEffect)(()=>{if(r)return;let e=Date.now(),a=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&T.dismiss(t.id);return}return setTimeout(()=>T.dismiss(t.id),r)});return()=>{a.forEach(e=>e&&clearTimeout(e))}},[t,r]);let a=(0,i.useCallback)(()=>{r&&C({type:6,time:Date.now()})},[r]),s=(0,i.useCallback)((e,r)=>{let{reverseOrder:a=!1,gutter:s=8,defaultPosition:o}=r||{},i=t.filter(t=>(t.position||o)===(e.position||o)&&t.height),n=i.findIndex(t=>t.id===e.id),l=i.filter((e,t)=>t<n&&e.visible).length;return i.filter(e=>e.visible).slice(...a?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+s,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)U(e.id,e.removeDelay);else{let t=F.get(e.id);t&&(clearTimeout(t),F.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:W,startPause:_,endPause:a,calculateOffset:s}}},M=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,B=j`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,L=j`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,R=w("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${M} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${B} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${L} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Z=j`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,J=w("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Z} 1s linear infinite;
`,Q=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Y=j`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,G=w("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Q} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Y} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,K=w("div")`
  position: absolute;
`,V=w("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=j`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=w("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:a}=e;return void 0!==t?"string"==typeof t?i.createElement(ee,null,t):t:"blank"===r?null:i.createElement(V,null,i.createElement(J,{...a}),"loading"!==r&&i.createElement(K,null,"error"===r?i.createElement(R,{...a}):i.createElement(G,{...a})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ea=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,es=w("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,eo=w("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let r=e.includes("top")?1:-1,[a,s]=$()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),ea(r)];return{animation:t?`${j(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${j(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},en=i.memo(({toast:e,position:t,style:r,children:a})=>{let s=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},o=i.createElement(et,{toast:e}),n=i.createElement(eo,{...e.ariaProps},k(e.message,e));return i.createElement(es,{className:e.className,style:{...s,...r,...e.style}},"function"==typeof a?a({icon:o,message:n}):i.createElement(i.Fragment,null,o,n))});o=i.createElement,m.p=void 0,x=o,y=void 0,v=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:a,children:s})=>{let o=i.useCallback(t=>{if(t){let r=()=>{a(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,a]);return i.createElement("div",{ref:o,className:t,style:r},s)},ec=(e,t)=>{let r=e.includes("top"),a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:$()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...a}},ed=b`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:a,children:s,containerStyle:o,containerClassName:n})=>{let{toasts:l,handlers:c}=q(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:n,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(r=>{let o=r.position||t,n=ec(o,c.calculateOffset(r,{reverseOrder:e,gutter:a,defaultPosition:t}));return i.createElement(el,{id:r.id,key:r.id,onHeightUpdate:c.updateHeight,className:r.visible?ed:"",style:n},"custom"===r.type?k(r.message,r):s?s(r):i.createElement(en,{toast:r,position:o}))}))},em=T}},function(e){e.O(0,[737,971,458,744],function(){return e(e.s=38770)}),_N_E=e.O()}]);