(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[626],{24101:function(e,r,t){Promise.resolve().then(t.bind(t,44338))},44338:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return l}});var s=t(57437),n=t(2265),a=t(24033),o=t(30540);function l(){let[e,r]=(0,n.useState)(""),[t,l]=(0,n.useState)(""),[d,i]=(0,n.useState)(""),[u,c]=(0,n.useState)(!1),m=(0,a.useRouter)(),p=async r=>{r.preventDefault(),i(""),c(!0);try{let r=await o.h.post("/auth/login",{username:e,password:t});localStorage.setItem("adminToken",r.data.token),localStorage.setItem("adminUser",JSON.stringify(r.data.user)),m.push("/")}catch(e){var s,n;i((null===(n=e.response)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.error)||"登录失败，请重试")}finally{c(!1)}};return(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8 bg-white p-10 rounded-lg shadow-md",children:[(0,s.jsx)("div",{children:(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"管理系统登录"})}),d&&(0,s.jsx)("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 mb-4",children:(0,s.jsx)("p",{className:"text-red-700",children:d})}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:p,children:[(0,s.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"username",className:"sr-only",children:"用户名"}),(0,s.jsx)("input",{id:"username",name:"username",type:"text",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"用户名",value:e,onChange:e=>r(e.target.value)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"sr-only",children:"密码"}),(0,s.jsx)("input",{id:"password",name:"password",type:"password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"密码",value:t,onChange:e=>l(e.target.value)})]})]}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:u,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300",children:u?"登录中...":"登录"})})]})]})})}},30540:function(e,r,t){"use strict";t.d(r,{h:function(){return s}});let s=t(54829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let r=localStorage.getItem("adminToken");return r&&(e.headers.Authorization="Bearer ".concat(r)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),r.Z=s},24033:function(e,r,t){e.exports=t(15313)}},function(e){e.O(0,[737,971,458,744],function(){return e(e.s=24101)}),_N_E=e.O()}]);