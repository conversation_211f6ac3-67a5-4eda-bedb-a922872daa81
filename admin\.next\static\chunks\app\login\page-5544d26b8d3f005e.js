(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[626],{24101:function(e,t,r){Promise.resolve().then(r.bind(r,44338))},31584:function(e,t,r){"use strict";r.d(t,{H:function(){return l},a:function(){return d}});var a=r(57437),o=r(2265),s=r(24033),i=r(30540);let n=(0,o.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,o.useState)(null),[d,c]=(0,o.useState)(!0),u=(0,s.useRouter)(),p=(0,s.usePathname)();(0,o.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(i.h.defaults.headers.common.Authorization="Bearer ".concat(e),l(JSON.parse(t))):"/login"!==p&&u.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{c(!1)}})()},[p,u]);let m=async(e,t)=>{try{let{user:r,token:a}=(await i.h.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(r)),i.h.defaults.headers.common.Authorization="Bearer ".concat(a),l(r),r}catch(e){throw console.error("登录失败:",e),e}};return(0,a.jsx)(n.Provider,{value:{user:r,loading:d,login:m,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.h.defaults.headers.common.Authorization,l(null),u.push("/login")},updateUserInfo:e=>{if(r){let t={...r,...e};l(t),localStorage.setItem("adminUser",JSON.stringify(t))}},isAuthenticated:!!r},children:t})}function d(){let e=(0,o.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},44338:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return l}});var a=r(57437),o=r(2265),s=r(24033),i=r(5925),n=r(31584);function l(){let[e,t]=(0,o.useState)(""),[r,l]=(0,o.useState)(""),[d,c]=(0,o.useState)(""),[u,p]=(0,o.useState)(!1),m=(0,s.useRouter)(),{login:f}=(0,n.a)(),h=async t=>{t.preventDefault(),c(""),p(!0);try{console.log("开始登录...",{username:e,password:r}),await f(e,r),console.log("登录成功，跳转到仪表板"),i.ZP.success("登录成功！"),m.push("/dashboard")}catch(t){var a,o;console.error("登录失败:",t);let e=(null===(o=t.response)||void 0===o?void 0:null===(a=o.data)||void 0===a?void 0:a.message)||t.message||"登录失败，请重试";c(e),i.ZP.error(e)}finally{p(!1)}};return(0,a.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8 bg-white p-10 rounded-lg shadow-md",children:[(0,a.jsx)("div",{children:(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"管理系统登录"})}),d&&(0,a.jsx)("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 mb-4",children:(0,a.jsx)("p",{className:"text-red-700",children:d})}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:h,children:[(0,a.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"username",className:"sr-only",children:"用户名"}),(0,a.jsx)("input",{id:"username",name:"username",type:"text",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"用户名",value:e,onChange:e=>t(e.target.value)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"sr-only",children:"密码"}),(0,a.jsx)("input",{id:"password",name:"password",type:"password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"密码",value:r,onChange:e=>l(e.target.value)})]})]}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:u,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300",children:u?"登录中...":"登录"})})]})]}),(0,a.jsx)(i.x7,{position:"top-right"})]})}},30540:function(e,t,r){"use strict";r.d(t,{h:function(){return a}});let a=r(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=a},24033:function(e,t,r){e.exports=r(15313)},5925:function(e,t,r){"use strict";let a,o;r.d(t,{x7:function(){return eu},ZP:function(){return ep},Am:function(){return T}});var s,i=r(2265);let n={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||n,d=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,p=(e,t)=>{let r="",a="",o="";for(let s in e){let i=e[s];"@"==s[0]?"i"==s[1]?r=s+" "+i+";":a+="f"==s[1]?p(i,s):s+"{"+p(i,"k"==s[1]?"":t)+"}":"object"==typeof i?a+=p(i,t?t.replace(/([^,])+/g,e=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):s):null!=i&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),o+=p.p?p.p(s,i):s+":"+i+";")}return r+(t&&o?t+"{"+o+"}":o)+a},m={},f=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+f(e[r]);return t}return e},h=(e,t,r,a,o)=>{var s;let i=f(e),n=m[i]||(m[i]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(i));if(!m[n]){let t=i!==e?e:(e=>{let t,r,a=[{}];for(;t=d.exec(e.replace(c,""));)t[4]?a.shift():t[3]?(r=t[3].replace(u," ").trim(),a.unshift(a[0][r]=a[0][r]||{})):a[0][t[1]]=t[2].replace(u," ").trim();return a[0]})(e);m[n]=p(o?{["@keyframes "+n]:t}:t,r?"":"."+n)}let l=r&&m.g?m.g:null;return r&&(m.g=m[n]),s=m[n],l?t.data=t.data.replace(l,s):-1===t.data.indexOf(s)&&(t.data=a?s+t.data:t.data+s),n},g=(e,t,r)=>e.reduce((e,a,o)=>{let s=t[o];if(s&&s.call){let e=s(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;s=t?"."+t:e&&"object"==typeof e?e.props?"":p(e,""):!1===e?"":e}return e+a+(null==s?"":s)},"");function y(e){let t=this||{},r=e.call?e(t.p):e;return h(r.unshift?r.raw?g(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let b,x,v,w=y.bind({k:1});function j(e,t){let r=this||{};return function(){let a=arguments;function o(s,i){let n=Object.assign({},s),l=n.className||o.className;r.p=Object.assign({theme:x&&x()},n),r.o=/ *go\d+/.test(l),n.className=y.apply(r,a)+(l?" "+l:""),t&&(n.ref=i);let d=e;return e[0]&&(d=n.as||e,delete n.as),v&&d[0]&&v(n),b(d,n)}return t?t(o):o}}var E=e=>"function"==typeof e,N=(e,t)=>E(e)?e(t):e,k=(a=0,()=>(++a).toString()),S=()=>{if(void 0===o&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");o=!e||e.matches}return o},I=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return I(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let o=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+o}))}}},$=[],C={toasts:[],pausedAt:void 0},O=e=>{C=I(C,e),$.forEach(e=>{e(C)})},A={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},P=(e={})=>{let[t,r]=(0,i.useState)(C),a=(0,i.useRef)(C);(0,i.useEffect)(()=>(a.current!==C&&r(C),$.push(r),()=>{let e=$.indexOf(r);e>-1&&$.splice(e,1)}),[]);let o=t.toasts.map(t=>{var r,a,o;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||A[t.type],style:{...e.style,...null==(o=e[t.type])?void 0:o.style,...t.style}}});return{...t,toasts:o}},z=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||k()}),D=e=>(t,r)=>{let a=z(t,e,r);return O({type:2,toast:a}),a.id},T=(e,t)=>D("blank")(e,t);T.error=D("error"),T.success=D("success"),T.loading=D("loading"),T.custom=D("custom"),T.dismiss=e=>{O({type:3,toastId:e})},T.remove=e=>O({type:4,toastId:e}),T.promise=(e,t,r)=>{let a=T.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let o=t.success?N(t.success,e):void 0;return o?T.success(o,{id:a,...r,...null==r?void 0:r.success}):T.dismiss(a),e}).catch(e=>{let o=t.error?N(t.error,e):void 0;o?T.error(o,{id:a,...r,...null==r?void 0:r.error}):T.dismiss(a)}),e};var _=(e,t)=>{O({type:1,toast:{id:e,height:t}})},U=()=>{O({type:5,time:Date.now()})},F=new Map,H=1e3,M=(e,t=H)=>{if(F.has(e))return;let r=setTimeout(()=>{F.delete(e),O({type:4,toastId:e})},t);F.set(e,r)},Z=e=>{let{toasts:t,pausedAt:r}=P(e);(0,i.useEffect)(()=>{if(r)return;let e=Date.now(),a=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&T.dismiss(t.id);return}return setTimeout(()=>T.dismiss(t.id),r)});return()=>{a.forEach(e=>e&&clearTimeout(e))}},[t,r]);let a=(0,i.useCallback)(()=>{r&&O({type:6,time:Date.now()})},[r]),o=(0,i.useCallback)((e,r)=>{let{reverseOrder:a=!1,gutter:o=8,defaultPosition:s}=r||{},i=t.filter(t=>(t.position||s)===(e.position||s)&&t.height),n=i.findIndex(t=>t.id===e.id),l=i.filter((e,t)=>t<n&&e.visible).length;return i.filter(e=>e.visible).slice(...a?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+o,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)M(e.id,e.removeDelay);else{let t=F.get(e.id);t&&(clearTimeout(t),F.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:_,startPause:U,endPause:a,calculateOffset:o}}},L=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,R=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,q=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${L} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${R} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${q} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,J=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y=j("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${J} 1s linear infinite;
`,G=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,K=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Q=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${G} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${K} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,V=j("div")`
  position: absolute;
`,W=j("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=j("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:a}=e;return void 0!==t?"string"==typeof t?i.createElement(ee,null,t):t:"blank"===r?null:i.createElement(W,null,i.createElement(Y,{...a}),"loading"!==r&&i.createElement(V,null,"error"===r?i.createElement(B,{...a}):i.createElement(Q,{...a})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ea=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,eo=j("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,es=j("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let r=e.includes("top")?1:-1,[a,o]=S()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),ea(r)];return{animation:t?`${w(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(o)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},en=i.memo(({toast:e,position:t,style:r,children:a})=>{let o=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},s=i.createElement(et,{toast:e}),n=i.createElement(es,{...e.ariaProps},N(e.message,e));return i.createElement(eo,{className:e.className,style:{...o,...r,...e.style}},"function"==typeof a?a({icon:s,message:n}):i.createElement(i.Fragment,null,s,n))});s=i.createElement,p.p=void 0,b=s,x=void 0,v=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:a,children:o})=>{let s=i.useCallback(t=>{if(t){let r=()=>{a(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,a]);return i.createElement("div",{ref:s,className:t,style:r},o)},ed=(e,t)=>{let r=e.includes("top"),a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:S()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...a}},ec=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:a,children:o,containerStyle:s,containerClassName:n})=>{let{toasts:l,handlers:d}=Z(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...s},className:n,onMouseEnter:d.startPause,onMouseLeave:d.endPause},l.map(r=>{let s=r.position||t,n=ed(s,d.calculateOffset(r,{reverseOrder:e,gutter:a,defaultPosition:t}));return i.createElement(el,{id:r.id,key:r.id,onHeightUpdate:d.updateHeight,className:r.visible?ec:"",style:n},"custom"===r.type?N(r.message,r):o?o(r):i.createElement(en,{toast:r,position:s}))}))},ep=T}},function(e){e.O(0,[737,971,458,744],function(){return e(e.s=24101)}),_N_E=e.O()}]);