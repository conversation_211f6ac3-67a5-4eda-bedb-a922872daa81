(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[626],{24101:function(e,t,r){Promise.resolve().then(r.bind(r,44338))},31584:function(e,t,r){"use strict";r.d(t,{H:function(){return l},a:function(){return d}});var o=r(57437),a=r(2265),s=r(24033),i=r(30540);let n=(0,a.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,a.useState)(null),[d,c]=(0,a.useState)(!0),[u,m]=(0,a.useState)(!1),p=(0,s.useRouter)(),f=(0,s.usePathname)();(0,a.useEffect)(()=>{let e=async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(e&&t&&"undefined"!==t&&"null"!==t)try{i.Z.defaults.headers.common.Authorization="Bearer ".concat(e);try{let e=await i.Z.get("/auth/me");if(e.data.success&&e.data.data.user)l(e.data.data.user),localStorage.setItem("adminUser",JSON.stringify(e.data.data.user));else throw Error("Token验证失败")}catch(e){console.error("Token验证失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.Z.defaults.headers.common.Authorization,l(null)}}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),l(null)}}catch(e){console.error("认证检查失败:",e),l(null)}finally{c(!1),m(!0)}};u||e()},[u]),(0,a.useEffect)(()=>{!u||d||r||"/login"===f||p.push("/login")},[u,d,r,f,p]);let h=async(e,t)=>{try{console.log("AuthContext: 发送登录请求",{username:e});let r=await i.Z.post("/auth/login",{username:e,password:t});if(console.log("AuthContext: 收到响应",r.data),!r.data||!r.data.data)throw Error("API响应格式错误");let{user:o,token:a}=r.data.data;if(!o||!a)throw Error("响应中缺少用户信息或令牌");return console.log("AuthContext: 解析的用户数据",{user:o,token:a}),localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(o)),i.Z.defaults.headers.common.Authorization="Bearer ".concat(a),l(o),console.log("AuthContext: 登录成功，用户状态已更新"),o}catch(e){throw console.error("AuthContext: 登录失败",e),e}};return(0,o.jsx)(n.Provider,{value:{user:r,loading:d,login:h,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete i.Z.defaults.headers.common.Authorization,l(null),p.push("/login")},updateUserInfo:e=>{if(r){let t={...r,...e};l(t),localStorage.setItem("adminUser",JSON.stringify(t))}},isAuthenticated:!!r},children:t})}function d(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},44338:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return l}});var o=r(57437),a=r(2265),s=r(24033),i=r(5925),n=r(31584);function l(){let[e,t]=(0,a.useState)(""),[r,l]=(0,a.useState)(""),[d,c]=(0,a.useState)(""),[u,m]=(0,a.useState)(!1),p=(0,s.useRouter)(),{login:f}=(0,n.a)(),h=async t=>{t.preventDefault(),c(""),m(!0);try{console.log("开始登录...",{username:e,password:r}),await f(e,r),console.log("登录成功，跳转到仪表板"),i.ZP.success("登录成功！"),p.push("/dashboard")}catch(t){var o,a;console.error("登录失败:",t);let e=(null===(a=t.response)||void 0===a?void 0:null===(o=a.data)||void 0===o?void 0:o.message)||t.message||"登录失败，请重试";c(e),i.ZP.error(e)}finally{m(!1)}};return(0,o.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8",children:[(0,o.jsxs)("div",{className:"max-w-md w-full space-y-8 bg-white p-10 rounded-lg shadow-md",children:[(0,o.jsx)("div",{children:(0,o.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"管理系统登录"})}),d&&(0,o.jsx)("div",{className:"bg-red-50 border-l-4 border-red-500 p-4 mb-4",children:(0,o.jsx)("p",{className:"text-red-700",children:d})}),(0,o.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:h,children:[(0,o.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"username",className:"sr-only",children:"用户名"}),(0,o.jsx)("input",{id:"username",name:"username",type:"text",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"用户名",value:e,onChange:e=>t(e.target.value)})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{htmlFor:"password",className:"sr-only",children:"密码"}),(0,o.jsx)("input",{id:"password",name:"password",type:"password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"密码",value:r,onChange:e=>l(e.target.value)})]})]}),(0,o.jsx)("div",{children:(0,o.jsx)("button",{type:"submit",disabled:u,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:bg-blue-300",children:u?"登录中...":"登录"})})]})]}),(0,o.jsx)(i.x7,{position:"top-right"})]})}},30540:function(e,t,r){"use strict";r.d(t,{h:function(){return o}});let o=r(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});o.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),o.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=o},24033:function(e,t,r){e.exports=r(15313)},5925:function(e,t,r){"use strict";let o,a;r.d(t,{x7:function(){return eu},ZP:function(){return em},Am:function(){return D}});var s,i=r(2265);let n={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||n,d=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,m=(e,t)=>{let r="",o="",a="";for(let s in e){let i=e[s];"@"==s[0]?"i"==s[1]?r=s+" "+i+";":o+="f"==s[1]?m(i,s):s+"{"+m(i,"k"==s[1]?"":t)+"}":"object"==typeof i?o+=m(i,t?t.replace(/([^,])+/g,e=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):s):null!=i&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=m.p?m.p(s,i):s+":"+i+";")}return r+(t&&a?t+"{"+a+"}":a)+o},p={},f=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+f(e[r]);return t}return e},h=(e,t,r,o,a)=>{var s;let i=f(e),n=p[i]||(p[i]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(i));if(!p[n]){let t=i!==e?e:(e=>{let t,r,o=[{}];for(;t=d.exec(e.replace(c,""));)t[4]?o.shift():t[3]?(r=t[3].replace(u," ").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(u," ").trim();return o[0]})(e);p[n]=m(a?{["@keyframes "+n]:t}:t,r?"":"."+n)}let l=r&&p.g?p.g:null;return r&&(p.g=p[n]),s=p[n],l?t.data=t.data.replace(l,s):-1===t.data.indexOf(s)&&(t.data=o?s+t.data:t.data+s),n},g=(e,t,r)=>e.reduce((e,o,a)=>{let s=t[a];if(s&&s.call){let e=s(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;s=t?"."+t:e&&"object"==typeof e?e.props?"":m(e,""):!1===e?"":e}return e+o+(null==s?"":s)},"");function y(e){let t=this||{},r=e.call?e(t.p):e;return h(r.unshift?r.raw?g(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let b,x,v,w=y.bind({k:1});function j(e,t){let r=this||{};return function(){let o=arguments;function a(s,i){let n=Object.assign({},s),l=n.className||a.className;r.p=Object.assign({theme:x&&x()},n),r.o=/ *go\d+/.test(l),n.className=y.apply(r,o)+(l?" "+l:""),t&&(n.ref=i);let d=e;return e[0]&&(d=n.as||e,delete n.as),v&&d[0]&&v(n),b(d,n)}return t?t(a):a}}var E=e=>"function"==typeof e,k=(e,t)=>E(e)?e(t):e,S=(o=0,()=>(++o).toString()),N=()=>{if(void 0===a&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");a=!e||e.matches}return a},I=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return I(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:o}=t;return{...e,toasts:e.toasts.map(e=>e.id===o||void 0===o?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},A=[],C={toasts:[],pausedAt:void 0},$=e=>{C=I(C,e),A.forEach(e=>{e(C)})},O={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},P=(e={})=>{let[t,r]=(0,i.useState)(C),o=(0,i.useRef)(C);(0,i.useEffect)(()=>(o.current!==C&&r(C),A.push(r),()=>{let e=A.indexOf(r);e>-1&&A.splice(e,1)}),[]);let a=t.toasts.map(t=>{var r,o,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(o=e[t.type])?void 0:o.duration)||(null==e?void 0:e.duration)||O[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},z=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||S()}),T=e=>(t,r)=>{let o=z(t,e,r);return $({type:2,toast:o}),o.id},D=(e,t)=>T("blank")(e,t);D.error=T("error"),D.success=T("success"),D.loading=T("loading"),D.custom=T("custom"),D.dismiss=e=>{$({type:3,toastId:e})},D.remove=e=>$({type:4,toastId:e}),D.promise=(e,t,r)=>{let o=D.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?k(t.success,e):void 0;return a?D.success(a,{id:o,...r,...null==r?void 0:r.success}):D.dismiss(o),e}).catch(e=>{let a=t.error?k(t.error,e):void 0;a?D.error(a,{id:o,...r,...null==r?void 0:r.error}):D.dismiss(o)}),e};var U=(e,t)=>{$({type:1,toast:{id:e,height:t}})},Z=()=>{$({type:5,time:Date.now()})},_=new Map,F=1e3,H=(e,t=F)=>{if(_.has(e))return;let r=setTimeout(()=>{_.delete(e),$({type:4,toastId:e})},t);_.set(e,r)},M=e=>{let{toasts:t,pausedAt:r}=P(e);(0,i.useEffect)(()=>{if(r)return;let e=Date.now(),o=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&D.dismiss(t.id);return}return setTimeout(()=>D.dismiss(t.id),r)});return()=>{o.forEach(e=>e&&clearTimeout(e))}},[t,r]);let o=(0,i.useCallback)(()=>{r&&$({type:6,time:Date.now()})},[r]),a=(0,i.useCallback)((e,r)=>{let{reverseOrder:o=!1,gutter:a=8,defaultPosition:s}=r||{},i=t.filter(t=>(t.position||s)===(e.position||s)&&t.height),n=i.findIndex(t=>t.id===e.id),l=i.filter((e,t)=>t<n&&e.visible).length;return i.filter(e=>e.visible).slice(...o?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+a,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)H(e.id,e.removeDelay);else{let t=_.get(e.id);t&&(clearTimeout(t),_.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:U,startPause:Z,endPause:o,calculateOffset:a}}},L=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,R=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,q=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,B=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${L} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${R} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${q} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,J=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y=j("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${J} 1s linear infinite;
`,G=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,K=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Q=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${G} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${K} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,V=j("div")`
  position: absolute;
`,W=j("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=j("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:r,iconTheme:o}=e;return void 0!==t?"string"==typeof t?i.createElement(ee,null,t):t:"blank"===r?null:i.createElement(W,null,i.createElement(Y,{...o}),"loading"!==r&&i.createElement(V,null,"error"===r?i.createElement(B,{...o}):i.createElement(Q,{...o})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,eo=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=j("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,es=j("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let r=e.includes("top")?1:-1,[o,a]=N()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(r),eo(r)];return{animation:t?`${w(o)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},en=i.memo(({toast:e,position:t,style:r,children:o})=>{let a=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},s=i.createElement(et,{toast:e}),n=i.createElement(es,{...e.ariaProps},k(e.message,e));return i.createElement(ea,{className:e.className,style:{...a,...r,...e.style}},"function"==typeof o?o({icon:s,message:n}):i.createElement(i.Fragment,null,s,n))});s=i.createElement,m.p=void 0,b=s,x=void 0,v=void 0;var el=({id:e,className:t,style:r,onHeightUpdate:o,children:a})=>{let s=i.useCallback(t=>{if(t){let r=()=>{o(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,o]);return i.createElement("div",{ref:s,className:t,style:r},a)},ed=(e,t)=>{let r=e.includes("top"),o=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:N()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...o}},ec=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,eu=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:o,children:a,containerStyle:s,containerClassName:n})=>{let{toasts:l,handlers:d}=M(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...s},className:n,onMouseEnter:d.startPause,onMouseLeave:d.endPause},l.map(r=>{let s=r.position||t,n=ed(s,d.calculateOffset(r,{reverseOrder:e,gutter:o,defaultPosition:t}));return i.createElement(el,{id:r.id,key:r.id,onHeightUpdate:d.updateHeight,className:r.visible?ec:"",style:n},"custom"===r.type?k(r.message,r):a?a(r):i.createElement(en,{toast:r,position:s}))}))},em=D}},function(e){e.O(0,[737,971,458,744],function(){return e(e.s=24101)}),_N_E=e.O()}]);