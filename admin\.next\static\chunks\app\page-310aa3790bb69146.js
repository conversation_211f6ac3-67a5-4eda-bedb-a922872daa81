(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{68363:function(e,t,s){Promise.resolve().then(s.bind(s,26911))},31584:function(e,t,s){"use strict";s.d(t,{H:function(){return c},a:function(){return o}});var a=s(57437),r=s(2265),n=s(24033),l=s(30540);let i=(0,r.createContext)(void 0);function c(e){let{children:t}=e,[s,c]=(0,r.useState)(null),[o,d]=(0,r.useState)(!0),h=(0,n.useRouter)(),m=(0,n.usePathname)();(0,r.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");e&&t?(l.h.defaults.headers.common.Authorization="Bearer ".concat(e),c(JSON.parse(t))):"/login"!==m&&h.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{d(!1)}})()},[m,h]);let x=async(e,t)=>{try{let{user:s,token:a}=(await l.h.post("/auth/login",{username:e,password:t})).data;return localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(s)),l.h.defaults.headers.common.Authorization="Bearer ".concat(a),c(s),s}catch(e){throw console.error("登录失败:",e),e}};return(0,a.jsx)(i.Provider,{value:{user:s,loading:o,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete l.h.defaults.headers.common.Authorization,c(null),h.push("/login")},isAuthenticated:!!s},children:t})}function o(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},26911:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return c}});var a=s(57437),r=s(2265),n=s(24033),l=s(31584),i=s(30540);function c(){let{user:e,loading:t,isAuthenticated:s}=(0,l.a)(),c=(0,n.useRouter)(),[o,d]=(0,r.useState)({totalUsers:0,totalArticles:0,totalInquiries:0,pendingInquiries:0});(0,r.useEffect)(()=>{if(!t&&!s){c.push("/login");return}s&&h()},[t,s,c]);let h=async()=>{try{let e=await i.h.get("/dashboard/stats");e.data.success&&d(e.data.data.overview)}catch(e){console.error("获取统计数据失败:",e)}};return t?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)("div",{className:"text-lg",children:"加载中..."})}):s?(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"仪表盘"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-700",children:"用户总数"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-600 mt-2",children:o.totalUsers}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"系统用户"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-700",children:"文章总数"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-green-600 mt-2",children:o.totalArticles}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"已发布文章"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-700",children:"待处理咨询"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-yellow-500 mt-2",children:o.pendingInquiries}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"当前积压"})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"快速操作"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)("a",{href:"/content/articles/new",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:(0,a.jsx)("span",{className:"block text-blue-600 font-medium",children:"发布文章"})}),(0,a.jsx)("a",{href:"/inquiries",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:(0,a.jsx)("span",{className:"block text-blue-600 font-medium",children:"处理咨询"})}),(0,a.jsx)("a",{href:"/content/services",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:(0,a.jsx)("span",{className:"block text-blue-600 font-medium",children:"管理服务"})}),(0,a.jsx)("a",{href:"/content/cases",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:(0,a.jsx)("span",{className:"block text-blue-600 font-medium",children:"管理案例"})})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"最近活动"}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"时间"})]})}),(0,a.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"更新了首页Banner"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"管理员"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-15 14:30"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"添加了新服务"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"编辑"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-15 11:20"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"回复了客户咨询"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"客服"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-14 16:45"})]})]})]})})]})]}):null}},30540:function(e,t,s){"use strict";s.d(t,{h:function(){return a}});let a=s(54829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e)))},24033:function(e,t,s){e.exports=s(15313)}},function(e){e.O(0,[737,971,458,744],function(){return e(e.s=68363)}),_N_E=e.O()}]);