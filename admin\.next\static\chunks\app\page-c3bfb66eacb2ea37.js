(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{68363:function(e,t,s){Promise.resolve().then(s.bind(s,26911))},31584:function(e,t,s){"use strict";s.d(t,{H:function(){return i},a:function(){return c}});var a=s(57437),r=s(2265),l=s(24033),n=s(30540);let o=(0,r.createContext)(void 0);function i(e){let{children:t}=e,[s,i]=(0,r.useState)(null),[c,d]=(0,r.useState)(!0),[h,m]=(0,r.useState)(!1),u=(0,l.useRouter)(),x=(0,l.usePathname)();(0,r.useEffect)(()=>{let e=async()=>{try{let e=localStorage.getItem("adminToken"),t=localStorage.getItem("adminUser");if(e&&t&&"undefined"!==t&&"null"!==t)try{n.Z.defaults.headers.common.Authorization="Bearer ".concat(e);try{let e=await n.Z.get("/auth/me");if(e.data.success&&e.data.data.user)i(e.data.data.user),localStorage.setItem("adminUser",JSON.stringify(e.data.data.user));else throw Error("Token验证失败")}catch(e){console.error("Token验证失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete n.Z.defaults.headers.common.Authorization,i(null)}}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),i(null)}}catch(e){console.error("认证检查失败:",e),i(null)}finally{d(!1),m(!0)}};h||e()},[h]),(0,r.useEffect)(()=>{!h||c||s||"/login"===x||u.push("/login")},[h,c,s,x,u]);let g=async(e,t)=>{try{console.log("AuthContext: 发送登录请求",{username:e});let s=await n.Z.post("/auth/login",{username:e,password:t});if(console.log("AuthContext: 收到响应",s.data),!s.data||!s.data.data)throw Error("API响应格式错误");let{user:a,token:r}=s.data.data;if(!a||!r)throw Error("响应中缺少用户信息或令牌");return console.log("AuthContext: 解析的用户数据",{user:a,token:r}),localStorage.setItem("adminToken",r),localStorage.setItem("adminUser",JSON.stringify(a)),n.Z.defaults.headers.common.Authorization="Bearer ".concat(r),i(a),console.log("AuthContext: 登录成功，用户状态已更新"),a}catch(e){throw console.error("AuthContext: 登录失败",e),e}};return(0,a.jsx)(o.Provider,{value:{user:s,loading:c,login:g,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete n.Z.defaults.headers.common.Authorization,i(null),u.push("/login")},updateUserInfo:e=>{if(s){let t={...s,...e};i(t),localStorage.setItem("adminUser",JSON.stringify(t))}},isAuthenticated:!!s},children:t})}function c(){let e=(0,r.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},26911:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return i}});var a=s(57437),r=s(2265),l=s(24033),n=s(31584),o=s(30540);function i(){let{user:e,loading:t,isAuthenticated:s}=(0,n.a)(),i=(0,l.useRouter)(),[c,d]=(0,r.useState)({totalUsers:0,totalArticles:0,totalInquiries:0,pendingInquiries:0});(0,r.useEffect)(()=>{if(!t&&!s){i.push("/login");return}s&&h()},[t,s,i]);let h=async()=>{try{let e=await o.h.get("/dashboard/stats");e.data.success&&d(e.data.data.overview)}catch(e){console.error("获取统计数据失败:",e)}};return t?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,a.jsx)("div",{className:"text-lg",children:"加载中..."})}):s?(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"仪表盘"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-700",children:"用户总数"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-blue-600 mt-2",children:c.totalUsers}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"系统用户"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-700",children:"文章总数"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-green-600 mt-2",children:c.totalArticles}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"已发布文章"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-700",children:"待处理咨询"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-yellow-500 mt-2",children:c.pendingInquiries}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"当前积压"})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"快速操作"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)("a",{href:"/content/articles/new",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:(0,a.jsx)("span",{className:"block text-blue-600 font-medium",children:"发布文章"})}),(0,a.jsx)("a",{href:"/inquiries",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:(0,a.jsx)("span",{className:"block text-blue-600 font-medium",children:"处理咨询"})}),(0,a.jsx)("a",{href:"/content/services",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:(0,a.jsx)("span",{className:"block text-blue-600 font-medium",children:"管理服务"})}),(0,a.jsx)("a",{href:"/content/cases",className:"bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50",children:(0,a.jsx)("span",{className:"block text-blue-600 font-medium",children:"管理案例"})})]})]}),(0,a.jsxs)("div",{className:"mt-8",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"最近活动"}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"时间"})]})}),(0,a.jsxs)("tbody",{className:"bg-white divide-y divide-gray-200",children:[(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"更新了首页Banner"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"管理员"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-15 14:30"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"添加了新服务"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"编辑"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-15 11:20"})]}),(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"回复了客户咨询"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"客服"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:"2023-12-14 16:45"})]})]})]})})]})]}):null}},30540:function(e,t,s){"use strict";s.d(t,{h:function(){return a}});let a=s(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=a},24033:function(e,t,s){e.exports=s(15313)}},function(e){e.O(0,[737,971,458,744],function(){return e(e.s=68363)}),_N_E=e.O()}]);