(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[178],{90773:function(e,r,s){Promise.resolve().then(s.bind(s,59939))},31584:function(e,r,s){"use strict";s.d(r,{H:function(){return d},a:function(){return i}});var t=s(57437),a=s(2265),o=s(24033),l=s(30540);let n=(0,a.createContext)(void 0);function d(e){let{children:r}=e,[s,d]=(0,a.useState)(null),[i,c]=(0,a.useState)(!0),m=(0,o.useRouter)(),u=(0,o.usePathname)();(0,a.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("adminToken"),r=localStorage.getItem("adminUser");e&&r?(l.h.defaults.headers.common.Authorization="Bearer ".concat(e),d(JSON.parse(r))):"/login"!==u&&m.push("/login")}catch(e){console.error("认证检查失败:",e)}finally{c(!1)}})()},[u,m]);let x=async(e,r)=>{try{let{user:s,token:t}=(await l.h.post("/auth/login",{username:e,password:r})).data;return localStorage.setItem("adminToken",t),localStorage.setItem("adminUser",JSON.stringify(s)),l.h.defaults.headers.common.Authorization="Bearer ".concat(t),d(s),s}catch(e){throw console.error("登录失败:",e),e}};return(0,t.jsx)(n.Provider,{value:{user:s,loading:i,login:x,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete l.h.defaults.headers.common.Authorization,d(null),m.push("/login")},updateUserInfo:e=>{if(s){let r={...s,...e};d(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!s},children:r})}function i(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},59939:function(e,r,s){"use strict";s.r(r),s.d(r,{default:function(){return d}});var t=s(57437),a=s(2265),o=s(61865),l=s(5925),n=s(31584);function d(){let{user:e,updateUserInfo:r}=(0,n.a)(),[s,d]=(0,a.useState)("profile"),[i,c]=(0,a.useState)(!1),[m,u]=(0,a.useState)(null),{register:x,handleSubmit:p,formState:{errors:h},reset:b}=(0,o.cI)({defaultValues:{name:(null==e?void 0:e.name)||"",email:(null==e?void 0:e.email)||"",avatar:(null==e?void 0:e.avatar)||"",phone:(null==e?void 0:e.phone)||"",position:(null==e?void 0:e.position)||"",bio:(null==e?void 0:e.bio)||""}}),{register:y,handleSubmit:g,formState:{errors:f},reset:j,watch:v}=(0,o.cI)(),w=v("newPassword"),N=async s=>{c(!0);try{await new Promise(e=>setTimeout(e,1e3)),r({...e,name:s.name,email:s.email,avatar:m||s.avatar,phone:s.phone,position:s.position,bio:s.bio}),l.ZP.success("个人资料已成功更新"),c(!1)}catch(e){console.error("更新个人资料失败:",e),l.ZP.error("更新个人资料失败，请重试"),c(!1)}},P=async e=>{c(!0);try{await new Promise(e=>setTimeout(e,1e3)),l.ZP.success("密码已成功修改"),j(),c(!1)}catch(e){console.error("修改密码失败:",e),l.ZP.error("修改密码失败，请确认当前密码是否正确"),c(!1)}};return(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"个人资料"}),(0,t.jsx)("p",{className:"text-gray-600",children:"管理您的账户信息和安全设置"})]}),(0,t.jsx)("div",{className:"mb-6 border-b border-gray-200",children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,t.jsx)("button",{onClick:()=>d("profile"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("profile"===s?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"个人信息"}),(0,t.jsx)("button",{onClick:()=>d("password"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("password"===s?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"修改密码"})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:["profile"===s&&(0,t.jsxs)("form",{onSubmit:p(N),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-6",children:[(0,t.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full overflow-hidden flex items-center justify-center",children:m?(0,t.jsx)("img",{src:m,alt:"头像预览",className:"h-full w-full object-cover"}):(null==e?void 0:e.avatar)?(0,t.jsx)("img",{src:e.avatar,alt:"当前头像",className:"h-full w-full object-cover"}):(0,t.jsx)("span",{className:"text-gray-500 text-xs",children:"无头像"})}),(0,t.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,t.jsx)("input",{id:"avatar",type:"file",accept:"image/*",className:"hidden",onChange:e=>{var r;let s=null===(r=e.target.files)||void 0===r?void 0:r[0];s&&u(URL.createObjectURL(s))}}),(0,t.jsx)("button",{type:"button",onClick:()=>{var e;return null===(e=document.getElementById("avatar"))||void 0===e?void 0:e.click()},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm",children:"更换头像"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"推荐使用正方形图片，最大2MB"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:["姓名 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{id:"name",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(h.name?"border-red-500":"border-gray-300"),...x("name",{required:"请输入姓名"})}),h.name&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.name.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["邮箱 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{id:"email",type:"email",className:"w-full px-3 py-2 border rounded-md ".concat(h.email?"border-red-500":"border-gray-300"),...x("email",{required:"请输入邮箱",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"请输入有效的邮箱地址"}})}),h.email&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.email.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"电话"}),(0,t.jsx)("input",{id:"phone",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...x("phone")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-1",children:"职位"}),(0,t.jsx)("input",{id:"position",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...x("position")})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-1",children:"个人简介"}),(0,t.jsx)("textarea",{id:"bio",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"简单介绍一下自己...",...x("bio")})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[(0,t.jsx)("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>b(),disabled:i,children:"取消"}),(0,t.jsx)("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:i,children:i?"保存中...":"保存修改"})]})]}),"password"===s&&(0,t.jsxs)("form",{onSubmit:g(P),className:"space-y-6 max-w-md",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["当前密码 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{id:"currentPassword",type:"password",className:"w-full px-3 py-2 border rounded-md ".concat(f.currentPassword?"border-red-500":"border-gray-300"),...y("currentPassword",{required:"请输入当前密码"})}),f.currentPassword&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:f.currentPassword.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["新密码 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{id:"newPassword",type:"password",className:"w-full px-3 py-2 border rounded-md ".concat(f.newPassword?"border-red-500":"border-gray-300"),...y("newPassword",{required:"请输入新密码",minLength:{value:8,message:"密码长度至少为8个字符"},pattern:{value:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,message:"密码必须包含大小写字母和数字"}})}),f.newPassword&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:f.newPassword.message}),(0,t.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"密码必须至少包含8个字符，包括大小写字母和数字"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["确认新密码 ",(0,t.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,t.jsx)("input",{id:"confirmPassword",type:"password",className:"w-full px-3 py-2 border rounded-md ".concat(f.confirmPassword?"border-red-500":"border-gray-300"),...y("confirmPassword",{required:"请确认新密码",validate:e=>e===w||"两次输入的密码不一致"})}),f.confirmPassword&&(0,t.jsx)("p",{className:"mt-1 text-sm text-red-500",children:f.confirmPassword.message})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[(0,t.jsx)("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>j(),disabled:i,children:"取消"}),(0,t.jsx)("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:i,children:i?"更新中...":"更新密码"})]})]})]}),(0,t.jsx)(l.x7,{position:"top-right"})]})}},30540:function(e,r,s){"use strict";s.d(r,{h:function(){return t}});let t=s(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});t.interceptors.request.use(e=>{let r=localStorage.getItem("adminToken");return r&&(e.headers.Authorization="Bearer ".concat(r)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),r.Z=t},24033:function(e,r,s){e.exports=s(15313)}},function(e){e.O(0,[737,279,971,458,744],function(){return e(e.s=90773)}),_N_E=e.O()}]);