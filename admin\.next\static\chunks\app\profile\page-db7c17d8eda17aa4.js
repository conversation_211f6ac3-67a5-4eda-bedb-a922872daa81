(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[178],{90773:function(e,r,t){Promise.resolve().then(t.bind(t,59939))},31584:function(e,r,t){"use strict";t.d(r,{H:function(){return d},a:function(){return i}});var s=t(57437),a=t(2265),o=t(24033),l=t(30540);let n=(0,a.createContext)(void 0);function d(e){let{children:r}=e,[t,d]=(0,a.useState)(null),[i,c]=(0,a.useState)(!0),[m,u]=(0,a.useState)(!1),x=(0,o.useRouter)(),p=(0,o.usePathname)();(0,a.useEffect)(()=>{m||(()=>{try{let e=localStorage.getItem("adminToken"),r=localStorage.getItem("adminUser");if(e&&r&&"undefined"!==r&&"null"!==r)try{l.Z.defaults.headers.common.Authorization="Bearer ".concat(e);let t=JSON.parse(r);d(t),console.log("从本地存储恢复用户状态:",t)}catch(e){console.error("解析用户数据失败:",e),localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),d(null)}}catch(e){console.error("认证检查失败:",e),d(null)}finally{c(!1),u(!0)}})()},[m]),(0,a.useEffect)(()=>{!m||i||t||"/login"===p||x.push("/login")},[m,i,t,p,x]),(0,a.useEffect)(()=>{let e=()=>{console.log("收到401错误，自动登出"),d(null),"/login"!==p&&x.push("/login")};return window.addEventListener("auth:logout",e),()=>{window.removeEventListener("auth:logout",e)}},[p,x]);let h=async(e,r)=>{try{console.log("AuthContext: 发送登录请求",{username:e});let t=await l.Z.post("/auth/login",{username:e,password:r});if(console.log("AuthContext: 收到响应",t.data),!t.data||!t.data.data)throw Error("API响应格式错误");let{user:s,token:a}=t.data.data;if(!s||!a)throw Error("响应中缺少用户信息或令牌");console.log("AuthContext: 解析的用户数据",{user:s,token:a}),localStorage.setItem("adminToken",a),localStorage.setItem("adminUser",JSON.stringify(s)),l.Z.defaults.headers.common.Authorization="Bearer ".concat(a),d(s),console.log("AuthContext: 登录成功，用户状态已更新")}catch(e){throw console.error("AuthContext: 登录失败",e),e}};return(0,s.jsx)(n.Provider,{value:{user:t,loading:i,login:h,logout:()=>{localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete l.Z.defaults.headers.common.Authorization,d(null),x.push("/login")},updateUserInfo:e=>{if(t){let r={...t,...e};d(r),localStorage.setItem("adminUser",JSON.stringify(r))}},isAuthenticated:!!t},children:r})}function i(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},59939:function(e,r,t){"use strict";t.r(r),t.d(r,{default:function(){return d}});var s=t(57437),a=t(2265),o=t(61865),l=t(5925),n=t(31584);function d(){let{user:e,updateUserInfo:r}=(0,n.a)(),[t,d]=(0,a.useState)("profile"),[i,c]=(0,a.useState)(!1),[m,u]=(0,a.useState)(null),{register:x,handleSubmit:p,formState:{errors:h},reset:b}=(0,o.cI)({defaultValues:{name:(null==e?void 0:e.name)||"",email:(null==e?void 0:e.email)||"",avatar:(null==e?void 0:e.avatar)||"",phone:(null==e?void 0:e.phone)||"",position:(null==e?void 0:e.position)||"",bio:(null==e?void 0:e.bio)||""}}),{register:g,handleSubmit:y,formState:{errors:f},reset:v,watch:j}=(0,o.cI)(),w=j("newPassword"),N=async t=>{c(!0);try{await new Promise(e=>setTimeout(e,1e3)),r({...e,name:t.name,email:t.email,avatar:m||t.avatar,phone:t.phone,position:t.position,bio:t.bio}),l.ZP.success("个人资料已成功更新"),c(!1)}catch(e){console.error("更新个人资料失败:",e),l.ZP.error("更新个人资料失败，请重试"),c(!1)}},P=async e=>{c(!0);try{await new Promise(e=>setTimeout(e,1e3)),l.ZP.success("密码已成功修改"),v(),c(!1)}catch(e){console.error("修改密码失败:",e),l.ZP.error("修改密码失败，请确认当前密码是否正确"),c(!1)}};return(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"个人资料"}),(0,s.jsx)("p",{className:"text-gray-600",children:"管理您的账户信息和安全设置"})]}),(0,s.jsx)("div",{className:"mb-6 border-b border-gray-200",children:(0,s.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,s.jsx)("button",{onClick:()=>d("profile"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("profile"===t?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"个人信息"}),(0,s.jsx)("button",{onClick:()=>d("password"),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat("password"===t?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:"修改密码"})]})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:["profile"===t&&(0,s.jsxs)("form",{onSubmit:p(N),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-6",children:[(0,s.jsx)("div",{className:"w-24 h-24 bg-gray-200 rounded-full overflow-hidden flex items-center justify-center",children:m?(0,s.jsx)("img",{src:m,alt:"头像预览",className:"h-full w-full object-cover"}):(null==e?void 0:e.avatar)?(0,s.jsx)("img",{src:e.avatar,alt:"当前头像",className:"h-full w-full object-cover"}):(0,s.jsx)("span",{className:"text-gray-500 text-xs",children:"无头像"})}),(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsx)("input",{id:"avatar",type:"file",accept:"image/*",className:"hidden",onChange:e=>{var r;let t=null===(r=e.target.files)||void 0===r?void 0:r[0];t&&u(URL.createObjectURL(t))}}),(0,s.jsx)("button",{type:"button",onClick:()=>{var e;return null===(e=document.getElementById("avatar"))||void 0===e?void 0:e.click()},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm",children:"更换头像"}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"推荐使用正方形图片，最大2MB"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:["姓名 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"name",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(h.name?"border-red-500":"border-gray-300"),...x("name",{required:"请输入姓名"})}),h.name&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.name.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:["邮箱 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"email",type:"email",className:"w-full px-3 py-2 border rounded-md ".concat(h.email?"border-red-500":"border-gray-300"),...x("email",{required:"请输入邮箱",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"请输入有效的邮箱地址"}})}),h.email&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:h.email.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700 mb-1",children:"电话"}),(0,s.jsx)("input",{id:"phone",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...x("phone")})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"position",className:"block text-sm font-medium text-gray-700 mb-1",children:"职位"}),(0,s.jsx)("input",{id:"position",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...x("position")})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-1",children:"个人简介"}),(0,s.jsx)("textarea",{id:"bio",rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md",placeholder:"简单介绍一下自己...",...x("bio")})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[(0,s.jsx)("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>b(),disabled:i,children:"取消"}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:i,children:i?"保存中...":"保存修改"})]})]}),"password"===t&&(0,s.jsxs)("form",{onSubmit:y(P),className:"space-y-6 max-w-md",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["当前密码 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"currentPassword",type:"password",className:"w-full px-3 py-2 border rounded-md ".concat(f.currentPassword?"border-red-500":"border-gray-300"),...g("currentPassword",{required:"请输入当前密码"})}),f.currentPassword&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:f.currentPassword.message})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["新密码 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"newPassword",type:"password",className:"w-full px-3 py-2 border rounded-md ".concat(f.newPassword?"border-red-500":"border-gray-300"),...g("newPassword",{required:"请输入新密码",minLength:{value:8,message:"密码长度至少为8个字符"},pattern:{value:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,message:"密码必须包含大小写字母和数字"}})}),f.newPassword&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:f.newPassword.message}),(0,s.jsx)("p",{className:"mt-1 text-xs text-gray-500",children:"密码必须至少包含8个字符，包括大小写字母和数字"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:["确认新密码 ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{id:"confirmPassword",type:"password",className:"w-full px-3 py-2 border rounded-md ".concat(f.confirmPassword?"border-red-500":"border-gray-300"),...g("confirmPassword",{required:"请确认新密码",validate:e=>e===w||"两次输入的密码不一致"})}),f.confirmPassword&&(0,s.jsx)("p",{className:"mt-1 text-sm text-red-500",children:f.confirmPassword.message})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[(0,s.jsx)("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>v(),disabled:i,children:"取消"}),(0,s.jsx)("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:i,children:i?"更新中...":"更新密码"})]})]})]}),(0,s.jsx)(l.x7,{position:"top-right"})]})}},30540:function(e,r,t){"use strict";t.d(r,{h:function(){return s}});let s=t(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{let r=localStorage.getItem("adminToken");return r&&(e.headers.Authorization="Bearer ".concat(r)),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete s.defaults.headers.common.Authorization,window.dispatchEvent(new CustomEvent("auth:logout"))),Promise.reject(e))),r.Z=s},24033:function(e,r,t){e.exports=t(15313)}},function(e){e.O(0,[737,279,971,458,744],function(){return e(e.s=90773)}),_N_E=e.O()}]);