(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[938],{55525:function(e,t,s){Promise.resolve().then(s.bind(s,6832))},6832:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return n}});var r=s(57437),a=s(2265),i=s(61865),o=s(5925);let l={siteName:"上海留学顾问",siteDescription:"专业的留学与职业规划咨询服务",contactEmail:"<EMAIL>",contactPhone:"021-12345678",address:"上海市浦东新区张江高科技园区博云路2号",icp:"沪ICP备12345678号",seoKeywords:"留学,职业规划,考研,保研,职业转型,上海留学顾问",seoDescription:"上海留学顾问提供专业的留学申请、考研保研规划、职业发展咨询等服务，助力学生和职场人士实现人生目标。",logoUrl:"/images/logo.png",faviconUrl:"/favicon.ico",footerCopyright:"\xa9 2023 上海留学顾问 版权所有",socialMedia:{weixin:"slhgw_weixin",weibo:"slhgw_weibo",zhihu:"slhgw_zhihu"}};function n(){let[e,t]=(0,a.useState)(!1),[s,n]=(0,a.useState)(null),[d,c]=(0,a.useState)(null),{register:m,handleSubmit:p,formState:{errors:u},reset:x}=(0,i.cI)({defaultValues:l}),h=async e=>{t(!0);try{await new Promise(e=>setTimeout(e,1e3)),o.ZP.success("系统设置已成功保存"),t(!1)}catch(e){console.error("保存系统设置失败:",e),o.ZP.error("保存系统设置失败，请重试"),t(!1)}};return(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold",children:"系统设置"}),(0,r.jsx)("p",{className:"text-gray-600",children:"管理网站基本信息和配置"})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,r.jsxs)("form",{onSubmit:p(h),className:"space-y-6",children:[(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium mb-4",children:"基本信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"siteName",className:"block text-sm font-medium text-gray-700 mb-1",children:["网站名称 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{id:"siteName",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(u.siteName?"border-red-500":"border-gray-300"),...m("siteName",{required:"请输入网站名称"})}),u.siteName&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:u.siteName.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"siteDescription",className:"block text-sm font-medium text-gray-700 mb-1",children:["网站描述 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{id:"siteDescription",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(u.siteDescription?"border-red-500":"border-gray-300"),...m("siteDescription",{required:"请输入网站描述"})}),u.siteDescription&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:u.siteDescription.message})]})]})]}),(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium mb-4",children:"联系信息"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"contactEmail",className:"block text-sm font-medium text-gray-700 mb-1",children:["联系邮箱 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{id:"contactEmail",type:"email",className:"w-full px-3 py-2 border rounded-md ".concat(u.contactEmail?"border-red-500":"border-gray-300"),...m("contactEmail",{required:"请输入联系邮箱",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"请输入有效的邮箱地址"}})}),u.contactEmail&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:u.contactEmail.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"contactPhone",className:"block text-sm font-medium text-gray-700 mb-1",children:["联系电话 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{id:"contactPhone",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(u.contactPhone?"border-red-500":"border-gray-300"),...m("contactPhone",{required:"请输入联系电话"})}),u.contactPhone&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:u.contactPhone.message})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsxs)("label",{htmlFor:"address",className:"block text-sm font-medium text-gray-700 mb-1",children:["公司地址 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{id:"address",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(u.address?"border-red-500":"border-gray-300"),...m("address",{required:"请输入公司地址"})}),u.address&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:u.address.message})]})]})]}),(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium mb-4",children:"SEO设置"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"seoKeywords",className:"block text-sm font-medium text-gray-700 mb-1",children:["SEO关键词 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{id:"seoKeywords",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(u.seoKeywords?"border-red-500":"border-gray-300"),...m("seoKeywords",{required:"请输入SEO关键词"})}),u.seoKeywords&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:u.seoKeywords.message}),(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"多个关键词用英文逗号分隔"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"seoDescription",className:"block text-sm font-medium text-gray-700 mb-1",children:["SEO描述 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"seoDescription",rows:3,className:"w-full px-3 py-2 border rounded-md ".concat(u.seoDescription?"border-red-500":"border-gray-300"),...m("seoDescription",{required:"请输入SEO描述"})}),u.seoDescription&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:u.seoDescription.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"icp",className:"block text-sm font-medium text-gray-700 mb-1",children:"ICP备案号"}),(0,r.jsx)("input",{id:"icp",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...m("icp")})]})]})]}),(0,r.jsxs)("div",{className:"border-b border-gray-200 pb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium mb-4",children:"网站资源"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"网站Logo"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("input",{id:"logo",type:"file",accept:"image/*",className:"hidden",onChange:e=>{var t;let s=null===(t=e.target.files)||void 0===t?void 0:t[0];s&&n(URL.createObjectURL(s))}}),(0,r.jsx)("button",{type:"button",onClick:()=>{var e;return null===(e=document.getElementById("logo"))||void 0===e?void 0:e.click()},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"选择Logo"}),(0,r.jsx)("div",{className:"h-12 w-24 bg-gray-200 rounded overflow-hidden flex items-center justify-center",children:s?(0,r.jsx)("img",{src:s,alt:"Logo预览",className:"h-full w-full object-contain"}):(0,r.jsx)("span",{className:"text-gray-500 text-xs",children:"Logo预览"})})]}),(0,r.jsx)("input",{type:"hidden",...m("logoUrl")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"网站图标 (Favicon)"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("input",{id:"favicon",type:"file",accept:"image/x-icon,image/png",className:"hidden",onChange:e=>{var t;let s=null===(t=e.target.files)||void 0===t?void 0:t[0];s&&c(URL.createObjectURL(s))}}),(0,r.jsx)("button",{type:"button",onClick:()=>{var e;return null===(e=document.getElementById("favicon"))||void 0===e?void 0:e.click()},className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"选择图标"}),(0,r.jsx)("div",{className:"h-8 w-8 bg-gray-200 rounded overflow-hidden flex items-center justify-center",children:d?(0,r.jsx)("img",{src:d,alt:"Favicon预览",className:"h-full w-full object-contain"}):(0,r.jsx)("span",{className:"text-gray-500 text-xs",children:"图标"})})]}),(0,r.jsx)("input",{type:"hidden",...m("faviconUrl")})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsxs)("label",{htmlFor:"footerCopyright",className:"block text-sm font-medium text-gray-700 mb-1",children:["页脚版权信息 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{id:"footerCopyright",type:"text",className:"w-full px-3 py-2 border rounded-md ".concat(u.footerCopyright?"border-red-500":"border-gray-300"),...m("footerCopyright",{required:"请输入页脚版权信息"})}),u.footerCopyright&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-500",children:u.footerCopyright.message})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-lg font-medium mb-4",children:"社交媒体"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"weixin",className:"block text-sm font-medium text-gray-700 mb-1",children:"微信公众号"}),(0,r.jsx)("input",{id:"weixin",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...m("socialMedia.weixin")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"weibo",className:"block text-sm font-medium text-gray-700 mb-1",children:"微博"}),(0,r.jsx)("input",{id:"weibo",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...m("socialMedia.weibo")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"zhihu",className:"block text-sm font-medium text-gray-700 mb-1",children:"知乎"}),(0,r.jsx)("input",{id:"zhihu",type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md",...m("socialMedia.zhihu")})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 pt-4",children:[(0,r.jsx)("button",{type:"button",className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",onClick:()=>x(l),disabled:e,children:"重置"}),(0,r.jsx)("button",{type:"submit",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50",disabled:e,children:e?"保存中...":"保存设置"})]})]})}),(0,r.jsx)(o.x7,{position:"top-right"})]})}},30622:function(e,t,s){"use strict";var r=s(2265),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,n={key:!0,ref:!0,__self:!0,__source:!0};function d(e,t,s){var r,i={},d=null,c=null;for(r in void 0!==s&&(d=""+s),void 0!==t.key&&(d=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,r)&&!n.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:a,type:e,key:d,ref:c,props:i,_owner:l.current}}t.Fragment=i,t.jsx=d,t.jsxs=d},57437:function(e,t,s){"use strict";e.exports=s(30622)},5925:function(e,t,s){"use strict";let r,a;s.d(t,{x7:function(){return em},ZP:function(){return ep},Am:function(){return z}});var i,o=s(2265);let l={data:""},n=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||l,d=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,m=/\n+/g,p=(e,t)=>{let s="",r="",a="";for(let i in e){let o=e[i];"@"==i[0]?"i"==i[1]?s=i+" "+o+";":r+="f"==i[1]?p(o,i):i+"{"+p(o,"k"==i[1]?"":t)+"}":"object"==typeof o?r+=p(o,t?t.replace(/([^,])+/g,e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):i):null!=o&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=p.p?p.p(i,o):i+":"+o+";")}return s+(t&&a?t+"{"+a+"}":a)+r},u={},x=e=>{if("object"==typeof e){let t="";for(let s in e)t+=s+x(e[s]);return t}return e},h=(e,t,s,r,a)=>{var i;let o=x(e),l=u[o]||(u[o]=(e=>{let t=0,s=11;for(;t<e.length;)s=101*s+e.charCodeAt(t++)>>>0;return"go"+s})(o));if(!u[l]){let t=o!==e?e:(e=>{let t,s,r=[{}];for(;t=d.exec(e.replace(c,""));)t[4]?r.shift():t[3]?(s=t[3].replace(m," ").trim(),r.unshift(r[0][s]=r[0][s]||{})):r[0][t[1]]=t[2].replace(m," ").trim();return r[0]})(e);u[l]=p(a?{["@keyframes "+l]:t}:t,s?"":"."+l)}let n=s&&u.g?u.g:null;return s&&(u.g=u[l]),i=u[l],n?t.data=t.data.replace(n,i):-1===t.data.indexOf(i)&&(t.data=r?i+t.data:t.data+i),l},f=(e,t,s)=>e.reduce((e,r,a)=>{let i=t[a];if(i&&i.call){let e=i(s),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=t?"."+t:e&&"object"==typeof e?e.props?"":p(e,""):!1===e?"":e}return e+r+(null==i?"":i)},"");function b(e){let t=this||{},s=e.call?e(t.p):e;return h(s.unshift?s.raw?f(s,[].slice.call(arguments,1),t.p):s.reduce((e,s)=>Object.assign(e,s&&s.call?s(t.p):s),{}):s,n(t.target),t.g,t.o,t.k)}b.bind({g:1});let g,y,v,j=b.bind({k:1});function N(e,t){let s=this||{};return function(){let r=arguments;function a(i,o){let l=Object.assign({},i),n=l.className||a.className;s.p=Object.assign({theme:y&&y()},l),s.o=/ *go\d+/.test(n),l.className=b.apply(s,r)+(n?" "+n:""),t&&(l.ref=o);let d=e;return e[0]&&(d=l.as||e,delete l.as),v&&d[0]&&v(l),g(d,l)}return t?t(a):a}}var w=e=>"function"==typeof e,E=(e,t)=>w(e)?e(t):e,k=(r=0,()=>(++r).toString()),_=()=>{if(void 0===a&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");a=!e||e.matches}return a},D=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:s}=t;return D(e,{type:e.toasts.find(e=>e.id===s.id)?1:0,toast:s});case 3:let{toastId:r}=t;return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},O=[],C={toasts:[],pausedAt:void 0},P=e=>{C=D(C,e),O.forEach(e=>{e(C)})},$={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},F=(e={})=>{let[t,s]=(0,o.useState)(C),r=(0,o.useRef)(C);(0,o.useEffect)(()=>(r.current!==C&&s(C),O.push(s),()=>{let e=O.indexOf(s);e>-1&&O.splice(e,1)}),[]);let a=t.toasts.map(t=>{var s,r,a;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(s=e[t.type])?void 0:s.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(r=e[t.type])?void 0:r.duration)||(null==e?void 0:e.duration)||$[t.type],style:{...e.style,...null==(a=e[t.type])?void 0:a.style,...t.style}}});return{...t,toasts:a}},S=(e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:(null==s?void 0:s.id)||k()}),I=e=>(t,s)=>{let r=S(t,e,s);return P({type:2,toast:r}),r.id},z=(e,t)=>I("blank")(e,t);z.error=I("error"),z.success=I("success"),z.loading=I("loading"),z.custom=I("custom"),z.dismiss=e=>{P({type:3,toastId:e})},z.remove=e=>P({type:4,toastId:e}),z.promise=(e,t,s)=>{let r=z.loading(t.loading,{...s,...null==s?void 0:s.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?E(t.success,e):void 0;return a?z.success(a,{id:r,...s,...null==s?void 0:s.success}):z.dismiss(r),e}).catch(e=>{let a=t.error?E(t.error,e):void 0;a?z.error(a,{id:r,...s,...null==s?void 0:s.error}):z.dismiss(r)}),e};var L=(e,t)=>{P({type:1,toast:{id:e,height:t}})},A=()=>{P({type:5,time:Date.now()})},U=new Map,R=1e3,M=(e,t=R)=>{if(U.has(e))return;let s=setTimeout(()=>{U.delete(e),P({type:4,toastId:e})},t);U.set(e,s)},T=e=>{let{toasts:t,pausedAt:s}=F(e);(0,o.useEffect)(()=>{if(s)return;let e=Date.now(),r=t.map(t=>{if(t.duration===1/0)return;let s=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(s<0){t.visible&&z.dismiss(t.id);return}return setTimeout(()=>z.dismiss(t.id),s)});return()=>{r.forEach(e=>e&&clearTimeout(e))}},[t,s]);let r=(0,o.useCallback)(()=>{s&&P({type:6,time:Date.now()})},[s]),a=(0,o.useCallback)((e,s)=>{let{reverseOrder:r=!1,gutter:a=8,defaultPosition:i}=s||{},o=t.filter(t=>(t.position||i)===(e.position||i)&&t.height),l=o.findIndex(t=>t.id===e.id),n=o.filter((e,t)=>t<l&&e.visible).length;return o.filter(e=>e.visible).slice(...r?[n+1]:[0,n]).reduce((e,t)=>e+(t.height||0)+a,0)},[t]);return(0,o.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)M(e.id,e.removeDelay);else{let t=U.get(e.id);t&&(clearTimeout(t),U.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:L,startPause:A,endPause:r,calculateOffset:a}}},q=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,K=j`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Z=j`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,H=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${q} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${K} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Z} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,B=j`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Y=N("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${B} 1s linear infinite;
`,V=j`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,W=j`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,G=N("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${V} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${W} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,J=N("div")`
  position: absolute;
`,Q=N("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=j`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,ee=N("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,et=({toast:e})=>{let{icon:t,type:s,iconTheme:r}=e;return void 0!==t?"string"==typeof t?o.createElement(ee,null,t):t:"blank"===s?null:o.createElement(Q,null,o.createElement(Y,{...r}),"loading"!==s&&o.createElement(J,null,"error"===s?o.createElement(H,{...r}):o.createElement(G,{...r})))},es=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,er=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ea=N("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,ei=N("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,eo=(e,t)=>{let s=e.includes("top")?1:-1,[r,a]=_()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[es(s),er(s)];return{animation:t?`${j(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${j(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},el=o.memo(({toast:e,position:t,style:s,children:r})=>{let a=e.height?eo(e.position||t||"top-center",e.visible):{opacity:0},i=o.createElement(et,{toast:e}),l=o.createElement(ei,{...e.ariaProps},E(e.message,e));return o.createElement(ea,{className:e.className,style:{...a,...s,...e.style}},"function"==typeof r?r({icon:i,message:l}):o.createElement(o.Fragment,null,i,l))});i=o.createElement,p.p=void 0,g=i,y=void 0,v=void 0;var en=({id:e,className:t,style:s,onHeightUpdate:r,children:a})=>{let i=o.useCallback(t=>{if(t){let s=()=>{r(e,t.getBoundingClientRect().height)};s(),new MutationObserver(s).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,r]);return o.createElement("div",{ref:i,className:t,style:s},a)},ed=(e,t)=>{let s=e.includes("top"),r=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:_()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(s?1:-1)}px)`,...s?{top:0}:{bottom:0},...r}},ec=b`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,em=({reverseOrder:e,position:t="top-center",toastOptions:s,gutter:r,children:a,containerStyle:i,containerClassName:l})=>{let{toasts:n,handlers:d}=T(s);return o.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...i},className:l,onMouseEnter:d.startPause,onMouseLeave:d.endPause},n.map(s=>{let i=s.position||t,l=ed(i,d.calculateOffset(s,{reverseOrder:e,gutter:r,defaultPosition:t}));return o.createElement(en,{id:s.id,key:s.id,onHeightUpdate:d.updateHeight,className:s.visible?ec:"",style:l},"custom"===s.type?E(s.message,s):a?a(s):o.createElement(el,{toast:s,position:i}))}))},ep=z}},function(e){e.O(0,[865,971,458,744],function(){return e(e.s=55525)}),_N_E=e.O()}]);