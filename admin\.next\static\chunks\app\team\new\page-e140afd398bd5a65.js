(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[360],{77925:function(e,t,s){Promise.resolve().then(s.bind(s,25006))},25006:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return x}});var r=s(57437),a=s(2265),d=s(24033),o=s(61865),l=s(5925),n=s(6834),i=s(61396),m=s.n(i),c=s(30540);function x(){let e=(0,d.useRouter)(),{register:t,handleSubmit:s,formState:{errors:i},setValue:x}=(0,o.cI)(),[u,p]=(0,a.useState)(!1),[h,b]=(0,a.useState)(null),f=async t=>{p(!0);let s=new FormData;s.append("name",t.name),s.append("title",t.title),t.avatar&&t.avatar[0]&&s.append("avatar",t.avatar[0]),s.append("department",t.department),s.append("order",t.order.toString()),s.append("bio",t.bio),s.append("status",t.status);try{await c.h.post("/team",s,{headers:{"Content-Type":"multipart/form-data"}}),l.ZP.success("团队成员创建成功！"),e.push("/team")}catch(e){console.error("创建团队成员失败:",e),l.ZP.error("创建团队成员失败，请稍后再试。")}finally{p(!1)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)(l.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"添加新团队成员"}),(0,r.jsx)(m(),{href:"/team",children:(0,r.jsxs)("button",{className:"bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[(0,r.jsx)(n.Ao2,{className:"mr-2"}),"返回列表"]})})]}),(0,r.jsxs)("form",{onSubmit:s(f),className:"bg-white p-8 rounded-xl shadow-xl space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:["姓名 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",id:"name",...t("name",{required:"姓名不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(i.name?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),i.name&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:i.name.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"title",className:"block text-sm font-medium text-gray-700 mb-1",children:["职位 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",id:"title",...t("title",{required:"职位不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(i.title?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),i.title&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:i.title.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"avatar",className:"block text-sm font-medium text-gray-700 mb-1",children:["头像 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"mt-1 flex items-center space-x-4",children:[(0,r.jsx)("span",{className:"inline-block h-20 w-20 rounded-full overflow-hidden bg-gray-100",children:h?(0,r.jsx)("img",{src:h,alt:"头像预览",className:"h-full w-full object-cover"}):(0,r.jsx)("div",{className:"h-full w-full flex items-center justify-center text-gray-400",children:(0,r.jsx)(n.Yjd,{className:"h-8 w-8"})})}),(0,r.jsx)("label",{htmlFor:"avatar-upload",className:"cursor-pointer bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out text-sm",children:"选择头像"}),(0,r.jsx)("input",{id:"avatar-upload",type:"file",accept:"image/*",...t("avatar",{required:"头像不能为空"}),className:"sr-only",onChange:e=>{if(e.target.files&&e.target.files[0]){let t=e.target.files[0];b(URL.createObjectURL(t)),x("avatar",e.target.files)}}})]}),i.avatar&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:i.avatar.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"department",className:"block text-sm font-medium text-gray-700 mb-1",children:["部门 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"text",id:"department",...t("department",{required:"部门不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(i.department?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"),placeholder:"例如: 留学规划部"}),i.department&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:i.department.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"order",className:"block text-sm font-medium text-gray-700 mb-1",children:["排序 (数字越小越靠前) ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("input",{type:"number",id:"order",...t("order",{required:"排序不能为空",valueAsNumber:!0,min:{value:0,message:"排序值不能小于0"}}),className:"mt-1 block w-full px-4 py-2 border ".concat(i.order?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"),defaultValue:0}),i.order&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:i.order.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"bio",className:"block text-sm font-medium text-gray-700 mb-1",children:["个人简介 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("textarea",{id:"bio",rows:5,...t("bio",{required:"个人简介不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(i.bio?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm")}),i.bio&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:i.bio.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-1",children:["状态 ",(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsxs)("select",{id:"status",...t("status",{required:"状态不能为空"}),className:"mt-1 block w-full px-4 py-2 border ".concat(i.status?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"),defaultValue:"active",children:[(0,r.jsx)("option",{value:"active",children:"启用"}),(0,r.jsx)("option",{value:"inactive",children:"禁用"})]}),i.status&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-500",children:i.status.message})]}),(0,r.jsx)("div",{className:"flex justify-end pt-4",children:(0,r.jsxs)("button",{type:"submit",disabled:u,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[u?(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):(0,r.jsx)(n.mW3,{className:"mr-2"}),u?"正在添加...":"添加成员"]})})]})]})}},30540:function(e,t,s){"use strict";s.d(t,{h:function(){return r}});let r=s(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),delete r.defaults.headers.common.Authorization,window.dispatchEvent(new CustomEvent("auth:logout"))),Promise.reject(e))),t.Z=r},24033:function(e,t,s){e.exports=s(15313)}},function(e){e.O(0,[737,892,865,61,971,458,744],function(){return e(e.s=77925)}),_N_E=e.O()}]);