(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[599],{10253:function(e,t,s){Promise.resolve().then(s.bind(s,94531))},94531:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return d}});var a=s(57437),r=s(2265),n=s(61396),i=s.n(n),c=s(5925),l=s(30540);function d(){let[e,t]=(0,r.useState)([]),[s,n]=(0,r.useState)(!0);(0,r.useEffect)(()=>{(async()=>{n(!0);try{let e=await l.h.get("/team");t(e.data.sort((e,t)=>e.order-t.order))}catch(e){c.ZP.error("获取团队成员列表失败"),console.error("获取团队成员列表失败:",e)}n(!1)})()},[]);let[d,o]=(0,r.useState)(!1),[x,h]=(0,r.useState)(null),m=async s=>{o(!0),h(s);try{await l.h.delete("/team/".concat(s)),t(e.filter(e=>e.id!==s)),c.ZP.success("团队成员已成功删除")}catch(e){console.error("删除团队成员失败:",e),c.ZP.error("删除团队成员失败，请重试")}finally{o(!1),h(null)}},p=async(s,a)=>{let r="active"===a?"inactive":"active";try{await l.h.patch("/team/".concat(s),{status:r}),t(e.map(e=>e.id===s?{...e,status:r}:e)),c.ZP.success("团队成员状态已更改为".concat("active"===r?"启用":"禁用"))}catch(e){console.error("更改团队成员状态失败:",e),c.ZP.error("更改团队成员状态失败，请重试")}},u=async(s,a)=>{let r=e.findIndex(e=>e.id===s);if(-1===r||"up"===a&&0===r||"down"===a&&r===e.length-1)return;let n=[...e],i="up"===a?r-1:r+1;[n[r],n[i]]=[n[i],n[r]],n[r].order=r+1,n[i].order=i+1;try{let e=n.map(e=>({id:e.id,order:e.order}));await l.h.patch("/team/reorder",{members:e}),t(n.sort((e,t)=>e.order-t.order)),c.ZP.success("团队成员排序已更新")}catch(e){console.error("更新团队成员排序失败:",e),c.ZP.error("更新团队成员排序失败，请重试")}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsx)(c.x7,{position:"top-center"}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800",children:"团队管理"}),(0,a.jsx)(i(),{href:"/team/new",children:(0,a.jsxs)("button",{className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})}),"添加团队成员"]})})]}),s?(0,a.jsx)("div",{className:"text-center py-10",children:(0,a.jsx)("p",{className:"text-lg text-gray-500",children:"正在加载团队成员..."})}):0===e.length?(0,a.jsxs)("div",{className:"text-center py-10 bg-white rounded-lg shadow",children:[(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,a.jsx)("p",{className:"mt-4 text-lg text-gray-500",children:"暂无团队成员。"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:'点击"添加团队成员"按钮来创建新的成员。'})]}):(0,a.jsx)("div",{className:"bg-white rounded-xl shadow-xl overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"排序"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"头像"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"姓名"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"职位"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"部门"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"更新日期"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(t=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:t.order}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("button",{onClick:()=>u(t.id,"up"),className:"text-gray-500 hover:text-gray-700 focus:outline-none",disabled:0===e.indexOf(t),children:"▲"}),(0,a.jsx)("button",{onClick:()=>u(t.id,"down"),className:"text-gray-500 hover:text-gray-700 focus:outline-none",disabled:e.indexOf(t)===e.length-1,children:"▼"})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-200 overflow-hidden",children:(0,a.jsx)("div",{className:"h-full w-full flex items-center justify-center text-gray-500 text-xs",children:"头像"})})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:t.name}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.title}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.department}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("active"===t.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:"active"===t.status?"启用":"禁用"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:t.updatedAt}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>p(t.id,t.status),className:"text-indigo-600 hover:text-indigo-900",children:"active"===t.status?"禁用":"启用"}),(0,a.jsx)(i(),{href:"/team/edit/".concat(t.id),className:"text-blue-600 hover:text-blue-900",children:"编辑"}),(0,a.jsx)("button",{onClick:()=>m(t.id),disabled:d&&x===t.id,className:"text-red-600 hover:text-red-900 disabled:text-gray-400",children:d&&x===t.id?"删除中...":"删除"})]})})]},t.id))})]})})]})}},30540:function(e,t,s){"use strict";s.d(t,{h:function(){return a}});let a=s(54829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});a.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),a.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),t.Z=a}},function(e){e.O(0,[737,892,971,458,744],function(){return e(e.s=10253)}),_N_E=e.O()}]);