(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[110],{93877:function(e,s,r){Promise.resolve().then(r.bind(r,60263))},60263:function(e,s,r){"use strict";r.r(s);var t=r(57437),a=r(2265),o=r(61865),n=r(5925),d=r(61396),i=r.n(d),l=r(24033),m=r(6834),c=r(30540);s.default=()=>{let{register:e,handleSubmit:s,formState:{errors:r},reset:d}=(0,o.cI)(),u=(0,l.useRouter)(),[x,p]=(0,a.useState)(!1),h=async e=>{p(!0);try{await c.h.post("/users",e),n.ZP.success("用户添加成功！"),d(),setTimeout(()=>{u.push("/users")},1500)}catch(e){e.response&&e.response.data&&e.response.data.message?n.ZP.error("添加用户失败: ".concat(e.response.data.message)):n.ZP.error("添加用户失败，请稍后再试。"),console.error("添加用户失败:",e)}p(!1)};return(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsx)(n.x7,{position:"top-center"}),(0,t.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 flex items-center",children:[(0,t.jsx)(m.zy7,{className:"mr-3 text-indigo-600"})," 添加新用户"]}),(0,t.jsx)(i(),{href:"/users",children:(0,t.jsxs)("button",{className:"bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[(0,t.jsx)(m.Ao2,{className:"mr-2"})," 返回用户列表"]})})]}),(0,t.jsxs)("form",{onSubmit:s(h),className:"bg-white p-8 rounded-lg shadow-xl space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-1",children:"用户名"}),(0,t.jsx)("input",{id:"username",type:"text",...e("username",{required:"用户名为必填项"}),className:"w-full px-4 py-2 border ".concat(r.username?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500")}),r.username&&(0,t.jsx)("p",{className:"mt-1 text-xs text-red-500",children:r.username.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-1",children:"姓名"}),(0,t.jsx)("input",{id:"name",type:"text",...e("name",{required:"姓名为必填项"}),className:"w-full px-4 py-2 border ".concat(r.name?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500")}),r.name&&(0,t.jsx)("p",{className:"mt-1 text-xs text-red-500",children:r.name.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"邮箱"}),(0,t.jsx)("input",{id:"email",type:"email",...e("email",{required:"邮箱为必填项",pattern:{value:/^\S+@\S+$/i,message:"请输入有效的邮箱地址"}}),className:"w-full px-4 py-2 border ".concat(r.email?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500")}),r.email&&(0,t.jsx)("p",{className:"mt-1 text-xs text-red-500",children:r.email.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"role",className:"block text-sm font-medium text-gray-700 mb-1",children:"角色"}),(0,t.jsxs)("select",{id:"role",...e("role",{required:"角色为必选项"}),className:"w-full px-4 py-2 border ".concat(r.role?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white"),children:[(0,t.jsx)("option",{value:"viewer",children:"查看者"}),(0,t.jsx)("option",{value:"editor",children:"编辑"}),(0,t.jsx)("option",{value:"admin",children:"管理员"})]}),r.role&&(0,t.jsx)("p",{className:"mt-1 text-xs text-red-500",children:r.role.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"初始密码 (可选)"}),(0,t.jsx)("input",{id:"password",type:"password",...e("password",{minLength:{value:6,message:"密码至少需要6个字符"}}),className:"w-full px-4 py-2 border ".concat(r.password?"border-red-500":"border-gray-300"," rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"),placeholder:"留空则由系统生成"}),r.password&&(0,t.jsx)("p",{className:"mt-1 text-xs text-red-500",children:r.password.message})]}),(0,t.jsx)("div",{className:"flex justify-end pt-4",children:(0,t.jsxs)("button",{type:"submit",disabled:x,className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed",children:[x?(0,t.jsx)(m.dAq,{className:"animate-spin mr-2"}):(0,t.jsx)(m.mW3,{className:"mr-2"}),x?"正在保存...":"保存用户"]})})]})]})}},30540:function(e,s,r){"use strict";r.d(s,{h:function(){return t}});let t=r(54829).Z.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});t.interceptors.request.use(e=>{let s=localStorage.getItem("adminToken");return s&&(e.headers.Authorization="Bearer ".concat(s)),e},e=>Promise.reject(e)),t.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e))),s.Z=t},24033:function(e,s,r){e.exports=r(15313)}},function(e){e.O(0,[737,892,865,61,971,458,744],function(){return e(e.s=93877)}),_N_E=e.O()}]);