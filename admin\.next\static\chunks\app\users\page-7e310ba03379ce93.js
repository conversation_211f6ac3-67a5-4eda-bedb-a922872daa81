(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[240],{46378:function(e,t,s){Promise.resolve().then(s.bind(s,61534))},61534:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return d}});var r=s(57437),a=s(2265),i=s(61396),n=s.n(i),l=s(5925),c=s(30540);function d(){let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!0),[d,o]=(0,a.useState)(""),[x,m]=(0,a.useState)(""),u=async()=>{i(!0);try{let e=await c.h.get("/users",{params:{search:d,role:x}});t(e.data)}catch(e){l.ZP.error("获取用户列表失败"),console.error("获取用户列表失败:",e)}i(!1)};(0,a.useEffect)(()=>{u()},[]),(0,a.useEffect)(()=>{let e=setTimeout(()=>{u()},300);return()=>clearTimeout(e)},[d,x]);let h=async(s,r)=>{try{let a="active"===r?"inactive":"active";await c.h.patch("/users/".concat(s,"/status"),{status:a}),t(e.map(e=>e.id===s?{...e,status:a}:e)),l.ZP.success("用户状态已更新为".concat("active"===a?"激活":"禁用"))}catch(e){l.ZP.error("更新失败，请重试"),console.error("更新用户状态失败:",e)}},p=async s=>{if(window.confirm("确定要删除此用户吗？此操作不可恢复。"))try{await c.h.delete("/users/".concat(s)),t(e.filter(e=>e.id!==s)),l.ZP.success("用户删除成功")}catch(e){l.ZP.error("删除用户失败"),console.error("删除用户失败:",e)}};return(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,r.jsx)(l.x7,{position:"top-center"}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-800 flex items-center",children:[(0,r.jsx)(FiUsers,{className:"mr-3 text-indigo-600"})," 用户管理"]}),(0,r.jsx)(n(),{href:"/users/new",children:(0,r.jsxs)("button",{className:"bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center",children:[(0,r.jsx)(FiPlusCircle,{className:"mr-2"})," 添加用户"]})})]}),(0,r.jsx)("div",{className:"mb-6 p-4 bg-white rounded-lg shadow",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"search",className:"block text-sm font-medium text-gray-700",children:"搜索用户"}),(0,r.jsxs)("div",{className:"mt-1 relative rounded-md shadow-sm",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(FiSearch,{className:"text-gray-400"})}),(0,r.jsx)("input",{type:"text",id:"search",className:"focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-2",placeholder:"按用户名、姓名或邮箱搜索...",value:d,onChange:e=>o(e.target.value)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"roleFilter",className:"block text-sm font-medium text-gray-700",children:"按角色筛选"}),(0,r.jsxs)("select",{id:"roleFilter",className:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md",value:x,onChange:e=>m(e.target.value),children:[(0,r.jsx)("option",{value:"",children:"所有角色"}),(0,r.jsx)("option",{value:"admin",children:"管理员"}),(0,r.jsx)("option",{value:"editor",children:"编辑"}),(0,r.jsx)("option",{value:"viewer",children:"查看者"})]})]})]})}),s?(0,r.jsx)("div",{className:"text-center py-10",children:(0,r.jsx)("p",{className:"text-lg text-gray-500",children:"正在加载用户列表..."})}):0===e.length?(0,r.jsxs)("div",{className:"text-center py-10 bg-white rounded-lg shadow",children:[(0,r.jsx)(FiXCircle,{className:"mx-auto text-gray-400 text-5xl mb-4"}),(0,r.jsx)("p",{className:"text-lg text-gray-500",children:"未找到用户。"}),(0,r.jsxs)("p",{className:"text-sm text-gray-400",children:["尝试调整搜索词或筛选条件，或",(0,r.jsx)(n(),{href:"/users/new",className:"text-indigo-600 hover:underline",children:"添加新用户"}),"。"]})]}):(0,r.jsx)("div",{className:"bg-white shadow-xl rounded-lg overflow-x-auto",children:(0,r.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"角色"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"最后登录"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"状态"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-center",children:"操作"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("admin"===e.role?"bg-purple-100 text-purple-800":"editor"===e.role?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"),children:"admin"===e.role?"管理员":"editor"===e.role?"编辑":"查看者"})}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.lastLogin}),(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,r.jsx)("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full ".concat("active"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:"active"===e.status?"激活":"禁用"})}),(0,r.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-center",children:[(0,r.jsx)(n(),{href:"/users/edit/".concat(e.id),className:"text-indigo-600 hover:text-indigo-800 transition duration-150 p-1 rounded-full hover:bg-indigo-100 inline-block mr-2",title:"编辑用户",children:(0,r.jsx)(FiEdit,{size:18})}),(0,r.jsx)("button",{onClick:()=>h(e.id,e.status),className:"p-1 rounded-full transition duration-150 ".concat("active"===e.status?"text-red-600 hover:text-red-800 hover:bg-red-100":"text-green-600 hover:text-green-800 hover:bg-green-100"),title:"active"===e.status?"禁用用户":"激活用户",children:"active"===e.status?(0,r.jsx)(FiToggleRight,{size:20}):(0,r.jsx)(FiToggleLeft,{size:20})}),(0,r.jsx)("button",{onClick:()=>p(e.id),className:"text-red-600 hover:text-red-800 transition duration-150 p-1 rounded-full hover:bg-red-100 ml-2",title:"删除用户",children:(0,r.jsx)(FiTrash2,{size:18})})]})]},e.id))})]})})]})}},30540:function(e,t,s){"use strict";s.d(t,{h:function(){return r}});let r=s(54829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});r.interceptors.request.use(e=>{let t=localStorage.getItem("adminToken");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),r.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("adminToken"),localStorage.removeItem("adminUser"),window.location.href="/login"),Promise.reject(e)))}},function(e){e.O(0,[737,892,971,458,744],function(){return e(e.s=46378)}),_N_E=e.O()}]);