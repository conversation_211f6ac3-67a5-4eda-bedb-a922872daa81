# 上海留学顾问管理系统

这是上海留学顾问网站的后台管理系统，用于管理网站内容、用户、咨询等。

## 项目结构

```
admin/
├── app/                  # Next.js 应用目录
│   ├── api/              # API 路由
│   ├── content/          # 内容管理模块
│   ├── inquiries/        # 咨询管理模块
│   ├── login/            # 登录页面
│   ├── settings/         # 系统设置
│   ├── team/             # 团队管理
│   ├── users/            # 用户管理
│   ├── globals.css       # 全局样式
│   ├── layout.tsx        # 根布局组件
│   └── page.tsx          # 仪表盘页面
├── middleware.ts         # 认证中间件
├── next.config.ts        # Next.js 配置
├── package.json          # 项目依赖
├── postcss.config.js     # PostCSS 配置
└── tailwind.config.js    # Tailwind CSS 配置
```

## 功能特性

- 用户认证与授权
- 内容管理（文章、服务、案例、FAQ等）
- 客户咨询管理
- 团队成员管理
- 系统设置

## 安装与运行

### 安装依赖

```bash
npm install
```

### 开发环境运行

```bash
npm run dev
```

应用将在 http://localhost:3001 运行

### 构建生产版本

```bash
npm run build
npm start
```

## 环境变量

创建 `.env.local` 文件并设置以下环境变量：

```
NEXTAUTH_URL=http://localhost:3001
NEXTAUTH_SECRET=your-secret-key
JWT_SECRET=your-jwt-secret
API_BASE_URL=http://localhost:3000/api
```

## 技术栈

- Next.js 14
- React 18
- Tailwind CSS
- JWT 认证
- React Hook Form
- Axios

## 开发指南

### 添加新页面

在 `app` 目录下创建新的目录和 `page.tsx` 文件。

### API 路由

API 路由位于 `app/api` 目录下，使用 Next.js 的 App Router API 路由功能。

### 认证

系统使用 JWT 进行认证，认证逻辑在 `middleware.ts` 中实现。

## 部署

可以部署到 Vercel、Netlify 或其他支持 Next.js 的平台。