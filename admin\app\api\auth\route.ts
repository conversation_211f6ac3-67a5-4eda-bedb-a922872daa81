import { NextResponse } from 'next/server';

// 重定向到主站的认证API
export async function POST(request: Request) {
  try {
    const body = await request.json();

    // 调用主站的认证API
    const response = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3000/api'}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body)
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(data, { status: response.status });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error('登录错误:', error);
    return NextResponse.json({ error: '服务器错误' }, { status: 500 });
  }
}