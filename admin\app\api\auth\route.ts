import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

// 模拟用户数据，实际项目中应该从数据库获取
const users = [
  {
    id: 1,
    username: 'admin',
    // 密码: admin123
    passwordHash: '$2a$10$N9qo8uLOickgx2ZMRZoMyeIjZAgcfl7p92ldGxad68LJZdL17lhWy',
    role: 'admin',
    name: '管理员'
  },
  {
    id: 2,
    username: 'editor',
    // 密码: editor123
    passwordHash: '$2a$10$IbfVjHmvGoSp23xN97QT9.LbQjjQdk.9G9BuuPaYOSRxL6tCUm5iq',
    role: 'editor',
    name: '编辑'
  }
];

export async function POST(request: Request) {
  try {
    const { username, password } = await request.json();

    // 查找用户
    const user = users.find(u => u.username === username);
    if (!user) {
      return NextResponse.json({ error: '用户名或密码错误' }, { status: 401 });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
    if (!isPasswordValid) {
      return NextResponse.json({ error: '用户名或密码错误' }, { status: 401 });
    }

    // 创建JWT令牌
    const token = jwt.sign(
      { 
        id: user.id, 
        username: user.username,
        role: user.role,
        name: user.name
      },
      process.env.JWT_SECRET || 'your-jwt-secret',
      { expiresIn: '1d' }
    );

    // 返回用户信息和令牌
    return NextResponse.json({
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        name: user.name
      },
      token
    });
  } catch (error) {
    console.error('登录错误:', error);
    return NextResponse.json({ error: '服务器错误' }, { status: 500 });
  }
}