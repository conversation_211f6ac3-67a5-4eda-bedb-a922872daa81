'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../components/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import toast, { Toaster } from 'react-hot-toast';
import api from '../utils/api';

interface Appointment {
  id: number;
  appointment_number: string;
  client_name: string;
  client_email: string;
  client_phone: string;
  consultant_id: number;
  consultant_name: string;
  consultant_specialty: string;
  appointment_date: string;
  appointment_time: string;
  duration: number;
  service_type: string;
  message: string;
  status: string;
  total_amount: number;
  admin_notes?: string;
  rating?: number;
  client_feedback?: string;
  created_at: string;
}

export default function AppointmentsPage() {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [statusFilter, setStatusFilter] = useState('');

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/login');
      return;
    }

    if (isAuthenticated) {
      fetchAppointments();
    }
  }, [loading, isAuthenticated, router, statusFilter]);

  const fetchAppointments = async () => {
    try {
      setIsLoading(true);
      const params: any = {};
      if (statusFilter) {
        params.status = statusFilter;
      }

      console.log('开始获取预约数据...');
      const response = await api.get('/appointments', { params });
      console.log('预约API响应:', response.data);

      if (response.data.success) {
        const appointmentsData = response.data.data.items || response.data.data || [];
        console.log('解析的预约数据:', appointmentsData);
        setAppointments(appointmentsData);
      } else {
        console.error('API返回失败状态:', response.data);
        toast.error('获取预约列表失败');
      }
    } catch (error) {
      console.error('获取预约列表失败:', error);
      toast.error('获取预约列表失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = async (appointmentId: number, status: string) => {
    try {
      await api.put(`/appointments/${appointmentId}`, { status });
      toast.success('预约状态更新成功');
      fetchAppointments();
    } catch (error) {
      console.error('状态更新失败:', error);
      toast.error('状态更新失败');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待确认';
      case 'confirmed':
        return '已确认';
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      default:
        return status;
    }
  };

  const getServiceTypeText = (serviceType: string) => {
    switch (serviceType) {
      case 'education_planning':
        return '教育规划';
      case 'career_planning':
        return '职业规划';
      case 'study_abroad':
        return '留学咨询';
      case 'psychological_counseling':
        return '心理咨询';
      case 'learning_improvement':
        return '学习能力提升';
      default:
        return serviceType || '其他';
    }
  };

  if (loading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">预约管理</h1>
        <div className="flex items-center space-x-4">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="">全部状态</option>
            <option value="pending">待确认</option>
            <option value="confirmed">已确认</option>
            <option value="completed">已完成</option>
            <option value="cancelled">已取消</option>
          </select>
        </div>
      </div>

      {isLoading ? (
        <div className="text-center py-8">加载中...</div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  预约编号
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  客户信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  咨询师
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  预约时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  服务类型
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  费用
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {appointments.map((appointment) => (
                <tr key={appointment.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {appointment.appointment_number}
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(appointment.created_at).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{appointment.client_name}</div>
                    <div className="text-sm text-gray-500">{appointment.client_email}</div>
                    <div className="text-sm text-gray-500">{appointment.client_phone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{appointment.consultant_name}</div>
                    <div className="text-sm text-gray-500">{appointment.consultant_specialty}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{appointment.appointment_date}</div>
                    <div className="text-sm text-gray-500">{appointment.appointment_time}</div>
                    <div className="text-sm text-gray-500">{appointment.duration}分钟</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {getServiceTypeText(appointment.service_type)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(appointment.status)}`}>
                      {getStatusText(appointment.status)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      ¥{appointment.total_amount}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => {
                        setSelectedAppointment(appointment);
                        setShowDetailModal(true);
                      }}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      查看详情
                    </button>
                    <select
                      value={appointment.status}
                      onChange={(e) => handleStatusChange(appointment.id, e.target.value)}
                      className="text-sm border border-gray-300 rounded px-2 py-1"
                    >
                      <option value="pending">待确认</option>
                      <option value="confirmed">已确认</option>
                      <option value="completed">已完成</option>
                      <option value="cancelled">已取消</option>
                    </select>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* 预约详情模态框 */}
      {showDetailModal && selectedAppointment && (
        <AppointmentDetailModal
          appointment={selectedAppointment}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedAppointment(null);
          }}
          onUpdate={() => {
            setShowDetailModal(false);
            setSelectedAppointment(null);
            fetchAppointments();
          }}
        />
      )}
    </div>
  );
}

// 预约详情模态框组件
function AppointmentDetailModal({ appointment, onClose, onUpdate }: {
  appointment: Appointment;
  onClose: () => void;
  onUpdate: () => void;
}) {
  const [adminNotes, setAdminNotes] = useState(appointment.admin_notes || '');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleUpdateNotes = async () => {
    setIsSubmitting(true);
    try {
      await api.put(`/appointments/${appointment.id}`, { admin_notes: adminNotes });
      toast.success('备注更新成功');
      onUpdate();
    } catch (error) {
      console.error('更新备注失败:', error);
      toast.error('更新备注失败');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-bold mb-4">预约详情</h2>

        {/* 基本信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <h3 className="font-semibold text-gray-700 mb-2">预约信息</h3>
            <p><strong>预约编号:</strong> {appointment.appointment_number}</p>
            <p><strong>预约时间:</strong> {appointment.appointment_date} {appointment.appointment_time}</p>
            <p><strong>咨询时长:</strong> {appointment.duration}分钟</p>
            <p><strong>服务类型:</strong> {appointment.service_type}</p>
            <p><strong>费用:</strong> ¥{appointment.total_amount}</p>
          </div>

          <div>
            <h3 className="font-semibold text-gray-700 mb-2">客户信息</h3>
            <p><strong>姓名:</strong> {appointment.client_name}</p>
            <p><strong>邮箱:</strong> {appointment.client_email}</p>
            <p><strong>电话:</strong> {appointment.client_phone}</p>
          </div>
        </div>

        {/* 咨询师信息 */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-700 mb-2">咨询师信息</h3>
          <p><strong>姓名:</strong> {appointment.consultant_name}</p>
          <p><strong>专业:</strong> {appointment.consultant_specialty}</p>
        </div>

        {/* 客户需求 */}
        {appointment.message && (
          <div className="mb-6">
            <h3 className="font-semibold text-gray-700 mb-2">客户需求</h3>
            <p className="bg-gray-50 p-3 rounded">{appointment.message}</p>
          </div>
        )}

        {/* 客户反馈 */}
        {appointment.client_feedback && (
          <div className="mb-6">
            <h3 className="font-semibold text-gray-700 mb-2">客户反馈</h3>
            <p className="bg-blue-50 p-3 rounded">{appointment.client_feedback}</p>
            {appointment.rating && (
              <p className="mt-2"><strong>评分:</strong> {appointment.rating}/5</p>
            )}
          </div>
        )}

        {/* 管理员备注 */}
        <div className="mb-6">
          <h3 className="font-semibold text-gray-700 mb-2">管理员备注</h3>
          <textarea
            value={adminNotes}
            onChange={(e) => setAdminNotes(e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md"
            placeholder="添加管理员备注..."
          />
          <button
            onClick={handleUpdateNotes}
            disabled={isSubmitting}
            className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
          >
            {isSubmitting ? '更新中...' : '更新备注'}
          </button>
        </div>

        <div className="flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
}
