'use client';

import React from 'react';
import { useAuth } from './AuthContext';
import { usePathname } from 'next/navigation';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { user, logout, isAuthenticated, loading } = useAuth();
  const pathname = usePathname();

  // 如果在登录页面，不显示管理布局
  if (pathname === '/login') {
    return <>{children}</>;
  }

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  // 如果未认证，显示登录提示
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100">
        <div className="max-w-md w-full bg-white p-8 rounded-lg shadow-md text-center">
          <h2 className="text-2xl font-bold mb-4">需要登录</h2>
          <p className="text-gray-600 mb-6">请先登录以访问管理系统</p>
          <a
            href="/login"
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700"
          >
            前往登录
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-layout min-h-screen bg-gray-100">
      <header className="bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50">
        <div className="flex justify-between items-center">
          <h1 className="text-xl font-semibold">后台管理系统</h1>
          <div className="flex items-center space-x-4">
            <span className="text-sm">欢迎，{user?.name}</span>
            <button
              onClick={logout}
              className="bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm"
            >
              登出
            </button>
          </div>
        </div>
      </header>
      <div className="flex pt-16">
        <nav className="bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40">
          {/* 导航链接 */}
          <ul className="space-y-2 mt-4">
            <li>
              <a
                href="/"
                className={`block p-2 hover:bg-gray-200 rounded ${pathname === '/' ? 'bg-blue-100 text-blue-600' : ''}`}
              >
                仪表盘
              </a>
            </li>
            <li>
              <a
                href="/users"
                className={`block p-2 hover:bg-gray-200 rounded ${pathname.startsWith('/users') ? 'bg-blue-100 text-blue-600' : ''}`}
              >
                用户管理
              </a>
            </li>
            <li>
              <a
                href="/content/articles"
                className={`block p-2 hover:bg-gray-200 rounded ${pathname.startsWith('/content/articles') ? 'bg-blue-100 text-blue-600' : ''}`}
              >
                文章管理
              </a>
            </li>
            <li>
              <a
                href="/content/services"
                className={`block p-2 hover:bg-gray-200 rounded ${pathname.startsWith('/content/services') ? 'bg-blue-100 text-blue-600' : ''}`}
              >
                服务管理
              </a>
            </li>
            <li>
              <a
                href="/content/cases"
                className={`block p-2 hover:bg-gray-200 rounded ${pathname.startsWith('/content/cases') ? 'bg-blue-100 text-blue-600' : ''}`}
              >
                案例管理
              </a>
            </li>
            <li>
              <a
                href="/content/banners"
                className={`block p-2 hover:bg-gray-200 rounded ${pathname.startsWith('/content/banners') ? 'bg-blue-100 text-blue-600' : ''}`}
              >
                Banner管理
              </a>
            </li>
            <li>
              <a
                href="/content/faq"
                className={`block p-2 hover:bg-gray-200 rounded ${pathname.startsWith('/content/faq') ? 'bg-blue-100 text-blue-600' : ''}`}
              >
                FAQ管理
              </a>
            </li>
            <li>
              <a
                href="/inquiries"
                className={`block p-2 hover:bg-gray-200 rounded ${pathname.startsWith('/inquiries') ? 'bg-blue-100 text-blue-600' : ''}`}
              >
                客户咨询
              </a>
            </li>
            <li>
              <a
                href="/team"
                className={`block p-2 hover:bg-gray-200 rounded ${pathname.startsWith('/team') ? 'bg-blue-100 text-blue-600' : ''}`}
              >
                团队管理
              </a>
            </li>
            <li>
              <a
                href="/settings"
                className={`block p-2 hover:bg-gray-200 rounded ${pathname.startsWith('/settings') ? 'bg-blue-100 text-blue-600' : ''}`}
              >
                系统设置
              </a>
            </li>
          </ul>
        </nav>
        <main className="flex-1 p-6 md:ml-64 bg-gray-100">
          {children}
        </main>
      </div>
    </div>
  );
}
