'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import api from '../utils/api';

interface User {
  id: number;
  username: string;
  name: string;
  email: string;
  role: string;
  avatar?: string;
  phone?: string;
  position?: string;
  bio?: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  updateUserInfo: (userData: Partial<User>) => void;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [initialized, setInitialized] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // 初始化时检查用户是否已登录
  useEffect(() => {
    const checkAuth = () => {
      try {
        const token = localStorage.getItem('adminToken');
        const userData = localStorage.getItem('adminUser');

        if (token && userData && userData !== 'undefined' && userData !== 'null') {
          try {
            // 设置axios默认头部包含认证令牌
            api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

            // 解析用户数据
            const parsedUser = JSON.parse(userData);
            setUser(parsedUser);
            console.log('从本地存储恢复用户状态:', parsedUser);
          } catch (parseError) {
            console.error('解析用户数据失败:', parseError);
            // 清除无效的数据
            localStorage.removeItem('adminToken');
            localStorage.removeItem('adminUser');
            setUser(null);
          }
        }
      } catch (error) {
        console.error('认证检查失败:', error);
        setUser(null);
      } finally {
        setLoading(false);
        setInitialized(true);
      }
    };

    if (!initialized) {
      checkAuth();
    }
  }, [initialized]);

  // 路径变化时检查是否需要重定向
  useEffect(() => {
    if (initialized && !loading) {
      if (!user && pathname !== '/login') {
        router.push('/login');
      }
    }
  }, [initialized, loading, user, pathname, router]);

  // 监听401错误导致的自动登出
  useEffect(() => {
    const handleAuthLogout = () => {
      console.log('收到401错误，自动登出');
      setUser(null);
      if (pathname !== '/login') {
        router.push('/login');
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('auth:logout', handleAuthLogout);

      return () => {
        window.removeEventListener('auth:logout', handleAuthLogout);
      };
    }
  }, [pathname, router]);

  // 登录函数
  const login = async (username: string, password: string) => {
    try {
      console.log('AuthContext: 发送登录请求', { username });
      const response = await api.post('/auth/login', { username, password });
      console.log('AuthContext: 收到响应', response.data);

      // 检查响应数据结构
      if (!response.data || !response.data.data) {
        throw new Error('API响应格式错误');
      }

      const { user, token } = response.data.data;

      if (!user || !token) {
        throw new Error('响应中缺少用户信息或令牌');
      }

      console.log('AuthContext: 解析的用户数据', { user, token });

      // 保存令牌和用户信息
      localStorage.setItem('adminToken', token);
      localStorage.setItem('adminUser', JSON.stringify(user));

      // 设置axios默认头部
      api.defaults.headers.common['Authorization'] = `Bearer ${token}`;

      setUser(user);
      console.log('AuthContext: 登录成功，用户状态已更新');
    } catch (error) {
      console.error('AuthContext: 登录失败', error);
      throw error;
    }
  };

  // 登出函数
  const logout = () => {
    localStorage.removeItem('adminToken');
    localStorage.removeItem('adminUser');
    delete api.defaults.headers.common['Authorization'];
    setUser(null);
    router.push('/login');
  };

  // 更新用户信息
  const updateUserInfo = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('adminUser', JSON.stringify(updatedUser));
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, logout, updateUserInfo, isAuthenticated: !!user }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}