'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../components/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import toast, { Toaster } from 'react-hot-toast';
import api from '../utils/api';

interface Consultant {
  id: number;
  name: string;
  email: string;
  phone: string;
  specialty: string;
  experience_years: number;
  education: string;
  certifications: string[] | string;
  bio: string;
  avatar_url: string;
  hourly_rate: number;
  languages: string[] | string;
  available_hours?: any;
  status: string;
  rating: number;
  total_appointments: number;
  created_at: string;
}

export default function ConsultantsPage() {
  const { isAuthenticated, loading } = useAuth();
  const router = useRouter();
  const [consultants, setConsultants] = useState<Consultant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingConsultant, setEditingConsultant] = useState<Consultant | null>(null);

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/login');
      return;
    }

    if (isAuthenticated) {
      fetchConsultants();
    }
  }, [loading, isAuthenticated, router]);

  const fetchConsultants = async () => {
    try {
      setIsLoading(true);
      console.log('开始获取咨询师数据...');
      const response = await api.get('/consultants');
      console.log('API响应:', response.data);

      if (response.data.success) {
        const consultantsData = response.data.data.items || response.data.data || [];
        console.log('解析的咨询师数据:', consultantsData);
        setConsultants(consultantsData);
      } else {
        console.error('API返回失败状态:', response.data);
        toast.error('获取咨询师列表失败');
      }
    } catch (error) {
      console.error('获取咨询师列表失败:', error);
      toast.error('获取咨询师列表失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteConsultant = async (consultantId: number) => {
    if (!confirm('确定要删除这个咨询师吗？')) return;

    try {
      await api.delete(`/consultants/${consultantId}`);
      toast.success('咨询师删除成功');
      fetchConsultants();
    } catch (error) {
      console.error('删除咨询师失败:', error);
      toast.error('删除咨询师失败');
    }
  };

  if (loading || !isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">咨询师管理</h1>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          新增咨询师
        </button>
      </div>

      {isLoading ? (
        <div className="text-center py-8">加载中...</div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  咨询师信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  专业领域
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  经验/费用
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  评分/预约数
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {consultants.map((consultant) => (
                <tr key={consultant.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        {consultant.avatar_url ? (
                          <img
                            className="h-10 w-10 rounded-full object-cover"
                            src={consultant.avatar_url}
                            alt={consultant.name}
                          />
                        ) : (
                          <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-700">
                              {consultant.name.charAt(0)}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{consultant.name}</div>
                        <div className="text-sm text-gray-500">{consultant.email}</div>
                        <div className="text-sm text-gray-500">{consultant.phone}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{consultant.specialty}</div>
                    <div className="text-sm text-gray-500">{consultant.education}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{consultant.experience_years}年经验</div>
                    <div className="text-sm text-gray-500">¥{consultant.hourly_rate}/小时</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">评分: {consultant.rating}</div>
                    <div className="text-sm text-gray-500">预约: {consultant.total_appointments}次</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      consultant.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {consultant.status === 'active' ? '活跃' : '禁用'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => setEditingConsultant(consultant)}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      编辑
                    </button>
                    <button
                      onClick={() => handleDeleteConsultant(consultant.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      删除
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* 创建/编辑咨询师模态框 */}
      {(showCreateModal || editingConsultant) && (
        <ConsultantModal
          consultant={editingConsultant}
          onClose={() => {
            setShowCreateModal(false);
            setEditingConsultant(null);
          }}
          onSuccess={() => {
            setShowCreateModal(false);
            setEditingConsultant(null);
            fetchConsultants();
          }}
        />
      )}

      <Toaster position="top-right" />
    </div>
  );
}

// 咨询师创建/编辑模态框组件
function ConsultantModal({ consultant, onClose, onSuccess }: {
  consultant: Consultant | null;
  onClose: () => void;
  onSuccess: () => void;
}) {
  const [formData, setFormData] = useState({
    name: consultant?.name || '',
    email: consultant?.email || '',
    phone: consultant?.phone || '',
    specialty: consultant?.specialty || '',
    experience_years: consultant?.experience_years || 0,
    education: consultant?.education || '',
    certifications: consultant?.certifications ? (Array.isArray(consultant.certifications) ? consultant.certifications.join(', ') : consultant.certifications) : '',
    bio: consultant?.bio || '',
    avatar_url: consultant?.avatar_url || '',
    hourly_rate: consultant?.hourly_rate || 0,
    languages: consultant?.languages ? (Array.isArray(consultant.languages) ? consultant.languages.join(', ') : consultant.languages) : '',
    available_hours: consultant ? JSON.stringify(consultant.available_hours || {}) : '{}',
    status: consultant?.status || 'active'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // 处理数据格式
      const submitData = {
        ...formData,
        certifications: formData.certifications.split(',').map(cert => cert.trim()).filter(cert => cert),
        languages: formData.languages.split(',').map(lang => lang.trim()).filter(lang => lang),
        available_hours: formData.available_hours ? JSON.parse(formData.available_hours) : {}
      };

      if (consultant) {
        // 编辑咨询师
        await api.put(`/consultants/${consultant.id}`, submitData);
        toast.success('咨询师更新成功');
      } else {
        // 创建咨询师
        await api.post('/consultants', submitData);
        toast.success('咨询师创建成功');
      }
      onSuccess();
    } catch (error) {
      console.error('操作失败:', error);
      toast.error(consultant ? '咨询师更新失败' : '咨询师创建失败');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <h2 className="text-xl font-bold mb-4">
          {consultant ? '编辑咨询师' : '新增咨询师'}
        </h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">姓名</label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">邮箱</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">电话</label>
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">专业领域</label>
              <input
                type="text"
                value={formData.specialty}
                onChange={(e) => setFormData({ ...formData, specialty: e.target.value })}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="如：教育规划,升学指导"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">工作经验(年)</label>
              <input
                type="number"
                value={formData.experience_years}
                onChange={(e) => setFormData({ ...formData, experience_years: parseInt(e.target.value) })}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                min="0"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">时薪(元)</label>
              <input
                type="number"
                value={formData.hourly_rate}
                onChange={(e) => setFormData({ ...formData, hourly_rate: parseInt(e.target.value) })}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
                min="0"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">教育背景</label>
            <input
              type="text"
              value={formData.education}
              onChange={(e) => setFormData({ ...formData, education: e.target.value })}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="如：华中师范大学教育学博士"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">资质证书</label>
            <input
              type="text"
              value={formData.certifications}
              onChange={(e) => setFormData({ ...formData, certifications: e.target.value })}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="多个证书用逗号分隔，如：国家认证教育规划师, 高级职业规划师"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">语言能力</label>
            <input
              type="text"
              value={formData.languages}
              onChange={(e) => setFormData({ ...formData, languages: e.target.value })}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="多种语言用逗号分隔，如：中文, 英语, 法语"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">头像URL</label>
            <input
              type="url"
              value={formData.avatar_url}
              onChange={(e) => setFormData({ ...formData, avatar_url: e.target.value })}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="https://example.com/avatar.jpg"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">个人简介</label>
            <textarea
              value={formData.bio}
              onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
              rows={4}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="请输入个人简介和专业背景..."
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">状态</label>
            <select
              value={formData.status}
              onChange={(e) => setFormData({ ...formData, status: e.target.value })}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            >
              <option value="active">活跃</option>
              <option value="inactive">禁用</option>
            </select>
          </div>

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
            >
              {isSubmitting ? '保存中...' : '保存'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
