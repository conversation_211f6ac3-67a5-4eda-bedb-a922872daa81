'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import toast, { Toaster } from 'react-hot-toast';
import { useAuth } from '../../../../components/AuthContext';
import api from '../../../../utils/api';
import {
  FiFileText,
  FiArrowLeft,
  FiSave,
  FiImage,
  FiEye
} from 'react-icons/fi';

interface Category {
  id: number;
  name: string;
}

interface Article {
  id: number;
  title: string;
  slug: string;
  summary: string;
  content: string;
  category_id: number;
  status: 'draft' | 'published';
  cover_image_url: string;
  author_id: number;
  views: number;
  published_at: string;
  created_at: string;
  updated_at: string;
}

interface ArticleFormData {
  title: string;
  slug: string;
  summary: string;
  content: string;
  category_id: number | '';
  status: 'draft' | 'published';
  cover_image_url: string;
}

export default function EditArticlePage() {
  const router = useRouter();
  const params = useParams();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [article, setArticle] = useState<Article | null>(null);
  const [formData, setFormData] = useState<ArticleFormData>({
    title: '',
    slug: '',
    summary: '',
    content: '',
    category_id: '',
    status: 'draft',
    cover_image_url: ''
  });

  useEffect(() => {
    if (params.id) {
      fetchArticle();
      fetchCategories();
    }
  }, [params.id]);

  const fetchArticle = async () => {
    try {
      const response = await api.get(`/articles/${params.id}`);
      if (response.data.success) {
        const articleData = response.data.data;
        setArticle(articleData);
        setFormData({
          title: articleData.title,
          slug: articleData.slug,
          summary: articleData.summary || '',
          content: articleData.content,
          category_id: articleData.category_id || '',
          status: articleData.status,
          cover_image_url: articleData.cover_image_url || ''
        });
      }
    } catch (error: any) {
      toast.error('获取文章信息失败');
      router.push('/content/articles');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await api.get('/categories');
      if (response.data.success) {
        setCategories(response.data.data);
      }
    } catch (error) {
      console.error('获取分类失败:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.title.trim()) {
      toast.error('请输入文章标题');
      return false;
    }
    if (!formData.content.trim()) {
      toast.error('请输入文章内容');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setSaving(true);
    try {
      const submitData = {
        ...formData,
        category_id: formData.category_id || null
      };
      
      await api.put(`/articles/${params.id}`, submitData);
      toast.success('文章更新成功');
      router.push('/content/articles');
    } catch (error: any) {
      toast.error(error.response?.data?.message || '更新文章失败');
    } finally {
      setSaving(false);
    }
  };

  const handlePreview = () => {
    // 简单的预览功能
    const previewWindow = window.open('', '_blank');
    if (previewWindow) {
      previewWindow.document.write(`
        <html>
          <head>
            <title>${formData.title}</title>
            <style>
              body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
              h1 { color: #333; }
              .summary { color: #666; font-style: italic; margin-bottom: 20px; }
              .content { line-height: 1.6; }
            </style>
          </head>
          <body>
            <h1>${formData.title}</h1>
            <div class="summary">${formData.summary}</div>
            <div class="content">${formData.content}</div>
          </body>
        </html>
      `);
      previewWindow.document.close();
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-10">
          <p className="text-lg text-gray-500">正在加载文章信息...</p>
        </div>
      </div>
    );
  }

  if (!article) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-10">
          <p className="text-lg text-gray-500">文章不存在</p>
          <Link href="/content/articles" className="text-blue-600 hover:underline">
            返回文章列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster position="top-center" />
      
      {/* 页面头部 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Link href="/content/articles" className="mr-4 p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <FiArrowLeft className="text-gray-600" size={20} />
          </Link>
          <h1 className="text-3xl font-bold text-gray-800 flex items-center">
            <FiFileText className="mr-3 text-blue-600" /> 编辑文章
          </h1>
        </div>
        <button
          onClick={handlePreview}
          className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors flex items-center"
        >
          <FiEye className="mr-2" />
          预览
        </button>
      </div>

      {/* 表单 */}
      <div className="bg-white rounded-lg shadow-lg p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 标题 */}
            <div className="lg:col-span-2">
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                文章标题 <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入文章标题"
                required
              />
            </div>

            {/* Slug */}
            <div>
              <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-2">
                URL别名
              </label>
              <input
                type="text"
                id="slug"
                name="slug"
                value={formData.slug}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="URL别名"
              />
            </div>

            {/* 分类 */}
            <div>
              <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 mb-2">
                文章分类
              </label>
              <select
                id="category_id"
                name="category_id"
                value={formData.category_id}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">请选择分类</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* 封面图片 */}
            <div className="lg:col-span-2">
              <label htmlFor="cover_image_url" className="block text-sm font-medium text-gray-700 mb-2">
                封面图片URL
              </label>
              <div className="flex">
                <input
                  type="url"
                  id="cover_image_url"
                  name="cover_image_url"
                  value={formData.cover_image_url}
                  onChange={handleInputChange}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入图片URL"
                />
                <button
                  type="button"
                  className="px-4 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-200 transition-colors"
                >
                  <FiImage />
                </button>
              </div>
            </div>

            {/* 摘要 */}
            <div className="lg:col-span-2">
              <label htmlFor="summary" className="block text-sm font-medium text-gray-700 mb-2">
                文章摘要
              </label>
              <textarea
                id="summary"
                name="summary"
                value={formData.summary}
                onChange={handleInputChange}
                rows={3}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入文章摘要"
              />
            </div>

            {/* 内容 */}
            <div className="lg:col-span-2">
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                文章内容 <span className="text-red-500">*</span>
              </label>
              <textarea
                id="content"
                name="content"
                value={formData.content}
                onChange={handleInputChange}
                rows={15}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入文章内容（支持HTML格式）"
                required
              />
              <p className="mt-1 text-sm text-gray-500">
                支持HTML标签，如 &lt;h2&gt;、&lt;p&gt;、&lt;ul&gt;、&lt;li&gt; 等
              </p>
            </div>

            {/* 状态 */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                发布状态
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="draft">草稿</option>
                <option value="published">已发布</option>
              </select>
            </div>

            {/* 文章信息 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                文章信息
              </label>
              <div className="text-sm text-gray-600 space-y-1">
                <p>浏览量：{article.views}</p>
                <p>创建时间：{new Date(article.created_at).toLocaleString()}</p>
                <p>更新时间：{new Date(article.updated_at).toLocaleString()}</p>
              </div>
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Link
              href="/content/articles"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
            >
              取消
            </Link>
            <button
              type="submit"
              disabled={saving}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  保存中...
                </>
              ) : (
                <>
                  <FiSave className="mr-2" />
                  保存更改
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
