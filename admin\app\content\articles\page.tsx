'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { api } from '@/utils/api';
import { toast } from 'react-hot-toast';

// 文章类型定义
interface Article {
  id: number;
  title: string;
  slug: string;
  summary: string;
  publishDate: string;
  status: 'published' | 'draft';
}

export default function ArticlesPage() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);

    useEffect(() => {
    const fetchArticles = async () => {
      setLoading(true);
      try {
        const response = await api.get<Article[]>('/content/articles');
        setArticles(response.data);
      } catch (error) {
        toast.error('获取文章列表失败');
        console.error('获取文章列表失败:', error);
      }
      setLoading(false);
    };
    fetchArticles();
  }, []);

  // 删除文章
  const handleDelete = async (id: number) => {
    if (window.confirm('确定要删除这篇文章吗？')) {
      try {
        await api.delete(`/content/articles/${id}`);
        setArticles(articles.filter(article => article.id !== id));
        toast.success('文章已删除');
      } catch (error) {
        toast.error('删除失败，请重试');
        console.error('删除文章失败:', error);
      }
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">文章管理</h2>
        <Link href="/content/articles/new" className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md">
          添加文章
        </Link>
      </div>

      {loading ? (
        <div className="text-center py-10">
          <p className="text-gray-500">加载中...</p>
        </div>
      ) : articles.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <p className="text-gray-500 mb-4">暂无文章</p>
          <Link href="/content/articles/new" className="text-blue-600 hover:text-blue-800">
            创建第一篇文章
          </Link>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">发布日期</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {articles.map((article) => (
                <tr key={article.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{article.title}</div>
                    <div className="text-sm text-gray-500">{article.summary.substring(0, 50)}...</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {article.publishDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${article.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                      {article.status === 'published' ? '已发布' : '草稿'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Link href={`/content/articles/edit/${article.id}`} className="text-blue-600 hover:text-blue-900 mr-4">编辑</Link>
                    <button onClick={() => handleDelete(article.id)} className="text-red-600 hover:text-red-900">删除</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}