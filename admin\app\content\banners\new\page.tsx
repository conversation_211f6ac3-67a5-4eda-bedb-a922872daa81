'use client';

import { useState, ChangeEvent, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, SubmitHandler } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { FiPlusCircle, FiUpload, FiSave, FiArrowLeft } from 'react-icons/fi';
import Link from 'next/link';
import { api } from '@/utils/api';

interface BannerFormData {
  title: string;
  image: FileList;
  link: string;
  position: string;
  order: number;
  status: 'active' | 'inactive';
  startDate?: string;
  endDate?: string;
}

export default function NewBannerPage() {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<BannerFormData>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleImageChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImagePreview(URL.createObjectURL(file));
      setValue('image', e.target.files);
    }
  };

  const onSubmit: SubmitHandler<BannerFormData> = async (data) => {
    setIsSubmitting(true);
    const formData = new FormData();
    formData.append('title', data.title);
    if (data.image && data.image[0]) {
      formData.append('image', data.image[0]);
    }
    formData.append('link', data.link);
    formData.append('position', data.position);
    formData.append('order', data.order.toString());
    formData.append('status', data.status);
    if (data.startDate) {
      formData.append('startDate', data.startDate);
    }
    if (data.endDate) {
      formData.append('endDate', data.endDate);
    }

    try {
      await api.post('/content/banners', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      toast.success('Banner创建成功！');
      router.push('/content/banners');
    } catch (error) {
      console.error('创建Banner失败:', error);
      toast.error('创建Banner失败，请稍后再试。');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster position="top-center" />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">创建新Banner</h1>
        <Link href="/content/banners">
          <button className="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center">
            <FiArrowLeft className="mr-2" />
            返回列表
          </button>
        </Link>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="bg-white p-8 rounded-xl shadow-xl space-y-6">
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
            Banner标题 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="title"
            {...register('title', { required: 'Banner标题不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.title ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          />
          {errors.title && <p className="mt-1 text-xs text-red-500">{errors.title.message}</p>}
        </div>

        <div>
          <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-1">
            Banner图片 <span className="text-red-500">*</span>
          </label>
          <div className="mt-1 flex items-center space-x-4">
            <span className="inline-block h-24 w-48 rounded-md overflow-hidden bg-gray-100">
              {imagePreview ? (
                <img src={imagePreview} alt="Banner预览" className="h-full w-full object-cover" />
              ) : (
                <div className="h-full w-full flex items-center justify-center text-gray-400">
                  <FiUpload className="h-8 w-8" />
                </div>
              )}
            </span>
            <label
              htmlFor="image-upload"
              className="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out text-sm"
            >
              选择图片
            </label>
            <input
              id="image-upload"
              type="file"
              accept="image/*"
              {...register('image', { required: 'Banner图片不能为空' })}
              className="sr-only"
              onChange={handleImageChange}
            />
          </div>
          {errors.image && <p className="mt-1 text-xs text-red-500">{errors.image.message}</p>}
        </div>

        <div>
          <label htmlFor="link" className="block text-sm font-medium text-gray-700 mb-1">
            链接地址 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="link"
            {...register('link', { required: '链接地址不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.link ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
            placeholder="例如: /services/study-abroad"
          />
          {errors.link && <p className="mt-1 text-xs text-red-500">{errors.link.message}</p>}
        </div>

        <div>
          <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">
            显示位置 <span className="text-red-500">*</span>
          </label>
          <select
            id="position"
            {...register('position', { required: '显示位置不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.position ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          >
            <option value="">选择位置</option>
            <option value="home_top">首页顶部</option>
            <option value="home_middle">首页中部</option>
            <option value="services_page">服务列表页</option>
            <option value="cases_page">案例列表页</option>
            {/* 可以根据实际需求添加更多位置 */}
          </select>
          {errors.position && <p className="mt-1 text-xs text-red-500">{errors.position.message}</p>}
        </div>

        <div>
          <label htmlFor="order" className="block text-sm font-medium text-gray-700 mb-1">
            排序 (数字越小越靠前) <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            id="order"
            {...register('order', { required: '排序不能为空', valueAsNumber: true, min: { value: 0, message: '排序不能小于0'} })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.order ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
            defaultValue={0}
          />
          {errors.order && <p className="mt-1 text-xs text-red-500">{errors.order.message}</p>}
        </div>

        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
            状态 <span className="text-red-500">*</span>
          </label>
          <select
            id="status"
            {...register('status', { required: '状态不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.status ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
            defaultValue="active"
          >
            <option value="active">启用</option>
            <option value="inactive">禁用</option>
          </select>
          {errors.status && <p className="mt-1 text-xs text-red-500">{errors.status.message}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-1">
              开始日期 (可选)
            </label>
            <input
              type="date"
              id="startDate"
              {...register('startDate')}
              className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
          <div>
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-1">
              结束日期 (可选)
            </label>
            <input
              type="date"
              id="endDate"
              {...register('endDate')}
              className="mt-1 block w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            />
          </div>
        </div>

        <div className="flex justify-end pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <FiSave className="mr-2" />
            )}
            {isSubmitting ? '正在保存...' : '保存Banner'}
          </button>
        </div>
      </form>
    </div>
  );
}