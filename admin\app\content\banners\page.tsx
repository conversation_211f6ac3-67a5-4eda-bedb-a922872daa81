'use client';

import { useState } from 'react';
import Link from 'next/link';
import toast, { Toaster } from 'react-hot-toast';
import { api } from '@/utils/api';
import { useEffect } from 'react';

// Banner数据类型
interface Banner {
  id: number;
  title: string;
  image: string;
  link: string;
  position: string;
  order: number;
  status: 'active' | 'inactive';
  startDate: string;
  endDate: string;
}

export default function BannersManagementPage() {
  const [banners, setBanners] = useState<Banner[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchBanners = async () => {
      setLoading(true);
      try {
        const response = await api.get<Banner[]>('/content/banners');
        // Sort banners by order initially
        setBanners(response.data.sort((a, b) => a.order - b.order));
      } catch (error) {
        toast.error('获取Banner列表失败');
        console.error('获取Banner列表失败:', error);
      }
      setLoading(false);
    };
    fetchBanners();
  }, []);
  const [isDeleting, setIsDeleting] = useState(false);
  const [bannerToDelete, setBannerToDelete] = useState<number | null>(null);

  // 处理删除Banner
  const handleDeleteBanner = async (id: number) => {
    setIsDeleting(true);
    setBannerToDelete(id);
    
    try {
      await api.delete(`/content/banners/${id}`);
      setBanners(banners.filter(banner => banner.id !== id));
      toast.success('Banner已成功删除');
    } catch (error) {
      console.error('删除Banner失败:', error);
      toast.error('删除Banner失败，请重试');
    } finally {
      setIsDeleting(false);
      setBannerToDelete(null);
    }
  };

  // 处理更改Banner状态
  const handleToggleStatus = async (id: number, currentStatus: string) => {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    
    try {
      await api.patch(`/content/banners/${id}`, { status: newStatus });
      setBanners(banners.map(banner => 
        banner.id === id ? { ...banner, status: newStatus as 'active' | 'inactive' } : banner
      ));
      toast.success(`Banner状态已更改为${newStatus === 'active' ? '启用' : '禁用'}`);
    } catch (error) {
      console.error('更改Banner状态失败:', error);
      toast.error('更改Banner状态失败，请重试');
    }
  };

  // 处理Banner排序
  const handleMoveOrder = async (id: number, direction: 'up' | 'down') => {
    const currentIndex = banners.findIndex(banner => banner.id === id);
    if (currentIndex === -1) return;
    
    // 如果是向上移动且已经是第一个，或向下移动且已经是最后一个，则不执行操作
    if ((direction === 'up' && currentIndex === 0) || 
        (direction === 'down' && currentIndex === banners.length - 1)) {
      return;
    }
    
    const newBanners = [...banners];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    // 交换位置
    [newBanners[currentIndex], newBanners[targetIndex]] = 
    [newBanners[targetIndex], newBanners[currentIndex]];
    
    // 更新排序值
    newBanners[currentIndex].order = currentIndex + 1;
    newBanners[targetIndex].order = targetIndex + 1;
    
    try {
      // Update order for all affected banners
      const updatedOrderData = newBanners.map(b => ({ id: b.id, order: b.order }));
      await api.patch('/content/banners/reorder', { banners: updatedOrderData });
      
      setBanners(newBanners.sort((a,b) => a.order - b.order)); // Ensure client side is also sorted
      toast.success('Banner排序已更新');
    } catch (error) {
      console.error('更新Banner排序失败:', error);
      toast.error('更新Banner排序失败，请重试');
    }
  };

  // 格式化位置显示
  const formatPosition = (position: string) => {
    const positionMap: {[key: string]: string} = {
      'home_top': '首页顶部',
      'home_middle': '首页中部',
      'home_bottom': '首页底部',
      'services_page': '服务页面',
      'about_page': '关于我们页面',
      'contact_page': '联系我们页面'
    };
    
    return positionMap[position] || position;
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Banner管理</h1>
        <Link 
          href="/content/banners/new" 
          className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
        >
          添加Banner
        </Link>
      </div>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排序</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">预览</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">位置</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">链接</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">有效期</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {banners.map((banner) => (
              <tr key={banner.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div className="flex items-center space-x-2">
                    <span>{banner.order}</span>
                    <div className="flex flex-col">
                      <button 
                        onClick={() => handleMoveOrder(banner.id, 'up')}
                        className="text-gray-500 hover:text-gray-700 focus:outline-none"
                        disabled={banners.indexOf(banner) === 0}
                      >
                        ▲
                      </button>
                      <button 
                        onClick={() => handleMoveOrder(banner.id, 'down')}
                        className="text-gray-500 hover:text-gray-700 focus:outline-none"
                        disabled={banners.indexOf(banner) === banners.length - 1}
                      >
                        ▼
                      </button>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="h-12 w-20 relative bg-gray-200 rounded overflow-hidden">
                    {/* 实际项目中应该使用真实图片 */}
                    <div className="absolute inset-0 flex items-center justify-center text-gray-500 text-xs">
                      Banner预览
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{banner.title}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatPosition(banner.position)}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 max-w-xs truncate">{banner.link}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span 
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${banner.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                  >
                    {banner.status === 'active' ? '启用' : '禁用'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {banner.startDate} 至 {banner.endDate}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleToggleStatus(banner.id, banner.status)}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      {banner.status === 'active' ? '禁用' : '启用'}
                    </button>
                    <Link 
                      href={`/content/banners/edit/${banner.id}`}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      编辑
                    </Link>
                    <button
                      onClick={() => handleDeleteBanner(banner.id)}
                      disabled={isDeleting && bannerToDelete === banner.id}
                      className="text-red-600 hover:text-red-900 disabled:text-gray-400"
                    >
                      {isDeleting && bannerToDelete === banner.id ? '删除中...' : '删除'}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <Toaster position="top-right" />
    </div>
  );
}