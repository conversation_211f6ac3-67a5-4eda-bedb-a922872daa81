'use client';

import { useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, SubmitHandler } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { api } from '@/utils/api';

// 案例表单数据类型
interface CaseFormData {
  title: string;
  slug: string;
  category: string;
  summary: string;
  content: string;
  client: string;
  result: string;
  status: 'published' | 'draft';
  featured: boolean;
}

export default function NewCasePage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [thumbnailPreview, setThumbnailPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // 使用 react-hook-form 管理表单
  const { 
    register, 
    handleSubmit, 
    formState: { errors },
    watch
  } = useForm<CaseFormData>({
    defaultValues: {
      status: 'draft',
      featured: false,
      category: '留学案例',
    }
  });

  // 监听标题变化，自动生成别名
  const watchTitle = watch('title');
  
  // 生成URL友好的别名
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fa5]+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  // 处理缩略图上传
  const handleThumbnailChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 创建预览URL
      const previewUrl = URL.createObjectURL(file);
      setThumbnailPreview(previewUrl);
    }
  };

  // 提交表单
  const onSubmit: SubmitHandler<CaseFormData> = async (data) => {
    setIsSubmitting(true);
    
    try {
      // 如果没有提供别名，根据标题生成
      if (!data.slug) {
        data.slug = generateSlug(data.title);
      }
      
      // 获取文件数据（实际项目中需要处理文件上传）
      const thumbnailFile = fileInputRef.current?.files?.[0];
      
      // 实际实现中应该使用 FormData 处理文件上传
      const formData = new FormData();
      Object.entries(data).forEach(([key, value]) => {
        // FormData expects string or Blob, ensure boolean is converted
        if (typeof value === 'boolean') {
          formData.append(key, String(value));
        } else {
          formData.append(key, value as string); // Cast to string, assuming other types are handled or are strings
        }
      });
      if (thumbnailFile) {
        formData.append('thumbnail', thumbnailFile);
      }
      await api.post('/content/cases', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      toast.success('案例创建成功');
      router.push('/content/cases');
    } catch (error) {
      console.error('创建案例失败:', error);
      toast.error('创建案例失败，请重试');
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">添加新案例</h1>
        <p className="text-gray-600">创建新的成功案例</p>
      </div>
      
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 案例标题 */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
              案例标题 <span className="text-red-500">*</span>
            </label>
            <input
              id="title"
              type="text"
              className={`w-full px-3 py-2 border rounded-md ${errors.title ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="输入案例标题"
              {...register('title', { required: '请输入案例标题' })}
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-500">{errors.title.message}</p>
            )}
          </div>
          
          {/* URL别名 */}
          <div>
            <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-1">
              URL别名
            </label>
            <input
              id="slug"
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder={watchTitle ? generateSlug(watchTitle) : '自动生成或手动输入'}
              {...register('slug')}
            />
            <p className="mt-1 text-sm text-gray-500">留空将根据标题自动生成</p>
          </div>
          
          {/* 案例分类 */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              案例分类 <span className="text-red-500">*</span>
            </label>
            <select
              id="category"
              className={`w-full px-3 py-2 border rounded-md ${errors.category ? 'border-red-500' : 'border-gray-300'}`}
              {...register('category', { required: '请选择案例分类' })}
            >
              <option value="留学案例">留学案例</option>
              <option value="保研案例">保研案例</option>
              <option value="考研案例">考研案例</option>
              <option value="职业转型">职业转型</option>
              <option value="职场晋升">职场晋升</option>
              <option value="其他案例">其他案例</option>
            </select>
            {errors.category && (
              <p className="mt-1 text-sm text-red-500">{errors.category.message}</p>
            )}
          </div>
          
          {/* 缩略图 */}
          <div>
            <label htmlFor="thumbnail" className="block text-sm font-medium text-gray-700 mb-1">
              缩略图 <span className="text-red-500">*</span>
            </label>
            <div className="flex items-center space-x-4">
              <input
                id="thumbnail"
                type="file"
                accept="image/*"
                ref={fileInputRef}
                onChange={handleThumbnailChange}
                className="hidden"
              />
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                选择图片
              </button>
              {thumbnailPreview && (
                <div className="relative h-20 w-32 bg-gray-200 rounded overflow-hidden">
                  <img 
                    src={thumbnailPreview} 
                    alt="缩略图预览" 
                    className="h-full w-full object-cover"
                  />
                </div>
              )}
              {!thumbnailPreview && (
                <div className="h-20 w-32 bg-gray-200 rounded flex items-center justify-center text-gray-500 text-sm">
                  无预览
                </div>
              )}
            </div>
            <p className="mt-1 text-sm text-gray-500">建议尺寸: 800x600px, 最大文件大小: 2MB</p>
          </div>
          
          {/* 案例摘要 */}
          <div>
            <label htmlFor="summary" className="block text-sm font-medium text-gray-700 mb-1">
              案例摘要 <span className="text-red-500">*</span>
            </label>
            <textarea
              id="summary"
              rows={3}
              className={`w-full px-3 py-2 border rounded-md ${errors.summary ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="简要描述案例的主要内容和亮点"
              {...register('summary', { required: '请输入案例摘要' })}
            />
            {errors.summary && (
              <p className="mt-1 text-sm text-red-500">{errors.summary.message}</p>
            )}
          </div>
          
          {/* 客户信息 */}
          <div>
            <label htmlFor="client" className="block text-sm font-medium text-gray-700 mb-1">
              客户信息 <span className="text-red-500">*</span>
            </label>
            <textarea
              id="client"
              rows={3}
              className={`w-full px-3 py-2 border rounded-md ${errors.client ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="描述客户的背景、需求和挑战"
              {...register('client', { required: '请输入客户信息' })}
            />
            {errors.client && (
              <p className="mt-1 text-sm text-red-500">{errors.client.message}</p>
            )}
          </div>
          
          {/* 案例详情 */}
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
              案例详情 <span className="text-red-500">*</span>
            </label>
            <textarea
              id="content"
              rows={10}
              className={`w-full px-3 py-2 border rounded-md ${errors.content ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="详细描述案例的过程、方法和策略"
              {...register('content', { required: '请输入案例详情' })}
            />
            {errors.content && (
              <p className="mt-1 text-sm text-red-500">{errors.content.message}</p>
            )}
            <p className="mt-1 text-sm text-gray-500">支持Markdown格式</p>
          </div>
          
          {/* 案例结果 */}
          <div>
            <label htmlFor="result" className="block text-sm font-medium text-gray-700 mb-1">
              案例结果 <span className="text-red-500">*</span>
            </label>
            <textarea
              id="result"
              rows={3}
              className={`w-full px-3 py-2 border rounded-md ${errors.result ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="描述案例的最终结果和成果"
              {...register('result', { required: '请输入案例结果' })}
            />
            {errors.result && (
              <p className="mt-1 text-sm text-red-500">{errors.result.message}</p>
            )}
          </div>
          
          {/* 状态和精选 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* 状态 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                状态
              </label>
              <div className="flex space-x-4">
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    className="form-radio h-4 w-4 text-primary-600"
                    value="draft"
                    {...register('status')}
                  />
                  <span className="ml-2">草稿</span>
                </label>
                <label className="inline-flex items-center">
                  <input
                    type="radio"
                    className="form-radio h-4 w-4 text-primary-600"
                    value="published"
                    {...register('status')}
                  />
                  <span className="ml-2">发布</span>
                </label>
              </div>
            </div>
            
            {/* 精选案例 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                精选案例
              </label>
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  className="form-checkbox h-4 w-4 text-primary-600"
                  {...register('featured')}
                />
                <span className="ml-2">设为精选案例（将在首页展示）</span>
              </label>
            </div>
          </div>
          
          {/* 提交按钮 */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              onClick={() => router.back()}
              disabled={isSubmitting}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? '保存中...' : '保存'}
            </button>
          </div>
        </form>
      </div>
      <Toaster position="top-right" />
    </div>
  );
}