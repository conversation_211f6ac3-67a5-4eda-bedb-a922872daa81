'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import toast, { Toaster } from 'react-hot-toast';
import api from '../../utils/api';
import { useEffect } from 'react';

// 案例数据类型
interface Case {
  id: number;
  title: string;
  slug: string;
  category: string;
  thumbnail: string;
  status: 'published' | 'draft';
  createdAt: string;
  updatedAt: string;
  featured: boolean;
}

export default function CasesManagementPage() {
  const [cases, setCases] = useState<Case[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCases = async () => {
      setLoading(true);
      try {
        const response = await api.get('/cases');
        // API返回分页格式：{success: true, data: {items: [...], pagination: {...}}}
        if (response.data.success && response.data.data.items) {
          // 映射API字段到管理系统期望的字段
          const mappedCases = response.data.data.items.map((caseItem: any) => ({
            id: caseItem.id,
            title: caseItem.title,
            category: caseItem.category || 'general',
            status: caseItem.status,
            featured: caseItem.is_featured || false,
            updatedAt: caseItem.updated_at
          }));
          setCases(mappedCases);
        } else {
          setCases([]);
        }
      } catch (error) {
        toast.error('获取案例列表失败');
        console.error('获取案例列表失败:', error);
      }
      setLoading(false);
    };
    fetchCases();
  }, []);
  const [isDeleting, setIsDeleting] = useState(false);
  const [caseToDelete, setCaseToDelete] = useState<number | null>(null);

  // 处理删除案例
  const handleDeleteCase = async (id: number) => {
    setIsDeleting(true);
    setCaseToDelete(id);

    try {
      await api.delete(`/cases/${id}`);
      setCases(cases.filter(caseItem => caseItem.id !== id));
      toast.success('案例已成功删除');
    } catch (error) {
      console.error('删除案例失败:', error);
      toast.error('删除案例失败，请重试');
    } finally {
      setIsDeleting(false);
      setCaseToDelete(null);
    }
  };

  // 处理更改案例状态
  const handleToggleStatus = async (id: number, currentStatus: string) => {
    const newStatus = currentStatus === 'published' ? 'draft' : 'published';

    try {
      await api.patch(`/cases/${id}`, { status: newStatus });
      setCases(cases.map(caseItem =>
        caseItem.id === id ? { ...caseItem, status: newStatus as 'published' | 'draft' } : caseItem
      ));
      toast.success(`案例状态已更改为${newStatus === 'published' ? '已发布' : '草稿'}`);
    } catch (error) {
      console.error('更改案例状态失败:', error);
      toast.error('更改案例状态失败，请重试');
    }
  };

  // 处理更改精选状态
  const handleToggleFeatured = async (id: number, currentFeatured: boolean) => {
    try {
      await api.patch(`/cases/${id}`, { featured: !currentFeatured });
      setCases(cases.map(caseItem =>
        caseItem.id === id ? { ...caseItem, featured: !currentFeatured } : caseItem
      ));
      toast.success(`案例已${!currentFeatured ? '设为' : '取消'}精选`);
    } catch (error) {
      console.error('更改精选状态失败:', error);
      toast.error('更改精选状态失败，请重试');
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">案例管理</h1>
        <Link
          href="/content/cases/new"
          className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
        >
          添加案例
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">缩略图</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">精选</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新日期</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {cases.map((caseItem) => (
              <tr key={caseItem.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{caseItem.id}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="h-12 w-16 relative bg-gray-200 rounded overflow-hidden">
                    {/* 实际项目中应该使用真实图片 */}
                    <div className="absolute inset-0 flex items-center justify-center text-gray-500 text-xs">
                      缩略图
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{caseItem.title}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{caseItem.category}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${caseItem.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}
                  >
                    {caseItem.status === 'published' ? '已发布' : '草稿'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${caseItem.featured ? 'bg-purple-100 text-purple-800' : 'bg-gray-100 text-gray-800'}`}
                  >
                    {caseItem.featured ? '是' : '否'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{caseItem.updatedAt}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleToggleStatus(caseItem.id, caseItem.status)}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      {caseItem.status === 'published' ? '设为草稿' : '发布'}
                    </button>
                    <button
                      onClick={() => handleToggleFeatured(caseItem.id, caseItem.featured)}
                      className="text-purple-600 hover:text-purple-900"
                    >
                      {caseItem.featured ? '取消精选' : '设为精选'}
                    </button>
                    <Link
                      href={`/content/cases/edit/${caseItem.id}`}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      编辑
                    </Link>
                    <button
                      onClick={() => handleDeleteCase(caseItem.id)}
                      disabled={isDeleting && caseToDelete === caseItem.id}
                      className="text-red-600 hover:text-red-900 disabled:text-gray-400"
                    >
                      {isDeleting && caseToDelete === caseItem.id ? '删除中...' : '删除'}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <Toaster position="top-right" />
    </div>
  );
}