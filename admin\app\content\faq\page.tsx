'use client';

import { useState } from 'react';
import Link from 'next/link';
import toast, { Toaster } from 'react-hot-toast';
import api from '../../../utils/api';

// FAQ数据类型
interface FAQ {
  id: number;
  question: string;
  answer: string;
  category: string;
  order: number;
  status: 'published' | 'draft';
  createdAt: string;
  updatedAt: string;
}

// 模拟FAQ数据
const MOCK_FAQS: FAQ[] = [
  {
    id: 1,
    question: '申请留学需要准备哪些材料？',
    answer: '申请留学通常需要准备以下材料：个人陈述、推荐信、成绩单、语言成绩证明（如托福、雅思）、简历、作品集（视专业而定）等。具体要求因学校和专业而异，建议提前查询目标院校的官方要求。',
    category: '留学申请',
    order: 1,
    status: 'published',
    createdAt: '2023-10-15',
    updatedAt: '2023-11-01',
  },
  {
    id: 2,
    question: '保研和考研有什么区别？',
    answer: '保研是免试推荐攻读研究生的方式，通常需要本科期间成绩优异，且获得学校的推荐资格。考研则是通过全国统一的研究生入学考试，参加初试和复试后被录取。保研免去了考试环节，但名额有限且竞争激烈；考研则机会更加公平，但需要付出更多备考努力。',
    category: '考研保研',
    order: 1,
    status: 'published',
    createdAt: '2023-10-16',
    updatedAt: '2023-10-30',
  },
  {
    id: 3,
    question: '如何确定适合自己的职业方向？',
    answer: '确定适合自己的职业方向可以从以下几个方面考虑：1）自我评估：了解自己的兴趣、能力、价值观和性格特点；2）市场调研：了解不同行业和职位的发展前景、薪资水平和工作内容；3）尝试实习或项目：通过实际体验来验证自己的兴趣和适应性；4）寻求专业指导：可以咨询职业规划师或行业内的专业人士。',
    category: '职业规划',
    order: 1,
    status: 'published',
    createdAt: '2023-10-17',
    updatedAt: '2023-10-29',
  },
  {
    id: 4,
    question: '职业转型需要具备哪些条件？',
    answer: '成功的职业转型通常需要：1）明确的目标和规划；2）相关的技能储备或学习能力；3）对新领域的了解和研究；4）良好的人际网络；5）足够的财务准备以应对过渡期；6）积极的心态和抗压能力。根据个人情况不同，可能还需要额外的教育背景或证书。',
    category: '职业转型',
    order: 1,
    status: 'published',
    createdAt: '2023-10-18',
    updatedAt: '2023-10-28',
  },
  {
    id: 5,
    question: '贵公司的服务收费标准是怎样的？',
    answer: '我们的服务收费根据不同的服务类型和客户需求而定，没有统一的标准价格。我们会根据您的具体情况和需求提供定制化的服务方案和相应的报价。您可以通过预约咨询，与我们的顾问详细沟通，获取适合您的服务方案和价格信息。',
    category: '服务咨询',
    order: 1,
    status: 'published',
    createdAt: '2023-10-19',
    updatedAt: '2023-10-27',
  },
];

export default function FAQManagementPage() {
  const [faqs, setFaqs] = useState<FAQ[]>(MOCK_FAQS);
  const [isDeleting, setIsDeleting] = useState(false);
  const [faqToDelete, setFaqToDelete] = useState<number | null>(null);
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  // 处理删除FAQ
  const handleDeleteFaq = async (id: number) => {
    setIsDeleting(true);
    setFaqToDelete(id);
    
    try {
      // 模拟API调用
      // 实际实现中应该使用 await api.delete(`/faqs/${id}`);
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 更新本地状态
      setFaqs(faqs.filter(faq => faq.id !== id));
      toast.success('FAQ已成功删除');
    } catch (error) {
      console.error('删除FAQ失败:', error);
      toast.error('删除FAQ失败，请重试');
    } finally {
      setIsDeleting(false);
      setFaqToDelete(null);
    }
  };

  // 处理更改FAQ状态
  const handleToggleStatus = async (id: number, currentStatus: string) => {
    const newStatus = currentStatus === 'published' ? 'draft' : 'published';
    
    try {
      // 模拟API调用
      // 实际实现中应该使用 await api.patch(`/faqs/${id}`, { status: newStatus });
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // 更新本地状态
      setFaqs(faqs.map(faq => 
        faq.id === id ? { ...faq, status: newStatus as 'published' | 'draft' } : faq
      ));
      
      toast.success(`FAQ状态已更改为${newStatus === 'published' ? '已发布' : '草稿'}`);
    } catch (error) {
      console.error('更改FAQ状态失败:', error);
      toast.error('更改FAQ状态失败，请重试');
    }
  };

  // 切换展开/折叠FAQ答案
  const toggleExpandFaq = (id: number) => {
    setExpandedFaq(expandedFaq === id ? null : id);
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">FAQ管理</h1>
        <Link 
          href="/content/faq/new" 
          className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
        >
          添加FAQ
        </Link>
      </div>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">问题</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新日期</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {faqs.map((faq) => (
              <>
                <tr key={faq.id} className="hover:bg-gray-50 cursor-pointer" onClick={() => toggleExpandFaq(faq.id)}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{faq.id}</td>
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    <div className="flex items-center">
                      <span className="mr-2">{expandedFaq === faq.id ? '▼' : '▶'}</span>
                      {faq.question}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{faq.category}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span 
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${faq.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}
                    >
                      {faq.status === 'published' ? '已发布' : '草稿'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{faq.updatedAt}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
                      <button
                        onClick={() => handleToggleStatus(faq.id, faq.status)}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        {faq.status === 'published' ? '设为草稿' : '发布'}
                      </button>
                      <Link 
                        href={`/content/faq/edit/${faq.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        编辑
                      </Link>
                      <button
                        onClick={() => handleDeleteFaq(faq.id)}
                        disabled={isDeleting && faqToDelete === faq.id}
                        className="text-red-600 hover:text-red-900 disabled:text-gray-400"
                      >
                        {isDeleting && faqToDelete === faq.id ? '删除中...' : '删除'}
                      </button>
                    </div>
                  </td>
                </tr>
                {expandedFaq === faq.id && (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 bg-gray-50">
                      <div className="text-sm text-gray-700 whitespace-pre-wrap">
                        <p className="font-semibold mb-2">答案：</p>
                        {faq.answer}
                      </div>
                    </td>
                  </tr>
                )}
              </>
            ))}
          </tbody>
        </table>
      </div>
      <Toaster position="top-right" />
    </div>
  );
}