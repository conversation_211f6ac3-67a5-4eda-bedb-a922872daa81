'use client';

import { useState, useEffect, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, SubmitHandler } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { FiPlusCircle, FiSave, FiArrowLeft } from 'react-icons/fi';
import Link from 'next/link';
import { api } from '@/utils/api';

interface FAQFormData {
  question: string;
  answer: string;
  categoryId: number | null; // Changed from category to categoryId
  status: 'published' | 'draft';
}

interface Category {
  id: number;
  name: string;
}

export default function NewFAQPage() {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<FAQFormData>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await api.get<Category[]>('/content/faqs/categories');
        setCategories(response.data);
      } catch (error) {
        toast.error('获取分类列表失败');
        console.error('获取分类列表失败:', error);
      }
    };
    fetchCategories();
  }, []);

  const onSubmit: SubmitHandler<FAQFormData> = async (data) => {
    setIsSubmitting(true);
    
    const payload = {
        ...data,
        categoryId: data.categoryId ? Number(data.categoryId) : null
    };

    try {
      await api.post('/content/faqs', payload);
      toast.success('FAQ创建成功！');
      router.push('/content/faqs');
    } catch (error) {
      console.error('创建FAQ失败:', error);
      toast.error('创建FAQ失败，请稍后再试。');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster position="top-center" />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">创建新FAQ</h1>
        <Link href="/content/faqs">
          <button className="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center">
            <FiArrowLeft className="mr-2" />
            返回列表
          </button>
        </Link>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="bg-white p-8 rounded-xl shadow-xl space-y-6">
        <div>
          <label htmlFor="question" className="block text-sm font-medium text-gray-700 mb-1">
            问题 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="question"
            {...register('question', { required: '问题不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.question ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          />
          {errors.question && <p className="mt-1 text-xs text-red-500">{errors.question.message}</p>}
        </div>

        <div>
          <label htmlFor="answer" className="block text-sm font-medium text-gray-700 mb-1">
            答案 <span className="text-red-500">*</span>
          </label>
          <textarea
            id="answer"
            rows={6}
            {...register('answer', { required: '答案不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.answer ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          />
          {errors.answer && <p className="mt-1 text-xs text-red-500">{errors.answer.message}</p>}
        </div>

        <div>
          <label htmlFor="categoryId" className="block text-sm font-medium text-gray-700 mb-1">
            分类
          </label>
          <select
            id="categoryId"
            {...register('categoryId')}
            className={`mt-1 block w-full px-4 py-2 border ${errors.categoryId ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
            defaultValue=""
          >
            <option value="">选择分类 (可选)</option>
            {categories.map(cat => (
              <option key={cat.id} value={cat.id}>{cat.name}</option>
            ))}
          </select>
          {errors.categoryId && <p className="mt-1 text-xs text-red-500">{errors.categoryId.message}</p>}
        </div>

        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
            状态 <span className="text-red-500">*</span>
          </label>
          <select
            id="status"
            {...register('status', { required: '状态不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.status ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
            defaultValue="draft"
          >
            <option value="draft">草稿</option>
            <option value="published">已发布</option>
          </select>
          {errors.status && <p className="mt-1 text-xs text-red-500">{errors.status.message}</p>}
        </div>

        <div className="flex justify-end pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <FiSave className="mr-2" />
            )}
            {isSubmitting ? '正在创建...' : '创建FAQ'}
          </button>
        </div>
      </form>
    </div>
  );
}