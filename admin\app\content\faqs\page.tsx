'use client';

import { useState, useEffect, FormEvent } from 'react';
import Link from 'next/link';
import toast, { Toaster } from 'react-hot-toast';
import { FiPlusCircle, FiEdit, FiTrash2, FiChevronDown, FiChevronUp, FiFilter, FiSearch, FiSave, FiXCircle } from 'react-icons/fi';
import { api } from '@/utils/api';

interface FAQ {
  id: number;
  question: string;
  answer: string;
  category: string;
  status: 'published' | 'draft';
  createdAt: string;
  updatedAt: string;
}

interface Category {
  id: number;
  name: string;
}

export default function FAQsManagementPage() {
  const [faqs, setFaqs] = useState<FAQ[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');
  const [editingFaq, setEditingFaq] = useState<FAQ | null>(null);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);

  const fetchFaqs = async () => {
    setLoading(true);
    try {
      const response = await api.get<FAQ[]>('/faqs', {
        params: {
          search: searchTerm,
          category: filterCategory,
        },
      });
      setFaqs(response.data);
    } catch (error) {
      toast.error('获取FAQ列表失败');
      console.error('获取FAQ列表失败:', error);
    }
    setLoading(false);
  };

  const fetchCategories = async () => {
    try {
      const response = await api.get<Category[]>('/content/faqs/categories');
      setCategories(response.data);
    } catch (error) {
      toast.error('获取分类列表失败');
      console.error('获取分类列表失败:', error);
    }
  };

  useEffect(() => {
    fetchFaqs();
    fetchCategories();
  }, []);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      fetchFaqs();
    }, 300);
    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm, filterCategory]);

  const handleDeleteFaq = async (id: number) => {
    if (window.confirm('确定要删除此FAQ吗？')) {
      try {
        await api.delete(`/faqs/${id}`);
        setFaqs(faqs.filter(faq => faq.id !== id));
        toast.success('FAQ删除成功');
      } catch (error) {
        toast.error('删除FAQ失败');
        console.error('删除FAQ失败:', error);
      }
    }
  };

  const handleEditFaq = (faq: FAQ) => {
    setEditingFaq(faq);
  };

  const handleUpdateFaq = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!editingFaq) return;

    try {
      const response = await api.put<FAQ>(`/faqs/${editingFaq.id}`, editingFaq);
      setFaqs(faqs.map(faq => (faq.id === editingFaq.id ? response.data : faq)));
      toast.success('FAQ更新成功');
      setEditingFaq(null);
    } catch (error) {
      toast.error('更新FAQ失败');
      console.error('更新FAQ失败:', error);
    }
  };

  const handleAddCategory = async () => {
    if (!newCategoryName.trim()) {
      toast.error('分类名称不能为空');
      return;
    }
    try {
      const response = await api.post<Category>('/content/faqs/categories', { name: newCategoryName });
      setCategories([...categories, response.data]);
      setNewCategoryName('');
      toast.success('分类添加成功');
    } catch (error) {
      toast.error('添加分类失败');
      console.error('添加分类失败:', error);
    }
  };

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category);
    setNewCategoryName(category.name); // Pre-fill input for editing
  };

  const handleUpdateCategory = async () => {
    if (!editingCategory || !newCategoryName.trim()) {
      toast.error('分类名称不能为空');
      return;
    }
    try {
      const response = await api.put<Category>(`/content/faqs/categories/${editingCategory.id}`, { name: newCategoryName });
      setCategories(categories.map(cat => cat.id === editingCategory.id ? response.data : cat));
      setNewCategoryName('');
      setEditingCategory(null);
      toast.success('分类更新成功');
    } catch (error) {
      toast.error('更新分类失败');
      console.error('更新分类失败:', error);
    }
  };

  const handleDeleteCategory = async (id: number) => {
    if (window.confirm('确定要删除此分类吗？删除分类将导致该分类下的FAQ变为未分类。')) {
      try {
        await api.delete(`/content/faqs/categories/${id}`);
        setCategories(categories.filter(cat => cat.id !== id));
        fetchFaqs(); // Refresh FAQs as their category might have changed
        toast.success('分类删除成功');
      } catch (error) {
        toast.error('删除分类失败');
        console.error('删除分类失败:', error);
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster position="top-center" />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">FAQ管理</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowCategoryModal(true)}
            className="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center"
          >
            <FiFilter className="mr-2" /> 管理分类
          </button>
          <Link href="/content/faqs/new">
            <button className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center">
              <FiPlusCircle className="mr-2" /> 添加新FAQ
            </button>
          </Link>
        </div>
      </div>

      {/* Filters */}
      <div className="mb-6 p-4 bg-white rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700">搜索问题</label>
            <div className="mt-1 relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                id="search"
                className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-2"
                placeholder="输入关键词..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div>
            <label htmlFor="categoryFilter" className="block text-sm font-medium text-gray-700">按分类筛选</label>
            <select
              id="categoryFilter"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
            >
              <option value="">所有分类</option>
              {categories.map(cat => (
                <option key={cat.id} value={cat.name}>{cat.name}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-10">
          <p className="text-lg text-gray-500">正在加载FAQ...</p>
        </div>
      ) : faqs.length === 0 ? (
         <div className="text-center py-10 bg-white rounded-lg shadow">
          <FiXCircle className="mx-auto text-gray-400 text-5xl mb-4" />
          <p className="text-lg text-gray-500">未找到FAQ。</p>
          <p className="text-sm text-gray-400">尝试调整搜索词或筛选条件，或<Link href="/content/faqs/new" className="text-indigo-600 hover:underline">添加新的FAQ</Link>。</p>
        </div>
      ) : (
        <div className="bg-white shadow-xl rounded-lg overflow-hidden">
          <ul className="divide-y divide-gray-200">
            {faqs.map((faq) => (
              <li key={faq.id} className="p-4 hover:bg-gray-50 transition duration-150">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-semibold text-indigo-700">{faq.question}</h3>
                    <p className="mt-1 text-sm text-gray-600 whitespace-pre-wrap">{faq.answer}</p>
                    <div className="mt-2 text-xs text-gray-500">
                      <span>分类: {faq.category || '未分类'}</span> |
                      <span>状态: <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${faq.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>{faq.status === 'published' ? '已发布' : '草稿'}</span></span> |
                      <span>最后更新: {new Date(faq.updatedAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="flex-shrink-0 flex space-x-2 ml-4">
                    <button
                      onClick={() => handleEditFaq(faq)}
                      className="text-blue-600 hover:text-blue-800 transition duration-150 p-1 rounded-full hover:bg-blue-100"
                      title="编辑"
                    >
                      <FiEdit size={18} />
                    </button>
                    <button
                      onClick={() => handleDeleteFaq(faq.id)}
                      className="text-red-600 hover:text-red-800 transition duration-150 p-1 rounded-full hover:bg-red-100"
                      title="删除"
                    >
                      <FiTrash2 size={18} />
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Edit FAQ Modal */}
      {editingFaq && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="bg-white p-8 rounded-lg shadow-xl w-full max-w-2xl transform transition-all">
            <h2 className="text-2xl font-bold mb-6 text-gray-800">编辑FAQ</h2>
            <form onSubmit={handleUpdateFaq} className="space-y-4">
              <div>
                <label htmlFor="editQuestion" className="block text-sm font-medium text-gray-700">问题</label>
                <input
                  type="text"
                  id="editQuestion"
                  value={editingFaq.question}
                  onChange={(e) => setEditingFaq({ ...editingFaq, question: e.target.value })}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  required
                />
              </div>
              <div>
                <label htmlFor="editAnswer" className="block text-sm font-medium text-gray-700">答案</label>
                <textarea
                  id="editAnswer"
                  rows={5}
                  value={editingFaq.answer}
                  onChange={(e) => setEditingFaq({ ...editingFaq, answer: e.target.value })}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  required
                />
              </div>
              <div>
                <label htmlFor="editCategory" className="block text-sm font-medium text-gray-700">分类</label>
                <select
                  id="editCategory"
                  value={editingFaq.category}
                  onChange={(e) => setEditingFaq({ ...editingFaq, category: e.target.value })}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                >
                  <option value="">选择分类</option>
                  {categories.map(cat => (
                    <option key={cat.id} value={cat.name}>{cat.name}</option>
                  ))}
                </select>
              </div>
              <div>
                <label htmlFor="editStatus" className="block text-sm font-medium text-gray-700">状态</label>
                <select
                  id="editStatus"
                  value={editingFaq.status}
                  onChange={(e) => setEditingFaq({ ...editingFaq, status: e.target.value as 'published' | 'draft' })}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                >
                  <option value="published">已发布</option>
                  <option value="draft">草稿</option>
                </select>
              </div>
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setEditingFaq(null)}
                  className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center"
                >
                  <FiSave className="mr-2" /> 保存更改
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Category Management Modal */}
      {showCategoryModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="bg-white p-8 rounded-lg shadow-xl w-full max-w-lg transform transition-all">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-gray-800">管理分类</h2>
                <button onClick={() => { setShowCategoryModal(false); setNewCategoryName(''); setEditingCategory(null); }} className="text-gray-500 hover:text-gray-700">
                    <FiXCircle size={24} />
                </button>
            </div>

            <div className="mb-6">
              <label htmlFor="newCategoryName" className="block text-sm font-medium text-gray-700">
                {editingCategory ? '编辑分类名称' : '添加新分类'}
              </label>
              <div className="mt-1 flex rounded-md shadow-sm">
                <input
                  type="text"
                  id="newCategoryName"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  className="focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-l-md sm:text-sm border-gray-300 px-3 py-2"
                  placeholder="例如：留学申请"
                />
                <button
                  type="button"
                  onClick={editingCategory ? handleUpdateCategory : handleAddCategory}
                  className={`${editingCategory ? 'bg-green-500 hover:bg-green-600' : 'bg-blue-500 hover:bg-blue-600'} inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-r-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150`}
                >
                  <FiSave className="mr-2" /> {editingCategory ? '更新' : '添加'}
                </button>
              </div>
              {editingCategory && (
                <button
                    type="button"
                    onClick={() => {setEditingCategory(null); setNewCategoryName('');}}
                    className="mt-2 text-sm text-gray-600 hover:text-gray-800"
                >
                    取消编辑
                </button>
              )}
            </div>

            <h3 className="text-lg font-medium text-gray-900 mb-2">现有分类</h3>
            {categories.length === 0 ? (
              <p className="text-sm text-gray-500">暂无分类。</p>
            ) : (
              <ul className="divide-y divide-gray-200 max-h-60 overflow-y-auto border rounded-md">
                {categories.map(cat => (
                  <li key={cat.id} className="px-4 py-3 flex justify-between items-center hover:bg-gray-50">
                    <span className="text-sm text-gray-800">{cat.name}</span>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEditCategory(cat)}
                        className="text-blue-600 hover:text-blue-800 p-1 rounded-full hover:bg-blue-100"
                        title="编辑分类"
                      >
                        <FiEdit size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteCategory(cat.id)}
                        className="text-red-600 hover:text-red-800 p-1 rounded-full hover:bg-red-100"
                        title="删除分类"
                      >
                        <FiTrash2 size={16} />
                      </button>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      )}
    </div>
  );
}