'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, SubmitHandler } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { api } from '@/utils/api';

// 服务表单数据类型
interface ServiceFormData {
  title: string;
  slug: string;
  category: string;
  description: string;
  content: string;
  status: 'published' | 'draft';
  icon?: string;
  order?: number;
}

export default function NewServicePage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // 使用 react-hook-form 管理表单
  const { 
    register, 
    handleSubmit, 
    formState: { errors },
    watch
  } = useForm<ServiceFormData>({
    defaultValues: {
      status: 'draft',
      category: '学业规划',
    }
  });

  // 监听标题变化，自动生成别名
  const watchTitle = watch('title');
  
  // 生成URL友好的别名
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^\w\u4e00-\u9fa5]+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  // 提交表单
  const onSubmit: SubmitHandler<ServiceFormData> = async (data) => {
    setIsSubmitting(true);
    
    try {
      // 如果没有提供别名，根据标题生成
      if (!data.slug) {
        data.slug = generateSlug(data.title);
      }
      
      await api.post('/content/services', data);
      toast.success('服务创建成功');
      router.push('/content/services');
    } catch (error) {
      console.error('创建服务失败:', error);
      toast.error('创建服务失败，请重试');
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">添加新服务</h1>
        <p className="text-gray-600">创建新的服务项目</p>
      </div>
      
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 服务标题 */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
              服务标题 <span className="text-red-500">*</span>
            </label>
            <input
              id="title"
              type="text"
              className={`w-full px-3 py-2 border rounded-md ${errors.title ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="输入服务标题"
              {...register('title', { required: '请输入服务标题' })}
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-500">{errors.title.message}</p>
            )}
          </div>
          
          {/* URL别名 */}
          <div>
            <label htmlFor="slug" className="block text-sm font-medium text-gray-700 mb-1">
              URL别名
            </label>
            <input
              id="slug"
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder={watchTitle ? generateSlug(watchTitle) : '自动生成或手动输入'}
              {...register('slug')}
            />
            <p className="mt-1 text-sm text-gray-500">留空将根据标题自动生成</p>
          </div>
          
          {/* 服务分类 */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              服务分类 <span className="text-red-500">*</span>
            </label>
            <select
              id="category"
              className={`w-full px-3 py-2 border rounded-md ${errors.category ? 'border-red-500' : 'border-gray-300'}`}
              {...register('category', { required: '请选择服务分类' })}
            >
              <option value="学业规划">学业规划</option>
              <option value="职业发展">职业发展</option>
              <option value="留学服务">留学服务</option>
              <option value="其他服务">其他服务</option>
            </select>
            {errors.category && (
              <p className="mt-1 text-sm text-red-500">{errors.category.message}</p>
            )}
          </div>
          
          {/* 服务简介 */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              服务简介 <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              rows={3}
              className={`w-full px-3 py-2 border rounded-md ${errors.description ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="简要描述服务内容和特点"
              {...register('description', { required: '请输入服务简介' })}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-500">{errors.description.message}</p>
            )}
          </div>
          
          {/* 服务详情 */}
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
              服务详情 <span className="text-red-500">*</span>
            </label>
            <textarea
              id="content"
              rows={10}
              className={`w-full px-3 py-2 border rounded-md ${errors.content ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="详细描述服务内容、流程、特色等"
              {...register('content', { required: '请输入服务详情' })}
            />
            {errors.content && (
              <p className="mt-1 text-sm text-red-500">{errors.content.message}</p>
            )}
            <p className="mt-1 text-sm text-gray-500">支持Markdown格式</p>
          </div>
          
          {/* 图标 */}
          <div>
            <label htmlFor="icon" className="block text-sm font-medium text-gray-700 mb-1">
              服务图标
            </label>
            <input
              id="icon"
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="输入图标名称或URL"
              {...register('icon')}
            />
            <p className="mt-1 text-sm text-gray-500">可以是图标名称或图片URL</p>
          </div>
          
          {/* 排序 */}
          <div>
            <label htmlFor="order" className="block text-sm font-medium text-gray-700 mb-1">
              排序
            </label>
            <input
              id="order"
              type="number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md"
              placeholder="数字越小排序越靠前"
              {...register('order', { valueAsNumber: true })}
            />
          </div>
          
          {/* 状态 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              状态
            </label>
            <div className="flex space-x-4">
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio h-4 w-4 text-primary-600"
                  value="draft"
                  {...register('status')}
                />
                <span className="ml-2">草稿</span>
              </label>
              <label className="inline-flex items-center">
                <input
                  type="radio"
                  className="form-radio h-4 w-4 text-primary-600"
                  value="published"
                  {...register('status')}
                />
                <span className="ml-2">发布</span>
              </label>
            </div>
          </div>
          
          {/* 提交按钮 */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              onClick={() => router.back()}
              disabled={isSubmitting}
            >
              取消
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? '保存中...' : '保存'}
            </button>
          </div>
        </form>
      </div>
      <Toaster position="top-right" />
    </div>
  );
}