'use client';

import { useState } from 'react';
import Link from 'next/link';
import toast, { Toaster } from 'react-hot-toast';
import { api } from '@/utils/api';
import { useEffect } from 'react';

interface Service {
  id: number;
  title: string;
  slug: string;
  category: string;
  status: 'published' | 'draft';
  createdAt: string;
  updatedAt: string;
}

export default function ServicesManagementPage() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchServices = async () => {
      setLoading(true);
      try {
        const response = await api.get('/services');
        // API返回分页格式：{success: true, data: {items: [...], pagination: {...}}}
        if (response.data.success && response.data.data.items) {
          // 映射API字段到管理系统期望的字段
          const mappedServices = response.data.data.items.map((service: any) => ({
            id: service.id,
            title: service.title,
            slug: service.slug,
            category: service.category || 'general',
            status: service.status,
            createdAt: service.created_at,
            updatedAt: service.updated_at
          }));
          setServices(mappedServices);
        } else {
          setServices([]);
        }
      } catch (error) {
        toast.error('获取服务列表失败');
        console.error('获取服务列表失败:', error);
      }
      setLoading(false);
    };
    fetchServices();
  }, []);
  const [isDeleting, setIsDeleting] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<number | null>(null);

  // 处理删除服务
  const handleDeleteService = async (id: number) => {
    setIsDeleting(true);
    setServiceToDelete(id);

    try {
      await api.delete(`/services/${id}`);
      setServices(services.filter(service => service.id !== id));
      toast.success('服务已成功删除');
    } catch (error) {
      console.error('删除服务失败:', error);
      toast.error('删除服务失败，请重试');
    } finally {
      setIsDeleting(false);
      setServiceToDelete(null);
    }
  };

  // 处理更改服务状态
  const handleToggleStatus = async (id: number, currentStatus: string) => {
    const newStatus = currentStatus === 'published' ? 'draft' : 'published';

    try {
      await api.patch(`/services/${id}`, { status: newStatus });
      setServices(services.map(service =>
        service.id === id ? { ...service, status: newStatus } : service
      ));
      toast.success(`服务状态已更改为${newStatus === 'published' ? '已发布' : '草稿'}`);
    } catch (error) {
      console.error('更改服务状态失败:', error);
      toast.error('更改服务状态失败，请重试');
    }
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">服务管理</h1>
        <Link
          href="/content/services/new"
          className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
        >
          添加服务
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">别名</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建日期</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新日期</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {services.map((service) => (
              <tr key={service.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{service.id}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{service.title}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{service.slug}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{service.category}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${service.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}
                  >
                    {service.status === 'published' ? '已发布' : '草稿'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{service.createdAt}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{service.updatedAt}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleToggleStatus(service.id, service.status)}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      {service.status === 'published' ? '设为草稿' : '发布'}
                    </button>
                    <Link
                      href={`/content/services/edit/${service.id}`}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      编辑
                    </Link>
                    <button
                      onClick={() => handleDeleteService(service.id)}
                      disabled={isDeleting && serviceToDelete === service.id}
                      className="text-red-600 hover:text-red-900 disabled:text-gray-400"
                    >
                      {isDeleting && serviceToDelete === service.id ? '删除中...' : '删除'}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <Toaster position="top-right" />
    </div>
  );
}