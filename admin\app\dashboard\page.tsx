'use client';

import { useState, useEffect } from 'react';
import { FiUsers, FiFileText, FiMessageSquare, FiEye, FiTrendingUp, FiCalendar } from 'react-icons/fi';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

// 统计卡片组件
interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  change?: string;
  isPositive?: boolean;
}

const StatCard = ({ title, value, icon, change, isPositive }: StatCardProps) => (
  <div className="bg-white rounded-lg shadow p-6">
    <div className="flex justify-between items-start">
      <div>
        <p className="text-gray-500 text-sm">{title}</p>
        <h3 className="text-2xl font-bold mt-1">{value}</h3>
        {change && (
          <p className={`text-sm mt-2 flex items-center ${isPositive ? 'text-green-500' : 'text-red-500'}`}>
            <FiTrendingUp className={`mr-1 ${isPositive ? '' : 'transform rotate-180'}`} />
            {change} {isPositive ? '增长' : '下降'}
          </p>
        )}
      </div>
      <div className="p-3 bg-primary-50 rounded-full">
        {icon}
      </div>
    </div>
  </div>
);

// 模拟数据
const visitData = [
  { name: '1月', 访问量: 4000 },
  { name: '2月', 访问量: 3000 },
  { name: '3月', 访问量: 2000 },
  { name: '4月', 访问量: 2780 },
  { name: '5月', 访问量: 1890 },
  { name: '6月', 访问量: 2390 },
  { name: '7月', 访问量: 3490 },
  { name: '8月', 访问量: 4200 },
  { name: '9月', 访问量: 5000 },
  { name: '10月', 访问量: 4300 },
  { name: '11月', 访问量: 4800 },
  { name: '12月', 访问量: 5200 },
];

const inquiryData = [
  { name: '1月', 咨询量: 400 },
  { name: '2月', 咨询量: 300 },
  { name: '3月', 咨询量: 200 },
  { name: '4月', 咨询量: 278 },
  { name: '5月', 咨询量: 189 },
  { name: '6月', 咨询量: 239 },
  { name: '7月', 咨询量: 349 },
  { name: '8月', 咨询量: 420 },
  { name: '9月', 咨询量: 500 },
  { name: '10月', 咨询量: 430 },
  { name: '11月', 咨询量: 480 },
  { name: '12月', 咨询量: 520 },
];

const serviceData = [
  { name: '留学申请', value: 40 },
  { name: '考研保研', value: 30 },
  { name: '职业规划', value: 20 },
  { name: '职业转型', value: 10 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const recentInquiries = [
  { id: 1, name: '张先生', service: '留学申请', date: '2023-12-01', status: '待回复' },
  { id: 2, name: '李女士', service: '职业规划', date: '2023-11-30', status: '已回复' },
  { id: 3, name: '王同学', service: '考研保研', date: '2023-11-29', status: '已回复' },
  { id: 4, name: '赵女士', service: '职业转型', date: '2023-11-28', status: '已回复' },
  { id: 5, name: '刘同学', service: '留学申请', date: '2023-11-27', status: '待回复' },
];

export default function DashboardPage() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">仪表盘</h1>
        <p className="text-gray-600">欢迎回来，查看网站的最新数据和统计信息</p>
      </div>
      
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard 
          title="总访问量" 
          value="24,532" 
          icon={<FiEye className="text-primary-600 text-xl" />} 
          change="12%"
          isPositive={true}
        />
        <StatCard 
          title="咨询数量" 
          value="1,423" 
          icon={<FiMessageSquare className="text-primary-600 text-xl" />} 
          change="8%"
          isPositive={true}
        />
        <StatCard 
          title="文章数量" 
          value="48" 
          icon={<FiFileText className="text-primary-600 text-xl" />} 
          change="5%"
          isPositive={true}
        />
        <StatCard 
          title="注册用户" 
          value="256" 
          icon={<FiUsers className="text-primary-600 text-xl" />} 
          change="15%"
          isPositive={true}
        />
      </div>
      
      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* 访问量趋势图 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium mb-4">访问量趋势</h2>
          {isClient && (
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={visitData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="访问量" stroke="#8884d8" activeDot={{ r: 8 }} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
        
        {/* 咨询量趋势图 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium mb-4">咨询量趋势</h2>
          {isClient && (
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={inquiryData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="咨询量" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
      </div>
      
      {/* 下半部分内容 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 服务分布饼图 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium mb-4">服务咨询分布</h2>
          {isClient && (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={serviceData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {serviceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          )}
        </div>
        
        {/* 最近咨询列表 */}
        <div className="bg-white rounded-lg shadow p-6 lg:col-span-2">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium">最近咨询</h2>
            <button className="text-primary-600 text-sm hover:underline">查看全部</button>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    咨询人
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    服务类型
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    日期
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    状态
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentInquiries.map((inquiry) => (
                  <tr key={inquiry.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {inquiry.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {inquiry.service}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <FiCalendar className="mr-2 text-gray-400" />
                        {inquiry.date}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${inquiry.status === '待回复' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                        {inquiry.status}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}