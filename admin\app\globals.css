@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

body {
  color: rgb(var(--foreground-rgb));
  background: rgb(var(--background-start-rgb));
}

/* 管理系统自定义样式 */
.admin-layout {
  min-height: 100vh;
}

/* 响应式导航栏 */
@media (max-width: 768px) {
  .admin-layout main {
    margin-left: 0;
  }
}