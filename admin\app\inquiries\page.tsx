'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '../components/AuthContext';
import { useRouter } from 'next/navigation';
import api from '../utils/api';
import { toast } from 'react-hot-toast';

// 咨询类型定义
interface Inquiry {
  id: number;
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  service_type?: string;
  preferred_date?: string;
  preferred_time?: string;
  status: 'pending' | 'replied' | 'closed';
  reply?: string;
  replied_at?: string;
  replied_by_user?: {
    id: number;
    name: string;
  };
  created_at: string;
}

export default function InquiriesPage() {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedInquiry, setSelectedInquiry] = useState<Inquiry | null>(null);
  const [replyText, setReplyText] = useState('');
  const [replying, setReplying] = useState(false);

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/login');
      return;
    }

    if (isAuthenticated) {
      fetchInquiries();
    }
  }, [authLoading, isAuthenticated, router]);

  const fetchInquiries = async () => {
    try {
      setLoading(true);
      const response = await api.get('/inquiries');
      if (response.data.success) {
        setInquiries(response.data.data.items);
      }
    } catch (error) {
      console.error('获取咨询列表失败:', error);
      toast.error('获取咨询列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开回复对话框
  const handleOpenReply = (inquiry: Inquiry) => {
    setSelectedInquiry(inquiry);
    setReplyText(inquiry.reply || '');
  };

  // 关闭回复对话框
  const handleCloseReply = () => {
    setSelectedInquiry(null);
    setReplyText('');
  };

  // 提交回复
  const handleSubmitReply = async () => {
    if (!selectedInquiry || !replyText.trim()) return;

    setReplying(true);
    try {
      await api.put(`/inquiries/${selectedInquiry.id}`, {
        reply: replyText,
        status: 'replied'
      });

      toast.success('回复已发送');
      handleCloseReply();
      fetchInquiries(); // 重新获取数据
    } catch (error) {
      toast.error('发送失败，请重试');
      console.error('回复咨询失败:', error);
    } finally {
      setReplying(false);
    }
  };

  // 更改咨询状态
  const handleChangeStatus = async (id: number, status: 'pending' | 'replied' | 'closed') => {
    try {
      await api.put(`/inquiries/${id}`, { status });
      toast.success('状态已更新');
      fetchInquiries(); // 重新获取数据
    } catch (error) {
      toast.error('更新失败，请重试');
      console.error('更新咨询状态失败:', error);
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">客户咨询管理</h2>

      {loading ? (
        <div className="text-center py-10">
          <p className="text-gray-500">加载中...</p>
        </div>
      ) : inquiries.length === 0 ? (
        <div className="bg-white rounded-lg shadow p-6 text-center">
          <p className="text-gray-500">暂无客户咨询</p>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户信息</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">咨询内容</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {inquiries.map((inquiry) => (
                <tr key={inquiry.id}>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">{inquiry.name}</div>
                    <div className="text-sm text-gray-500">{inquiry.email}</div>
                    <div className="text-sm text-gray-500">{inquiry.phone}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">{inquiry.message}</div>
                    {inquiry.reply && (
                      <div className="mt-2 text-sm text-gray-500">
                        <span className="font-medium">回复:</span> {inquiry.reply}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {inquiry.createdAt}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${inquiry.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : inquiry.status === 'replied' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                      {inquiry.status === 'pending' ? '待处理' : inquiry.status === 'replied' ? '已回复' : '已关闭'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => handleOpenReply(inquiry)}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      回复
                    </button>
                    <select
                      value={inquiry.status}
                      onChange={(e) => handleChangeStatus(inquiry.id, e.target.value as any)}
                      className="text-sm border border-gray-300 rounded px-2 py-1"
                    >
                      <option value="pending">待处理</option>
                      <option value="replied">已回复</option>
                      <option value="closed">已关闭</option>
                    </select>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* 回复对话框 */}
      {selectedInquiry && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl">
            <h3 className="text-lg font-medium text-gray-900 mb-4">回复咨询</h3>

            <div className="mb-4 p-4 bg-gray-50 rounded">
              <p className="text-sm text-gray-700"><span className="font-medium">客户:</span> {selectedInquiry.name}</p>
              <p className="text-sm text-gray-700 mt-1"><span className="font-medium">咨询内容:</span> {selectedInquiry.message}</p>
            </div>

            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2" htmlFor="reply">
                回复内容
              </label>
              <textarea
                id="reply"
                rows={6}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="请输入回复内容..."
                value={replyText}
                onChange={(e) => setReplyText(e.target.value)}
              />
            </div>

            <div className="flex items-center justify-end">
              <button
                type="button"
                onClick={handleCloseReply}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded mr-2 focus:outline-none focus:shadow-outline"
                disabled={replying}
              >
                取消
              </button>
              <button
                type="button"
                onClick={handleSubmitReply}
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                disabled={replying || !replyText.trim()}
              >
                {replying ? '发送中...' : '发送回复'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}