'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../components/AuthContext';
import { useRouter } from 'next/navigation';
import toast, { Toaster } from 'react-hot-toast';
import { api } from '@/utils/api';

// 咨询类型定义
interface Inquiry {
  id: number;
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  service_type?: string;
  preferred_date?: string;
  preferred_time?: string;
  status: 'pending' | 'replied' | 'closed';
  reply?: string;
  replied_at?: string;
  replied_by_user?: {
    id: number;
    name: string;
  };
  created_at: string;
}

export default function InquiriesPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [loading, setLoading] = useState(true);
  const [replyContent, setReplyContent] = useState('');
  const [replyingTo, setReplyingTo] = useState<number | null>(null);
  const [replySending, setReplySending] = useState(false);
  const [filter, setFilter] = useState<'all' | 'pending' | 'replied' | 'closed'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // 获取咨询列表
  useEffect(() => {
    const fetchInquiries = async () => {
      try {
        setLoading(true);
        const response = await api.get('/inquiries');
        setInquiries(response.data);
      } catch (error) {
        console.error('获取咨询列表失败:', error);
        toast.error('获取咨询列表失败');
      } finally {
        setLoading(false);
      }
    };

    fetchInquiries();
  }, []);

  // 过滤咨询列表
  const filteredInquiries = inquiries.filter(inquiry => {
    // 状态过滤
    if (filter !== 'all' && inquiry.status !== filter) {
      return false;
    }

    // 搜索过滤
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        inquiry.name.toLowerCase().includes(searchLower) ||
        inquiry.email.toLowerCase().includes(searchLower) ||
        inquiry.subject.toLowerCase().includes(searchLower) ||
        inquiry.message.toLowerCase().includes(searchLower)
      );
    }

    return true;
  });

  // 回复咨询
  const handleReply = async (id: number) => {
    if (!replyContent.trim()) {
      toast.error('回复内容不能为空');
      return;
    }

    try {
      setReplySending(true);
      await api.post(`/inquiries/${id}/reply`, {
        reply: replyContent,
        replied_by_user_id: user?.id
      });

      // 更新本地状态
      setInquiries(prevInquiries =>
        prevInquiries.map(inquiry =>
          inquiry.id === id
            ? {
                ...inquiry,
                status: 'replied',
                reply: replyContent,
                replied_at: new Date().toISOString(),
                replied_by_user: {
                  id: user?.id || 0,
                  name: user?.name || ''
                }
              }
            : inquiry
        )
      );

      setReplyContent('');
      setReplyingTo(null);
      toast.success('回复成功');
    } catch (error) {
      console.error('回复咨询失败:', error);
      toast.error('回复咨询失败');
    } finally {
      setReplySending(false);
    }
  };

  // 关闭咨询
  const handleClose = async (id: number) => {
    try {
      await api.patch(`/inquiries/${id}/status`, { status: 'closed' });

      // 更新本地状态
      setInquiries(prevInquiries =>
        prevInquiries.map(inquiry =>
          inquiry.id === id
            ? { ...inquiry, status: 'closed' }
            : inquiry
        )
      );

      toast.success('咨询已关闭');
    } catch (error) {
      console.error('关闭咨询失败:', error);
      toast.error('关闭咨询失败');
    }
  };

  // 重新打开咨询
  const handleReopen = async (id: number) => {
    try {
      await api.patch(`/inquiries/${id}/status`, { status: 'pending' });

      // 更新本地状态
      setInquiries(prevInquiries =>
        prevInquiries.map(inquiry =>
          inquiry.id === id
            ? { ...inquiry, status: 'pending' }
            : inquiry
        )
      );

      toast.success('咨询已重新打开');
    } catch (error) {
      console.error('重新打开咨询失败:', error);
      toast.error('重新打开咨询失败');
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取状态标签样式
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'replied':
        return 'bg-green-100 text-green-800';
      case 'closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待处理';
      case 'replied':
        return '已回复';
      case 'closed':
        return '已关闭';
      default:
        return '未知状态';
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">咨询管理</h1>

      {/* 过滤和搜索 */}
      <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
        <div className="flex space-x-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 rounded ${filter === 'all' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            全部
          </button>
          <button
            onClick={() => setFilter('pending')}
            className={`px-3 py-1 rounded ${filter === 'pending' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            待处理
          </button>
          <button
            onClick={() => setFilter('replied')}
            className={`px-3 py-1 rounded ${filter === 'replied' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            已回复
          </button>
          <button
            onClick={() => setFilter('closed')}
            className={`px-3 py-1 rounded ${filter === 'closed' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
          >
            已关闭
          </button>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="搜索咨询..."
            className="border rounded px-3 py-1 w-full md:w-64"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      {/* 咨询列表 */}
      {loading ? (
        <div className="text-center py-10">加载中...</div>
      ) : filteredInquiries.length === 0 ? (
        <div className="text-center py-10 text-gray-500">暂无咨询数据</div>
      ) : (
        <div className="space-y-6">
          {filteredInquiries.map((inquiry) => (
            <div key={inquiry.id} className="border rounded-lg overflow-hidden bg-white shadow-sm">
              <div className="p-4 border-b">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-bold text-lg">{inquiry.subject}</h3>
                    <p className="text-sm text-gray-600">
                      {inquiry.name} ({inquiry.email})
                      {inquiry.phone && ` · ${inquiry.phone}`}
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`text-xs px-2 py-1 rounded ${getStatusClass(inquiry.status)}`}>
                      {getStatusText(inquiry.status)}
                    </span>
                    <span className="text-xs text-gray-500">{formatDate(inquiry.created_at)}</span>
                  </div>
                </div>
              </div>
              <div className="p-4 bg-gray-50">
                <p className="whitespace-pre-wrap">{inquiry.message}</p>
              </div>
              {inquiry.reply && (
                <div className="p-4 border-t bg-blue-50">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium">回复</h4>
                    <span className="text-xs text-gray-500">
                      {inquiry.replied_at && formatDate(inquiry.replied_at)}
                      {inquiry.replied_by_user && ` · ${inquiry.replied_by_user.name}`}
                    </span>
                  </div>
                  <p className="whitespace-pre-wrap">{inquiry.reply}</p>
                </div>
              )}
              <div className="p-4 border-t bg-gray-100 flex justify-end space-x-2">
                {inquiry.status === 'pending' && (
                  <button
                    onClick={() => setReplyingTo(inquiry.id)}
                    className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    回复
                  </button>
                )}
                {inquiry.status === 'pending' && (
                  <button
                    onClick={() => handleClose(inquiry.id)}
                    className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    关闭
                  </button>
                )}
                {inquiry.status === 'replied' && (
                  <button
                    onClick={() => handleClose(inquiry.id)}
                    className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600"
                  >
                    关闭
                  </button>
                )}
                {inquiry.status === 'closed' && (
                  <button
                    onClick={() => handleReopen(inquiry.id)}
                    className="px-3 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600"
                  >
                    重新打开
                  </button>
                )}
              </div>
              {replyingTo === inquiry.id && (
                <div className="p-4 border-t">
                  <textarea
                    className="w-full border rounded p-2 mb-2"
                    rows={4}
                    placeholder="输入回复内容..."
                    value={replyContent}
                    onChange={(e) => setReplyContent(e.target.value)}
                  ></textarea>
                  <div className="flex justify-end space-x-2">
                    <button
                      onClick={() => {
                        setReplyingTo(null);
                        setReplyContent('');
                      }}
                      className="px-3 py-1 bg-gray-300 rounded hover:bg-gray-400"
                    >
                      取消
                    </button>
                    <button
                      onClick={() => handleReply(inquiry.id)}
                      disabled={replySending}
                      className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-blue-300"
                    >
                      {replySending ? '发送中...' : '发送回复'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
      <Toaster />
    </div>
  );
}