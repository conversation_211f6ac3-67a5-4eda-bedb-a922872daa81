import React from 'react';
import './globals.css';
import { AuthProvider } from './components/AuthContext';
import { Toaster } from 'react-hot-toast';

export const metadata = {
  title: '后台管理系统',
  description: '上海留学顾问管理系统',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh">
      <body>
        <AuthProvider>
          <div className="admin-layout min-h-screen bg-gray-100">
            <header className="bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50">
              <h1 className="text-xl font-semibold">后台管理系统</h1>
            </header>
            <div className="flex pt-16">
              <nav className="bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40">
                {/* 导航链接 */}
                <ul className="space-y-2 mt-4">
                  <li><a href="/" className="block p-2 hover:bg-gray-200 rounded">仪表盘</a></li>
                  <li><a href="/users" className="block p-2 hover:bg-gray-200 rounded">用户管理</a></li>
                  <li><a href="/content/articles" className="block p-2 hover:bg-gray-200 rounded">文章管理</a></li>
                  <li><a href="/content/services" className="block p-2 hover:bg-gray-200 rounded">服务管理</a></li>
                  <li><a href="/content/cases" className="block p-2 hover:bg-gray-200 rounded">案例管理</a></li>
                  <li><a href="/content/banners" className="block p-2 hover:bg-gray-200 rounded">Banner管理</a></li>
                  <li><a href="/content/faq" className="block p-2 hover:bg-gray-200 rounded">FAQ管理</a></li>
                  <li><a href="/inquiries" className="block p-2 hover:bg-gray-200 rounded">客户咨询</a></li>
                  <li><a href="/team" className="block p-2 hover:bg-gray-200 rounded">团队管理</a></li>
                  <li><a href="/settings" className="block p-2 hover:bg-gray-200 rounded">系统设置</a></li>
                </ul>
              </nav>
              <main className="flex-1 p-6 md:ml-64 bg-gray-100">
                {children}
              </main>
            </div>
          </div>
          <Toaster position="top-right" />
        </AuthProvider>
      </body>
    </html>
  );
}