'use client';

import { useState } from 'react';
import { FiSearch, FiFilter, FiDownload, FiClock, FiUser, FiActivity } from 'react-icons/fi';

// 日志类型定义
interface LogEntry {
  id: number;
  action: string;
  user: string;
  userRole: string;
  timestamp: string;
  ip: string;
  details: string;
  module: 'content' | 'user' | 'system' | 'auth';
  level: 'info' | 'warning' | 'error';
}

// 模拟日志数据
const MOCK_LOGS: LogEntry[] = [
  {
    id: 1,
    action: '用户登录',
    user: '管理员',
    userRole: '超级管理员',
    timestamp: '2023-12-01 14:32:45',
    ip: '*************',
    details: '管理员成功登录系统',
    module: 'auth',
    level: 'info'
  },
  {
    id: 2,
    action: '创建文章',
    user: '内容编辑',
    userRole: '编辑',
    timestamp: '2023-12-01 15:10:22',
    ip: '*************',
    details: '创建了新文章「留学申请流程详解」',
    module: 'content',
    level: 'info'
  },
  {
    id: 3,
    action: '修改用户',
    user: '管理员',
    userRole: '超级管理员',
    timestamp: '2023-12-01 16:05:18',
    ip: '*************',
    details: '修改了用户「张编辑」的权限',
    module: 'user',
    level: 'warning'
  },
  {
    id: 4,
    action: '删除文章',
    user: '内容编辑',
    userRole: '编辑',
    timestamp: '2023-12-01 16:45:33',
    ip: '*************',
    details: '删除了文章「过期内容」',
    module: 'content',
    level: 'warning'
  },
  {
    id: 5,
    action: '系统设置修改',
    user: '管理员',
    userRole: '超级管理员',
    timestamp: '2023-12-01 17:20:11',
    ip: '*************',
    details: '更新了网站SEO设置',
    module: 'system',
    level: 'info'
  },
  {
    id: 6,
    action: '登录失败',
    user: '未知用户',
    userRole: '未知',
    timestamp: '2023-12-01 18:05:42',
    ip: '************',
    details: '多次尝试登录失败，账户暂时锁定',
    module: 'auth',
    level: 'error'
  },
  {
    id: 7,
    action: '创建用户',
    user: '管理员',
    userRole: '超级管理员',
    timestamp: '2023-12-02 09:15:30',
    ip: '*************',
    details: '创建了新用户「李编辑」',
    module: 'user',
    level: 'info'
  },
  {
    id: 8,
    action: '回复咨询',
    user: '客服人员',
    userRole: '客服',
    timestamp: '2023-12-02 10:30:25',
    ip: '*************',
    details: '回复了用户「王先生」的咨询',
    module: 'content',
    level: 'info'
  },
  {
    id: 9,
    action: '系统备份',
    user: '系统',
    userRole: '系统',
    timestamp: '2023-12-02 12:00:00',
    ip: '127.0.0.1',
    details: '系统自动备份完成',
    module: 'system',
    level: 'info'
  },
  {
    id: 10,
    action: '数据库错误',
    user: '系统',
    userRole: '系统',
    timestamp: '2023-12-02 14:22:18',
    ip: '127.0.0.1',
    details: '数据库连接超时，自动重连成功',
    module: 'system',
    level: 'error'
  },
  {
    id: 11,
    action: '更新服务',
    user: '内容编辑',
    userRole: '编辑',
    timestamp: '2023-12-02 15:40:12',
    ip: '*************',
    details: '更新了服务「职业规划咨询」的详情',
    module: 'content',
    level: 'info'
  },
  {
    id: 12,
    action: '用户登出',
    user: '客服人员',
    userRole: '客服',
    timestamp: '2023-12-02 17:30:45',
    ip: '*************',
    details: '用户主动登出系统',
    module: 'auth',
    level: 'info'
  },
];

// 日志级别对应的样式
const getLevelStyle = (level: LogEntry['level']) => {
  switch (level) {
    case 'info':
      return 'bg-blue-100 text-blue-800';
    case 'warning':
      return 'bg-yellow-100 text-yellow-800';
    case 'error':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// 模块对应的图标
const getModuleIcon = (module: LogEntry['module']) => {
  switch (module) {
    case 'content':
      return <FiActivity className="text-blue-500" />;
    case 'user':
      return <FiUser className="text-green-500" />;
    case 'system':
      return <FiActivity className="text-purple-500" />;
    case 'auth':
      return <FiUser className="text-orange-500" />;
    default:
      return <FiActivity className="text-gray-500" />;
  }
};

export default function LogsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedModule, setSelectedModule] = useState<string>('all');
  const [selectedLevel, setSelectedLevel] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);
  
  // 过滤日志
  const filteredLogs = MOCK_LOGS.filter(log => {
    const matchesSearch = 
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.user.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.details.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesModule = selectedModule === 'all' || log.module === selectedModule;
    const matchesLevel = selectedLevel === 'all' || log.level === selectedLevel;
    
    return matchesSearch && matchesModule && matchesLevel;
  });
  
  // 分页
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredLogs.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredLogs.length / itemsPerPage);
  
  // 导出日志
  const exportLogs = () => {
    // 实际实现中应该调用API导出日志
    alert('日志导出功能将在实际环境中实现');
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">系统日志</h1>
        <p className="text-gray-600">查看和管理系统操作日志</p>
      </div>
      
      {/* 过滤和搜索工具栏 */}
      <div className="bg-white rounded-lg shadow p-4 mb-6">
        <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
          {/* 搜索框 */}
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiSearch className="text-gray-400" />
            </div>
            <input
              type="text"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="搜索日志..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {/* 模块筛选 */}
          <div className="w-full md:w-48">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiFilter className="text-gray-400" />
              </div>
              <select
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none"
                value={selectedModule}
                onChange={(e) => setSelectedModule(e.target.value)}
              >
                <option value="all">所有模块</option>
                <option value="content">内容管理</option>
                <option value="user">用户管理</option>
                <option value="system">系统管理</option>
                <option value="auth">认证授权</option>
              </select>
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
          
          {/* 级别筛选 */}
          <div className="w-full md:w-48">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiFilter className="text-gray-400" />
              </div>
              <select
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none"
                value={selectedLevel}
                onChange={(e) => setSelectedLevel(e.target.value)}
              >
                <option value="all">所有级别</option>
                <option value="info">信息</option>
                <option value="warning">警告</option>
                <option value="error">错误</option>
              </select>
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </div>
          
          {/* 导出按钮 */}
          <button
            onClick={exportLogs}
            className="w-full md:w-auto px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 flex items-center justify-center space-x-2"
          >
            <FiDownload />
            <span>导出日志</span>
          </button>
        </div>
      </div>
      
      {/* 日志列表 */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  用户
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  时间
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  IP地址
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  级别
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  详情
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentItems.map((log) => (
                <tr key={log.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8 flex items-center justify-center">
                        {getModuleIcon(log.module)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{log.action}</div>
                        <div className="text-xs text-gray-500">{log.module}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{log.user}</div>
                    <div className="text-xs text-gray-500">{log.userRole}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center text-sm text-gray-500">
                      <FiClock className="mr-1" />
                      {log.timestamp}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {log.ip}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getLevelStyle(log.level)}`}>
                      {log.level === 'info' ? '信息' : log.level === 'warning' ? '警告' : '错误'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                    {log.details}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {/* 分页控件 */}
        {totalPages > 1 && (
          <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  显示第 <span className="font-medium">{indexOfFirstItem + 1}</span> 到 <span className="font-medium">{Math.min(indexOfLastItem, filteredLogs.length)}</span> 条，共 <span className="font-medium">{filteredLogs.length}</span> 条记录
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">上一页</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                  
                  {/* 页码按钮 */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${currentPage === page ? 'z-10 bg-primary-50 border-primary-500 text-primary-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}`}
                    >
                      {page}
                    </button>
                  ))}
                  
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span className="sr-only">下一页</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}