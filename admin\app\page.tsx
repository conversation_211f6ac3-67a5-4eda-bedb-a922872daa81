'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from './components/AuthContext';
import api from './utils/api';
import Link from 'next/link';

export default function AdminDashboardPage() {
  const { user, loading, isAuthenticated } = useAuth();
  const router = useRouter();
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalArticles: 0,
    totalInquiries: 0,
    pendingInquiries: 0
  });

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/login');
      return;
    }

    if (isAuthenticated) {
      // 获取仪表盘统计数据
      fetchDashboardStats();
    }
  }, [loading, isAuthenticated, router]);

  const fetchDashboardStats = async () => {
    try {
      const response = await api.get('/dashboard/stats');
      if (response.data.success) {
        setStats(response.data.data.overview);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }
  return (
    <div>
      <h2 className="text-2xl font-semibold mb-6">仪表盘</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* 仪表盘组件 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-700">用户总数</h3>
          <p className="text-3xl font-bold text-blue-600 mt-2">{stats.totalUsers}</p>
          <p className="text-sm text-gray-500 mt-1">系统用户</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-700">文章总数</h3>
          <p className="text-3xl font-bold text-green-600 mt-2">{stats.totalArticles}</p>
          <p className="text-sm text-gray-500 mt-1">已发布文章</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-700">待处理咨询</h3>
          <p className="text-3xl font-bold text-yellow-500 mt-2">{stats.pendingInquiries}</p>
          <p className="text-sm text-gray-500 mt-1">当前积压</p>
        </div>
      </div>

      <div className="mt-8">
        <h3 className="text-xl font-semibold mb-4">快速操作</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/content/articles/new" className="bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50">
            <span className="block text-blue-600 font-medium">发布文章</span>
          </Link>
          <Link href="/inquiries" className="bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50">
            <span className="block text-blue-600 font-medium">处理咨询</span>
          </Link>
          <Link href="/content/services" className="bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50">
            <span className="block text-blue-600 font-medium">管理服务</span>
          </Link>
          <Link href="/content/cases" className="bg-white p-4 rounded-lg shadow text-center hover:bg-blue-50">
            <span className="block text-blue-600 font-medium">管理案例</span>
          </Link>
        </div>
      </div>

      <div className="mt-8">
        <h3 className="text-xl font-semibold mb-4">最近活动</h3>
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">更新了首页Banner</td>
                <td className="px-6 py-4 whitespace-nowrap">管理员</td>
                <td className="px-6 py-4 whitespace-nowrap">2023-12-15 14:30</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">添加了新服务</td>
                <td className="px-6 py-4 whitespace-nowrap">编辑</td>
                <td className="px-6 py-4 whitespace-nowrap">2023-12-15 11:20</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap">回复了客户咨询</td>
                <td className="px-6 py-4 whitespace-nowrap">客服</td>
                <td className="px-6 py-4 whitespace-nowrap">2023-12-14 16:45</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}