'use client';

import { useState } from 'react';
import { useF<PERSON>, SubmitHandler } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { useAuth } from '../components/AuthContext';
import api from '../utils/api';

// 个人资料数据类型
interface ProfileData {
  name: string;
  email: string;
  avatar: string;
  phone?: string;
  position?: string;
  bio?: string;
}

// 密码修改数据类型
interface PasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export default function ProfilePage() {
  const { user, updateUserInfo } = useAuth();
  const [activeTab, setActiveTab] = useState<'profile' | 'password'>('profile');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  
  // 使用 react-hook-form 管理个人资料表单
  const { 
    register: registerProfile, 
    handleSubmit: handleSubmitProfile, 
    formState: { errors: profileErrors },
    reset: resetProfile
  } = useForm<ProfileData>({
    defaultValues: {
      name: user?.name || '',
      email: user?.email || '',
      avatar: user?.avatar || '',
      phone: user?.phone || '',
      position: user?.position || '',
      bio: user?.bio || ''
    }
  });

  // 使用 react-hook-form 管理密码修改表单
  const { 
    register: registerPassword, 
    handleSubmit: handleSubmitPassword, 
    formState: { errors: passwordErrors },
    reset: resetPassword,
    watch
  } = useForm<PasswordData>();

  // 监听新密码，用于确认密码验证
  const newPassword = watch('newPassword');

  // 处理头像上传预览
  const handleAvatarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const previewUrl = URL.createObjectURL(file);
      setAvatarPreview(previewUrl);
    }
  };

  // 提交个人资料表单
  const onSubmitProfile: SubmitHandler<ProfileData> = async (data) => {
    setIsSubmitting(true);
    
    try {
      // 模拟API调用
      // 实际实现中应该使用 await api.put('/users/profile', data);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 更新本地用户信息
      updateUserInfo({
        ...user,
        name: data.name,
        email: data.email,
        avatar: avatarPreview || data.avatar,
        phone: data.phone,
        position: data.position,
        bio: data.bio
      });
      
      toast.success('个人资料已成功更新');
      setIsSubmitting(false);
    } catch (error) {
      console.error('更新个人资料失败:', error);
      toast.error('更新个人资料失败，请重试');
      setIsSubmitting(false);
    }
  };

  // 提交密码修改表单
  const onSubmitPassword: SubmitHandler<PasswordData> = async (data) => {
    setIsSubmitting(true);
    
    try {
      // 模拟API调用
      // 实际实现中应该使用 await api.put('/users/password', { currentPassword: data.currentPassword, newPassword: data.newPassword });
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('密码已成功修改');
      resetPassword();
      setIsSubmitting(false);
    } catch (error) {
      console.error('修改密码失败:', error);
      toast.error('修改密码失败，请确认当前密码是否正确');
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">个人资料</h1>
        <p className="text-gray-600">管理您的账户信息和安全设置</p>
      </div>
      
      {/* 标签切换 */}
      <div className="mb-6 border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('profile')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'profile' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            个人信息
          </button>
          <button
            onClick={() => setActiveTab('password')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${activeTab === 'password' ? 'border-primary-500 text-primary-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'}`}
          >
            修改密码
          </button>
        </nav>
      </div>
      
      <div className="bg-white rounded-lg shadow p-6">
        {/* 个人资料表单 */}
        {activeTab === 'profile' && (
          <form onSubmit={handleSubmitProfile(onSubmitProfile)} className="space-y-6">
            {/* 头像上传 */}
            <div className="flex flex-col items-center sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-6">
              <div className="w-24 h-24 bg-gray-200 rounded-full overflow-hidden flex items-center justify-center">
                {avatarPreview ? (
                  <img 
                    src={avatarPreview} 
                    alt="头像预览" 
                    className="h-full w-full object-cover"
                  />
                ) : user?.avatar ? (
                  <img 
                    src={user.avatar} 
                    alt="当前头像" 
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <span className="text-gray-500 text-xs">无头像</span>
                )}
              </div>
              <div className="flex flex-col space-y-2">
                <input
                  id="avatar"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleAvatarChange}
                />
                <button
                  type="button"
                  onClick={() => document.getElementById('avatar')?.click()}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 text-sm"
                >
                  更换头像
                </button>
                <p className="text-xs text-gray-500">推荐使用正方形图片，最大2MB</p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 姓名 */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  姓名 <span className="text-red-500">*</span>
                </label>
                <input
                  id="name"
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md ${profileErrors.name ? 'border-red-500' : 'border-gray-300'}`}
                  {...registerProfile('name', { required: '请输入姓名' })}
                />
                {profileErrors.name && (
                  <p className="mt-1 text-sm text-red-500">{profileErrors.name.message}</p>
                )}
              </div>
              
              {/* 邮箱 */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  邮箱 <span className="text-red-500">*</span>
                </label>
                <input
                  id="email"
                  type="email"
                  className={`w-full px-3 py-2 border rounded-md ${profileErrors.email ? 'border-red-500' : 'border-gray-300'}`}
                  {...registerProfile('email', { 
                    required: '请输入邮箱',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: '请输入有效的邮箱地址'
                    }
                  })}
                />
                {profileErrors.email && (
                  <p className="mt-1 text-sm text-red-500">{profileErrors.email.message}</p>
                )}
              </div>
              
              {/* 电话 */}
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  电话
                </label>
                <input
                  id="phone"
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  {...registerProfile('phone')}
                />
              </div>
              
              {/* 职位 */}
              <div>
                <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">
                  职位
                </label>
                <input
                  id="position"
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  {...registerProfile('position')}
                />
              </div>
            </div>
            
            {/* 个人简介 */}
            <div>
              <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                个人简介
              </label>
              <textarea
                id="bio"
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md"
                placeholder="简单介绍一下自己..."
                {...registerProfile('bio')}
              />
            </div>
            
            {/* 提交按钮 */}
            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                onClick={() => resetProfile()}
                disabled={isSubmitting}
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
                disabled={isSubmitting}
              >
                {isSubmitting ? '保存中...' : '保存修改'}
              </button>
            </div>
          </form>
        )}
        
        {/* 密码修改表单 */}
        {activeTab === 'password' && (
          <form onSubmit={handleSubmitPassword(onSubmitPassword)} className="space-y-6 max-w-md">
            {/* 当前密码 */}
            <div>
              <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">
                当前密码 <span className="text-red-500">*</span>
              </label>
              <input
                id="currentPassword"
                type="password"
                className={`w-full px-3 py-2 border rounded-md ${passwordErrors.currentPassword ? 'border-red-500' : 'border-gray-300'}`}
                {...registerPassword('currentPassword', { required: '请输入当前密码' })}
              />
              {passwordErrors.currentPassword && (
                <p className="mt-1 text-sm text-red-500">{passwordErrors.currentPassword.message}</p>
              )}
            </div>
            
            {/* 新密码 */}
            <div>
              <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
                新密码 <span className="text-red-500">*</span>
              </label>
              <input
                id="newPassword"
                type="password"
                className={`w-full px-3 py-2 border rounded-md ${passwordErrors.newPassword ? 'border-red-500' : 'border-gray-300'}`}
                {...registerPassword('newPassword', { 
                  required: '请输入新密码',
                  minLength: {
                    value: 8,
                    message: '密码长度至少为8个字符'
                  },
                  pattern: {
                    value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
                    message: '密码必须包含大小写字母和数字'
                  }
                })}
              />
              {passwordErrors.newPassword && (
                <p className="mt-1 text-sm text-red-500">{passwordErrors.newPassword.message}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">密码必须至少包含8个字符，包括大小写字母和数字</p>
            </div>
            
            {/* 确认新密码 */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                确认新密码 <span className="text-red-500">*</span>
              </label>
              <input
                id="confirmPassword"
                type="password"
                className={`w-full px-3 py-2 border rounded-md ${passwordErrors.confirmPassword ? 'border-red-500' : 'border-gray-300'}`}
                {...registerPassword('confirmPassword', { 
                  required: '请确认新密码',
                  validate: value => value === newPassword || '两次输入的密码不一致'
                })}
              />
              {passwordErrors.confirmPassword && (
                <p className="mt-1 text-sm text-red-500">{passwordErrors.confirmPassword.message}</p>
              )}
            </div>
            
            {/* 提交按钮 */}
            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                onClick={() => resetPassword()}
                disabled={isSubmitting}
              >
                取消
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
                disabled={isSubmitting}
              >
                {isSubmitting ? '更新中...' : '更新密码'}
              </button>
            </div>
          </form>
        )}
      </div>
      <Toaster position="top-right" />
    </div>
  );
}