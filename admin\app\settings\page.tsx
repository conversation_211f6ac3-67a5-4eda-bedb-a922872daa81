'use client';

import { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import api from '../utils/api';

// 系统设置数据类型
interface SystemSettings {
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  icp: string;
  seoKeywords: string;
  seoDescription: string;
  logoUrl: string;
  faviconUrl: string;
  footerCopyright: string;
  socialMedia: {
    weixin: string;
    weibo: string;
    zhihu: string;
  };
}

// 模拟系统设置数据
const MOCK_SETTINGS: SystemSettings = {
  siteName: '上海留学顾问',
  siteDescription: '专业的留学与职业规划咨询服务',
  contactEmail: '<EMAIL>',
  contactPhone: '021-12345678',
  address: '上海市浦东新区张江高科技园区博云路2号',
  icp: '沪ICP备12345678号',
  seoKeywords: '留学,职业规划,考研,保研,职业转型,上海留学顾问',
  seoDescription: '上海留学顾问提供专业的留学申请、考研保研规划、职业发展咨询等服务，助力学生和职场人士实现人生目标。',
  logoUrl: '/images/logo.png',
  faviconUrl: '/favicon.ico',
  footerCopyright: '© 2023 上海留学顾问 版权所有',
  socialMedia: {
    weixin: 'slhgw_weixin',
    weibo: 'slhgw_weibo',
    zhihu: 'slhgw_zhihu',
  },
};

export default function SettingsPage() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [faviconPreview, setFaviconPreview] = useState<string | null>(null);
  
  // 使用 react-hook-form 管理表单
  const { 
    register, 
    handleSubmit, 
    formState: { errors },
    reset
  } = useForm<SystemSettings>({
    defaultValues: MOCK_SETTINGS
  });

  // 处理Logo上传预览
  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const previewUrl = URL.createObjectURL(file);
      setLogoPreview(previewUrl);
    }
  };

  // 处理Favicon上传预览
  const handleFaviconChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const previewUrl = URL.createObjectURL(file);
      setFaviconPreview(previewUrl);
    }
  };

  // 提交表单
  const onSubmit: SubmitHandler<SystemSettings> = async (data) => {
    setIsSubmitting(true);
    
    try {
      // 模拟API调用
      // 实际实现中应该使用 await api.put('/settings', data);
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success('系统设置已成功保存');
      setIsSubmitting(false);
    } catch (error) {
      console.error('保存系统设置失败:', error);
      toast.error('保存系统设置失败，请重试');
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">系统设置</h1>
        <p className="text-gray-600">管理网站基本信息和配置</p>
      </div>
      
      <div className="bg-white rounded-lg shadow p-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 基本信息设置 */}
          <div className="border-b border-gray-200 pb-6">
            <h2 className="text-lg font-medium mb-4">基本信息</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 网站名称 */}
              <div>
                <label htmlFor="siteName" className="block text-sm font-medium text-gray-700 mb-1">
                  网站名称 <span className="text-red-500">*</span>
                </label>
                <input
                  id="siteName"
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md ${errors.siteName ? 'border-red-500' : 'border-gray-300'}`}
                  {...register('siteName', { required: '请输入网站名称' })}
                />
                {errors.siteName && (
                  <p className="mt-1 text-sm text-red-500">{errors.siteName.message}</p>
                )}
              </div>
              
              {/* 网站描述 */}
              <div>
                <label htmlFor="siteDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  网站描述 <span className="text-red-500">*</span>
                </label>
                <input
                  id="siteDescription"
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md ${errors.siteDescription ? 'border-red-500' : 'border-gray-300'}`}
                  {...register('siteDescription', { required: '请输入网站描述' })}
                />
                {errors.siteDescription && (
                  <p className="mt-1 text-sm text-red-500">{errors.siteDescription.message}</p>
                )}
              </div>
            </div>
          </div>
          
          {/* 联系信息设置 */}
          <div className="border-b border-gray-200 pb-6">
            <h2 className="text-lg font-medium mb-4">联系信息</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 联系邮箱 */}
              <div>
                <label htmlFor="contactEmail" className="block text-sm font-medium text-gray-700 mb-1">
                  联系邮箱 <span className="text-red-500">*</span>
                </label>
                <input
                  id="contactEmail"
                  type="email"
                  className={`w-full px-3 py-2 border rounded-md ${errors.contactEmail ? 'border-red-500' : 'border-gray-300'}`}
                  {...register('contactEmail', { 
                    required: '请输入联系邮箱',
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: '请输入有效的邮箱地址'
                    }
                  })}
                />
                {errors.contactEmail && (
                  <p className="mt-1 text-sm text-red-500">{errors.contactEmail.message}</p>
                )}
              </div>
              
              {/* 联系电话 */}
              <div>
                <label htmlFor="contactPhone" className="block text-sm font-medium text-gray-700 mb-1">
                  联系电话 <span className="text-red-500">*</span>
                </label>
                <input
                  id="contactPhone"
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md ${errors.contactPhone ? 'border-red-500' : 'border-gray-300'}`}
                  {...register('contactPhone', { required: '请输入联系电话' })}
                />
                {errors.contactPhone && (
                  <p className="mt-1 text-sm text-red-500">{errors.contactPhone.message}</p>
                )}
              </div>
              
              {/* 公司地址 */}
              <div className="md:col-span-2">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                  公司地址 <span className="text-red-500">*</span>
                </label>
                <input
                  id="address"
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md ${errors.address ? 'border-red-500' : 'border-gray-300'}`}
                  {...register('address', { required: '请输入公司地址' })}
                />
                {errors.address && (
                  <p className="mt-1 text-sm text-red-500">{errors.address.message}</p>
                )}
              </div>
            </div>
          </div>
          
          {/* SEO设置 */}
          <div className="border-b border-gray-200 pb-6">
            <h2 className="text-lg font-medium mb-4">SEO设置</h2>
            <div className="grid grid-cols-1 gap-6">
              {/* SEO关键词 */}
              <div>
                <label htmlFor="seoKeywords" className="block text-sm font-medium text-gray-700 mb-1">
                  SEO关键词 <span className="text-red-500">*</span>
                </label>
                <input
                  id="seoKeywords"
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md ${errors.seoKeywords ? 'border-red-500' : 'border-gray-300'}`}
                  {...register('seoKeywords', { required: '请输入SEO关键词' })}
                />
                {errors.seoKeywords && (
                  <p className="mt-1 text-sm text-red-500">{errors.seoKeywords.message}</p>
                )}
                <p className="mt-1 text-sm text-gray-500">多个关键词用英文逗号分隔</p>
              </div>
              
              {/* SEO描述 */}
              <div>
                <label htmlFor="seoDescription" className="block text-sm font-medium text-gray-700 mb-1">
                  SEO描述 <span className="text-red-500">*</span>
                </label>
                <textarea
                  id="seoDescription"
                  rows={3}
                  className={`w-full px-3 py-2 border rounded-md ${errors.seoDescription ? 'border-red-500' : 'border-gray-300'}`}
                  {...register('seoDescription', { required: '请输入SEO描述' })}
                />
                {errors.seoDescription && (
                  <p className="mt-1 text-sm text-red-500">{errors.seoDescription.message}</p>
                )}
              </div>
              
              {/* ICP备案号 */}
              <div>
                <label htmlFor="icp" className="block text-sm font-medium text-gray-700 mb-1">
                  ICP备案号
                </label>
                <input
                  id="icp"
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  {...register('icp')}
                />
              </div>
            </div>
          </div>
          
          {/* 网站资源设置 */}
          <div className="border-b border-gray-200 pb-6">
            <h2 className="text-lg font-medium mb-4">网站资源</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Logo设置 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  网站Logo
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    id="logo"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleLogoChange}
                  />
                  <button
                    type="button"
                    onClick={() => document.getElementById('logo')?.click()}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    选择Logo
                  </button>
                  <div className="h-12 w-24 bg-gray-200 rounded overflow-hidden flex items-center justify-center">
                    {logoPreview ? (
                      <img 
                        src={logoPreview} 
                        alt="Logo预览" 
                        className="h-full w-full object-contain"
                      />
                    ) : (
                      <span className="text-gray-500 text-xs">Logo预览</span>
                    )}
                  </div>
                </div>
                <input
                  type="hidden"
                  {...register('logoUrl')}
                />
              </div>
              
              {/* Favicon设置 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  网站图标 (Favicon)
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    id="favicon"
                    type="file"
                    accept="image/x-icon,image/png"
                    className="hidden"
                    onChange={handleFaviconChange}
                  />
                  <button
                    type="button"
                    onClick={() => document.getElementById('favicon')?.click()}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    选择图标
                  </button>
                  <div className="h-8 w-8 bg-gray-200 rounded overflow-hidden flex items-center justify-center">
                    {faviconPreview ? (
                      <img 
                        src={faviconPreview} 
                        alt="Favicon预览" 
                        className="h-full w-full object-contain"
                      />
                    ) : (
                      <span className="text-gray-500 text-xs">图标</span>
                    )}
                  </div>
                </div>
                <input
                  type="hidden"
                  {...register('faviconUrl')}
                />
              </div>
              
              {/* 页脚版权信息 */}
              <div className="md:col-span-2">
                <label htmlFor="footerCopyright" className="block text-sm font-medium text-gray-700 mb-1">
                  页脚版权信息 <span className="text-red-500">*</span>
                </label>
                <input
                  id="footerCopyright"
                  type="text"
                  className={`w-full px-3 py-2 border rounded-md ${errors.footerCopyright ? 'border-red-500' : 'border-gray-300'}`}
                  {...register('footerCopyright', { required: '请输入页脚版权信息' })}
                />
                {errors.footerCopyright && (
                  <p className="mt-1 text-sm text-red-500">{errors.footerCopyright.message}</p>
                )}
              </div>
            </div>
          </div>
          
          {/* 社交媒体设置 */}
          <div>
            <h2 className="text-lg font-medium mb-4">社交媒体</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* 微信 */}
              <div>
                <label htmlFor="weixin" className="block text-sm font-medium text-gray-700 mb-1">
                  微信公众号
                </label>
                <input
                  id="weixin"
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  {...register('socialMedia.weixin')}
                />
              </div>
              
              {/* 微博 */}
              <div>
                <label htmlFor="weibo" className="block text-sm font-medium text-gray-700 mb-1">
                  微博
                </label>
                <input
                  id="weibo"
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  {...register('socialMedia.weibo')}
                />
              </div>
              
              {/* 知乎 */}
              <div>
                <label htmlFor="zhihu" className="block text-sm font-medium text-gray-700 mb-1">
                  知乎
                </label>
                <input
                  id="zhihu"
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  {...register('socialMedia.zhihu')}
                />
              </div>
            </div>
          </div>
          
          {/* 提交按钮 */}
          <div className="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              onClick={() => reset(MOCK_SETTINGS)}
              disabled={isSubmitting}
            >
              重置
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50"
              disabled={isSubmitting}
            >
              {isSubmitting ? '保存中...' : '保存设置'}
            </button>
          </div>
        </form>
      </div>
      <Toaster position="top-right" />
    </div>
  );
}