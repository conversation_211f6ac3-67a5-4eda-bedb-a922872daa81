'use client';

import { useState, ChangeEvent, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import { useForm, SubmitHandler } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import { FiUserPlus, FiUpload, FiSave, FiArrowLeft } from 'react-icons/fi';
import Link from 'next/link';
import { api } from '@/utils/api';

interface TeamMemberFormData {
  name: string;
  title: string;
  avatar: FileList;
  department: string;
  order: number;
  bio: string;
  status: 'active' | 'inactive';
}

export default function NewTeamMemberPage() {
  const router = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<TeamMemberFormData>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  const handleAvatarChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setAvatarPreview(URL.createObjectURL(file));
      setValue('avatar', e.target.files);
    }
  };

  const onSubmit: SubmitHandler<TeamMemberFormData> = async (data) => {
    setIsSubmitting(true);
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('title', data.title);
    if (data.avatar && data.avatar[0]) {
      formData.append('avatar', data.avatar[0]);
    }
    formData.append('department', data.department);
    formData.append('order', data.order.toString());
    formData.append('bio', data.bio);
    formData.append('status', data.status);

    try {
      await api.post('/team', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      toast.success('团队成员创建成功！');
      router.push('/team');
    } catch (error) {
      console.error('创建团队成员失败:', error);
      toast.error('创建团队成员失败，请稍后再试。');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster position="top-center" />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">添加新团队成员</h1>
        <Link href="/team">
          <button className="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center">
            <FiArrowLeft className="mr-2" />
            返回列表
          </button>
        </Link>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="bg-white p-8 rounded-xl shadow-xl space-y-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
            姓名 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="name"
            {...register('name', { required: '姓名不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          />
          {errors.name && <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>}
        </div>

        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
            职位 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="title"
            {...register('title', { required: '职位不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.title ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          />
          {errors.title && <p className="mt-1 text-xs text-red-500">{errors.title.message}</p>}
        </div>

        <div>
          <label htmlFor="avatar" className="block text-sm font-medium text-gray-700 mb-1">
            头像 <span className="text-red-500">*</span>
          </label>
          <div className="mt-1 flex items-center space-x-4">
            <span className="inline-block h-20 w-20 rounded-full overflow-hidden bg-gray-100">
              {avatarPreview ? (
                <img src={avatarPreview} alt="头像预览" className="h-full w-full object-cover" />
              ) : (
                <div className="h-full w-full flex items-center justify-center text-gray-400">
                  <FiUpload className="h-8 w-8" />
                </div>
              )}
            </span>
            <label
              htmlFor="avatar-upload"
              className="cursor-pointer bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out text-sm"
            >
              选择头像
            </label>
            <input
              id="avatar-upload"
              type="file"
              accept="image/*"
              {...register('avatar', { required: '头像不能为空' })}
              className="sr-only"
              onChange={handleAvatarChange}
            />
          </div>
          {errors.avatar && <p className="mt-1 text-xs text-red-500">{errors.avatar.message}</p>}
        </div>

        <div>
          <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
            部门 <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="department"
            {...register('department', { required: '部门不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.department ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
            placeholder="例如: 留学规划部"
          />
          {errors.department && <p className="mt-1 text-xs text-red-500">{errors.department.message}</p>}
        </div>

        <div>
          <label htmlFor="order" className="block text-sm font-medium text-gray-700 mb-1">
            排序 (数字越小越靠前) <span className="text-red-500">*</span>
          </label>
          <input
            type="number"
            id="order"
            {...register('order', { required: '排序不能为空', valueAsNumber: true, min: { value: 0, message: '排序值不能小于0' } })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.order ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
            defaultValue={0}
          />
          {errors.order && <p className="mt-1 text-xs text-red-500">{errors.order.message}</p>}
        </div>

        <div>
          <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
            个人简介 <span className="text-red-500">*</span>
          </label>
          <textarea
            id="bio"
            rows={5}
            {...register('bio', { required: '个人简介不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.bio ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
          />
          {errors.bio && <p className="mt-1 text-xs text-red-500">{errors.bio.message}</p>}
        </div>

        <div>
          <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
            状态 <span className="text-red-500">*</span>
          </label>
          <select
            id="status"
            {...register('status', { required: '状态不能为空' })}
            className={`mt-1 block w-full px-4 py-2 border ${errors.status ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm`}
            defaultValue="active"
          >
            <option value="active">启用</option>
            <option value="inactive">禁用</option>
          </select>
          {errors.status && <p className="mt-1 text-xs text-red-500">{errors.status.message}</p>}
        </div>

        <div className="flex justify-end pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2.5 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <FiSave className="mr-2" />
            )}
            {isSubmitting ? '正在添加...' : '添加成员'}
          </button>
        </div>
      </form>
    </div>
  );
}