'use client';

import { useState } from 'react';
import Link from 'next/link';
import toast, { Toaster } from 'react-hot-toast';
import { api } from '@/utils/api';
import { useEffect } from 'react';

// 团队成员数据类型
interface TeamMember {
  id: number;
  name: string;
  title: string;
  avatar: string;
  department: string;
  order: number;
  bio: string;
  status: 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export default function TeamManagementPage() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTeamMembers = async () => {
      setLoading(true);
      try {
        const response = await api.get<TeamMember[]>('/team');
        setTeamMembers(response.data.sort((a, b) => a.order - b.order));
      } catch (error) {
        toast.error('获取团队成员列表失败');
        console.error('获取团队成员列表失败:', error);
      }
      setLoading(false);
    };
    fetchTeamMembers();
  }, []);
  const [isDeleting, setIsDeleting] = useState(false);
  const [memberToDelete, setMemberToDelete] = useState<number | null>(null);

  // 处理删除团队成员
  const handleDeleteMember = async (id: number) => {
    setIsDeleting(true);
    setMemberToDelete(id);
    
    try {
      await api.delete(`/team/${id}`);
      setTeamMembers(teamMembers.filter(member => member.id !== id));
      toast.success('团队成员已成功删除');
    } catch (error) {
      console.error('删除团队成员失败:', error);
      toast.error('删除团队成员失败，请重试');
    } finally {
      setIsDeleting(false);
      setMemberToDelete(null);
    }
  };

  // 处理更改团队成员状态
  const handleToggleStatus = async (id: number, currentStatus: string) => {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    
    try {
      await api.patch(`/team/${id}`, { status: newStatus });
      setTeamMembers(teamMembers.map(member => 
        member.id === id ? { ...member, status: newStatus as 'active' | 'inactive' } : member
      ));
      toast.success(`团队成员状态已更改为${newStatus === 'active' ? '启用' : '禁用'}`);
    } catch (error) {
      console.error('更改团队成员状态失败:', error);
      toast.error('更改团队成员状态失败，请重试');
    }
  };

  // 处理团队成员排序
  const handleMoveOrder = async (id: number, direction: 'up' | 'down') => {
    const currentIndex = teamMembers.findIndex(member => member.id === id);
    if (currentIndex === -1) return;
    
    // 如果是向上移动且已经是第一个，或向下移动且已经是最后一个，则不执行操作
    if ((direction === 'up' && currentIndex === 0) || 
        (direction === 'down' && currentIndex === teamMembers.length - 1)) {
      return;
    }
    
    const newTeamMembers = [...teamMembers];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    
    // 交换位置
    [newTeamMembers[currentIndex], newTeamMembers[targetIndex]] = 
    [newTeamMembers[targetIndex], newTeamMembers[currentIndex]];
    
    // 更新排序值
    newTeamMembers[currentIndex].order = currentIndex + 1;
    newTeamMembers[targetIndex].order = targetIndex + 1;
    
    try {
      const updatedOrderData = newTeamMembers.map(m => ({ id: m.id, order: m.order }));
      await api.patch('/team/reorder', { members: updatedOrderData });
      setTeamMembers(newTeamMembers.sort((a,b) => a.order - b.order));
      toast.success('团队成员排序已更新');
    } catch (error) {
      console.error('更新团队成员排序失败:', error);
      toast.error('更新团队成员排序失败，请重试');
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster position="top-center" />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">团队管理</h1>
        <Link href="/team/new">
          <button className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            添加团队成员
          </button>
        </Link>
      </div>
      
      {loading ? (
        <div className="text-center py-10">
          <p className="text-lg text-gray-500">正在加载团队成员...</p>
        </div>
      ) : teamMembers.length === 0 ? (
        <div className="text-center py-10 bg-white rounded-lg shadow">
          <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p className="mt-4 text-lg text-gray-500">暂无团队成员。</p>
          <p className="text-sm text-gray-400">点击"添加团队成员"按钮来创建新的成员。</p>
        </div>
      ) : (
        <div className="bg-white rounded-xl shadow-xl overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">排序</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">头像</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职位</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">更新日期</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {teamMembers.map((member) => (
                <tr key={member.id}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex items-center space-x-2">
                      <span>{member.order}</span>
                      <div className="flex flex-col">
                        <button 
                          onClick={() => handleMoveOrder(member.id, 'up')}
                          className="text-gray-500 hover:text-gray-700 focus:outline-none"
                          disabled={teamMembers.indexOf(member) === 0}
                        >
                          ▲
                        </button>
                        <button 
                          onClick={() => handleMoveOrder(member.id, 'down')}
                          className="text-gray-500 hover:text-gray-700 focus:outline-none"
                          disabled={teamMembers.indexOf(member) === teamMembers.length - 1}
                        >
                          ▼
                        </button>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="h-10 w-10 rounded-full bg-gray-200 overflow-hidden">
                      {/* 实际项目中应该使用真实头像 */}
                      <div className="h-full w-full flex items-center justify-center text-gray-500 text-xs">
                        头像
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{member.name}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{member.title}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{member.department}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span 
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${member.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                    >
                      {member.status === 'active' ? '启用' : '禁用'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{member.updatedAt}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleToggleStatus(member.id, member.status)}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        {member.status === 'active' ? '禁用' : '启用'}
                      </button>
                      <Link 
                        href={`/team/edit/${member.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        编辑
                      </Link>
                      <button
                        onClick={() => handleDeleteMember(member.id)}
                        disabled={isDeleting && memberToDelete === member.id}
                        className="text-red-600 hover:text-red-900 disabled:text-gray-400"
                      >
                        {isDeleting && memberToDelete === member.id ? '删除中...' : '删除'}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}