'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import toast, { Toaster } from 'react-hot-toast';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FiUserPlus, FiArrowLeft, FiSave, FiLoader } from 'react-icons/fi';
import { api } from '@/utils/api';

interface UserFormData {
  username: string;
  name: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  password?: string; // 密码可选，后端可以生成默认密码
}

const NewUserPage: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<UserFormData>();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const onSubmit: SubmitHandler<UserFormData> = async (data) => {
    setIsSubmitting(true);
    try {
      await api.post('/users', data);
      toast.success('用户添加成功！');
      reset();
      // 可选：延迟一段时间后跳转，给用户查看toast提示的时间
      setTimeout(() => {
        router.push('/users');
      }, 1500);
    } catch (error: any) {
      if (error.response && error.response.data && error.response.data.message) {
        toast.error(`添加用户失败: ${error.response.data.message}`);
      } else {
        toast.error('添加用户失败，请稍后再试。');
      }
      console.error('添加用户失败:', error);
    }
    setIsSubmitting(false);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster position="top-center" />
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center">
          <FiUserPlus className="mr-3 text-indigo-600" /> 添加新用户
        </h1>
        <Link href="/users">
          <button className="bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center">
            <FiArrowLeft className="mr-2" /> 返回用户列表
          </button>
        </Link>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="bg-white p-8 rounded-lg shadow-xl space-y-6">
        <div>
          <label htmlFor="username" className="block text-sm font-medium text-gray-700 mb-1">用户名</label>
          <input
            id="username"
            type="text"
            {...register('username', { required: '用户名为必填项' })}
            className={`w-full px-4 py-2 border ${errors.username ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}
          />
          {errors.username && <p className="mt-1 text-xs text-red-500">{errors.username.message}</p>}
        </div>

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">姓名</label>
          <input
            id="name"
            type="text"
            {...register('name', { required: '姓名为必填项' })}
            className={`w-full px-4 py-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}
          />
          {errors.name && <p className="mt-1 text-xs text-red-500">{errors.name.message}</p>}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
          <input
            id="email"
            type="email"
            {...register('email', {
              required: '邮箱为必填项',
              pattern: {
                value: /^\S+@\S+$/i,
                message: '请输入有效的邮箱地址'
              }
            })}
            className={`w-full px-4 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}
          />
          {errors.email && <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>}
        </div>

        <div>
          <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">角色</label>
          <select
            id="role"
            {...register('role', { required: '角色为必选项' })}
            className={`w-full px-4 py-2 border ${errors.role ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 bg-white`}
          >
            <option value="viewer">查看者</option>
            <option value="editor">编辑</option>
            <option value="admin">管理员</option>
          </select>
          {errors.role && <p className="mt-1 text-xs text-red-500">{errors.role.message}</p>}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">初始密码 (可选)</label>
          <input
            id="password"
            type="password"
            {...register('password', { minLength: { value: 6, message: '密码至少需要6个字符' } })}
            className={`w-full px-4 py-2 border ${errors.password ? 'border-red-500' : 'border-gray-300'} rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500`}
            placeholder="留空则由系统生成"
          />
          {errors.password && <p className="mt-1 text-xs text-red-500">{errors.password.message}</p>}
        </div>

        <div className="flex justify-end pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <FiLoader className="animate-spin mr-2" />
            ) : (
              <FiSave className="mr-2" />
            )}
            {isSubmitting ? '正在保存...' : '保存用户'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default NewUserPage;