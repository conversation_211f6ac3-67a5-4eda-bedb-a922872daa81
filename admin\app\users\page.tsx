'use client';

import React, { useState, useEffect } from 'react';
import toast, { Toaster } from 'react-hot-toast';
import Link from 'next/link';
import { FiUsers, FiEdit, FiToggleLeft, FiToggleRight, FiPlusCircle, FiSearch, FiFilter, FiXCircle } from 'react-icons/fi';
import { api } from '@/utils/api';

// 用户类型定义
interface User {
  id: number;
  username: string;
  name: string;
  email: string;
  role: 'admin' | 'editor' | 'viewer';
  lastLogin: string;
  status: 'active' | 'inactive';
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

    const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('');

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await api.get<User[]>('/users', {
        params: {
          search: searchTerm,
          role: filterRole,
        },
      });
      setUsers(response.data);
    } catch (error) {
      toast.error('获取用户列表失败');
      console.error('获取用户列表失败:', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    const delayDebounceFn = setTimeout(() => {
      fetchUsers();
    }, 300);
    return () => clearTimeout(delayDebounceFn);
  }, [searchTerm, filterRole]);

  // 更改用户状态
  const handleToggleStatus = async (id: number, currentStatus: 'active' | 'inactive') => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      
      await api.patch(`/users/${id}/status`, { status: newStatus });
      setUsers(users.map(user => 
        user.id === id ? { ...user, status: newStatus } : user
      ));
      toast.success(`用户状态已更新为${newStatus === 'active' ? '激活' : '禁用'}`);
    } catch (error) {
      toast.error('更新失败，请重试');
      console.error('更新用户状态失败:', error);
    }
  };

  const handleDeleteUser = async (id: number) => {
    if (window.confirm('确定要删除此用户吗？此操作不可恢复。')) {
      try {
        await api.delete(`/users/${id}`);
        setUsers(users.filter(user => user.id !== id));
        toast.success('用户删除成功');
      } catch (error) {
        toast.error('删除用户失败');
        console.error('删除用户失败:', error);
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Toaster position="top-center" />
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800 flex items-center">
          <FiUsers className="mr-3 text-indigo-600" /> 用户管理
        </h1>
        <Link href="/users/new">
          <button className="bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out flex items-center">
            <FiPlusCircle className="mr-2" /> 添加用户
          </button>
        </Link>
      </div>

      {/* Filters */}
      <div className="mb-6 p-4 bg-white rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-gray-700">搜索用户</label>
            <div className="mt-1 relative rounded-md shadow-sm">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FiSearch className="text-gray-400" />
              </div>
              <input
                type="text"
                id="search"
                className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md py-2"
                placeholder="按用户名、姓名或邮箱搜索..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div>
            <label htmlFor="roleFilter" className="block text-sm font-medium text-gray-700">按角色筛选</label>
            <select
              id="roleFilter"
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
              value={filterRole}
              onChange={(e) => setFilterRole(e.target.value)}
            >
              <option value="">所有角色</option>
              <option value="admin">管理员</option>
              <option value="editor">编辑</option>
              <option value="viewer">查看者</option>
            </select>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="text-center py-10">
          <p className="text-lg text-gray-500">正在加载用户列表...</p>
        </div>
      ) : users.length === 0 ? (
        <div className="text-center py-10 bg-white rounded-lg shadow">
          <FiXCircle className="mx-auto text-gray-400 text-5xl mb-4" />
          <p className="text-lg text-gray-500">未找到用户。</p>
          <p className="text-sm text-gray-400">尝试调整搜索词或筛选条件，或<Link href="/users/new" className="text-indigo-600 hover:underline">添加新用户</Link>。</p>
        </div>
      ) : (
        <div className="bg-white shadow-xl rounded-lg overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最后登录</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider text-center">操作</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {users.map((user) => (
                <tr key={user.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.role === 'admin' ? 'bg-purple-100 text-purple-800' : user.role === 'editor' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                      {user.role === 'admin' ? '管理员' : user.role === 'editor' ? '编辑' : '查看者'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {user.lastLogin}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${user.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                      {user.status === 'active' ? '激活' : '禁用'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-center">
                    <Link href={`/users/edit/${user.id}`} className="text-indigo-600 hover:text-indigo-800 transition duration-150 p-1 rounded-full hover:bg-indigo-100 inline-block mr-2" title="编辑用户">
                      <FiEdit size={18} />
                    </Link>
                    <button 
                      onClick={() => handleToggleStatus(user.id, user.status)}
                      className={`p-1 rounded-full transition duration-150 ${user.status === 'active' ? 'text-red-600 hover:text-red-800 hover:bg-red-100' : 'text-green-600 hover:text-green-800 hover:bg-green-100'}`}
                      title={user.status === 'active' ? '禁用用户' : '激活用户'}
                    >
                      {user.status === 'active' ? <FiToggleRight size={20} /> : <FiToggleLeft size={20} />}
                    </button>
                    <button 
                      onClick={() => handleDeleteUser(user.id)}
                      className="text-red-600 hover:text-red-800 transition duration-150 p-1 rounded-full hover:bg-red-100 ml-2"
                      title="删除用户"
                    >
                      <FiTrash2 size={18} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}