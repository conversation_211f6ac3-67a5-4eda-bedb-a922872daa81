import axios from 'axios';

// 创建一个axios实例，设置基础URL和默认配置
const api = axios.create({
  baseURL: process.env.API_BASE_URL || 'http://localhost:3001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器，添加认证令牌
api.interceptors.request.use(
  (config) => {
    // 从本地存储获取令牌
    const token = typeof window !== 'undefined' ? localStorage.getItem('adminToken') : null;

    // 如果有令牌，添加到请求头
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器，处理常见错误
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 处理401未授权错误
    if (error.response && error.response.status === 401) {
      // 清除本地存储的令牌
      if (typeof window !== 'undefined') {
        localStorage.removeItem('adminToken');
        localStorage.removeItem('adminUser');

        // 重定向到登录页
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

export default api;