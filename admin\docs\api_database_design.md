# CMS 系统 API 接口与数据库设计

本文档概述了上海留学顾问CMS系统的API接口设计和数据库表结构设计，旨在为前后端开发提供统一的规范和参考。

## 1. API 接口设计

CMS系统将采用RESTful API风格。所有API请求和响应均使用JSON格式。API基础路径为 `/api`。

### 1.1 认证 (Auth)

*   **POST `/api/auth/login`**: 用户登录
    *   请求体: `{ username, password }`
    *   响应体 (成功): `{ token, user: { id, name, email, role, avatar } }`
    *   响应体 (失败): `{ error: "错误信息" }`
*   **POST `/api/auth/logout`**: 用户登出 (通常由前端清除token实现，后端可选择性实现session失效)
*   **GET `/api/auth/me`**: 获取当前用户信息 (通过Token验证)
    *   响应体: `{ user: { id, name, email, role, avatar, phone, position, bio } }`

### 1.2 用户管理 (Users)

*   **GET `/api/users`**: 获取用户列表 (支持分页、搜索、排序)
    *   查询参数: `page`, `limit`, `search`, `sortBy`, `sortOrder`
    *   响应体: `{ users: User[], total: number }`
*   **GET `/api/users/{id}`**: 获取单个用户信息
    *   响应体: `User`
*   **POST `/api/users`**: 创建新用户
    *   请求体: `CreateUserInput` (包含name, email, password, role, status等)
    *   响应体: `User`
*   **PUT `/api/users/{id}`**: 更新用户信息
    *   请求体: `UpdateUserInput` (可更新name, email, role, status, avatar, phone, position, bio等)
    *   响应体: `User`
*   **DELETE `/api/users/{id}`**: 删除用户
    *   响应体: `{ message: "用户删除成功" }`
*   **PUT `/api/users/profile`**: 更新当前登录用户个人资料 (由 `AuthContext` 使用)
    *   请求体: `UpdateProfileInput` (可更新name, email, avatar, phone, position, bio)
    *   响应体: `User`
*   **PUT `/api/users/password`**: 修改当前登录用户密码 (由 `AuthContext` 使用)
    *   请求体: `{ currentPassword, newPassword }`
    *   响应体: `{ message: "密码修改成功" }`

### 1.3 文章管理 (Articles)

*   **GET `/api/articles`**: 获取文章列表 (支持分页、搜索、排序、按分类/状态筛选)
    *   查询参数: `page`, `limit`, `search`, `sortBy`, `sortOrder`, `category`, `status`
    *   响应体: `{ articles: Article[], total: number }`
*   **GET `/api/articles/{id}`**: 获取单篇文章详情
    *   响应体: `Article`
*   **POST `/api/articles`**: 创建新文章
    *   请求体: `CreateArticleInput` (包含title, slug, summary, content, categoryId, status, authorId, coverImageUrl等)
    *   响应体: `Article`
*   **PUT `/api/articles/{id}`**: 更新文章
    *   请求体: `UpdateArticleInput`
    *   响应体: `Article`
*   **DELETE `/api/articles/{id}`**: 删除文章
    *   响应体: `{ message: "文章删除成功" }`

### 1.4 服务管理 (Services)

*   **GET `/api/services`**: 获取服务列表 (支持分页、搜索、排序、按分类/状态筛选)
    *   响应体: `{ services: Service[], total: number }`
*   **GET `/api/services/{id}`**: 获取单个服务详情
    *   响应体: `Service`
*   **POST `/api/services`**: 创建新服务
    *   请求体: `CreateServiceInput` (包含title, slug, category, summary, description, icon, sortOrder, status等)
    *   响应体: `Service`
*   **PUT `/api/services/{id}`**: 更新服务
    *   请求体: `UpdateServiceInput`
    *   响应体: `Service`
*   **DELETE `/api/services/{id}`**: 删除服务
    *   响应体: `{ message: "服务删除成功" }`

### 1.5 案例管理 (Cases)

*   **GET `/api/cases`**: 获取案例列表 (支持分页、搜索、排序、按分类/状态/精选筛选)
    *   响应体: `{ cases: Case[], total: number }`
*   **GET `/api/cases/{id}`**: 获取单个案例详情
    *   响应体: `Case`
*   **POST `/api/cases`**: 创建新案例
    *   请求体: `CreateCaseInput` (包含title, slug, category, thumbnail, summary, clientInfo, details, result, status, isFeatured等)
    *   响应体: `Case`
*   **PUT `/api/cases/{id}`**: 更新案例
    *   请求体: `UpdateCaseInput`
    *   响应体: `Case`
*   **DELETE `/api/cases/{id}`**: 删除案例
    *   响应体: `{ message: "案例删除成功" }`

### 1.6 Banner管理 (Banners)

*   **GET `/api/banners`**: 获取Banner列表 (支持分页、排序、按状态筛选)
    *   响应体: `{ banners: Banner[], total: number }`
*   **GET `/api/banners/{id}`**: 获取单个Banner详情
    *   响应体: `Banner`
*   **POST `/api/banners`**: 创建新Banner
    *   请求体: `CreateBannerInput` (包含title, imageUrl, linkUrl, sortOrder, status, description, placement等)
    *   响应体: `Banner`
*   **PUT `/api/banners/{id}`**: 更新Banner
    *   请求体: `UpdateBannerInput`
    *   响应体: `Banner`
*   **DELETE `/api/banners/{id}`**: 删除Banner
    *   响应体: `{ message: "Banner删除成功" }`

### 1.7 FAQ管理 (Faqs)

*   **GET `/api/faqs`**: 获取FAQ列表 (支持分页、搜索、排序、按分类/状态筛选)
    *   响应体: `{ faqs: Faq[], total: number }`
*   **GET `/api/faqs/{id}`**: 获取单个FAQ详情
    *   响应体: `Faq`
*   **POST `/api/faqs`**: 创建新FAQ
    *   请求体: `CreateFaqInput` (包含question, answer, category, sortOrder, status等)
    *   响应体: `Faq`
*   **PUT `/api/faqs/{id}`**: 更新FAQ
    *   请求体: `UpdateFaqInput`
    *   响应体: `Faq`
*   **DELETE `/api/faqs/{id}`**: 删除FAQ
    *   响应体: `{ message: "FAQ删除成功" }`

### 1.8 团队管理 (Team)

*   **GET `/api/team`**: 获取团队成员列表 (支持分页、排序、按状态筛选)
    *   响应体: `{ teamMembers: TeamMember[], total: number }`
*   **GET `/api/team/{id}`**: 获取单个团队成员详情
    *   响应体: `TeamMember`
*   **POST `/api/team`**: 创建新团队成员
    *   请求体: `CreateTeamMemberInput` (包含name, position, bio, avatarUrl, socialLinks, sortOrder, status等)
    *   响应体: `TeamMember`
*   **PUT `/api/team/{id}`**: 更新团队成员
    *   请求体: `UpdateTeamMemberInput`
    *   响应体: `TeamMember`
*   **DELETE `/api/team/{id}`**: 删除团队成员
    *   响应体: `{ message: "团队成员删除成功" }`

### 1.9 咨询管理 (Inquiries)

*   **GET `/api/inquiries`**: 获取咨询列表 (支持分页、搜索、排序、按状态/服务类型筛选)
    *   响应体: `{ inquiries: Inquiry[], total: number }`
*   **GET `/api/inquiries/{id}`**: 获取单个咨询详情
    *   响应体: `Inquiry`
*   **PUT `/api/inquiries/{id}`**: 更新咨询状态或回复
    *   请求体: `UpdateInquiryInput` (包含status, replyContent等)
    *   响应体: `Inquiry`
*   **DELETE `/api/inquiries/{id}`**: 删除咨询
    *   响应体: `{ message: "咨询删除成功" }`

### 1.10 系统设置 (Settings)

*   **GET `/api/settings`**: 获取系统设置
    *   响应体: `SystemSettings`
*   **PUT `/api/settings`**: 更新系统设置
    *   请求体: `SystemSettings`
    *   响应体: `SystemSettings`

### 1.11 系统日志 (Logs)

*   **GET `/api/logs`**: 获取系统日志列表 (支持分页、搜索、按模块/级别/时间范围筛选)
    *   查询参数: `page`, `limit`, `search`, `module`, `level`, `startDate`, `endDate`
    *   响应体: `{ logs: LogEntry[], total: number }`

### 1.12 仪表盘 (Dashboard)

*   **GET `/api/dashboard/stats`**: 获取仪表盘统计数据
    *   响应体: `{ totalVisits, totalInquiries, totalArticles, totalUsers, ... }`
*   **GET `/api/dashboard/visit-trend`**: 获取访问量趋势数据
    *   查询参数: `period` (e.g., '7days', '30days', 'year')
    *   响应体: `[{ date, count }]`
*   **GET `/api/dashboard/inquiry-trend`**: 获取咨询量趋势数据
    *   查询参数: `period`
    *   响应体: `[{ date, count }]`
*   **GET `/api/dashboard/service-distribution`**: 获取服务咨询分布数据
    *   响应体: `[{ serviceName, count }]`
*   **GET `/api/dashboard/recent-inquiries`**: 获取最近咨询列表
    *   查询参数: `limit`
    *   响应体: `Inquiry[]`

## 2. 数据库设计

以下是建议的数据库表结构。具体实现时，可以根据选择的数据库系统（如PostgreSQL, MySQL, SQLite等）进行调整。

### 2.1 `users` 表 (用户信息)

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    role VARCHAR(50) NOT NULL DEFAULT 'editor', -- e.g., 'admin', 'editor', 'viewer'
    avatar_url TEXT,
    phone VARCHAR(50),
    position VARCHAR(255),
    bio TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- e.g., 'active', 'inactive', 'pending'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 2.2 `articles` 表 (文章信息)

```sql
CREATE TABLE articles (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    summary TEXT,
    content TEXT NOT NULL,
    cover_image_url TEXT,
    author_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    category_id INTEGER REFERENCES article_categories(id) ON DELETE SET NULL, -- (需要 article_categories 表)
    status VARCHAR(50) NOT NULL DEFAULT 'draft', -- e.g., 'draft', 'published', 'archived'
    published_at TIMESTAMP WITH TIME ZONE,
    views INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_articles_slug ON articles(slug);
CREATE INDEX idx_articles_status ON articles(status);
CREATE INDEX idx_articles_author_id ON articles(author_id);
CREATE INDEX idx_articles_category_id ON articles(category_id);
```

### 2.3 `article_categories` 表 (文章分类)

```sql
CREATE TABLE article_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 2.4 `services` 表 (服务信息)

```sql
CREATE TABLE services (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    category VARCHAR(100), -- 可以规范化到单独的 services_categories 表
    summary TEXT,
    description TEXT NOT NULL,
    icon_url TEXT, -- 或 icon_class VARCHAR(100)
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'draft', -- e.g., 'draft', 'published'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_services_slug ON services(slug);
CREATE INDEX idx_services_status ON services(status);
```

### 2.5 `cases` 表 (案例信息)

```sql
CREATE TABLE cases (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    category_id INTEGER REFERENCES case_categories(id) ON DELETE SET NULL, -- (需要 case_categories 表)
    thumbnail_url TEXT,
    summary TEXT,
    client_info TEXT, -- 可以是JSON或独立字段
    details TEXT NOT NULL,
    result TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'draft', -- e.g., 'draft', 'published'
    is_featured BOOLEAN DEFAULT FALSE,
    case_date DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_cases_slug ON cases(slug);
CREATE INDEX idx_cases_status ON cases(status);
CREATE INDEX idx_cases_is_featured ON cases(is_featured);
```

### 2.6 `case_categories` 表 (案例分类)

```sql
CREATE TABLE case_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 2.7 `banners` 表 (Banner信息)

```sql
CREATE TABLE banners (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    image_url TEXT NOT NULL,
    link_url TEXT,
    description TEXT,
    placement VARCHAR(100), -- e.g., 'homepage_top', 'sidebar_ad'
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'draft', -- e.g., 'draft', 'active', 'inactive'
    start_date TIMESTAMP WITH TIME ZONE,
    end_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_banners_status ON banners(status);
CREATE INDEX idx_banners_placement ON banners(placement);
```

### 2.8 `faqs` 表 (FAQ信息)

```sql
CREATE TABLE faqs (
    id SERIAL PRIMARY KEY,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category_id INTEGER REFERENCES faq_categories(id) ON DELETE SET NULL, -- (需要 faq_categories 表)
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'draft', -- e.g., 'draft', 'published'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_faqs_status ON faqs(status);
```

### 2.9 `faq_categories` 表 (FAQ分类)

```sql
CREATE TABLE faq_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 2.10 `team_members` 表 (团队成员信息)

```sql
CREATE TABLE team_members (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    position VARCHAR(255),
    bio TEXT,
    avatar_url TEXT,
    social_links JSONB, -- e.g., { "linkedin": "url", "twitter": "url" }
    sort_order INTEGER DEFAULT 0,
    status VARCHAR(50) NOT NULL DEFAULT 'active', -- e.g., 'active', 'inactive'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_team_members_status ON team_members(status);
```

### 2.11 `inquiries` 表 (客户咨询信息)

```sql
CREATE TABLE inquiries (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    service_id INTEGER REFERENCES services(id) ON DELETE SET NULL, -- 或 service_name VARCHAR(255)
    message TEXT NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- e.g., 'pending', 'replied', 'closed'
    reply_content TEXT,
    replied_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    replied_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_inquiries_status ON inquiries(status);
```

### 2.12 `system_settings` 表 (系统设置)

存储为键值对或单个JSON对象。为简单起见，这里使用单个JSON对象存储。

```sql
CREATE TABLE system_settings (
    id INTEGER PRIMARY KEY DEFAULT 1, -- 仅一行记录
    settings JSONB NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT single_row_check CHECK (id = 1)
);
-- 示例 settings JSON:
-- {
--   "siteName": "上海留学顾问",
--   "siteDescription": "专业的留学与职业规划咨询服务",
--   "contactEmail": "<EMAIL>",
--   "contactPhone": "021-12345678",
--   "address": "上海市浦东新区张江高科技园区博云路2号",
--   "icp": "沪ICP备12345678号",
--   "seoKeywords": "留学,职业规划",
--   "seoDescription": "上海留学顾问提供专业的留学申请...",
--   "logoUrl": "/images/logo.png",
--   "faviconUrl": "/favicon.ico",
--   "footerCopyright": "© 2023 上海留学顾问 版权所有",
--   "socialMedia": {
--     "weixin": "slhgw_weixin",
--     "weibo": "slhgw_weibo",
--     "zhihu": "slhgw_zhihu"
--   }
-- }
```

### 2.13 `activity_logs` 表 (系统操作日志)

```sql
CREATE TABLE activity_logs (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL, -- 操作用户，系统操作可为NULL
    action VARCHAR(255) NOT NULL, -- e.g., 'USER_LOGIN', 'CREATE_ARTICLE', 'UPDATE_SETTINGS'
    module VARCHAR(100), -- e.g., 'auth', 'content', 'user', 'system'
    level VARCHAR(50) NOT NULL DEFAULT 'info', -- e.g., 'info', 'warning', 'error'
    ip_address VARCHAR(100),
    user_agent TEXT,
    details JSONB, -- 详细信息，如修改前后的数据
    target_resource_id VARCHAR(255), -- 相关资源ID，如文章ID，用户ID
    target_resource_type VARCHAR(100), -- 相关资源类型，如 'article', 'user'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_action ON activity_logs(action);
CREATE INDEX idx_activity_logs_module ON activity_logs(module);
CREATE INDEX idx_activity_logs_level ON activity_logs(level);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);
```

## 3. 数据类型说明 (示例)

以下是一些在API请求/响应体中可能用到的数据结构（TypeScript接口形式）。

```typescript
// User.ts
interface User {
  id: number;
  username: string;
  email: string;
  name?: string;
  role: 'admin' | 'editor' | 'viewer';
  avatarUrl?: string;
  phone?: string;
  position?: string;
  bio?: string;
  status: 'active' | 'inactive' | 'pending';
  createdAt: string; // ISO Date string
  updatedAt: string; // ISO Date string
}

// Article.ts
interface Article {
  id: number;
  title: string;
  slug: string;
  summary?: string;
  content: string;
  coverImageUrl?: string;
  author?: User; // 或 authorId: number
  category?: ArticleCategory; // 或 categoryId: number
  status: 'draft' | 'published' | 'archived';
  publishedAt?: string; // ISO Date string
  views?: number;
  createdAt: string;
  updatedAt: string;
}

// ... 其他模块的数据类型定义
```

## 4. 后续步骤

1.  **后端开发**：根据此API设计实现后端服务，连接并操作数据库。
2.  **前端开发**：修改CMS前端页面，使用 `admin/app/utils/api.ts` 中的axios实例对接已实现的API接口，替换所有模拟数据和操作。
3.  **数据库迁移**：使用上述DDL语句在目标数据库中创建表结构。

此文档将作为开发过程中的重要参考，如有变更，应及时更新。