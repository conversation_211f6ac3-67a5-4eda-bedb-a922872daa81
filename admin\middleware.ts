import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { jwtVerify } from 'jose';

// 不需要认证的路径
const publicPaths = [
  '/login',
  '/api/auth'
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // 检查是否是公开路径
  if (publicPaths.some(path => pathname.startsWith(path))) {
    return NextResponse.next();
  }
  
  // 获取认证令牌
  const token = request.cookies.get('adminToken')?.value;
  
  // 如果没有令牌，重定向到登录页面
  if (!token) {
    const url = new URL('/login', request.url);
    return NextResponse.redirect(url);
  }
  
  try {
    // 验证令牌
    const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'your-jwt-secret');
    await jwtVerify(token, secret);
    return NextResponse.next();
  } catch (error) {
    // 令牌无效，重定向到登录页面
    const url = new URL('/login', request.url);
    return NextResponse.redirect(url);
  }
}

// 配置中间件匹配的路径
export const config = {
  matcher: [
    /*
     * 匹配所有路径，除了：
     * - api 路由 (除了 /api/auth)
     * - 静态文件 (_next/static, favicon.ico 等)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};