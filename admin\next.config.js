/** @type {import('next').NextConfig} */
const nextConfig = {
  env: {
    NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'http://localhost:3002',
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || 'your-secret-key',
    JWT_SECRET: process.env.JWT_SECRET || 'your-jwt-secret',
    API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:3001/api',
  },
  // 移除rewrites，让管理系统独立运行
};

module.exports = nextConfig;
