{"version": 3, "sources": ["../../../../src/build/babel/plugins/optimize-hook-destructuring.ts"], "names": ["isHook", "isBuiltInHook", "types", "t", "visitor", "CallExpression", "path", "state", "onlyBuiltIns", "opts", "libs", "lib", "concat", "isVariableDeclarator", "parent", "isArrayPattern", "id", "<PERSON><PERSON><PERSON>", "node", "callee", "name", "binding", "scope", "getBinding", "kind", "specifier", "source", "value", "some", "test", "objectPattern", "elements", "reduce", "patterns", "element", "i", "objectProperty", "numericLiteral", "Program", "traverse"], "mappings": ";;;;+BAYA;;;eAAA;;;AAPA,sCAAsC;AACtC,MAAMA,SAAS;AAEf,sDAAsD;AACtD,MAAMC,gBACJ;AAEa,SAAf,SAAyB,EACvBC,OAAOC,CAAC,EAGT;IACC,MAAMC,UAAU;QACdC,gBAAeC,IAAyC,EAAEC,KAAU;YAClE,MAAMC,eAAeD,MAAME,IAAI,CAACD,YAAY;YAE5C,+EAA+E;YAC/E,MAAME,OACJH,MAAME,IAAI,CAACE,GAAG,IACbJ,CAAAA,MAAME,IAAI,CAACE,GAAG,KAAK,OAChB;gBAAC;gBAAS;aAAe,GACzB,EAAE,CAACC,MAAM,CAACL,MAAME,IAAI,CAACE,GAAG,CAAA;YAE9B,uEAAuE;YACvE,IAAI,CAACR,EAAEU,oBAAoB,CAACP,KAAKQ,MAAM,GAAG;YAE1C,wEAAwE;YACxE,IAAI,CAACX,EAAEY,cAAc,CAACT,KAAKQ,MAAM,CAACE,EAAE,GAAG;YAEvC,4CAA4C;YAC5C,MAAMC,WAAW,AAACX,KAAKY,IAAI,CAACC,MAAM,CAA2BC,IAAI;YAEjE,IAAIV,MAAM;gBACR,MAAMW,UAAUf,KAAKgB,KAAK,CAACC,UAAU,CAACN;gBACtC,gBAAgB;gBAChB,IAAI,CAACI,WAAWA,QAAQG,IAAI,KAAK,UAAU;gBAE3C,MAAMC,YAAY,AAACJ,QAAQf,IAAI,CAACQ,MAAM,CACnCY,MAAM,CAACC,KAAK;gBACf,cAAc;gBACd,IAAI,CAACjB,KAAKkB,IAAI,CAAC,CAACjB,MAAaA,QAAQc,YAAY;YACnD;YAEA,6DAA6D;YAC7D,IAAI,CAAC,AAACjB,CAAAA,eAAeP,gBAAgBD,MAAK,EAAG6B,IAAI,CAACZ,WAAW;YAE7DX,KAAKQ,MAAM,CAACE,EAAE,GAAGb,EAAE2B,aAAa,CAC9BxB,KAAKQ,MAAM,CAACE,EAAE,CAACe,QAAQ,CAACC,MAAM,CAC5B,CAACC,UAAUC,SAASC;gBAClB,IAAID,YAAY,MAAM;oBACpB,OAAOD;gBACT;gBAEA,OAAOA,SAASrB,MAAM,CACpBT,EAAEiC,cAAc,CACdjC,EAAEkC,cAAc,CAACF,IACjB,iBAAiB;gBACjBD;YAMN,GACA,EAAE;QAGR;IACF;IAEA,OAAO;QACLd,MAAM;QACNhB,SAAS;YACP,kFAAkF;YAClFkC,SAAQhC,IAAI,EAAEC,KAAK;gBACjBD,KAAKiC,QAAQ,CAACnC,SAASG;YACzB;QACF;IACF;AACF"}