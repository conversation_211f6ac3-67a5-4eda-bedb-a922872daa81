{"version": 3, "sources": ["../../src/build/load-jsconfig.ts"], "names": ["loadJsConfig", "TSCONFIG_WARNED", "parseJsonFile", "filePath", "JSON5", "require", "contents", "readFileSync", "trim", "parse", "err", "isError", "codeFrameColumns", "codeFrame", "String", "start", "line", "lineNumber", "column", "columnNumber", "message", "highlightCode", "Error", "dir", "config", "jsConfig", "typeScriptPath", "deps", "hasNecessaryDependencies", "pkg", "file", "exportsRestrict", "resolved", "get", "tsConfigPath", "path", "join", "typescript", "tsconfigPath", "useTypeScript", "Boolean", "fs", "existsSync", "implicit<PERSON><PERSON><PERSON>l", "Log", "info", "ts", "Promise", "resolve", "tsConfig", "getTypeScriptConfiguration", "compilerOptions", "options", "dirname", "jsConfigPath", "resolvedBaseUrl", "baseUrl", "isImplicit"], "mappings": ";;;;+BA6CA;;;eAA8BA;;;6DA7Cb;4DACF;6DAEM;4CACsB;gEAEvB;0CACqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzC,IAAIC,kBAAkB;AAEtB,SAASC,cAAcC,QAAgB;IACrC,MAAMC,QAAQC,QAAQ;IACtB,MAAMC,WAAWC,IAAAA,gBAAY,EAACJ,UAAU;IAExC,6BAA6B;IAC7B,IAAIG,SAASE,IAAI,OAAO,IAAI;QAC1B,OAAO,CAAC;IACV;IAEA,IAAI;QACF,OAAOJ,MAAMK,KAAK,CAACH;IACrB,EAAE,OAAOI,KAAK;QACZ,IAAI,CAACC,IAAAA,gBAAO,EAACD,MAAM,MAAMA;QACzB,MAAM,EAAEE,gBAAgB,EAAE,GAAGP,QAAQ;QACrC,MAAMQ,YAAYD,iBAChBE,OAAOR,WACP;YACES,OAAO;gBACLC,MAAM,AAACN,IAAwCO,UAAU,IAAI;gBAC7DC,QAAQ,AAACR,IAA0CS,YAAY,IAAI;YACrE;QACF,GACA;YAAEC,SAASV,IAAIU,OAAO;YAAEC,eAAe;QAAK;QAE9C,MAAM,IAAIC,MAAM,CAAC,iBAAiB,EAAEnB,SAAS,IAAI,EAAEU,UAAU,CAAC;IAChE;AACF;AAQe,eAAeb,aAC5BuB,GAAW,EACXC,MAA0B;QA+CtBC;IAzCJ,IAAIC;IACJ,IAAI;QACF,MAAMC,OAAO,MAAMC,IAAAA,kDAAwB,EAACL,KAAK;YAC/C;gBACEM,KAAK;gBACLC,MAAM;gBACNC,iBAAiB;YACnB;SACD;QACDL,iBAAiBC,KAAKK,QAAQ,CAACC,GAAG,CAAC;IACrC,EAAE,OAAM,CAAC;IACT,MAAMC,eAAeC,aAAI,CAACC,IAAI,CAACb,KAAKC,OAAOa,UAAU,CAACC,YAAY;IAClE,MAAMC,gBAAgBC,QAAQd,kBAAkBe,WAAE,CAACC,UAAU,CAACR;IAE9D,IAAIS;IACJ,IAAIlB;IACJ,mCAAmC;IACnC,IAAIc,eAAe;QACjB,IACEf,OAAOa,UAAU,CAACC,YAAY,KAAK,mBACnCrC,oBAAoB,OACpB;YACAA,kBAAkB;YAClB2C,KAAIC,IAAI,CAAC,CAAC,qBAAqB,EAAErB,OAAOa,UAAU,CAACC,YAAY,CAAC,CAAC;QACnE;QAEA,MAAMQ,KAAM,MAAMC,QAAQC,OAAO,CAC/B3C,QAAQqB;QAEV,MAAMuB,WAAW,MAAMC,IAAAA,sDAA0B,EAACJ,IAAIZ,cAAc;QACpET,WAAW;YAAE0B,iBAAiBF,SAASG,OAAO;QAAC;QAC/CT,kBAAkBR,aAAI,CAACkB,OAAO,CAACnB;IACjC;IAEA,MAAMoB,eAAenB,aAAI,CAACC,IAAI,CAACb,KAAK;IACpC,IAAI,CAACgB,iBAAiBE,WAAE,CAACC,UAAU,CAACY,eAAe;QACjD7B,WAAWvB,cAAcoD;QACzBX,kBAAkBR,aAAI,CAACkB,OAAO,CAACC;IACjC;IAEA,IAAIC;IACJ,IAAI9B,6BAAAA,4BAAAA,SAAU0B,eAAe,qBAAzB1B,0BAA2B+B,OAAO,EAAE;QACtCD,kBAAkB;YAChBC,SAASrB,aAAI,CAACa,OAAO,CAACzB,KAAKE,SAAS0B,eAAe,CAACK,OAAO;YAC3DC,YAAY;QACd;IACF,OAAO;QACL,IAAId,iBAAiB;YACnBY,kBAAkB;gBAChBC,SAASb;gBACTc,YAAY;YACd;QACF;IACF;IAEA,OAAO;QACLlB;QACAd;QACA8B;IACF;AACF"}