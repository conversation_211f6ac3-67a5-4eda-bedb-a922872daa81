{"version": 3, "sources": ["../../../src/build/swc/options.ts"], "names": ["getParserOptions", "getJestSWCOptions", "getLoaderSWCOptions", "nextDistPath", "regeneratorRuntimePath", "require", "resolve", "filename", "jsConfig", "rest", "isTSFile", "endsWith", "isTypeScript", "enableDecorators", "Boolean", "compilerOptions", "experimentalDecorators", "syntax", "dynamicImport", "decorators", "importAssertions", "getBaseSWCOptions", "jest", "development", "hasReactRefresh", "globalWindow", "esm", "modularizeImports", "swcPlugins", "resolvedBaseUrl", "swcCacheDir", "serverComponents", "isReactServerLayer", "parserConfig", "paths", "emitDecoratorMetadata", "useDefineForClassFields", "plugins", "filter", "Array", "isArray", "map", "name", "options", "jsc", "baseUrl", "externalHelpers", "process", "versions", "pnp", "parser", "experimental", "keepImportAttributes", "emitAssertForImportAttributes", "cacheRoot", "transform", "hidden", "legacyDecorator", "decoratorMetadata", "react", "importSource", "jsxImportSource", "emotion", "runtime", "pragma", "pragmaFrag", "throwIfNamespace", "useBuiltins", "refresh", "optimizer", "simplify", "globals", "typeofs", "window", "envs", "NODE_ENV", "regenerator", "importPath", "sourceMaps", "undefined", "removeConsole", "reactRemoveProperties", "Object", "fromEntries", "entries", "mod", "config", "key", "value", "relay", "styledJsx", "getEmotionOptions", "styledComponents", "getStyledComponentsOptions", "serverActions", "enabled", "preferEsm", "styledComponentsConfig", "displayName", "getModuleOptions", "module", "type", "emotionConfig", "autoLabel", "sourcemap", "importMap", "labelFormat", "sourceMap", "isServer", "pagesDir", "baseOptions", "isNextDist", "test", "env", "targets", "node", "disableNextSsg", "disablePageConfig", "appDir", "isPageFile", "optimizeServerReact", "optimizePackageImports", "supportedBrowsers", "relativeFilePathFromRoot", "fontLoaders", "cjsRequireOptimizer", "packages", "transforms", "NextRequest", "NextResponse", "ImageResponse", "userAgentFromString", "userAgent", "optimize_use_state", "autoModularizeImports", "isDevelopment", "isServerCompiler", "length", "target"], "mappings": ";;;;;;;;;;;;;;;;IAegBA,gBAAgB;eAAhBA;;IAwPAC,iBAAiB;eAAjBA;;IA2DAC,mBAAmB;eAAnBA;;;AA1ThB,MAAMC,eACJ;AAEF,MAAMC,yBAAyBC,QAAQC,OAAO,CAC5C;AAGK,SAASN,iBAAiB,EAAEO,QAAQ,EAAEC,QAAQ,EAAE,GAAGC,MAAW;QAIjED;IAHF,MAAME,WAAWH,SAASI,QAAQ,CAAC;IACnC,MAAMC,eAAeF,YAAYH,SAASI,QAAQ,CAAC;IACnD,MAAME,mBAAmBC,QACvBN,6BAAAA,4BAAAA,SAAUO,eAAe,qBAAzBP,0BAA2BQ,sBAAsB;IAEnD,OAAO;QACL,GAAGP,IAAI;QACPQ,QAAQL,eAAe,eAAe;QACtCM,eAAe;QACfC,YAAYN;QACZ,qKAAqK;QACrK,CAACD,eAAe,QAAQ,MAAM,EAAE,CAACF;QACjCU,kBAAkB;IACpB;AACF;AAEA,SAASC,kBAAkB,EACzBd,QAAQ,EACRe,IAAI,EACJC,WAAW,EACXC,eAAe,EACfC,YAAY,EACZC,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfc,eAAe,EACfrB,QAAQ,EACRsB,WAAW,EACXC,gBAAgB,EAChBC,kBAAkB,EAgBnB;QAEexB,2BAEZA,4BAGAA,4BAGAA,4BAoCQA;IA7CV,MAAMyB,eAAejC,iBAAiB;QAAEO;QAAUC;IAAS;IAC3D,MAAM0B,QAAQ1B,6BAAAA,4BAAAA,SAAUO,eAAe,qBAAzBP,0BAA2B0B,KAAK;IAC9C,MAAMrB,mBAAmBC,QACvBN,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2BQ,sBAAsB;IAEnD,MAAMmB,wBAAwBrB,QAC5BN,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2B2B,qBAAqB;IAElD,MAAMC,0BAA0BtB,QAC9BN,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2B4B,uBAAuB;IAEpD,MAAMC,UAAU,AAACT,CAAAA,cAAc,EAAE,AAAD,EAC7BU,MAAM,CAACC,MAAMC,OAAO,EACpBC,GAAG,CAAC,CAAC,CAACC,MAAMC,QAAa,GAAK;YAACtC,QAAQC,OAAO,CAACoC;YAAOC;SAAQ;IAEjE,OAAO;QACLC,KAAK;YACH,GAAIf,mBAAmBK,QACnB;gBACEW,SAAShB,gBAAgBgB,OAAO;gBAChCX;YACF,IACA,CAAC,CAAC;YACNY,iBAAiB,CAACC,QAAQC,QAAQ,CAACC,GAAG,IAAI,CAAC3B;YAC3C4B,QAAQjB;YACRkB,cAAc;gBACZC,sBAAsB;gBACtBC,+BAA+B;gBAC/BhB;gBACAiB,WAAWxB;YACb;YACAyB,WAAW;gBACT,sIAAsI;gBACtI,GAAIjC,OACA;oBACEkC,QAAQ;wBACNlC,MAAM;oBACR;gBACF,IACA,CAAC,CAAC;gBACNmC,iBAAiB5C;gBACjB6C,mBAAmBvB;gBACnBC,yBAAyBA;gBACzBuB,OAAO;oBACLC,cACEpD,CAAAA,6BAAAA,6BAAAA,SAAUO,eAAe,qBAAzBP,2BAA2BqD,eAAe,KACzC9C,CAAAA,CAAAA,mCAAAA,gBAAiB+C,OAAO,KAAI,CAAC9B,qBAC1B,mBACA,OAAM;oBACZ+B,SAAS;oBACTC,QAAQ;oBACRC,YAAY;oBACZC,kBAAkB;oBAClB3C,aAAa,CAAC,CAACA;oBACf4C,aAAa;oBACbC,SAAS,CAAC,CAAC5C;gBACb;gBACA6C,WAAW;oBACTC,UAAU;oBACVC,SAASjD,OACL,OACA;wBACEkD,SAAS;4BACPC,QAAQhD,eAAe,WAAW;wBACpC;wBACAiD,MAAM;4BACJC,UAAUpD,cAAc,kBAAkB;wBAC5C;oBAEF;gBACN;gBACAqD,aAAa;oBACXC,YAAYzE;gBACd;YACF;QACF;QACA0E,YAAYxD,OAAO,WAAWyD;QAC9BC,aAAa,EAAEjE,mCAAAA,gBAAiBiE,aAAa;QAC7C,sDAAsD;QACtD,yDAAyD;QACzDC,uBAAuB3D,OACnB,QACAP,mCAAAA,gBAAiBkE,qBAAqB;QAC1C,wCAAwC;QACxCtD,mBAAmBA,oBACfuD,OAAOC,WAAW,CAChBD,OAAOE,OAAO,CAACzD,mBAAmBc,GAAG,CAAC,CAAC,CAAC4C,KAAKC,OAAO,GAAK;gBACvDD;gBACA;oBACE,GAAGC,MAAM;oBACT/B,WACE,OAAO+B,OAAO/B,SAAS,KAAK,WACxB+B,OAAO/B,SAAS,GAChB2B,OAAOE,OAAO,CAACE,OAAO/B,SAAS,EAAEd,GAAG,CAAC,CAAC,CAAC8C,KAAKC,MAAM,GAAK;4BACrDD;4BACAC;yBACD;gBACT;aACD,KAEHT;QACJU,KAAK,EAAE1E,mCAAAA,gBAAiB0E,KAAK;QAC7B,kFAAkF;QAClFC,WAAW,CAAC;QACZ,2GAA2G;QAC3G,GAAI,CAAC1D,sBAAsB;YACzB,mEAAmE;YACnE8B,SAAS6B,kBAAkB5E,mCAAAA,gBAAiB+C,OAAO,EAAEvC;YACrD,mEAAmE;YACnEqE,kBAAkBC,2BAChB9E,mCAAAA,gBAAiB6E,gBAAgB,EACjCrE;QAEJ,CAAC;QACDQ,kBACEA,oBAAoB,CAACT,OACjB;YACEU,oBAAoB,CAAC,CAACA;QACxB,IACA+C;QACNe,eACE/D,oBAAoB,CAACT,OACjB;YACE,+BAA+B;YAC/B,2BAA2B;YAC3ByE,SAAS;YACT/D,oBAAoB,CAAC,CAACA;QACxB,IACA+C;QACN,0CAA0C;QAC1C,gDAAgD;QAChDiB,WAAWtE;IACb;AACF;AAEA,SAASmE,2BACPI,sBAAoE,EACpE1E,WAAgB;IAEhB,IAAI,CAAC0E,wBAAwB;QAC3B,OAAO;IACT,OAAO,IAAI,OAAOA,2BAA2B,UAAU;QACrD,OAAO;YACL,GAAGA,sBAAsB;YACzBC,aAAaD,uBAAuBC,WAAW,IAAIpF,QAAQS;QAC7D;IACF,OAAO;QACL,OAAO;YACL2E,aAAapF,QAAQS;QACvB;IACF;AACF;AAEA;;;;;;;;;AASA,GACA,SAAS4E,iBACPzE,MAA2B,KAAK;IAEhC,OAAOA,MAAM;QAAE0E,QAAQ;YAAEC,MAAM;QAAM;IAAE,IAAI,CAAC;AAC9C;AAEA,SAASV,kBACPW,aAAkD,EAClD/E,WAAoB;IAEpB,IAAI,CAAC+E,eAAe;QAClB,OAAO;IACT;IACA,IAAIC,YAAY,CAAC,CAAChF;IAClB,OAAQ,OAAO+E,kBAAkB,YAAYA,cAAcC,SAAS;QAClE,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;YACHA,YAAY;YACZ;QACF,KAAK;QACL;YACE;IACJ;IACA,OAAO;QACLR,SAAS;QACTQ;QACAC,WAAWjF;QACX,GAAI,OAAO+E,kBAAkB,YAAY;YACvCG,WAAWH,cAAcG,SAAS;YAClCC,aAAaJ,cAAcI,WAAW;YACtCF,WAAWjF,eAAe+E,cAAcK,SAAS;QACnD,CAAC;IACH;AACF;AAEO,SAAS1G,kBAAkB,EAChC2G,QAAQ,EACRrG,QAAQ,EACRmB,GAAG,EACHC,iBAAiB,EACjBC,UAAU,EACVb,eAAe,EACfP,QAAQ,EACRqB,eAAe,EACfgF,QAAQ,EAYT;IACC,IAAIC,cAAczF,kBAAkB;QAClCd;QACAe,MAAM;QACNC,aAAa;QACbC,iBAAiB;QACjBC,cAAc,CAACmF;QACfjF;QACAC;QACAb;QACAP;QACAqB;QACAH;QACA,oDAAoD;QACpDM,oBAAoB;QACpB,oDAAoD;QACpDD,kBAAkB;IACpB;IAEA,MAAMgF,aAAa5G,aAAa6G,IAAI,CAACzG;IAErC,OAAO;QACL,GAAGuG,WAAW;QACdG,KAAK;YACHC,SAAS;gBACP,yCAAyC;gBACzCC,MAAMpE,QAAQC,QAAQ,CAACmE,IAAI;YAC7B;QACF;QACAf,QAAQ;YACNC,MAAM3E,OAAO,CAACqF,aAAa,QAAQ;QACrC;QACAK,gBAAgB;QAChBC,mBAAmB;QACnBR;IACF;AACF;AAEO,SAAS3G,oBAAoB,EAClC,+EAA+E;AAC/E,mBAAmB;AACnBK,QAAQ,EACRgB,WAAW,EACXqF,QAAQ,EACRC,QAAQ,EACRS,MAAM,EACNC,UAAU,EACV/F,eAAe,EACfG,iBAAiB,EACjB6F,mBAAmB,EACnBC,sBAAsB,EACtB7F,UAAU,EACVb,eAAe,EACfP,QAAQ,EACRkH,iBAAiB,EACjB5F,WAAW,EACX6F,wBAAwB,EACxB5F,gBAAgB,EAChBC,kBAAkB,EAClBN,GAAG,EAuBJ;IACC,IAAIoF,cAAmBzF,kBAAkB;QACvCd;QACAgB;QACAE,cAAc,CAACmF;QACfpF;QACAG;QACAC;QACAb;QACAP;QACA,mBAAmB;QACnBsB;QACAE;QACAD;QACAL,KAAK,CAAC,CAACA;IACT;IACAoF,YAAYc,WAAW,GAAG;QACxBA,aAAa;YACX;YACA;YAEA,8CAA8C;YAC9C;YACA;SACD;QACDD;IACF;IACAb,YAAYe,mBAAmB,GAAG;QAChCC,UAAU;YACR,eAAe;gBACbC,YAAY;oBACVC,aAAa;oBACbC,cAAc;oBACdC,eAAe;oBACfC,qBAAqB;oBACrBC,WAAW;gBACb;YACF;QACF;IACF;IAEA,IAAIZ,uBAAuBZ,YAAY,CAACrF,aAAa;QACnDuF,YAAYU,mBAAmB,GAAG;YAChCa,oBAAoB;QACtB;IACF;IAEA,kDAAkD;IAClD,IAAIZ,wBAAwB;QAC1BX,YAAYwB,qBAAqB,GAAG;YAClCR,UAAUL;QACZ;IACF;IAEA,MAAMV,aAAa5G,aAAa6G,IAAI,CAACzG;IAErC,IAAIqG,UAAU;QACZ,OAAO;YACL,GAAGE,WAAW;YACd,8FAA8F;YAC9FM,gBAAgB;YAChBC,mBAAmB;YACnBkB,eAAehH;YACfiH,kBAAkB5B;YAClBC;YACAS;YACAtB,WAAW,CAAC,CAACtE;YACb6F;YACAN,KAAK;gBACHC,SAAS;oBACP,yCAAyC;oBACzCC,MAAMpE,QAAQC,QAAQ,CAACmE,IAAI;gBAC7B;YACF;YACA,GAAGhB,iBAAiBzE,IAAI;QAC1B;IACF,OAAO;QACL,MAAMiB,UAAU;YACd,GAAGmE,WAAW;YACd,0DAA0D;YAC1D,GAAIC,aACA;gBACEX,QAAQ;oBACNC,MAAM;gBACR;YACF,IACAF,iBAAiBzE,IAAI;YACzB0F,gBAAgB,CAACG;YACjBgB,eAAehH;YACfiH,kBAAkB5B;YAClBC;YACAS;YACAC;YACA,GAAIG,qBAAqBA,kBAAkBe,MAAM,GAAG,IAChD;gBACExB,KAAK;oBACHC,SAASQ;gBACX;YACF,IACA,CAAC,CAAC;QACR;QACA,IAAI,CAAC/E,QAAQsE,GAAG,EAAE;YAChB,6CAA6C;YAC7CtE,QAAQC,GAAG,CAAC8F,MAAM,GAAG;QACvB;QACA,OAAO/F;IACT;AACF"}