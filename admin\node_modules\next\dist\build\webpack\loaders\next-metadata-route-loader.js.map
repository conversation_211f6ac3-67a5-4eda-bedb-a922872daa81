{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-metadata-route-loader.ts"], "names": ["getFilenameAndExtension", "errorOnBadHandler", "resourcePath", "JSON", "stringify", "cacheHeader", "none", "longCache", "revalidate", "filename", "path", "basename", "name", "ext", "split", "getContentType", "imageExtMimeTypeMap", "getStaticAssetRouteCode", "fileBaseName", "cache", "process", "env", "NODE_ENV", "code", "fs", "promises", "readFile", "toString", "getDynamicTextRouteCode", "getDynamicImageRouteCode", "getDynamicSiteMapRouteCode", "page", "staticGenerationCode", "includes", "nextMetadataRouterLoader", "isDynamic", "getOptions"], "mappings": ";;;;;;;;;;;;;;;IA0BgBA,uBAAuB;eAAvBA;;IAyNhB,OAAuC;eAAvC;;;2DAlPe;6DACE;0BACmB;;;;;;AAEpC,SAASC,kBAAkBC,YAAoB;IAC7C,OAAO,CAAC;;kDAEwC,EAAEC,KAAKC,SAAS,CAC5DF,cACA;;EAEJ,CAAC;AACH;AAEA,MAAMG,cAAc;IAClBC,MAAM;IACNC,WAAW;IACXC,YAAY;AACd;AAOO,SAASR,wBAAwBE,YAAoB;IAC1D,MAAMO,WAAWC,aAAI,CAACC,QAAQ,CAACT;IAC/B,MAAM,CAACU,MAAMC,IAAI,GAAGJ,SAASK,KAAK,CAAC,KAAK;IACxC,OAAO;QAAEF;QAAMC;IAAI;AACrB;AAEA,SAASE,eAAeb,YAAoB;IAC1C,IAAI,EAAEU,IAAI,EAAEC,GAAG,EAAE,GAAGb,wBAAwBE;IAC5C,IAAIW,QAAQ,OAAOA,MAAM;IAEzB,IAAID,SAAS,aAAaC,QAAQ,OAAO,OAAO;IAChD,IAAID,SAAS,WAAW,OAAO;IAC/B,IAAIA,SAAS,UAAU,OAAO;IAC9B,IAAIA,SAAS,YAAY,OAAO;IAEhC,IAAIC,QAAQ,SAASA,QAAQ,UAAUA,QAAQ,SAASA,QAAQ,OAAO;QACrE,OAAOG,6BAAmB,CAACH,IAAI;IACjC;IACA,OAAO;AACT;AAEA,mHAAmH;AACnH,eAAeI,wBACbf,YAAoB,EACpBgB,YAAoB;IAEpB,MAAMC,QACJD,iBAAiB,YACb,uCACAE,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzBjB,YAAYC,IAAI,GAChBD,YAAYE,SAAS;IAC3B,MAAMgB,OAAO,CAAC;;;oBAGI,EAAEpB,KAAKC,SAAS,CAACW,eAAeb,eAAe;2BACxC,EAAEC,KAAKC,SAAS,CACvC,AAAC,CAAA,MAAMoB,WAAE,CAACC,QAAQ,CAACC,QAAQ,CAACxB,aAAY,EAAGyB,QAAQ,CAAC,WACpD;;;;;;;uBAOmB,EAAExB,KAAKC,SAAS,CAACe,OAAO;;;;;;AAM/C,CAAC;IACC,OAAOI;AACT;AAEA,SAASK,wBAAwB1B,YAAoB;IACnD,OAAO,CAAC;;oBAEU,EAAEC,KAAKC,SAAS,CAACF,cAAc;;;oBAG/B,EAAEC,KAAKC,SAAS,CAACW,eAAeb,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACJ,wBAAwBE,cAAcU,IAAI,EAAE;;AAE9E,EAAEX,kBAAkBC,cAAc;;;;;;;;;uBASX,EAAEC,KAAKC,SAAS,CAACC,YAAYG,UAAU,EAAE;;;;AAIhE,CAAC;AACD;AAEA,iCAAiC;AACjC,SAASqB,yBAAyB3B,YAAoB;IACpD,OAAO,CAAC;;0BAEgB,EAAEC,KAAKC,SAAS,CAACF,cAAc;;;;;;;AAOzD,EAAED,kBAAkBC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;AAyBlC,CAAC;AACD;AAEA,SAAS4B,2BAA2B5B,YAAoB,EAAE6B,IAAY;IACpE,IAAIC,uBAAuB;IAE3B,IACEZ,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBACzBS,KAAKE,QAAQ,CAAC,sBACd;QACAD,uBAAuB,CAAC;;;;;;;;;;IAUxB,CAAC;IACH;IAEA,MAAMT,OAAO,CAAC;;0BAEU,EAAEpB,KAAKC,SAAS,CAACF,cAAc;;;;;;oBAMrC,EAAEC,KAAKC,SAAS,CAACW,eAAeb,eAAe;iBAClD,EAAEC,KAAKC,SAAS,CAACJ,wBAAwBE,cAAcU,IAAI,EAAE;;AAE9E,EAAEX,kBAAkBC,cAAc;;AAElC,EAAE,GAAG,wCAAwC,IAAG;cAClC,EAAEC,KAAKC,SAAS,CAACF,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA8BtB,EAAEC,KAAKC,SAAS,CAACC,YAAYG,UAAU,EAAE;;;;;AAKhE,EAAEwB,qBAAqB;AACvB,CAAC;IACC,OAAOT;AACT;AACA,gEAAgE;AAChE,gFAAgF;AAChF,oDAAoD;AACpD,MAAMW,2BACJ;IACE,MAAM,EAAEhC,YAAY,EAAE,GAAG,IAAI;IAC7B,MAAM,EAAE6B,IAAI,EAAEI,SAAS,EAAE,GAAG,IAAI,CAACC,UAAU;IAC3C,MAAM,EAAExB,MAAMM,YAAY,EAAE,GAAGlB,wBAAwBE;IAEvD,IAAIqB,OAAO;IACX,IAAIY,cAAc,KAAK;QACrB,IAAIjB,iBAAiB,YAAYA,iBAAiB,YAAY;YAC5DK,OAAOK,wBAAwB1B;QACjC,OAAO,IAAIgB,iBAAiB,WAAW;YACrCK,OAAOO,2BAA2B5B,cAAc6B;QAClD,OAAO;YACLR,OAAOM,yBAAyB3B;QAClC;IACF,OAAO;QACLqB,OAAO,MAAMN,wBAAwBf,cAAcgB;IACrD;IAEA,OAAOK;AACT;MAEF,WAAeW"}