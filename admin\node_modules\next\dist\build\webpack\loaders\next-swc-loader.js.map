{"version": 3, "sources": ["../../../../src/build/webpack/loaders/next-swc-loader.ts"], "names": ["pitch", "sw<PERSON><PERSON><PERSON><PERSON>", "raw", "loaderTransform", "parentTrace", "source", "inputSourceMap", "nextConfig", "filename", "resourcePath", "loaderOptions", "getOptions", "isServer", "rootDir", "pagesDir", "appDir", "hasReactRefresh", "jsConfig", "supportedBrowsers", "swcCacheDir", "serverComponents", "isReactServerLayer", "esm", "isPageFile", "startsWith", "relativeFilePathFromRoot", "path", "relative", "swcOptions", "getLoaderSWCOptions", "development", "mode", "modularizeImports", "optimizePackageImports", "experimental", "swcPlugins", "compilerOptions", "compiler", "optimizeServerReact", "programmaticOptions", "JSON", "stringify", "undefined", "sourceMaps", "sourceMap", "inlineSourcesContent", "sourceFileName", "jsc", "transform", "react", "Object", "prototype", "hasOwnProperty", "call", "swcSpan", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "then", "output", "eliminatedPackages", "pkg", "parse", "add", "code", "map", "EXCLUDED_PATHS", "callback", "async", "process", "versions", "pnp", "test", "loaders", "length", "loaderIndex", "isAbsolute", "isWasm", "loaderSpan", "currentTraceSpan", "addDependency", "r", "inputSource", "transformedSource", "outputSourceMap", "err"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA;;;;;;;;;;;;;;;;IA2HgBA,KAAK;eAALA;;IAuBhB,OAmBC;eAnBuBC;;IAsBXC,GAAG;eAAHA;;;qBArKqB;yBACE;8DACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBjC,eAAeC,gBAEbC,WAAgB,EAChBC,MAAe,EACfC,cAAoB;QAiCMC,0BACZA,2BAESA;IAlCvB,wBAAwB;IACxB,MAAMC,WAAW,IAAI,CAACC,YAAY;IAElC,IAAIC,gBAAkC,IAAI,CAACC,UAAU,MAAM,CAAC;IAE5D,MAAM,EACJC,QAAQ,EACRC,OAAO,EACPC,QAAQ,EACRC,MAAM,EACNC,eAAe,EACfT,UAAU,EACVU,QAAQ,EACRC,iBAAiB,EACjBC,WAAW,EACXC,gBAAgB,EAChBC,kBAAkB,EAClBC,GAAG,EACJ,GAAGZ;IACJ,MAAMa,aAAaf,SAASgB,UAAU,CAACV;IACvC,MAAMW,2BAA2BC,aAAI,CAACC,QAAQ,CAACd,SAASL;IAExD,MAAMoB,aAAaC,IAAAA,4BAAmB,EAAC;QACrCf;QACAC;QACAP;QACAI;QACAW;QACAO,aAAa,IAAI,CAACC,IAAI,KAAK;QAC3Bf;QACAgB,iBAAiB,EAAEzB,8BAAAA,WAAYyB,iBAAiB;QAChDC,sBAAsB,EAAE1B,+BAAAA,2BAAAA,WAAY2B,YAAY,qBAAxB3B,yBAA0B0B,sBAAsB;QACxEE,UAAU,EAAE5B,+BAAAA,4BAAAA,WAAY2B,YAAY,qBAAxB3B,0BAA0B4B,UAAU;QAChDC,eAAe,EAAE7B,8BAAAA,WAAY8B,QAAQ;QACrCC,mBAAmB,EAAE/B,+BAAAA,4BAAAA,WAAY2B,YAAY,qBAAxB3B,0BAA0B+B,mBAAmB;QAClErB;QACAC;QACAC;QACAM;QACAL;QACAC;QACAC;IACF;IAEA,MAAMiB,sBAAsB;QAC1B,GAAGX,UAAU;QACbpB;QACAF,gBAAgBA,iBAAiBkC,KAAKC,SAAS,CAACnC,kBAAkBoC;QAElE,sEAAsE;QACtEC,YAAY,IAAI,CAACC,SAAS;QAC1BC,sBAAsB,IAAI,CAACD,SAAS;QAEpC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXE,gBAAgBtC;IAClB;IAEA,IAAI,CAAC+B,oBAAoBjC,cAAc,EAAE;QACvC,OAAOiC,oBAAoBjC,cAAc;IAC3C;IAEA,+BAA+B;IAC/B,IACE,IAAI,CAACyB,IAAI,IACTQ,oBAAoBQ,GAAG,IACvBR,oBAAoBQ,GAAG,CAACC,SAAS,IACjCT,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,IACvC,CAACC,OAAOC,SAAS,CAACC,cAAc,CAACC,IAAI,CACnCd,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,EACvC,gBAEF;QACAV,oBAAoBQ,GAAG,CAACC,SAAS,CAACC,KAAK,CAACnB,WAAW,GACjD,IAAI,CAACC,IAAI,KAAK;IAClB;IAEA,MAAMuB,UAAUlD,YAAYmD,UAAU,CAAC;IACvC,OAAOD,QAAQE,YAAY,CAAC,IAC1BR,IAAAA,cAAS,EAAC3C,QAAekC,qBAAqBkB,IAAI,CAAC,CAACC;YAClD,IAAIA,OAAOC,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,EAAE;gBACxD,KAAK,MAAMC,OAAOpB,KAAKqB,KAAK,CAACH,OAAOC,kBAAkB,EAAG;oBACvD,IAAI,CAACA,kBAAkB,CAACG,GAAG,CAACF;gBAC9B;YACF;YACA,OAAO;gBAACF,OAAOK,IAAI;gBAAEL,OAAOM,GAAG,GAAGxB,KAAKqB,KAAK,CAACH,OAAOM,GAAG,IAAItB;aAAU;QACvE;AAEJ;AAEA,MAAMuB,iBACJ;AAEK,SAASjE;IACd,MAAMkE,WAAW,IAAI,CAACC,KAAK;IACzB,CAAA;QACA,IACE,kDAAkD;QAClD,CAACC,QAAQC,QAAQ,CAACC,GAAG,IACrB,CAACL,eAAeM,IAAI,CAAC,IAAI,CAAC9D,YAAY,KACtC,IAAI,CAAC+D,OAAO,CAACC,MAAM,GAAG,MAAM,IAAI,CAACC,WAAW,IAC5CC,IAAAA,gBAAU,EAAC,IAAI,CAAClE,YAAY,KAC5B,CAAE,MAAMmE,IAAAA,WAAM,KACd;YACA,MAAMC,aAAa,IAAI,CAACC,gBAAgB,CAACvB,UAAU,CAAC;YACpD,IAAI,CAACwB,aAAa,CAAC,IAAI,CAACtE,YAAY;YACpC,OAAOoE,WAAWrB,YAAY,CAAC,IAC7BrD,gBAAgBkD,IAAI,CAAC,IAAI,EAAEwB;QAE/B;IACF,CAAA,IAAKpB,IAAI,CAAC,CAACuB;QACT,IAAIA,GAAG,OAAOd,SAAS,SAASc;QAChCd;IACF,GAAGA;AACL;AAEe,SAASjE,UAEtBgF,WAAmB,EACnB3E,cAAmB;IAEnB,MAAMuE,aAAa,IAAI,CAACC,gBAAgB,CAACvB,UAAU,CAAC;IACpD,MAAMW,WAAW,IAAI,CAACC,KAAK;IAC3BU,WACGrB,YAAY,CAAC,IACZrD,gBAAgBkD,IAAI,CAAC,IAAI,EAAEwB,YAAYI,aAAa3E,iBAErDmD,IAAI,CACH,CAAC,CAACyB,mBAAmBC,gBAAqB;QACxCjB,SAAS,MAAMgB,mBAAmBC,mBAAmB7E;IACvD,GACA,CAAC8E;QACClB,SAASkB;IACX;AAEN;AAGO,MAAMlF,MAAM"}