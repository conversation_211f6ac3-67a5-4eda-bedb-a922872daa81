{"version": 3, "sources": ["../../../src/client/components/layout-router.tsx"], "names": ["OuterLayoutRouter", "walkAddRefetch", "segmentPathToWalk", "treeToRecreate", "segment", "parallelRouteKey", "isLast", "length", "matchSegment", "hasOwnProperty", "subTree", "undefined", "slice", "findDOMNode", "instance", "window", "process", "env", "NODE_ENV", "originalConsoleError", "console", "error", "messages", "includes", "ReactDOM", "rectProperties", "shouldSkipElement", "element", "getComputedStyle", "position", "warn", "rect", "getBoundingClientRect", "every", "item", "topOfElementInViewport", "viewportHeight", "top", "getHashFragmentDomNode", "hashFragment", "document", "body", "getElementById", "getElementsByName", "InnerScrollAndFocusHandler", "React", "Component", "componentDidMount", "handlePotentialScroll", "componentDidUpdate", "props", "focusAndScrollRef", "apply", "render", "children", "segmentPath", "segmentPaths", "some", "scrollRefSegmentPath", "index", "domNode", "Element", "HTMLElement", "nextElement<PERSON><PERSON>ling", "handleSmoothScroll", "scrollIntoView", "htmlElement", "documentElement", "clientHeight", "scrollTop", "dontForceLayout", "onlyHashChange", "focus", "ScrollAndFocusHandler", "context", "useContext", "GlobalLayoutRouterContext", "Error", "InnerLayoutRouter", "parallel<PERSON><PERSON>er<PERSON>ey", "url", "childNodes", "tree", "cache<PERSON>ey", "buildId", "changeByServerResponse", "fullTree", "childNode", "get", "status", "CacheStates", "LAZY_INITIALIZED", "refetchTree", "DATA_FETCH", "data", "fetchServerResponse", "URL", "location", "origin", "nextUrl", "subTreeData", "head", "parallelRoutes", "Map", "set", "flightData", "overrideCanonicalUrl", "use", "setTimeout", "startTransition", "createInfinitePromise", "subtree", "LayoutRouterContext", "Provider", "value", "LoadingBoundary", "loading", "loadingStyles", "loadingScripts", "hasLoading", "Suspense", "fallback", "errorStyles", "errorScripts", "templateStyles", "templateScripts", "template", "notFound", "notFoundStyles", "styles", "childNodesForParallelRouter", "treeSegment", "currentChildSegmentValue", "getSegmentValue", "preservedSegments", "map", "preservedSegment", "preservedSegmentValue", "createRouterCache<PERSON>ey", "TemplateContext", "key", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "NotFoundBoundary", "RedirectBoundary", "isActive"], "mappings": "AAAA;;;;;+BA+cA;;;CAGC,GACD;;;eAAwBA;;;;;iEAxc0C;mEAC7C;+CAMd;qCAC6B;iCACE;+BACR;+BACD;oCACM;kCACF;kCACA;iCACD;sCACK;AAErC;;;CAGC,GACD,SAASC,eACPC,iBAAgD,EAChDC,cAAiC;IAEjC,IAAID,mBAAmB;QACrB,MAAM,CAACE,SAASC,iBAAiB,GAAGH;QACpC,MAAMI,SAASJ,kBAAkBK,MAAM,KAAK;QAE5C,IAAIC,IAAAA,2BAAY,EAACL,cAAc,CAAC,EAAE,EAAEC,UAAU;YAC5C,IAAID,cAAc,CAAC,EAAE,CAACM,cAAc,CAACJ,mBAAmB;gBACtD,IAAIC,QAAQ;oBACV,MAAMI,UAAUT,eACdU,WACAR,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAErC,OAAO;wBACLF,cAAc,CAAC,EAAE;wBACjB;4BACE,GAAGA,cAAc,CAAC,EAAE;4BACpB,CAACE,iBAAiB,EAAE;gCAClBK,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACVA,OAAO,CAAC,EAAE;gCACV;6BACD;wBACH;qBACD;gBACH;gBAEA,OAAO;oBACLP,cAAc,CAAC,EAAE;oBACjB;wBACE,GAAGA,cAAc,CAAC,EAAE;wBACpB,CAACE,iBAAiB,EAAEJ,eAClBC,kBAAkBU,KAAK,CAAC,IACxBT,cAAc,CAAC,EAAE,CAACE,iBAAiB;oBAEvC;iBACD;YACH;QACF;IACF;IAEA,OAAOF;AACT;AAEA,4FAA4F;AAC5F;;CAEC,GACD,SAASU,YACPC,QAAoD;IAEpD,+BAA+B;IAC/B,IAAI,OAAOC,WAAW,aAAa,OAAO;IAC1C,wDAAwD;IACxD,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,uBAAuBC,QAAQC,KAAK;QAC1C,IAAI;YACFD,QAAQC,KAAK,GAAG;iDAAIC;oBAAAA;;gBAClB,4DAA4D;gBAC5D,IAAI,CAACA,QAAQ,CAAC,EAAE,CAACC,QAAQ,CAAC,6CAA6C;oBACrEJ,wBAAwBG;gBAC1B;YACF;YACA,OAAOE,iBAAQ,CAACX,WAAW,CAACC;QAC9B,SAAU;YACRM,QAAQC,KAAK,GAAGF;QAClB;IACF;IACA,OAAOK,iBAAQ,CAACX,WAAW,CAACC;AAC9B;AAEA,MAAMW,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD;;CAEC,GACD,SAASC,kBAAkBC,OAAoB;IAC7C,kGAAkG;IAClG,0FAA0F;IAC1F,mDAAmD;IACnD,IAAI;QAAC;QAAU;KAAQ,CAACJ,QAAQ,CAACK,iBAAiBD,SAASE,QAAQ,GAAG;QACpE,IAAIb,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1CE,QAAQU,IAAI,CACV,4FACAH;QAEJ;QACA,OAAO;IACT;IAEA,2FAA2F;IAC3F,wDAAwD;IACxD,MAAMI,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOP,eAAeQ,KAAK,CAAC,CAACC,OAASH,IAAI,CAACG,KAAK,KAAK;AACvD;AAEA;;CAEC,GACD,SAASC,uBAAuBR,OAAoB,EAAES,cAAsB;IAC1E,MAAML,OAAOJ,QAAQK,qBAAqB;IAC1C,OAAOD,KAAKM,GAAG,IAAI,KAAKN,KAAKM,GAAG,IAAID;AACtC;AAEA;;;;;CAKC,GACD,SAASE,uBAAuBC,YAAoB;IAClD,+EAA+E;IAC/E,IAAIA,iBAAiB,OAAO;QAC1B,OAAOC,SAASC,IAAI;IACtB;QAIED;IAFF,qFAAqF;IACrF,OACEA,CAAAA,2BAAAA,SAASE,cAAc,CAACH,yBAAxBC,2BACA,8FAA8F;IAC9FA,SAASG,iBAAiB,CAACJ,aAAa,CAAC,EAAE;AAE/C;AAMA,MAAMK,mCAAmCC,cAAK,CAACC,SAAS;IAoGtDC,oBAAoB;QAClB,IAAI,CAACC,qBAAqB;IAC5B;IAEAC,qBAAqB;QACnB,sJAAsJ;QACtJ,IAAI,IAAI,CAACC,KAAK,CAACC,iBAAiB,CAACC,KAAK,EAAE;YACtC,IAAI,CAACJ,qBAAqB;QAC5B;IACF;IAEAK,SAAS;QACP,OAAO,IAAI,CAACH,KAAK,CAACI,QAAQ;IAC5B;;;aAhHAN,wBAAwB;YACtB,qGAAqG;YACrG,MAAM,EAAEG,iBAAiB,EAAEI,WAAW,EAAE,GAAG,IAAI,CAACL,KAAK;YAErD,IAAIC,kBAAkBC,KAAK,EAAE;gBAC3B,uEAAuE;gBACvE,6EAA6E;gBAC7E,wEAAwE;gBACxE,IACED,kBAAkBK,YAAY,CAACjD,MAAM,KAAK,KAC1C,CAAC4C,kBAAkBK,YAAY,CAACC,IAAI,CAAC,CAACC,uBACpCH,YAAYtB,KAAK,CAAC,CAAC7B,SAASuD,QAC1BnD,IAAAA,2BAAY,EAACJ,SAASsD,oBAAoB,CAACC,MAAM,KAGrD;oBACA;gBACF;gBAEA,IAAIC,UAEiC;gBACrC,MAAMrB,eAAeY,kBAAkBZ,YAAY;gBAEnD,IAAIA,cAAc;oBAChBqB,UAAUtB,uBAAuBC;gBACnC;gBAEA,kGAAkG;gBAClG,yEAAyE;gBACzE,IAAI,CAACqB,SAAS;oBACZA,UAAU/C,YAAY,IAAI;gBAC5B;gBAEA,uGAAuG;gBACvG,IAAI,CAAE+C,CAAAA,mBAAmBC,OAAM,GAAI;oBACjC;gBACF;gBAEA,4FAA4F;gBAC5F,2EAA2E;gBAC3E,MAAO,CAAED,CAAAA,mBAAmBE,WAAU,KAAMpC,kBAAkBkC,SAAU;oBACtE,uGAAuG;oBACvG,IAAIA,QAAQG,kBAAkB,KAAK,MAAM;wBACvC;oBACF;oBACAH,UAAUA,QAAQG,kBAAkB;gBACtC;gBAEA,6EAA6E;gBAC7EZ,kBAAkBC,KAAK,GAAG;gBAC1BD,kBAAkBZ,YAAY,GAAG;gBACjCY,kBAAkBK,YAAY,GAAG,EAAE;gBAEnCQ,IAAAA,sCAAkB,EAChB;oBACE,uEAAuE;oBACvE,IAAIzB,cAAc;wBACdqB,QAAwBK,cAAc;wBAExC;oBACF;oBACA,oFAAoF;oBACpF,4CAA4C;oBAC5C,MAAMC,cAAc1B,SAAS2B,eAAe;oBAC5C,MAAM/B,iBAAiB8B,YAAYE,YAAY;oBAE/C,oEAAoE;oBACpE,IAAIjC,uBAAuByB,SAAwBxB,iBAAiB;wBAClE;oBACF;oBAEA,2FAA2F;oBAC3F,kHAAkH;oBAClH,qHAAqH;oBACrH,6HAA6H;oBAC7H8B,YAAYG,SAAS,GAAG;oBAExB,mFAAmF;oBACnF,IAAI,CAAClC,uBAAuByB,SAAwBxB,iBAAiB;wBAEjEwB,QAAwBK,cAAc;oBAC1C;gBACF,GACA;oBACE,oDAAoD;oBACpDK,iBAAiB;oBACjBC,gBAAgBpB,kBAAkBoB,cAAc;gBAClD;gBAGF,wEAAwE;gBACxEpB,kBAAkBoB,cAAc,GAAG;gBAEnC,2BAA2B;gBAC3BX,QAAQY,KAAK;YACf;QACF;;AAgBF;AAEA,SAASC,sBAAsB,KAM9B;IAN8B,IAAA,EAC7BlB,WAAW,EACXD,QAAQ,EAIT,GAN8B;IAO7B,MAAMoB,UAAUC,IAAAA,iBAAU,EAACC,wDAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,IAAIG,MAAM;IAClB;IAEA,qBACE,6BAACjC;QACCW,aAAaA;QACbJ,mBAAmBuB,QAAQvB,iBAAiB;OAE3CG;AAGP;AAEA;;CAEC,GACD,SAASwB,kBAAkB,KAiB1B;IAjB0B,IAAA,EACzBC,iBAAiB,EACjBC,GAAG,EACHC,UAAU,EACV1B,WAAW,EACX2B,IAAI,EACJ,oDAAoD;IACpD,YAAY;IACZC,QAAQ,EAST,GAjB0B;IAkBzB,MAAMT,UAAUC,IAAAA,iBAAU,EAACC,wDAAyB;IACpD,IAAI,CAACF,SAAS;QACZ,MAAM,IAAIG,MAAM;IAClB;IAEA,MAAM,EAAEO,OAAO,EAAEC,sBAAsB,EAAEH,MAAMI,QAAQ,EAAE,GAAGZ;IAE5D,yDAAyD;IACzD,IAAIa,YAAYN,WAAWO,GAAG,CAACL;IAE/B,oGAAoG;IACpG,IAAI,CAACI,aAAaA,UAAUE,MAAM,KAAKC,0CAAW,CAACC,gBAAgB,EAAE;QACnE;;KAEC,GACD,sBAAsB;QACtB,MAAMC,cAAc3F,eAAe;YAAC;eAAOsD;SAAY,EAAE+B;QAEzDC,YAAY;YACVE,QAAQC,0CAAW,CAACG,UAAU;YAC9BC,MAAMC,IAAAA,wCAAmB,EACvB,IAAIC,IAAIhB,KAAKiB,SAASC,MAAM,GAC5BN,aACAlB,QAAQyB,OAAO,EACff;YAEFgB,aAAa;YACbC,MACEd,aAAaA,UAAUE,MAAM,KAAKC,0CAAW,CAACC,gBAAgB,GAC1DJ,UAAUc,IAAI,GACd1F;YACN2F,gBACEf,aAAaA,UAAUE,MAAM,KAAKC,0CAAW,CAACC,gBAAgB,GAC1DJ,UAAUe,cAAc,GACxB,IAAIC;QACZ;QAEA;;KAEC,GACDtB,WAAWuB,GAAG,CAACrB,UAAUI;IAC3B;IAEA,kGAAkG;IAClG,IAAI,CAACA,WAAW;QACd,MAAM,IAAIV,MAAM;IAClB;IAEA,kGAAkG;IAClG,IAAIU,UAAUa,WAAW,IAAIb,UAAUO,IAAI,EAAE;QAC3C,MAAM,IAAIjB,MAAM;IAClB;IAEA,6FAA6F;IAC7F,IAAIU,UAAUO,IAAI,EAAE;QAClB;;KAEC,GACD,8DAA8D;QAC9D,MAAM,CAACW,YAAYC,qBAAqB,GAAGC,IAAAA,UAAG,EAACpB,UAAUO,IAAI;QAE7D,sEAAsE;QACtEP,UAAUO,IAAI,GAAG;QAEjB,wGAAwG;QACxGc,WAAW;YACTC,IAAAA,sBAAe,EAAC;gBACdxB,uBAAuBC,UAAUmB,YAAYC;YAC/C;QACF;QACA,yGAAyG;QACzGC,IAAAA,UAAG,EAACG,IAAAA,sCAAqB;IAC3B;IAEA,yIAAyI;IACzI,wFAAwF;IACxF,IAAI,CAACvB,UAAUa,WAAW,EAAE;QAC1BO,IAAAA,UAAG,EAACG,IAAAA,sCAAqB;IAC3B;IAEA,MAAMC,UACJ,4EAA4E;kBAC5E,6BAACC,kDAAmB,CAACC,QAAQ;QAC3BC,OAAO;YACLhC,MAAMA,IAAI,CAAC,EAAE,CAACH,kBAAkB;YAChCE,YAAYM,UAAUe,cAAc;YACpC,kDAAkD;YAClDtB,KAAKA;QACP;OAECO,UAAUa,WAAW;IAG1B,iFAAiF;IACjF,OAAOW;AACT;AAEA;;;CAGC,GACD,SAASI,gBAAgB,KAYxB;IAZwB,IAAA,EACvB7D,QAAQ,EACR8D,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,UAAU,EAOX,GAZwB;IAavB,IAAIA,YAAY;QACd,qBACE,6BAACC,eAAQ;YACPC,wBACE,4DACGJ,eACAC,gBACAF;WAIJ9D;IAGP;IAEA,qBAAO,4DAAGA;AACZ;AAMe,SAAStD,kBAAkB,KAgCzC;IAhCyC,IAAA,EACxC+E,iBAAiB,EACjBxB,WAAW,EACXlC,KAAK,EACLqG,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfT,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,UAAU,EACVO,QAAQ,EACRC,QAAQ,EACRC,cAAc,EACdC,MAAM,EAiBP,GAhCyC;IAiCxC,MAAMvD,UAAUC,IAAAA,iBAAU,EAACqC,kDAAmB;IAC9C,IAAI,CAACtC,SAAS;QACZ,MAAM,IAAIG,MAAM;IAClB;IAEA,MAAM,EAAEI,UAAU,EAAEC,IAAI,EAAEF,GAAG,EAAE,GAAGN;IAElC,4CAA4C;IAC5C,IAAIwD,8BAA8BjD,WAAWO,GAAG,CAACT;IACjD,mEAAmE;IACnE,yJAAyJ;IACzJ,IAAI,CAACmD,6BAA6B;QAChCA,8BAA8B,IAAI3B;QAClCtB,WAAWuB,GAAG,CAACzB,mBAAmBmD;IACpC;IAEA,qCAAqC;IACrC,8IAA8I;IAC9I,MAAMC,cAAcjD,IAAI,CAAC,EAAE,CAACH,kBAAkB,CAAC,EAAE;IAEjD,gIAAgI;IAChI,MAAMqD,2BAA2BC,IAAAA,gCAAe,EAACF;IAEjD;;GAEC,GACD,+DAA+D;IAC/D,MAAMG,oBAA+B;QAACH;KAAY;IAElD,qBACE,4DACGF,QACAK,kBAAkBC,GAAG,CAAC,CAACC;QACtB,MAAMC,wBAAwBJ,IAAAA,gCAAe,EAACG;QAC9C,MAAMrD,WAAWuD,IAAAA,0CAAoB,EAACF;QAEtC,OACE;;;;;;;;UAQA,iBACA,6BAACG,8CAAe,CAAC1B,QAAQ;YACvB2B,KAAKF,IAAAA,0CAAoB,EAACF,kBAAkB;YAC5CtB,qBACE,6BAACzC;gBAAsBlB,aAAaA;6BAClC,6BAACsF,4BAAa;gBACZC,gBAAgBzH;gBAChBqG,aAAaA;gBACbC,cAAcA;6BAEd,6BAACR;gBACCI,YAAYA;gBACZH,SAASA;gBACTC,eAAeA;gBACfC,gBAAgBA;6BAEhB,6BAACyB,kCAAgB;gBACfhB,UAAUA;gBACVC,gBAAgBA;6BAEhB,6BAACgB,kCAAgB,sBACf,6BAAClE;gBACCC,mBAAmBA;gBACnBC,KAAKA;gBACLE,MAAMA;gBACND,YAAYiD;gBACZ3E,aAAaA;gBACb4B,UAAUA;gBACV8D,UACEb,6BAA6BK;;WAU5Cb,gBACAC,iBACAC;IAGP;AAGN"}