{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/launchEditor.ts"], "names": ["launchEditor", "isTerminalEditor", "editor", "COMMON_EDITORS_MACOS", "COMMON_EDITORS_LINUX", "atom", "Brackets", "code", "vscodium", "emacs", "gvim", "sublime_text", "vim", "COMMON_EDITORS_WIN", "WINDOWS_FILE_NAME_ACCESS_LIST", "getArgumentsForLineNumber", "fileName", "lineNumber", "colNumber", "editorBasename", "path", "basename", "replace", "toString", "guessEditor", "process", "env", "REACT_EDITOR", "shellQuote", "parse", "platform", "output", "child_process", "execSync", "processNames", "Object", "keys", "i", "length", "processName", "indexOf", "runningProcesses", "split", "processPath", "trim", "error", "VISUAL", "EDITOR", "printInstructions", "errorMessage", "console", "log", "red", "cyan", "green", "fs", "existsSync", "Number", "isInteger", "args", "toLowerCase", "startsWith", "test", "os", "release", "relative", "concat", "push", "p", "undefined", "spawn", "stdio", "detached", "quote", "on", "errorCode", "message"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;CAsBC;;;;+BAwYQA;;;eAAAA;;;;4BAvYwB;wEACP;6DACX;6DACA;+DACE;qEAEM;AAEvB,SAASC,iBAAiBC,MAAc;IACtC,OAAQA;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAQ;gBACX,OAAO;YACT;QACA;YAAS,CACT;IACF;IACA,OAAO;AACT;AAEA,+DAA+D;AAC/D,+EAA+E;AAC/E,wBAAwB;AACxB,MAAMC,uBAAuB;IAC3B,8CAA8C;IAC9C,wDACE;IACF,sDAAsD;IACtD,8DACE;IACF,kEACE;IACF,kEACE;IACF,gEACE;IACF,2EACE;IACF,sDACE;IACF,oDACE;IACF,gDACE;IACF,uDACE;IACF,sDACE;IACF,oDACE;IACF,uDACE;IACF,sDACE;IACF,sDACE;IACF,kDAAkD;IAClD,kDACE;IACF,gDACE;AACJ;AAEA,MAAMC,uBAAuB;IAC3BC,MAAM;IACNC,UAAU;IACVC,MAAM;IACN,iBAAiB;IACjBC,UAAU;IACVC,OAAO;IACPC,MAAM;IACN,WAAW;IACX,eAAe;IACf,cAAc;IACd,eAAe;IACfC,cAAc;IACdC,KAAK;IACL,eAAe;IACf,aAAa;IACb,YAAY;AACd;AAEA,MAAMC,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,kEAAkE;AAClE,6EAA6E;AAC7E,sEAAsE;AACtE,MAAMC,gCACJ;AAEF,SAASC,0BACPb,MAAc,EACdc,QAAgB,EAChBC,UAAkB,EAClBC,SAAiB;IAEjB,MAAMC,iBAAiBC,aAAI,CAACC,QAAQ,CAACnB,QAAQoB,OAAO,CAAC,qBAAqB;IAC1E,OAAQH;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACnB,OAAO;oBAACH,WAAW,MAAMC,aAAa,MAAMC;iBAAU;YACxD;QACA,KAAK;QACL,KAAK;YAAS;gBACZ,OAAO;oBAACF,WAAW,MAAMC;iBAAW;YACtC;QACA,KAAK;YAAa;gBAChB,OAAO;oBAAC,OAAOA;oBAAY,OAAOC;oBAAWF;iBAAS;YACxD;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAQ;gBACX,OAAO;oBAAC,MAAMC;oBAAYD;iBAAS;YACrC;QACA,KAAK;QACL,KAAK;YAAe;gBAClB,OAAO;oBAAC,MAAMC,aAAa,MAAMC;oBAAWF;iBAAS;YACvD;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAQ;gBACX,OAAO;oBAAC;oBAAUC,WAAWM,QAAQ;oBAAIP;iBAAS;YACpD;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAY;gBACf,OAAO;oBAAC;oBAAMA,WAAW,MAAMC,aAAa,MAAMC;iBAAU;YAC9D;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAW;gBACd,OAAO;oBAAC;oBAAUD,WAAWM,QAAQ;oBAAIP;iBAAS;YACpD;QACA;YAAS;gBACP,oDAAoD;gBACpD,8DAA8D;gBAC9D,8CAA8C;gBAC9C,OAAO;oBAACA;iBAAS;YACnB;IACF;AACF;AAEA,SAASQ;IACP,8BAA8B;IAC9B,IAAIC,QAAQC,GAAG,CAACC,YAAY,EAAE;QAC5B,OAAOC,mBAAU,CAACC,KAAK,CAACJ,QAAQC,GAAG,CAACC,YAAY;IAClD;IAEA,wDAAwD;IACxD,4BAA4B;IAC5B,2BAA2B;IAC3B,IAAI;QACF,IAAIF,QAAQK,QAAQ,KAAK,UAAU;YACjC,MAAMC,SAASC,sBAAa,CAACC,QAAQ,CAAC,QAAQV,QAAQ;YACtD,MAAMW,eAAeC,OAAOC,IAAI,CAACjC;YACjC,IAAK,IAAIkC,IAAI,GAAGA,IAAIH,aAAaI,MAAM,EAAED,IAAK;gBAC5C,MAAME,cAAcL,YAAY,CAACG,EAAE;gBACnC,IAAIN,OAAOS,OAAO,CAACD,iBAAiB,CAAC,GAAG;oBACtC,OAAO;wBAAEpC,oBAA4B,CAACoC,YAAY;qBAAC;gBACrD;YACF;QACF,OAAO,IAAId,QAAQK,QAAQ,KAAK,SAAS;YACvC,kEAAkE;YAClE,wEAAwE;YACxE,MAAMC,SAASC,sBAAa,CACzBC,QAAQ,CACP,sEAEDV,QAAQ;YACX,MAAMkB,mBAAmBV,OAAOW,KAAK,CAAC;YACtC,IAAK,IAAIL,IAAI,GAAGA,IAAII,iBAAiBH,MAAM,EAAED,IAAK;gBAChD,MAAMM,cAAcF,gBAAgB,CAACJ,EAAE,CAACO,IAAI;gBAC5C,MAAML,cAAcnB,aAAI,CAACC,QAAQ,CAACsB;gBAClC,IAAI9B,mBAAmB2B,OAAO,CAACD,iBAAiB,CAAC,GAAG;oBAClD,OAAO;wBAACI;qBAAY;gBACtB;YACF;QACF,OAAO,IAAIlB,QAAQK,QAAQ,KAAK,SAAS;YACvC,8BAA8B;YAC9B,oCAAoC;YACpC,iCAAiC;YACjC,MAAMC,SAASC,sBAAa,CACzBC,QAAQ,CAAC,yCACTV,QAAQ;YACX,MAAMW,eAAeC,OAAOC,IAAI,CAAChC;YACjC,IAAK,IAAIiC,IAAI,GAAGA,IAAIH,aAAaI,MAAM,EAAED,IAAK;gBAC5C,MAAME,cAAcL,YAAY,CAACG,EAAE;gBACnC,IAAIN,OAAOS,OAAO,CAACD,iBAAiB,CAAC,GAAG;oBACtC,OAAO;wBAAEnC,oBAA4B,CAACmC,YAAY;qBAAW;gBAC/D;YACF;QACF;IACF,EAAE,OAAOM,OAAO;IACd,YAAY;IACd;IAEA,sCAAsC;IACtC,IAAIpB,QAAQC,GAAG,CAACoB,MAAM,EAAE;QACtB,OAAO;YAACrB,QAAQC,GAAG,CAACoB,MAAM;SAAC;IAC7B,OAAO,IAAIrB,QAAQC,GAAG,CAACqB,MAAM,EAAE;QAC7B,OAAO;YAACtB,QAAQC,GAAG,CAACqB,MAAM;SAAC;IAC7B;IAEA,OAAO,EAAE;AACX;AAEA,SAASC,kBAAkBhC,QAAgB,EAAEiC,YAA2B;IACtEC,QAAQC,GAAG;IACXD,QAAQC,GAAG,CACTC,IAAAA,eAAG,EAAC,oBAAoBhC,aAAI,CAACC,QAAQ,CAACL,YAAY;IAEpD,IAAIiC,cAAc;QAChB,IAAIA,YAAY,CAACA,aAAaX,MAAM,GAAG,EAAE,KAAK,KAAK;YACjDW,gBAAgB;QAClB;QACAC,QAAQC,GAAG,CAACC,IAAAA,eAAG,EAAC,8CAA8CH;IAChE;IACAC,QAAQC,GAAG;IACXD,QAAQC,GAAG,CACT,0DACEE,IAAAA,gBAAI,EAAC,uBACL,aACAC,IAAAA,iBAAK,EAAC,gBACN,kCACA;IAEJJ,QAAQC,GAAG;AACb;AAEA,SAASnD,aAAagB,QAAgB,EAAEC,UAAkB,EAAEC,SAAiB;IAC3E,IAAI,CAACqC,WAAE,CAACC,UAAU,CAACxC,WAAW;QAC5B;IACF;IAEA,wDAAwD;IACxD,8GAA8G;IAC9G,sCAAsC;IACtC,IAAI,CAAEyC,CAAAA,OAAOC,SAAS,CAACzC,eAAeA,aAAa,CAAA,GAAI;QACrD;IACF;IAEA,8DAA8D;IAC9D,eAAe;IACf,IAAI,CAAEwC,CAAAA,OAAOC,SAAS,CAACxC,cAAcA,YAAY,CAAA,GAAI;QACnDA,YAAY;IACd;IAEA,IAAI,CAAChB,QAAQ,GAAGyD,KAAK,GAAGnC;IAExB,IAAI,CAACtB,QAAQ;QACX8C,kBAAkBhC,UAAU;QAC5B;IACF;IAEA,IAAId,OAAO0D,WAAW,OAAO,QAAQ;QACnC;IACF;IAEA,IACEnC,QAAQK,QAAQ,KAAK,WACrBd,SAAS6C,UAAU,CAAC,YACpB,aAAaC,IAAI,CAACC,WAAE,CAACC,OAAO,KAC5B;QACA,8DAA8D;QAC9D,mDAAmD;QACnD,gEAAgE;QAChE,kGAAkG;QAClG,gEAAgE;QAChE,oEAAoE;QACpEhD,WAAWI,aAAI,CAAC6C,QAAQ,CAAC,IAAIjD;IAC/B;IAEA,2EAA2E;IAC3E,+EAA+E;IAC/E,4EAA4E;IAC5E,uEAAuE;IACvE,IACES,QAAQK,QAAQ,KAAK,WACrB,CAAChB,8BAA8BgD,IAAI,CAAC9C,SAAS4B,IAAI,KACjD;QACAM,QAAQC,GAAG;QACXD,QAAQC,GAAG,CACTC,IAAAA,eAAG,EAAC,oBAAoBhC,aAAI,CAACC,QAAQ,CAACL,YAAY;QAEpDkC,QAAQC,GAAG;QACXD,QAAQC,GAAG,CACT,4EACE,sEACA,uEACA;QAEJD,QAAQC,GAAG;QACX;IACF;IAEA,IAAIlC,YAAY;QACd0C,OAAOA,KAAKO,MAAM,CAChBnD,0BAA0Bb,QAAQc,UAAUC,YAAYC;IAE5D,OAAO;QACLyC,KAAKQ,IAAI,CAACnD;IACZ;IAEA,IAAIoD,IAA4CC;IAChD,IAAI5C,QAAQK,QAAQ,KAAK,SAAS;QAChC,kEAAkE;QAClE,qBAAqB;QACrBsC,IAAIpC,sBAAa,CAACsC,KAAK,CAAC,WAAW;YAAC;YAAMpE;SAAO,CAACgE,MAAM,CAACP,OAAO;YAC9DY,OAAO;YACPC,UAAU;QACZ;IACF,OAAO,IAAIvE,iBAAiBC,SAAS;QACnC,IAAIuB,QAAQK,QAAQ,KAAK,UAAU;YACjCsC,IAAIpC,sBAAa,CAACsC,KAAK,CACrB,aACA;gBACE;gBACC,+CAA4C1C,mBAAU,CAAC6C,KAAK,CAAC;oBAC5DvE;uBACGyD;iBACJ,IAAE;aACJ,EACD;gBAAEY,OAAO;YAAS;QAEtB,OAAO;YACLvB,kBAAkBhC,UAAU;QAC9B;IACF,OAAO;QACLoD,IAAIpC,sBAAa,CAACsC,KAAK,CAACpE,QAAQyD,MAAM;YAAEY,OAAO;QAAU;IAC3D;IAEA,IAAIH,GAAG;QACLA,EAAEM,EAAE,CAAC,QAAQ,SAAUC,SAAS;YAC9B,IAAIA,WAAW;gBACb3B,kBAAkBhC,UAAU,WAAW2D,YAAY;YACrD;QACF;QACAP,EAAEM,EAAE,CAAC,SAAS,SAAU7B,KAAK;YAC3BG,kBAAkBhC,UAAU6B,MAAM+B,OAAO;QAC3C;IACF;AACF"}