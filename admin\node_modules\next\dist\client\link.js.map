{"version": 3, "sources": ["../../src/client/link.tsx"], "names": ["prefetched", "Set", "prefetch", "router", "href", "as", "options", "appOptions", "isAppRouter", "window", "isLocalURL", "bypassPrefetchedCheck", "locale", "undefined", "prefetched<PERSON><PERSON>", "has", "add", "prefetchPromise", "Promise", "resolve", "catch", "err", "process", "env", "NODE_ENV", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "replace", "shallow", "scroll", "nodeName", "isAnchorNodeName", "toUpperCase", "preventDefault", "navigate", "routerScroll", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "Link", "forwardRef", "LinkComponent", "props", "forwardedRef", "children", "hrefProp", "asProp", "childrenProp", "prefetchProp", "passHref", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "restProps", "a", "pagesRouter", "useContext", "RouterContext", "appRouter", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "createPropError", "args", "Error", "key", "expected", "actual", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "hasWarned", "useRef", "current", "console", "warn", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "resolvedAs", "resolveHref", "previousHref", "previousAs", "child", "Children", "only", "type", "childRef", "ref", "setIntersectionRef", "isVisible", "resetVisible", "useIntersection", "rootMargin", "setRef", "useCallback", "el", "useEffect", "kind", "childProps", "defaultPrevented", "priority", "isAbsoluteUrl", "cur<PERSON><PERSON><PERSON>", "localeDomain", "isLocaleDomain", "getDomainLocale", "locales", "domainLocales", "addBasePath", "addLocale", "defaultLocale", "cloneElement"], "mappings": "AAAA;;;;;+BAmvBA;;;eAAA;;;;gEA5uBkB;6BAEU;4BACD;2BACD;uBACI;2BACJ;4CACI;+CACG;iCAKD;iCACA;6BACJ;oCACC;AA0F7B,MAAMA,aAAa,IAAIC;AAUvB,SAASC,SACPC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACVC,OAAwB,EACxBC,UAAoC,EACpCC,WAAoB;IAEpB,IAAI,OAAOC,WAAW,aAAa;QACjC;IACF;IAEA,gJAAgJ;IAChJ,IAAI,CAACD,eAAe,CAACE,IAAAA,sBAAU,EAACN,OAAO;QACrC;IACF;IAEA,4EAA4E;IAC5E,YAAY;IACZ,IAAI,CAACE,QAAQK,qBAAqB,EAAE;QAClC,MAAMC,SACJ,iEAAiE;QACjE,OAAON,QAAQM,MAAM,KAAK,cACtBN,QAAQM,MAAM,GAEhB,YAAYT,SACVA,OAAOS,MAAM,GACbC;QAEN,MAAMC,gBAAgBV,OAAO,MAAMC,KAAK,MAAMO;QAE9C,kEAAkE;QAClE,IAAIZ,WAAWe,GAAG,CAACD,gBAAgB;YACjC;QACF;QAEA,+BAA+B;QAC/Bd,WAAWgB,GAAG,CAACF;IACjB;IAEA,MAAMG,kBAAkBT,cACpB,AAACL,OAA6BD,QAAQ,CAACE,MAAMG,cAC7C,AAACJ,OAAsBD,QAAQ,CAACE,MAAMC,IAAIC;IAE9C,uDAAuD;IACvD,0DAA0D;IAC1D,sDAAsD;IACtD,yDAAyD;IACzDY,QAAQC,OAAO,CAACF,iBAAiBG,KAAK,CAAC,CAACC;QACtC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC,qCAAqC;YACrC,MAAMH;QACR;IACF;AACF;AAEA,SAASI,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACE,AAACD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBnC,MAAsC,EACtCC,IAAY,EACZC,EAAU,EACVkC,OAAiB,EACjBC,OAAiB,EACjBC,MAAgB,EAChB7B,MAAuB,EACvBJ,WAAqB;IAErB,MAAM,EAAEkC,QAAQ,EAAE,GAAGJ,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMe,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACED,oBACClB,CAAAA,gBAAgBa,MACf,gJAAgJ;IAC/I,CAAC9B,eAAe,CAACE,IAAAA,sBAAU,EAACN,KAAK,GACpC;QACA,8CAA8C;QAC9C;IACF;IAEAkC,EAAEO,cAAc;IAEhB,MAAMC,WAAW;QACf,wEAAwE;QACxE,MAAMC,eAAeN,iBAAAA,SAAU;QAC/B,IAAI,oBAAoBtC,QAAQ;YAC9BA,MAAM,CAACoC,UAAU,YAAY,OAAO,CAACnC,MAAMC,IAAI;gBAC7CmC;gBACA5B;gBACA6B,QAAQM;YACV;QACF,OAAO;YACL5C,MAAM,CAACoC,UAAU,YAAY,OAAO,CAAClC,MAAMD,MAAM;gBAC/CqC,QAAQM;YACV;QACF;IACF;IAEA,IAAIvC,aAAa;QACfwC,cAAK,CAACC,eAAe,CAACH;IACxB,OAAO;QACLA;IACF;AACF;AAOA,SAASI,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,IAAAA,oBAAS,EAACD;AACnB;AAEA;;CAEC,GACD,MAAME,qBAAOL,cAAK,CAACM,UAAU,CAC3B,SAASC,cAAcC,KAAK,EAAEC,YAAY;IACxC,IAAIC;IAEJ,MAAM,EACJtD,MAAMuD,QAAQ,EACdtD,IAAIuD,MAAM,EACVF,UAAUG,YAAY,EACtB3D,UAAU4D,eAAe,IAAI,EAC7BC,QAAQ,EACRxB,OAAO,EACPC,OAAO,EACPC,MAAM,EACN7B,MAAM,EACNoD,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtB,GAAGC,WACJ,GAAGd;IAEJE,WAAWG;IAEX,IACEQ,kBACC,CAAA,OAAOX,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,yBAAW,6BAACa,WAAGb;IACjB;IAEA,MAAMc,cAAcxB,cAAK,CAACyB,UAAU,CAACC,yCAAa;IAClD,MAAMC,YAAY3B,cAAK,CAACyB,UAAU,CAACG,+CAAgB;IACnD,MAAMzE,SAASqE,sBAAAA,cAAeG;IAE9B,0DAA0D;IAC1D,MAAMnE,cAAc,CAACgE;IAErB,MAAMK,kBAAkBf,iBAAiB;IACzC;;;;;KAKC,GACD,MAAMgB,kBACJhB,iBAAiB,OAAOiB,gCAAY,CAACC,IAAI,GAAGD,gCAAY,CAACE,IAAI;IAE/D,IAAI3D,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,SAAS0D,gBAAgBC,IAIxB;YACC,OAAO,IAAIC,MACT,AAAC,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAO9E,WAAW,cACf,qEACA,EAAC;QAEX;QAEA,sCAAsC;QACtC,MAAM+E,qBAAsD;YAC1DpF,MAAM;QACR;QACA,MAAMqF,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACP;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACE7B,KAAK,CAAC6B,IAAI,IAAI,QACb,OAAO7B,KAAK,CAAC6B,IAAI,KAAK,YAAY,OAAO7B,KAAK,CAAC6B,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQ/B,KAAK,CAAC6B,IAAI,KAAK,OAAO,SAAS,OAAO7B,KAAK,CAAC6B,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMQ,IAAWR;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMS,qBAAsD;YAC1DzF,IAAI;YACJkC,SAAS;YACTE,QAAQ;YACRD,SAAS;YACTuB,UAAU;YACV7D,UAAU;YACVU,QAAQ;YACRoD,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;QAClB;QACA,MAAM0B,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACP;YACrB,MAAMW,UAAU,OAAOxC,KAAK,CAAC6B,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAI7B,KAAK,CAAC6B,IAAI,IAAIW,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IAAIX,QAAQ,UAAU;gBAC3B,IAAI7B,KAAK,CAAC6B,IAAI,IAAIW,YAAY,UAAU;oBACtC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,gBACR;gBACA,IAAI7B,KAAK,CAAC6B,IAAI,IAAIW,YAAY,YAAY;oBACxC,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO,IACLX,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,kBACR;gBACA,IAAI7B,KAAK,CAAC6B,IAAI,IAAI,QAAQW,YAAY,WAAW;oBAC/C,MAAMd,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQS;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWR;YACnB;QACF;QAEA,4FAA4F;QAC5F,sDAAsD;QACtD,MAAMY,YAAYjD,cAAK,CAACkD,MAAM,CAAC;QAC/B,IAAI1C,MAAMtD,QAAQ,IAAI,CAAC+F,UAAUE,OAAO,IAAI,CAAC3F,aAAa;YACxDyF,UAAUE,OAAO,GAAG;YACpBC,QAAQC,IAAI,CACV;QAEJ;IACF;IAEA,IAAI/E,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,IAAIhB,eAAe,CAACoD,QAAQ;YAC1B,IAAIxD;YACJ,IAAI,OAAOuD,aAAa,UAAU;gBAChCvD,OAAOuD;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS2C,QAAQ,KAAK,UAC7B;gBACAlG,OAAOuD,SAAS2C,QAAQ;YAC1B;YAEA,IAAIlG,MAAM;gBACR,MAAMmG,oBAAoBnG,KACvBoG,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,IAAInB,MACR,AAAC,mBAAiBhF,OAAK;gBAE3B;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAG2C,cAAK,CAAC6D,OAAO,CAAC;QACjC,IAAI,CAACrC,aAAa;YAChB,MAAMsC,eAAe5D,kBAAkBS;YACvC,OAAO;gBACLvD,MAAM0G;gBACNzG,IAAIuD,SAASV,kBAAkBU,UAAUkD;YAC3C;QACF;QAEA,MAAM,CAACA,cAAcC,WAAW,GAAGC,IAAAA,wBAAW,EAC5CxC,aACAb,UACA;QAGF,OAAO;YACLvD,MAAM0G;YACNzG,IAAIuD,SACAoD,IAAAA,wBAAW,EAACxC,aAAaZ,UACzBmD,cAAcD;QACpB;IACF,GAAG;QAACtC;QAAab;QAAUC;KAAO;IAElC,MAAMqD,eAAejE,cAAK,CAACkD,MAAM,CAAS9F;IAC1C,MAAM8G,aAAalE,cAAK,CAACkD,MAAM,CAAS7F;IAExC,oFAAoF;IACpF,IAAI8G;IACJ,IAAI9C,gBAAgB;QAClB,IAAI/C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAIwC,SAAS;gBACXoC,QAAQC,IAAI,CACV,AAAC,oDAAoD1C,WAAS;YAElE;YACA,IAAIO,kBAAkB;gBACpBkC,QAAQC,IAAI,CACV,AAAC,yDAAyD1C,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQnE,cAAK,CAACoE,QAAQ,CAACC,IAAI,CAAC3D;YAC9B,EAAE,OAAOrC,KAAK;gBACZ,IAAI,CAACqC,UAAU;oBACb,MAAM,IAAI0B,MACR,AAAC,uDAAuDzB,WAAS;gBAErE;gBACA,MAAM,IAAIyB,MACR,AAAC,6DAA6DzB,WAAS,8FACpE,CAAA,OAAOlD,WAAW,cACf,sEACA,EAAC;YAEX;QACF,OAAO;YACL0G,QAAQnE,cAAK,CAACoE,QAAQ,CAACC,IAAI,CAAC3D;QAC9B;IACF,OAAO;QACL,IAAIpC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,IAAI,CAACkC,4BAAD,AAACA,SAAkB4D,IAAI,MAAK,KAAK;gBACnC,MAAM,IAAIlC,MACR;YAEJ;QACF;IACF;IAEA,MAAMmC,WAAgBlD,iBAClB8C,SAAS,OAAOA,UAAU,YAAYA,MAAMK,GAAG,GAC/C/D;IAEJ,MAAM,CAACgE,oBAAoBC,WAAWC,aAAa,GAAGC,IAAAA,gCAAe,EAAC;QACpEC,YAAY;IACd;IAEA,MAAMC,SAAS9E,cAAK,CAAC+E,WAAW,CAC9B,CAACC;QACC,4EAA4E;QAC5E,IAAId,WAAWf,OAAO,KAAK9F,MAAM4G,aAAad,OAAO,KAAK/F,MAAM;YAC9DuH;YACAT,WAAWf,OAAO,GAAG9F;YACrB4G,aAAad,OAAO,GAAG/F;QACzB;QAEAqH,mBAAmBO;QACnB,IAAIT,UAAU;YACZ,IAAI,OAAOA,aAAa,YAAYA,SAASS;iBACxC,IAAI,OAAOT,aAAa,UAAU;gBACrCA,SAASpB,OAAO,GAAG6B;YACrB;QACF;IACF,GACA;QAAC3H;QAAIkH;QAAUnH;QAAMuH;QAAcF;KAAmB;IAGxD,2DAA2D;IAC3DzE,cAAK,CAACiF,SAAS,CAAC;QACd,gHAAgH;QAChH,IAAI3G,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;YACzC;QACF;QAEA,IAAI,CAACrB,QAAQ;YACX;QACF;QAEA,2DAA2D;QAC3D,IAAI,CAACuH,aAAa,CAAC7C,iBAAiB;YAClC;QACF;QAEA,oBAAoB;QACpB3E,SACEC,QACAC,MACAC,IACA;YAAEO;QAAO,GACT;YACEsH,MAAMpD;QACR,GACAtE;IAEJ,GAAG;QACDH;QACAD;QACAsH;QACA9G;QACAiE;QACAL,+BAAAA,YAAa5D,MAAM;QACnBT;QACAK;QACAsE;KACD;IAED,MAAMqD,aAMF;QACFX,KAAKM;QACL9D,SAAQ1B,CAAC;YACP,IAAIhB,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;gBACzC,IAAI,CAACc,GAAG;oBACN,MAAM,IAAI8C,MACP;gBAEL;YACF;YAEA,IAAI,CAACf,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQ1B;YACV;YAEA,IACE+B,kBACA8C,MAAM3D,KAAK,IACX,OAAO2D,MAAM3D,KAAK,CAACQ,OAAO,KAAK,YAC/B;gBACAmD,MAAM3D,KAAK,CAACQ,OAAO,CAAC1B;YACtB;YAEA,IAAI,CAACnC,QAAQ;gBACX;YACF;YAEA,IAAImC,EAAE8F,gBAAgB,EAAE;gBACtB;YACF;YAEA/F,YACEC,GACAnC,QACAC,MACAC,IACAkC,SACAC,SACAC,QACA7B,QACAJ;QAEJ;QACAyD,cAAa3B,CAAC;YACZ,IAAI,CAAC+B,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiB5B;YACnB;YAEA,IACE+B,kBACA8C,MAAM3D,KAAK,IACX,OAAO2D,MAAM3D,KAAK,CAACS,YAAY,KAAK,YACpC;gBACAkD,MAAM3D,KAAK,CAACS,YAAY,CAAC3B;YAC3B;YAEA,IAAI,CAACnC,QAAQ;gBACX;YACF;YAEA,IACE,AAAC,CAAA,CAAC0E,mBAAmBvD,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAY,KAC1DhB,aACA;gBACA;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEO;gBACAyH,UAAU;gBACV,gGAAgG;gBAChG1H,uBAAuB;YACzB,GACA;gBACEuH,MAAMpD;YACR,GACAtE;QAEJ;QACA2D,cAAa7B,CAAC;YACZ,IAAI,CAAC+B,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiB9B;YACnB;YAEA,IACE+B,kBACA8C,MAAM3D,KAAK,IACX,OAAO2D,MAAM3D,KAAK,CAACW,YAAY,KAAK,YACpC;gBACAgD,MAAM3D,KAAK,CAACW,YAAY,CAAC7B;YAC3B;YAEA,IAAI,CAACnC,QAAQ;gBACX;YACF;YAEA,IAAI,CAAC0E,mBAAmBrE,aAAa;gBACnC;YACF;YAEAN,SACEC,QACAC,MACAC,IACA;gBACEO;gBACAyH,UAAU;gBACV,gGAAgG;gBAChG1H,uBAAuB;YACzB,GACA;gBACEuH,MAAMpD;YACR,GACAtE;QAEJ;IACF;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,oFAAoF;IACpF,IAAI8H,IAAAA,oBAAa,EAACjI,KAAK;QACrB8H,WAAW/H,IAAI,GAAGC;IACpB,OAAO,IACL,CAACgE,kBACDN,YACCoD,MAAMG,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUH,MAAM3D,KAAK,AAAD,GAC7C;QACA,MAAM+E,YACJ,OAAO3H,WAAW,cAAcA,SAAS4D,+BAAAA,YAAa5D,MAAM;QAE9D,uEAAuE;QACvE,uEAAuE;QACvE,MAAM4H,eACJhE,CAAAA,+BAAAA,YAAaiE,cAAc,KAC3BC,IAAAA,gCAAe,EACbrI,IACAkI,WACA/D,+BAAAA,YAAamE,OAAO,EACpBnE,+BAAAA,YAAaoE,aAAa;QAG9BT,WAAW/H,IAAI,GACboI,gBACAK,IAAAA,wBAAW,EAACC,IAAAA,oBAAS,EAACzI,IAAIkI,WAAW/D,+BAAAA,YAAauE,aAAa;IACnE;IAEA,OAAO1E,+BACLrB,cAAK,CAACgG,YAAY,CAAC7B,OAAOgB,4BAE1B,6BAAC5D;QAAG,GAAGD,SAAS;QAAG,GAAG6D,UAAU;OAC7BzE;AAGP;MAGF,WAAeL"}