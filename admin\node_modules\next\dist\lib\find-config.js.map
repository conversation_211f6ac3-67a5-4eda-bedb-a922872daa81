{"version": 3, "sources": ["../../src/lib/find-config.ts"], "names": ["findConfigPath", "findConfig", "dir", "key", "findUp", "cwd", "directory", "_returnFile", "packageJsonPath", "packageJson", "require", "filePath", "endsWith", "fileContents", "fs", "readFileSync", "JSON5", "parse"], "mappings": ";;;;;;;;;;;;;;;IAQgBA,cAAc;eAAdA;;IAuBMC,UAAU;eAAVA;;;+DA/BH;2DACJ;8DACG;;;;;;AAMX,SAASD,eACdE,GAAW,EACXC,GAAW;IAEX,4EAA4E;IAC5E,mBAAmB;IACnB,OAAOC,IAAAA,eAAM,EACX;QACE,CAAC,CAAC,EAAED,IAAI,OAAO,CAAC;QAChB,CAAC,EAAEA,IAAI,YAAY,CAAC;QACpB,CAAC,CAAC,EAAEA,IAAI,KAAK,CAAC;QACd,CAAC,EAAEA,IAAI,UAAU,CAAC;QAClB,CAAC,EAAEA,IAAI,WAAW,CAAC;KACpB,EACD;QACEE,KAAKH;IACP;AAEJ;AAKO,eAAeD,WACpBK,SAAiB,EACjBH,GAAW,EACXI,WAAqB;IAErB,oEAAoE;IACpE,MAAMC,kBAAkB,MAAMJ,IAAAA,eAAM,EAAC,gBAAgB;QAAEC,KAAKC;IAAU;IACtE,IAAIE,iBAAiB;QACnB,MAAMC,cAAcC,QAAQF;QAC5B,IAAIC,WAAW,CAACN,IAAI,IAAI,QAAQ,OAAOM,WAAW,CAACN,IAAI,KAAK,UAAU;YACpE,OAAOM,WAAW,CAACN,IAAI;QACzB;IACF;IAEA,MAAMQ,WAAW,MAAMX,eAAeM,WAAWH;IAEjD,IAAIQ,UAAU;QACZ,IAAIA,SAASC,QAAQ,CAAC,UAAUD,SAASC,QAAQ,CAAC,SAAS;YACzD,OAAOF,QAAQC;QACjB;QAEA,sEAAsE;QACtE,kEAAkE;QAClE,MAAME,eAAeC,WAAE,CAACC,YAAY,CAACJ,UAAU;QAC/C,OAAOK,cAAK,CAACC,KAAK,CAACJ;IACrB;IAEA,OAAO;AACT"}