{"version": 3, "sources": ["../../src/lib/format-server-error.ts"], "names": ["formatServerError", "invalidServerComponentReactHooks", "setMessage", "error", "message", "stack", "lines", "split", "join", "includes", "addedMessage", "clientHook", "regex", "RegExp", "test"], "mappings": ";;;;+BAwBgBA;;;eAAAA;;;AAxBhB,MAAMC,mCAAmC;IACvC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,SAASC,WAAWC,KAAY,EAAEC,OAAe;IAC/CD,MAAMC,OAAO,GAAGA;IAChB,IAAID,MAAME,KAAK,EAAE;QACf,MAAMC,QAAQH,MAAME,KAAK,CAACE,KAAK,CAAC;QAChCD,KAAK,CAAC,EAAE,GAAGF;QACXD,MAAME,KAAK,GAAGC,MAAME,IAAI,CAAC;IAC3B;AACF;AAEO,SAASR,kBAAkBG,KAAY;IAC5C,IAAI,QAAOA,yBAAAA,MAAOC,OAAO,MAAK,UAAU;IAExC,IACED,MAAMC,OAAO,CAACK,QAAQ,CACpB,+DAEF;QACA,MAAMC,eACJ;QAEF,qEAAqE;QACrE,IAAIP,MAAMC,OAAO,CAACK,QAAQ,CAACC,eAAe;QAE1CR,WACEC,OACA,CAAC,EAAEA,MAAMC,OAAO,CAAC;;AAEvB,EAAEM,aAAa,CAAC;QAEZ;IACF;IAEA,IAAIP,MAAMC,OAAO,CAACK,QAAQ,CAAC,oCAAoC;QAC7DP,WACEC,OACA;QAEF;IACF;IAEA,KAAK,MAAMQ,cAAcV,iCAAkC;QACzD,MAAMW,QAAQ,IAAIC,OAAO,CAAC,GAAG,EAAEF,WAAW,sBAAsB,CAAC;QACjE,IAAIC,MAAME,IAAI,CAACX,MAAMC,OAAO,GAAG;YAC7BF,WACEC,OACA,CAAC,EAAEQ,WAAW,oLAAoL,CAAC;YAErM;QACF;IACF;AACF"}