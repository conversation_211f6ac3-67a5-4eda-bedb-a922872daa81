{"version": 3, "sources": ["../../src/lib/has-necessary-dependencies.ts"], "names": ["hasNecessaryDependencies", "baseDir", "requiredPackages", "resolutions", "Map", "missingPackages", "Promise", "all", "map", "p", "pkgPath", "fs", "realpath", "resolveFrom", "pkg", "pkgDir", "dirname", "exportsRestrict", "fileNameToVerify", "relative", "file", "fileToVerify", "join", "existsSync", "set", "push", "_", "resolved", "missing"], "mappings": ";;;;+BAes<PERSON>;;;eAAAA;;;oBAfqB;6BACf;sBACY;AAajC,eAAeA,yBACpBC,OAAe,EACfC,gBAAqC;IAErC,IAAIC,cAAc,IAAIC;IACtB,MAAMC,kBAAuC,EAAE;IAE/C,MAAMC,QAAQC,GAAG,CACfL,iBAAiBM,GAAG,CAAC,OAAOC;QAC1B,IAAI;YACF,MAAMC,UAAU,MAAMC,YAAE,CAACC,QAAQ,CAC/BC,IAAAA,wBAAW,EAACZ,SAAS,CAAC,EAAEQ,EAAEK,GAAG,CAAC,aAAa,CAAC;YAE9C,MAAMC,SAASC,IAAAA,aAAO,EAACN;YAEvB,IAAID,EAAEQ,eAAe,EAAE;gBACrB,MAAMC,mBAAmBC,IAAAA,cAAQ,EAACV,EAAEK,GAAG,EAAEL,EAAEW,IAAI;gBAC/C,IAAIF,kBAAkB;oBACpB,MAAMG,eAAeC,IAAAA,UAAI,EAACP,QAAQG;oBAClC,IAAIK,IAAAA,cAAU,EAACF,eAAe;wBAC5BlB,YAAYqB,GAAG,CAACf,EAAEK,GAAG,EAAEO;oBACzB,OAAO;wBACL,OAAOhB,gBAAgBoB,IAAI,CAAChB;oBAC9B;gBACF,OAAO;oBACLN,YAAYqB,GAAG,CAACf,EAAEK,GAAG,EAAEJ;gBACzB;YACF,OAAO;gBACLP,YAAYqB,GAAG,CAACf,EAAEK,GAAG,EAAED,IAAAA,wBAAW,EAACZ,SAASQ,EAAEW,IAAI;YACpD;QACF,EAAE,OAAOM,GAAG;YACV,OAAOrB,gBAAgBoB,IAAI,CAAChB;QAC9B;IACF;IAGF,OAAO;QACLkB,UAAUxB;QACVyB,SAASvB;IACX;AACF"}