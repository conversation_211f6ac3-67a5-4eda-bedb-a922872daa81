{"version": 3, "sources": ["../../src/lib/mkcert.ts"], "names": ["createSelfSignedCertificate", "MKCERT_VERSION", "getBinaryName", "platform", "process", "arch", "Error", "downloadBinary", "binaryName", "cacheDirectory", "getCacheDirectory", "binaryPath", "path", "join", "fs", "existsSync", "downloadUrl", "promises", "mkdir", "recursive", "Log", "info", "response", "fetch", "ok", "body", "status", "arrayBuffer", "buffer", "<PERSON><PERSON><PERSON>", "from", "writeFile", "chmod", "err", "error", "host", "certDir", "resolvedCertDir", "resolve", "cwd", "keyP<PERSON>", "certPath", "defaultHosts", "hosts", "includes", "execSync", "stdio", "caLocation", "toString", "trim", "gitignore<PERSON>ath", "gitignore", "readFile", "appendFile", "key", "cert", "rootCA"], "mappings": ";;;;+BAmEsBA;;;eAAAA;;;2DAnEP;6DACE;mCACiB;6DACb;+BACI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEzB,MAAMC,iBAAiB;AAQvB,SAASC;IACP,MAAMC,WAAWC,QAAQD,QAAQ;IACjC,MAAME,OAAOD,QAAQC,IAAI,KAAK,QAAQ,UAAUD,QAAQC,IAAI;IAE5D,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,SAAS,EAAEI,KAAK,IAAI,CAAC;IACvD;IACA,IAAIF,aAAa,UAAU;QACzB,OAAO,CAAC,OAAO,EAAEF,eAAe,QAAQ,EAAEI,KAAK,CAAC;IAClD;IACA,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,OAAO,EAAEI,KAAK,CAAC;IACjD;IAEA,MAAM,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,SAAS,CAAC;AACrD;AAEA,eAAeI;IACb,IAAI;QACF,MAAMC,aAAaN;QACnB,MAAMO,iBAAiBC,IAAAA,oCAAiB,EAAC;QACzC,MAAMC,aAAaC,aAAI,CAACC,IAAI,CAACJ,gBAAgBD;QAE7C,IAAIM,WAAE,CAACC,UAAU,CAACJ,aAAa;YAC7B,OAAOA;QACT;QAEA,MAAMK,cAAc,CAAC,wDAAwD,EAAEf,eAAe,CAAC,EAAEO,WAAW,CAAC;QAE7G,MAAMM,WAAE,CAACG,QAAQ,CAACC,KAAK,CAACT,gBAAgB;YAAEU,WAAW;QAAK;QAE1DC,KAAIC,IAAI,CAAC,CAAC,6BAA6B,CAAC;QAExC,MAAMC,WAAW,MAAMC,MAAMP;QAE7B,IAAI,CAACM,SAASE,EAAE,IAAI,CAACF,SAASG,IAAI,EAAE;YAClC,MAAM,IAAInB,MAAM,CAAC,2BAA2B,EAAEgB,SAASI,MAAM,CAAC,CAAC;QACjE;QAEAN,KAAIC,IAAI,CAAC,CAAC,iDAAiD,CAAC;QAE5D,MAAMM,cAAc,MAAML,SAASK,WAAW;QAC9C,MAAMC,SAASC,OAAOC,IAAI,CAACH;QAE3B,MAAMb,WAAE,CAACG,QAAQ,CAACc,SAAS,CAACpB,YAAYiB;QACxC,MAAMd,WAAE,CAACG,QAAQ,CAACe,KAAK,CAACrB,YAAY;QAEpC,OAAOA;IACT,EAAE,OAAOsB,KAAK;QACZb,KAAIc,KAAK,CAAC,6BAA6BD;IACzC;AACF;AAEO,eAAejC,4BACpBmC,IAAa,EACbC,UAAkB,cAAc;IAEhC,IAAI;QACF,MAAMzB,aAAa,MAAMJ;QACzB,IAAI,CAACI,YAAY,MAAM,IAAIL,MAAM;QAEjC,MAAM+B,kBAAkBzB,aAAI,CAAC0B,OAAO,CAAClC,QAAQmC,GAAG,IAAI,CAAC,EAAE,EAAEH,QAAQ,CAAC;QAElE,MAAMtB,WAAE,CAACG,QAAQ,CAACC,KAAK,CAACmB,iBAAiB;YACvClB,WAAW;QACb;QAEA,MAAMqB,UAAU5B,aAAI,CAAC0B,OAAO,CAACD,iBAAiB;QAC9C,MAAMI,WAAW7B,aAAI,CAAC0B,OAAO,CAACD,iBAAiB;QAE/CjB,KAAIC,IAAI,CACN;QAGF,MAAMqB,eAAe;YAAC;YAAa;YAAa;SAAM;QAEtD,MAAMC,QACJR,QAAQ,CAACO,aAAaE,QAAQ,CAACT,QAC3B;eAAIO;YAAcP;SAAK,GACvBO;QAENG,IAAAA,uBAAQ,EACN,CAAC,CAAC,EAAElC,WAAW,sBAAsB,EAAE6B,QAAQ,cAAc,EAAEC,SAAS,EAAE,EAAEE,MAAM9B,IAAI,CACpF,KACA,CAAC,EACH;YAAEiC,OAAO;QAAS;QAGpB,MAAMC,aAAaF,IAAAA,uBAAQ,EAAC,CAAC,CAAC,EAAElC,WAAW,SAAS,CAAC,EAAEqC,QAAQ,GAAGC,IAAI;QAEtE,IAAI,CAACnC,WAAE,CAACC,UAAU,CAACyB,YAAY,CAAC1B,WAAE,CAACC,UAAU,CAAC0B,WAAW;YACvD,MAAM,IAAInC,MAAM;QAClB;QAEAc,KAAIC,IAAI,CAAC,CAAC,+BAA+B,EAAE0B,WAAW,CAAC;QACvD3B,KAAIC,IAAI,CAAC,CAAC,wBAAwB,EAAEgB,gBAAgB,CAAC;QAErD,MAAMa,gBAAgBtC,aAAI,CAAC0B,OAAO,CAAClC,QAAQmC,GAAG,IAAI;QAElD,IAAIzB,WAAE,CAACC,UAAU,CAACmC,gBAAgB;YAChC,MAAMC,YAAY,MAAMrC,WAAE,CAACG,QAAQ,CAACmC,QAAQ,CAACF,eAAe;YAC5D,IAAI,CAACC,UAAUP,QAAQ,CAACR,UAAU;gBAChChB,KAAIC,IAAI,CAAC;gBAET,MAAMP,WAAE,CAACG,QAAQ,CAACoC,UAAU,CAACH,eAAe,CAAC,EAAE,EAAEd,QAAQ,CAAC;YAC5D;QACF;QAEA,OAAO;YACLkB,KAAKd;YACLe,MAAMd;YACNe,QAAQ,CAAC,EAAET,WAAW,WAAW,CAAC;QACpC;IACF,EAAE,OAAOd,KAAK;QACZb,KAAIc,KAAK,CACP,qEACAD;IAEJ;AACF"}