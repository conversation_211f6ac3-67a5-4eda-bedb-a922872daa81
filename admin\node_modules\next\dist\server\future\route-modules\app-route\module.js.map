{"version": 3, "sources": ["../../../../../src/server/future/route-modules/app-route/module.ts"], "names": ["AppRouteRouteModule", "RouteModule", "sharedModules", "constructor", "userland", "definition", "resolvedPagePath", "nextConfigOutput", "requestAsyncStorage", "staticGenerationAsyncStorage", "serverHooks", "header<PERSON><PERSON>s", "staticGenerationBailout", "actionAsyncStorage", "methods", "autoImplementMethods", "nonStaticMethods", "getNonStaticMethods", "dynamic", "Error", "pathname", "process", "env", "NODE_ENV", "lowercased", "HTTP_METHODS", "map", "method", "toLowerCase", "Log", "error", "toUpperCase", "some", "resolve", "isHTTPMethod", "handleBadRequestResponse", "execute", "request", "context", "handler", "requestContext", "req", "renderOpts", "previewProps", "prerenderManifest", "preview", "staticGenerationContext", "urlPathname", "nextUrl", "fetchCache", "response", "run", "isAppRoute", "isAction", "getIsServerAction", "RequestAsyncStorageWrapper", "wrap", "StaticGenerationAsyncStorageWrapper", "staticGenerationStore", "getTracer", "join", "forceDynamic", "forceStatic", "dynamicShouldError", "revalidate", "wrappedRequest", "proxyRequest", "route", "getPathnameFromAbsolutePath", "getRootSpanAttributes", "set", "trace", "AppRouteRouteHandlersSpan", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "attributes", "patchFetch", "res", "params", "parsedUrlQueryToParams", "undefined", "Response", "fetchMetrics", "waitUntil", "Promise", "all", "Object", "values", "pendingRevalidates", "addImplicitTags", "fetchTags", "tags", "requestStore", "getStore", "mutableCookies", "headers", "Headers", "appendMutableCookies", "body", "status", "statusText", "handleInternalServerErrorResponse", "has", "get", "handle", "err", "resolveHandlerError"], "mappings": ";;;;;;;;;;;;;;;IAqHaA,mBAAmB;eAAnBA;;IAoWb,OAAkC;eAAlC;;;6BA/cO;4CAIA;qDAIA;kCAIA;sBACsD;4BACjB;wBAClB;2BACgB;6CACE;8BACf;qCACO;6DACf;sCACgB;qCACD;gCACC;wCACE;4EAEV;iEACA;yCACW;6CAEJ;sDACS;4CACV;uEACJ;yCACG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyE3B,MAAMA,4BAA4BC,wBAAW;qBAgC3BC,gBAAgBA;IAevCC,YAAY,EACVC,QAAQ,EACRC,UAAU,EACVC,gBAAgB,EAChBC,gBAAgB,EACW,CAAE;QAC7B,KAAK,CAAC;YAAEH;YAAUC;QAAW;QAjD/B;;GAEC,QACeG,sBAAsBA,gDAAmB;QAEzD;;GAEC,QACeC,+BAA+BA,kEAA4B;QAE3E;;;GAGC,QACeC,cAAcA;QAE9B;;;GAGC,QACeC,cAAcA;QAE9B;;;GAGC,QACeC,0BAA0BA,gDAAuB;QAIjE;;;GAGC,QACeC,qBAAqBA,8CAAkB;QAiBrD,IAAI,CAACP,gBAAgB,GAAGA;QACxB,IAAI,CAACC,gBAAgB,GAAGA;QAExB,yEAAyE;QACzE,mBAAmB;QACnB,IAAI,CAACO,OAAO,GAAGC,IAAAA,0CAAoB,EAACX;QAEpC,6CAA6C;QAC7C,IAAI,CAACY,gBAAgB,GAAGC,IAAAA,wCAAmB,EAACb;QAE5C,qDAAqD;QACrD,IAAI,CAACc,OAAO,GAAG,IAAI,CAACd,QAAQ,CAACc,OAAO;QACpC,IAAI,IAAI,CAACX,gBAAgB,KAAK,UAAU;YACtC,IAAI,CAAC,IAAI,CAACW,OAAO,IAAI,IAAI,CAACA,OAAO,KAAK,QAAQ;gBAC5C,IAAI,CAACA,OAAO,GAAG;YACjB,OAAO,IAAI,IAAI,CAACA,OAAO,KAAK,iBAAiB;gBAC3C,MAAM,IAAIC,MACR,CAAC,gDAAgD,EAAEd,WAAWe,QAAQ,CAAC,wHAAwH,CAAC;YAEpM;QACF;QAEA,oEAAoE;QACpE,eAAe;QACf,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;YAC1C,6EAA6E;YAC7E,oCAAoC;YACpC,MAAMC,aAAaC,kBAAY,CAACC,GAAG,CAAC,CAACC,SAAWA,OAAOC,WAAW;YAClE,KAAK,MAAMD,UAAUH,WAAY;gBAC/B,IAAIG,UAAU,IAAI,CAACvB,QAAQ,EAAE;oBAC3ByB,KAAIC,KAAK,CACP,CAAC,2BAA2B,EAAEH,OAAO,MAAM,EACzC,IAAI,CAACrB,gBAAgB,CACtB,yBAAyB,EAAEqB,OAAOI,WAAW,GAAG,gCAAgC,CAAC;gBAEtF;YACF;YAEA,2EAA2E;YAC3E,gCAAgC;YAChC,IAAI,aAAa,IAAI,CAAC3B,QAAQ,EAAE;gBAC9ByB,KAAIC,KAAK,CACP,CAAC,4BAA4B,EAAE,IAAI,CAACxB,gBAAgB,CAAC,sDAAsD,CAAC;YAEhH;YAEA,0EAA0E;YAC1E,YAAY;YACZ,IAAI,CAACmB,kBAAY,CAACO,IAAI,CAAC,CAACL,SAAWA,UAAU,IAAI,CAACvB,QAAQ,GAAG;gBAC3DyB,KAAIC,KAAK,CACP,CAAC,6BAA6B,EAAE,IAAI,CAACxB,gBAAgB,CAAC,8CAA8C,CAAC;YAEzG;QACF;IACF;IAEA;;;;;GAKC,GACD,AAAQ2B,QAAQN,MAAc,EAAqB;QACjD,yEAAyE;QACzE,IAAI,CAACO,IAAAA,kBAAY,EAACP,SAAS,OAAOQ,0CAAwB;QAE1D,sBAAsB;QACtB,OAAO,IAAI,CAACrB,OAAO,CAACa,OAAO;IAC7B;IAEA;;GAEC,GACD,MAAcS,QACZC,OAAoB,EACpBC,OAAoC,EACjB;QACnB,iDAAiD;QACjD,MAAMC,UAAU,IAAI,CAACN,OAAO,CAACI,QAAQV,MAAM;QAE3C,mCAAmC;QACnC,MAAMa,iBAAiC;YACrCC,KAAKJ;QACP;QAGEG,eAAuBE,UAAU,GAAG;YACpCC,cAAcL,QAAQM,iBAAiB,CAACC,OAAO;QACjD;QAEA,6CAA6C;QAC7C,MAAMC,0BAAmD;YACvDC,aAAaV,QAAQW,OAAO,CAAC5B,QAAQ;YACrCsB,YAAYJ,QAAQI,UAAU;QAChC;QAEA,+CAA+C;QAC/CI,wBAAwBJ,UAAU,CAACO,UAAU,GAAG,IAAI,CAAC7C,QAAQ,CAAC6C,UAAU;QAExE,0EAA0E;QAC1E,wEAAwE;QACxE,+CAA+C;QAC/C,MAAMC,WAAoB,MAAM,IAAI,CAACrC,kBAAkB,CAACsC,GAAG,CACzD;YACEC,YAAY;YACZC,UAAUC,IAAAA,0CAAiB,EAACjB;QAC9B,GACA,IACEkB,sDAA0B,CAACC,IAAI,CAC7B,IAAI,CAAChD,mBAAmB,EACxBgC,gBACA,IACEiB,wEAAmC,CAACD,IAAI,CACtC,IAAI,CAAC/C,4BAA4B,EACjCqC,yBACA,CAACY;wBAuDCC;oBAtDA,mEAAmE;oBACnE,6BAA6B;oBAC7B,IAAI,IAAI,CAAC3C,gBAAgB,EAAE;wBACzB,IAAI,CAACJ,uBAAuB,CAC1B,CAAC,wBAAwB,EAAE,IAAI,CAACI,gBAAgB,CAAC4C,IAAI,CACnD,MACA,CAAC;oBAEP;oBAEA,oEAAoE;oBACpE,OAAQ,IAAI,CAAC1C,OAAO;wBAClB,KAAK;4BACH,6DAA6D;4BAC7D,gCAAgC;4BAChCwC,sBAAsBG,YAAY,GAAG;4BACrC,IAAI,CAACjD,uBAAuB,CAAC,CAAC,aAAa,CAAC,EAAE;gCAC5CM,SAAS,IAAI,CAACA,OAAO;4BACvB;4BACA;wBACF,KAAK;4BACH,4DAA4D;4BAC5D,+BAA+B;4BAC/BwC,sBAAsBI,WAAW,GAAG;4BACpC;wBACF,KAAK;4BACH,8DAA8D;4BAC9D,mDAAmD;4BACnDJ,sBAAsBK,kBAAkB,GAAG;4BAC3C;wBACF;4BACE;oBACJ;oBAEA,kEAAkE;oBAClE,oEAAoE;oBACpE,8BAA8B;oBAC9BL,sBAAsBM,UAAU,KAC9B,IAAI,CAAC5D,QAAQ,CAAC4D,UAAU,IAAI;oBAE9B,mEAAmE;oBACnE,yDAAyD;oBACzD,MAAMC,iBAAiBC,IAAAA,0BAAY,EACjC7B,SACA;wBAAEnB,SAAS,IAAI,CAACA,OAAO;oBAAC,GACxB;wBACEP,aAAa,IAAI,CAACA,WAAW;wBAC7BD,aAAa,IAAI,CAACA,WAAW;wBAC7BE,yBAAyB,IAAI,CAACA,uBAAuB;oBACvD;oBAGF,mDAAmD;oBACnD,MAAMuD,QAAQC,IAAAA,wDAA2B,EAAC,IAAI,CAAC9D,gBAAgB;qBAC/DqD,mCAAAA,IAAAA,iBAAS,IAAGU,qBAAqB,uBAAjCV,iCAAqCW,GAAG,CAAC,cAAcH;oBACvD,OAAOR,IAAAA,iBAAS,IAAGY,KAAK,CACtBC,oCAAyB,CAACC,UAAU,EACpC;wBACEC,UAAU,CAAC,0BAA0B,EAAEP,MAAM,CAAC;wBAC9CQ,YAAY;4BACV,cAAcR;wBAChB;oBACF,GACA;4BA4BIT;wBA3BF,0BAA0B;wBAC1BkB,IAAAA,sBAAU,EAAC;4BACTlE,aAAa,IAAI,CAACA,WAAW;4BAC7BD,8BACE,IAAI,CAACA,4BAA4B;wBACrC;wBACA,MAAMoE,MAAM,MAAMtC,QAAQ0B,gBAAgB;4BACxCa,QAAQxC,QAAQwC,MAAM,GAClBC,IAAAA,8CAAsB,EAACzC,QAAQwC,MAAM,IACrCE;wBACN;wBACA,IAAI,CAAEH,CAAAA,eAAeI,QAAO,GAAI;4BAC9B,MAAM,IAAI9D,MACR,CAAC,4CAA4C,EAAE,IAAI,CAACb,gBAAgB,CAAC,0FAA0F,CAAC;wBAEpK;wBACEgC,QAAQI,UAAU,CAASwC,YAAY,GACvCxB,sBAAsBwB,YAAY;wBAEpC5C,QAAQI,UAAU,CAACyC,SAAS,GAAGC,QAAQC,GAAG,CACxCC,OAAOC,MAAM,CACX7B,sBAAsB8B,kBAAkB,IAAI,EAAE;wBAIlDC,IAAAA,2BAAe,EAAC/B;wBACdpB,QAAQI,UAAU,CAASgD,SAAS,IACpChC,8BAAAA,sBAAsBiC,IAAI,qBAA1BjC,4BAA4BE,IAAI,CAAC;wBAEnC,4DAA4D;wBAC5D,0DAA0D;wBAC1D,QAAQ;wBACR,MAAMgC,eAAe,IAAI,CAACpF,mBAAmB,CAACqF,QAAQ;wBACtD,IAAID,gBAAgBA,aAAaE,cAAc,EAAE;4BAC/C,MAAMC,UAAU,IAAIC,QAAQnB,IAAIkB,OAAO;4BACvC,IACEE,IAAAA,oCAAoB,EAClBF,SACAH,aAAaE,cAAc,GAE7B;gCACA,OAAO,IAAIb,SAASJ,IAAIqB,IAAI,EAAE;oCAC5BC,QAAQtB,IAAIsB,MAAM;oCAClBC,YAAYvB,IAAIuB,UAAU;oCAC1BL;gCACF;4BACF;wBACF;wBAEA,OAAOlB;oBACT;gBAEJ;QAKV,yEAAyE;QACzE,kBAAkB;QAClB,IAAI,CAAE3B,CAAAA,oBAAoB+B,QAAO,GAAI;YACnC,qEAAqE;YACrE,OAAOoB,IAAAA,mDAAiC;QAC1C;QAEA,IAAInD,SAAS6C,OAAO,CAACO,GAAG,CAAC,yBAAyB;YAChD,oEAAoE;YACpE,6EAA6E;YAC7E,MAAM,IAAInF,MACR;QAGF,6EAA6E;QAC7E,iEAAiE;QAEjE,2EAA2E;QAC3E,6EAA6E;QAC7E,0EAA0E;QAC1E,mCAAmC;QACnC,sBAAsB;QACtB,8CAA8C;QAC9C,IAAI;QAEJ,yEAAyE;QACzE,gDAAgD;QAChD,oEAAoE;QACpE,kDAAkD;QAClD,qEAAqE;QACrE,yDAAyD;QAC3D;QAEA,IAAI+B,SAAS6C,OAAO,CAACQ,GAAG,CAAC,yBAAyB,KAAK;YACrD,iEAAiE;YACjE,MAAM,IAAIpF,MACR;QAEJ;QAEA,OAAO+B;IACT;IAEA,MAAasD,OACXnE,OAAoB,EACpBC,OAAoC,EACjB;QACnB,IAAI;YACF,yCAAyC;YACzC,MAAMY,WAAW,MAAM,IAAI,CAACd,OAAO,CAACC,SAASC;YAE7C,uCAAuC;YACvC,OAAOY;QACT,EAAE,OAAOuD,KAAK;YACZ,+DAA+D;YAC/D,MAAMvD,WAAWwD,IAAAA,wCAAmB,EAACD;YACrC,IAAI,CAACvD,UAAU,MAAMuD;YAErB,wCAAwC;YACxC,OAAOvD;QACT;IACF;AACF;MAEA,WAAelD"}