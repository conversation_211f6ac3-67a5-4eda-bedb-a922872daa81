{"version": 3, "sources": ["../../../../../src/server/future/route-modules/pages-api/module.ts"], "names": ["PagesAPIRouteModule", "RouteModule", "constructor", "options", "userland", "default", "Error", "definition", "page", "render", "req", "res", "context", "apiResolver", "query", "previewProps", "revalidate", "trustHostHeader", "allowedRevalidateHeaderKeys", "hostname", "minimalMode", "dev"], "mappings": ";;;;;;;;;;;;;;;IAsGaA,mBAAmB;eAAnBA;;IA4Cb,OAAkC;eAAlC;;;6BA3I2D;6BAC/B;AA8FrB,MAAMA,4BAA4BC,wBAAW;IAIlDC,YAAYC,OAAmC,CAAE;QAC/C,KAAK,CAACA;QAEN,IAAI,OAAOA,QAAQC,QAAQ,CAACC,OAAO,KAAK,YAAY;YAClD,MAAM,IAAIC,MACR,CAAC,KAAK,EAAEH,QAAQI,UAAU,CAACC,IAAI,CAAC,oCAAoC,CAAC;QAEzE;IACF;IAEA;;;;;GAKC,GACD,MAAaC,OACXC,GAAoB,EACpBC,GAAmB,EACnBC,OAAoC,EACrB;QACf,MAAMC,IAAAA,wBAAW,EACfH,KACAC,KACAC,QAAQE,KAAK,EACb,IAAI,CAACV,QAAQ,EACb;YACE,GAAGQ,QAAQG,YAAY;YACvBC,YAAYJ,QAAQI,UAAU;YAC9BC,iBAAiBL,QAAQK,eAAe;YACxCC,6BAA6BN,QAAQM,2BAA2B;YAChEC,UAAUP,QAAQO,QAAQ;QAC5B,GACAP,QAAQQ,WAAW,EACnBR,QAAQS,GAAG,EACXT,QAAQJ,IAAI;IAEhB;AACF;MAEA,WAAeR"}