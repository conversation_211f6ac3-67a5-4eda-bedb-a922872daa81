{"version": 3, "sources": ["../../src/telemetry/post-payload.ts"], "names": ["_postPayload", "endpoint", "body", "signal", "AbortSignal", "timeout", "retry", "fetch", "method", "JSON", "stringify", "headers", "then", "res", "ok", "err", "Error", "statusText", "response", "minTimeout", "retries", "factor", "catch"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;mEAFE;;;;;;AAEX,SAASA,aAAaC,QAAgB,EAAEC,IAAY,EAAEC,MAAY;IACvE,IAAI,CAACA,UAAU,aAAaC,aAAa;QACvCD,SAASC,YAAYC,OAAO,CAAC;IAC/B;IACA,OACEC,IAAAA,mBAAK,EACH,IACEC,MAAMN,UAAU;YACdO,QAAQ;YACRN,MAAMO,KAAKC,SAAS,CAACR;YACrBS,SAAS;gBAAE,gBAAgB;YAAmB;YAC9CR;QACF,GAAGS,IAAI,CAAC,CAACC;YACP,IAAI,CAACA,IAAIC,EAAE,EAAE;gBACX,MAAMC,MAAM,IAAIC,MAAMH,IAAII,UAAU;gBAClCF,IAAYG,QAAQ,GAAGL;gBACzB,MAAME;YACR;QACF,IACF;QAAEI,YAAY;QAAKC,SAAS;QAAGC,QAAQ;IAAE,GAExCC,KAAK,CAAC;IACL,kDAAkD;IACpD,EACA,2BAA2B;KAC1BV,IAAI,CACH,KAAO,GACP,KAAO;AAGf"}