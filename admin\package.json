{"name": "slhgw-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start -p 3002", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "jose": "^5.10.0", "jsonwebtoken": "^9.0.2", "next": "14.0.4", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "recharts": "^2.8.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8.5.3", "typescript": "^5"}}