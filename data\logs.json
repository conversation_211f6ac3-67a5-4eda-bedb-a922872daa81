[{"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 1, "created_at": "2025-05-25T06:20:31.206Z", "updated_at": "2025-05-25T06:20:31.206Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 2, "created_at": "2025-05-27T03:37:49.599Z", "updated_at": "2025-05-27T03:37:49.599Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 3, "created_at": "2025-05-27T03:52:14.037Z", "updated_at": "2025-05-27T03:52:14.037Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 4, "created_at": "2025-05-27T03:52:24.725Z", "updated_at": "2025-05-27T03:52:24.726Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 5, "created_at": "2025-05-27T03:53:05.683Z", "updated_at": "2025-05-27T03:53:05.683Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 6, "created_at": "2025-05-27T03:53:25.303Z", "updated_at": "2025-05-27T03:53:25.303Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 7, "created_at": "2025-05-27T03:58:15.286Z", "updated_at": "2025-05-27T03:58:15.286Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 8, "created_at": "2025-05-27T03:59:02.918Z", "updated_at": "2025-05-27T03:59:02.918Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 9, "created_at": "2025-05-27T03:59:11.776Z", "updated_at": "2025-05-27T03:59:11.776Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 10, "created_at": "2025-05-27T03:59:25.746Z", "updated_at": "2025-05-27T03:59:25.746Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 11, "created_at": "2025-05-27T03:59:51.811Z", "updated_at": "2025-05-27T03:59:51.811Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 12, "created_at": "2025-05-27T03:59:52.506Z", "updated_at": "2025-05-27T03:59:52.506Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 13, "created_at": "2025-05-27T03:59:52.936Z", "updated_at": "2025-05-27T03:59:52.936Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 14, "created_at": "2025-05-27T03:59:53.120Z", "updated_at": "2025-05-27T03:59:53.120Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 15, "created_at": "2025-05-27T03:59:53.287Z", "updated_at": "2025-05-27T03:59:53.287Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 16, "created_at": "2025-05-27T04:00:37.685Z", "updated_at": "2025-05-27T04:00:37.685Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 17, "created_at": "2025-05-27T04:03:41.735Z", "updated_at": "2025-05-27T04:03:41.735Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 18, "created_at": "2025-05-27T04:04:09.064Z", "updated_at": "2025-05-27T04:04:09.064Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 19, "created_at": "2025-05-27T04:20:51.174Z", "updated_at": "2025-05-27T04:20:51.174Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 20, "created_at": "2025-05-27T05:35:47.448Z", "updated_at": "2025-05-27T05:35:47.448Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 21, "created_at": "2025-05-27T06:43:32.952Z", "updated_at": "2025-05-27T06:43:32.952Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 22, "created_at": "2025-05-27T06:43:54.881Z", "updated_at": "2025-05-27T06:43:54.881Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 23, "created_at": "2025-05-27T06:47:20.996Z", "updated_at": "2025-05-27T06:47:20.996Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 24, "created_at": "2025-05-27T06:55:36.355Z", "updated_at": "2025-05-27T06:55:36.355Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 25, "created_at": "2025-05-27T07:02:05.190Z", "updated_at": "2025-05-27T07:02:05.190Z"}, {"user_id": 1, "action": "USER_LOGIN", "module": "auth", "level": "info", "ip_address": "::1", "user_agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36 Edg/*********", "details": "{\"username\":\"admin\"}", "id": 26, "created_at": "2025-05-27T07:02:38.999Z", "updated_at": "2025-05-27T07:02:38.999Z"}]