/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // 在生产构建时忽略ESLint错误
    ignoreDuringBuilds: true,
  },
  typescript: {
    // 在生产构建时忽略TypeScript错误
    ignoreBuildErrors: true,
  },
  images: {
    // 允许外部图片域名
    domains: ['localhost', 'example.com'],
    // 忽略图片优化警告
    unoptimized: true,
  },
  // 实验性功能
  experimental: {
    // 启用应用目录
    appDir: true,
  },
};

module.exports = nextConfig;
