<svg width="1920" height="480" viewBox="0 0 1920 480" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1920" height="480" fill="url(#paint0_linear)"/>
<circle cx="400" cy="180" r="160" fill="url(#paint1_radial)" fill-opacity="0.22"/>
<circle cx="1600" cy="350" r="200" fill="url(#paint2_radial)" fill-opacity="0.16"/>
<defs>
<linearGradient id="paint0_linear" x1="0" y1="0" x2="1920" y2="480" gradientUnits="userSpaceOnUse">
<stop stop-color="#2563EB"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientTransform="translate(400 180) rotate(90) scale(160)" gradientUnits="userSpaceOnUse">
<stop stop-color="#60A5FA"/>
<stop offset="1" stop-color="#2563EB" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientTransform="translate(1600 350) rotate(90) scale(200)" gradientUnits="userSpaceOnUse">
<stop stop-color="#93C5FD"/>
<stop offset="1" stop-color="#1E40AF" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>