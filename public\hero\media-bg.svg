<svg width="1920" height="480" viewBox="0 0 1920 480" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1920" height="480" fill="url(#paint0_linear)"/>
<circle cx="350" cy="150" r="170" fill="url(#paint1_radial)" fill-opacity="0.21"/>
<circle cx="1650" cy="370" r="210" fill="url(#paint2_radial)" fill-opacity="0.15"/>
<defs>
<linearGradient id="paint0_linear" x1="0" y1="0" x2="1920" y2="480" gradientUnits="userSpaceOnUse">
<stop stop-color="#2563EB"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientTransform="translate(350 150) rotate(90) scale(170)" gradientUnits="userSpaceOnUse">
<stop stop-color="#60A5FA"/>
<stop offset="1" stop-color="#2563EB" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientTransform="translate(1650 370) rotate(90) scale(210)" gradientUnits="userSpaceOnUse">
<stop stop-color="#93C5FD"/>
<stop offset="1" stop-color="#1E40AF" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>