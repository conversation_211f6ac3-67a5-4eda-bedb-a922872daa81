<svg width="1920" height="480" viewBox="0 0 1920 480" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1920" height="480" fill="url(#paint0_linear)"/>
<circle cx="300" cy="120" r="180" fill="url(#paint1_radial)" fill-opacity="0.25"/>
<circle cx="1700" cy="400" r="220" fill="url(#paint2_radial)" fill-opacity="0.18"/>
<defs>
<linearGradient id="paint0_linear" x1="0" y1="0" x2="1920" y2="480" gradientUnits="userSpaceOnUse">
<stop stop-color="#2563EB"/>
<stop offset="1" stop-color="#1E40AF"/>
</linearGradient>
<radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientTransform="translate(300 120) rotate(90) scale(180)" gradientUnits="userSpaceOnUse">
<stop stop-color="#60A5FA"/>
<stop offset="1" stop-color="#2563EB" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientTransform="translate(1700 400) rotate(90) scale(220)" gradientUnits="userSpaceOnUse">
<stop stop-color="#93C5FD"/>
<stop offset="1" stop-color="#1E40AF" stop-opacity="0"/>
</radialGradient>
</defs>
</svg>