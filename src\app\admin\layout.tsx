import React from 'react';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // For now, a simple layout. Later, we can add sidebar, header, etc.
  // We also need to protect this layout and its children routes.
  return (
    <div className="admin-layout min-h-screen bg-gray-100">
      <header className="bg-blue-600 text-white p-4 shadow-md fixed top-0 left-0 right-0 z-50">
        <h1 className="text-xl font-semibold">后台管理系统</h1>
      </header>
      <div className="flex pt-16">
        <nav className="bg-white shadow-md p-4 w-64 fixed h-full top-16 left-0 hidden md:block z-40">
          {/* Placeholder for navigation links */}
          <ul className="space-y-2 mt-4">
            <li><a href="/admin" className="block p-2 hover:bg-gray-200 rounded">仪表盘</a></li>
            <li><a href="/admin/users" className="block p-2 hover:bg-gray-200 rounded">用户管理</a></li>
            <li><a href="/admin/content/articles" className="block p-2 hover:bg-gray-200 rounded">文章管理</a></li>
            <li><a href="/admin/content/services" className="block p-2 hover:bg-gray-200 rounded">服务管理</a></li>
            <li><a href="/admin/content/cases" className="block p-2 hover:bg-gray-200 rounded">案例管理</a></li>
            <li><a href="/admin/content/banners" className="block p-2 hover:bg-gray-200 rounded">Banner管理</a></li>
            <li><a href="/admin/content/faq" className="block p-2 hover:bg-gray-200 rounded">FAQ管理</a></li>
            <li><a href="/admin/inquiries" className="block p-2 hover:bg-gray-200 rounded">客户咨询</a></li>
            <li><a href="/admin/team" className="block p-2 hover:bg-gray-200 rounded">团队管理</a></li>
            <li><a href="/admin/settings" className="block p-2 hover:bg-gray-200 rounded">系统设置</a></li>
            {/* Add more links as features are developed */}
          </ul>
        </nav>
        <main className="flex-1 p-6 md:ml-64 bg-gray-100">
          {children}
        </main>
      </div>
      {/* Footer can be part of the main content scroll or fixed depending on design */}
      {/* <footer className="bg-gray-200 text-center p-4 text-sm text-gray-600 md:ml-64">
        © {new Date().getFullYear()} 后台管理系统
      </footer> */}
    </div>
  );
}