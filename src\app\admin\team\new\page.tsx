'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { FaUserPlus, FaArrowLeft } from 'react-icons/fa';

// 假设的API调用函数，用于创建团队成员
async function createTeamMember(data: any) {
  // 在实际应用中，这里会是 fetch 或 axios 等API请求
  console.log('Creating team member:', data);
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
  // 模拟成功响应
  return { success: true, member: { ...data, id: Date.now().toString() } }; 
}

export default function NewTeamMemberPage() {
  const router = useRouter();
  const [name, setName] = useState('');
  const [title, setTitle] = useState('');
  const [qualifications, setQualifications] = useState('');
  const [bio, setBio] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      const response = await createTeamMember({
        name,
        title,
        qualifications,
        bio,
        imageUrl,
      });

      if (response.success) {
        // 成功后可以跳转到团队成员列表页或详情页
        router.push('/admin/team'); 
      } else {
        setError('创建团队成员失败，请稍后重试。');
      }
    } catch (err) {
      setError('发生错误，请检查网络连接或联系管理员。');
      console.error(err);
    }
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4 md:p-8">
      <div className="max-w-3xl mx-auto bg-white shadow-xl rounded-lg p-6 md:p-10">
        <div className="flex items-center justify-between mb-8 pb-4 border-b border-gray-200">
          <div className="flex items-center">
            <FaUserPlus className="text-3xl text-blue-600 mr-3" />
            <h1 className="text-2xl md:text-3xl font-bold text-gray-800">添加新团队成员</h1>
          </div>
          <Link href="/admin/team" className="text-blue-600 hover:text-blue-700 flex items-center transition-colors">
            <FaArrowLeft className="mr-2" />
            返回列表
          </Link>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-100 text-red-700 border border-red-300 rounded-md">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">姓名</label>
            <input
              type="text"
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="例如：张三"
            />
          </div>

          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">职位/称谓</label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              required
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="例如：首席教育顾问"
            />
          </div>

          <div>
            <label htmlFor="qualifications" className="block text-sm font-medium text-gray-700 mb-1">资质/头衔</label>
            <input
              type="text"
              id="qualifications"
              value={qualifications}
              onChange={(e) => setQualifications(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="例如：国家认证生涯规划师、教育学博士"
            />
          </div>

          <div>
            <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">简介</label>
            <textarea
              id="bio"
              value={bio}
              onChange={(e) => setBio(e.target.value)}
              rows={4}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="介绍成员的背景、经验等"
            ></textarea>
          </div>

          <div>
            <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-1">照片链接 (URL)</label>
            <input
              type="url"
              id="imageUrl"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 transition-colors"
              placeholder="例如：/team/member-new.jpg 或 https://example.com/image.jpg"
            />
            <p className="mt-1 text-xs text-gray-500">请提供成员照片的公开访问链接。如果使用相对路径，请确保图片已上传到 public 目录下。</p>
          </div>

          <div className="pt-4 flex justify-end space-x-3">
            <Link href="/admin/team" className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
              取消
            </Link>
            <button
              type="submit"
              disabled={isLoading}
              className={`px-6 py-2 border border-transparent rounded-md shadow-sm text-white font-medium transition-colors
                ${isLoading ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500'}`}
            >
              {isLoading ? '正在保存...' : '保存成员信息'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}