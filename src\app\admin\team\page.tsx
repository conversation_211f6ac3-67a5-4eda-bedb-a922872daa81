import React from 'react';
import Link from 'next/link';

// 模拟团队成员数据
const mockTeamMembers = [
  {
    id: 'member-1',
    name: '王老师',
    title: '首席教育规划师',
    expertise: 'K12教育规划、升学指导',
    imageUrl: '/images/team/member1.jpg', // 示例图片路径
    order: 1,
    isActive: true,
  },
  {
    id: 'member-2',
    name: '李顾问',
    title: '资深职业发展顾问',
    expertise: '职业转型、生涯规划',
    imageUrl: '/images/team/member2.jpg',
    order: 2,
    isActive: true,
  },
  {
    id: 'member-3',
    name: '张博士',
    title: '教育心理学专家',
    expertise: '儿童心理发展、学习能力评估',
    imageUrl: '/images/team/member3.jpg',
    order: 3,
    isActive: false, // 示例：已离职或暂不显示
  },
];

export default function AdminTeamPage() {
  // TODO: Implement state for team members, fetching from API, reordering, etc.
  const teamMembers = mockTeamMembers.sort((a, b) => a.order - b.order);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">团队成员管理</h2>
        <Link href="/admin/team/new" className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg">
          添加新成员
        </Link>
      </div>

      <p className="mb-4 text-sm text-gray-600">管理团队成员的信息、照片和资质。可以拖拽调整显示顺序。</p>

      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                照片
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                姓名
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                头衔
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                专长领域
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                顺序
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                状态
              </th>
              <th scope="col" className="relative px-6 py-3">
                <span className="sr-only">操作</span>
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {teamMembers.map((member) => (
              <tr key={member.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <img src={member.imageUrl || '/images/team/default-avatar.png'} alt={member.name} className="h-16 w-16 object-cover rounded-full" />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {member.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {member.title}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {member.expertise}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                  {member.order}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${member.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
                  >
                    {member.isActive ? '在职' : '离职/隐藏'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <Link href={`/admin/team/edit/${member.id}`} className="text-blue-600 hover:text-blue-800 mr-3">
                    编辑
                  </Link>
                  {/* TODO: Implement delete functionality with confirmation */}
                  <button className="text-red-600 hover:text-red-800">
                    删除
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* TODO: Implement drag-and-drop reordering for team members */}
    </div>
  );
}