export async function POST(request) {
  try {
    // 解析请求体
    const data = await request.json();
    
    // 验证必填字段
    const requiredFields = ['name', 'email', 'phone', 'serviceType', 'preferredDate', 'preferredTime'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return new Response(
          JSON.stringify({ success: false, message: `${field}字段是必填的` }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        );
      }
    }
    
    // 在实际项目中，这里会有发送邮件、保存到数据库、预约日历等操作
    // 例如：
    // await saveAppointmentToDatabase(data);
    // await sendConfirmationEmail(data);
    // await addToCalendar(data);
    
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 返回成功响应
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: '您的预约申请已成功提交，我们的顾问将尽快与您联系确认详情！',
        appointmentId: 'APT' + Date.now() // 生成一个临时的预约ID
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
    
  } catch (error) {
    console.error('处理预约表单时出错:', error);
    
    // 返回错误响应
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: '预约提交失败，请稍后再试或直接联系我们。' 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}