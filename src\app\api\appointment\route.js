import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  withErrorHandling,
  validateRequiredFields,
  validateEmail,
  validatePhone
} from '@/lib/utils.js';

async function appointmentHandler(request) {
  const body = await request.json();

  // 验证必填字段
  validateRequiredFields(body, ['name', 'email', 'phone', 'serviceType', 'preferredDate', 'preferredTime']);

  const {
    name,
    email,
    phone,
    serviceType,
    preferredDate,
    preferredTime,
    message
  } = body;

  // 验证数据
  validateEmail(email);
  validatePhone(phone);

  const db = await getDatabase();

  // 创建预约记录
  const newInquiry = await db.insert('inquiries', {
    name,
    email,
    phone,
    subject: `预约咨询 - ${serviceType}`,
    message: message || `客户希望预约${serviceType}服务咨询`,
    service_type: serviceType,
    preferred_date: preferredDate,
    preferred_time: preferredTime,
    status: 'pending'
  });

  return successResponse(
    {
      id: newInquiry.id,
      appointmentId: `APT${newInquiry.id.toString().padStart(6, '0')}`
    },
    '您的预约申请已成功提交，我们的顾问将尽快与您联系确认详情！'
  );
}

export const POST = withErrorHandling(appointmentHandler);