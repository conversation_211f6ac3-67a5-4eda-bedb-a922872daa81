import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  withErrorHandling,
  requireAuth
} from '@/lib/utils.js';

// 获取单个预约
async function getAppointment(request, { params }) {
  const { id } = params;
  const db = await getDatabase();

  const appointment = await db.get(`
    SELECT 
      a.*,
      c.name as consultant_name,
      c.specialty as consultant_specialty,
      c.avatar_url as consultant_avatar,
      c.email as consultant_email,
      c.phone as consultant_phone
    FROM appointments a
    LEFT JOIN consultants c ON a.consultant_id = c.id
    WHERE a.id = ?
  `, [id]);

  if (!appointment) {
    throw new Error('预约不存在');
  }

  return successResponse(appointment);
}

// 更新预约状态
async function updateAppointment(request, { params }) {
  await requireAuth(request);
  
  const { id } = params;
  const body = await request.json();
  
  const {
    status,
    admin_notes,
    rating,
    client_feedback,
    appointment_date,
    appointment_time,
    duration
  } = body;

  const db = await getDatabase();

  // 检查预约是否存在
  const existingAppointment = await db.get(
    'SELECT * FROM appointments WHERE id = ?',
    [id]
  );

  if (!existingAppointment) {
    throw new Error('预约不存在');
  }

  const updateData = {};
  
  if (status !== undefined) {
    updateData.status = status;
    
    // 如果状态变为已完成，记录完成时间
    if (status === 'completed') {
      updateData.completed_at = new Date().toISOString();
    }
    
    // 如果状态变为已取消，记录取消时间
    if (status === 'cancelled') {
      updateData.cancelled_at = new Date().toISOString();
    }
  }

  if (admin_notes !== undefined) updateData.admin_notes = admin_notes;
  if (rating !== undefined) updateData.rating = rating;
  if (client_feedback !== undefined) updateData.client_feedback = client_feedback;
  if (appointment_date !== undefined) updateData.appointment_date = appointment_date;
  if (appointment_time !== undefined) updateData.appointment_time = appointment_time;
  if (duration !== undefined) updateData.duration = duration;

  updateData.updated_at = new Date().toISOString();

  await db.update('appointments', updateData, { id });

  // 如果有评分，更新咨询师的平均评分
  if (rating !== undefined && status === 'completed') {
    const avgRating = await db.get(`
      SELECT AVG(rating) as avg_rating 
      FROM appointments 
      WHERE consultant_id = ? AND rating IS NOT NULL
    `, [existingAppointment.consultant_id]);

    if (avgRating.avg_rating) {
      await db.update('consultants', 
        { rating: parseFloat(avgRating.avg_rating).toFixed(1) },
        { id: existingAppointment.consultant_id }
      );
    }
  }

  return successResponse(null, '预约信息更新成功');
}

// 删除预约
async function deleteAppointment(request, { params }) {
  await requireAuth(request);
  
  const { id } = params;
  const db = await getDatabase();

  // 检查预约状态
  const appointment = await db.get(
    'SELECT status, consultant_id FROM appointments WHERE id = ?',
    [id]
  );

  if (!appointment) {
    throw new Error('预约不存在');
  }

  if (appointment.status === 'completed') {
    throw new Error('已完成的预约无法删除');
  }

  await db.delete('appointments', { id });

  // 更新咨询师预约统计
  await db.run(
    'UPDATE consultants SET total_appointments = total_appointments - 1 WHERE id = ?',
    [appointment.consultant_id]
  );

  return successResponse(null, '预约删除成功');
}

export const GET = withErrorHandling(getAppointment);
export const PUT = withErrorHandling(updateAppointment);
export const DELETE = withErrorHandling(deleteAppointment);
