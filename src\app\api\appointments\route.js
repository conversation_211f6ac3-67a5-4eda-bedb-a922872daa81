import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateRequiredFields,
  validateEmail,
  validatePhone
} from '@/lib/utils.js';

// 获取预约列表
async function getAppointments(request) {
  const { searchParams } = new URL(request.url);
  const status = searchParams.get('status');
  const consultant_id = searchParams.get('consultant_id');
  const date_from = searchParams.get('date_from');
  const date_to = searchParams.get('date_to');
  const page = parseInt(searchParams.get('page')) || 1;
  const limit = parseInt(searchParams.get('limit')) || 50;

  const db = await getDatabase();

  // 获取所有预约和咨询师数据
  let appointments = await db.getAll('appointments');
  const consultants = await db.getAll('consultants');

  // 筛选条件
  if (status) {
    appointments = appointments.filter(apt => apt.status === status);
  }

  if (consultant_id) {
    appointments = appointments.filter(apt => apt.consultant_id == consultant_id);
  }

  if (date_from) {
    appointments = appointments.filter(apt => apt.appointment_date >= date_from);
  }

  if (date_to) {
    appointments = appointments.filter(apt => apt.appointment_date <= date_to);
  }

  // 添加咨询师信息
  const appointmentsWithConsultant = appointments.map(apt => {
    const consultant = consultants.find(c => c.id == apt.consultant_id);
    return {
      ...apt,
      consultant_name: consultant?.name || '未知咨询师',
      consultant_specialty: consultant?.specialty || '',
      consultant_avatar: consultant?.avatar_url || ''
    };
  });

  // 排序
  appointmentsWithConsultant.sort((a, b) => {
    const dateA = new Date(`${a.appointment_date} ${a.appointment_time}`);
    const dateB = new Date(`${b.appointment_date} ${b.appointment_time}`);
    return dateB - dateA;
  });

  // 分页
  const total = appointmentsWithConsultant.length;
  const offset = (page - 1) * limit;
  const paginatedAppointments = appointmentsWithConsultant.slice(offset, offset + limit);

  return successResponse({
    items: paginatedAppointments,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

// 创建预约
async function createAppointment(request) {
  const body = await request.json();

  validateRequiredFields(body, [
    'client_name',
    'client_email',
    'client_phone',
    'consultant_id',
    'appointment_date',
    'appointment_time',
    'service_type'
  ]);

  const {
    client_name,
    client_email,
    client_phone,
    consultant_id,
    appointment_date,
    appointment_time,
    service_type,
    message,
    duration = 60
  } = body;

  validateEmail(client_email);
  validatePhone(client_phone);

  const db = await getDatabase();

  // 获取所有数据
  const consultants = await db.getAll('consultants');
  const appointments = await db.getAll('appointments');

  // 检查咨询师是否存在且可用
  const consultant = consultants.find(c => c.id == consultant_id && c.status === 'active');

  if (!consultant) {
    throw new Error('咨询师不存在或不可用');
  }

  // 检查时间冲突
  const conflictingAppointment = appointments.find(apt =>
    apt.consultant_id == consultant_id &&
    apt.appointment_date === appointment_date &&
    apt.appointment_time === appointment_time &&
    (apt.status === 'pending' || apt.status === 'confirmed')
  );

  if (conflictingAppointment) {
    throw new Error('该时间段已被预约，请选择其他时间');
  }

  // 生成预约编号和ID
  const appointmentNumber = `APT${Date.now().toString().slice(-8)}`;
  const newId = appointments.length > 0 ? Math.max(...appointments.map(a => a.id)) + 1 : 1;

  const newAppointment = {
    id: newId,
    appointment_number: appointmentNumber,
    client_name,
    client_email,
    client_phone,
    consultant_id,
    appointment_date,
    appointment_time,
    duration,
    service_type,
    message,
    status: 'pending',
    total_amount: consultant.hourly_rate * (duration / 60),
    admin_notes: '',
    rating: null,
    client_feedback: null,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  // 保存新预约
  appointments.push(newAppointment);
  await db.writeTable('appointments', appointments);

  // 更新咨询师预约统计
  const consultantIndex = consultants.findIndex(c => c.id == consultant_id);
  if (consultantIndex !== -1) {
    consultants[consultantIndex].total_appointments = (consultants[consultantIndex].total_appointments || 0) + 1;
    consultants[consultantIndex].updated_at = new Date().toISOString();
    await db.writeTable('consultants', consultants);
  }

  return successResponse({
    id: newAppointment.id,
    appointment_number: appointmentNumber,
    consultant_name: consultant.name,
    appointment_date,
    appointment_time
  }, '预约申请已成功提交，我们会尽快与您确认！');
}

export const GET = withErrorHandling(getAppointments);
export const POST = withErrorHandling(createAppointment);
