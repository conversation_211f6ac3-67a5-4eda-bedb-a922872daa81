import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateRequiredFields,
  validateEmail,
  validatePhone
} from '@/lib/utils.js';

// 获取预约列表
async function getAppointments(request) {
  const { searchParams } = new URL(request.url);
  const status = searchParams.get('status');
  const consultant_id = searchParams.get('consultant_id');
  const date_from = searchParams.get('date_from');
  const date_to = searchParams.get('date_to');
  const page = parseInt(searchParams.get('page')) || 1;
  const limit = parseInt(searchParams.get('limit')) || 10;
  const offset = (page - 1) * limit;

  const db = await getDatabase();

  let query = `
    SELECT
      a.*,
      c.name as consultant_name,
      c.specialty as consultant_specialty,
      c.avatar_url as consultant_avatar
    FROM appointments a
    LEFT JOIN consultants c ON a.consultant_id = c.id
    WHERE 1=1
  `;
  let params = [];

  if (status) {
    query += ' AND a.status = ?';
    params.push(status);
  }

  if (consultant_id) {
    query += ' AND a.consultant_id = ?';
    params.push(consultant_id);
  }

  if (date_from) {
    query += ' AND a.appointment_date >= ?';
    params.push(date_from);
  }

  if (date_to) {
    query += ' AND a.appointment_date <= ?';
    params.push(date_to);
  }

  query += ' ORDER BY a.appointment_date DESC, a.appointment_time DESC LIMIT ? OFFSET ?';
  params.push(limit, offset);

  const appointments = await db.all(query, params);

  // 获取总数
  let countQuery = 'SELECT COUNT(*) as total FROM appointments a WHERE 1=1';
  let countParams = [];

  if (status) {
    countQuery += ' AND a.status = ?';
    countParams.push(status);
  }

  if (consultant_id) {
    countQuery += ' AND a.consultant_id = ?';
    countParams.push(consultant_id);
  }

  if (date_from) {
    countQuery += ' AND a.appointment_date >= ?';
    countParams.push(date_from);
  }

  if (date_to) {
    countQuery += ' AND a.appointment_date <= ?';
    countParams.push(date_to);
  }

  const { total } = await db.get(countQuery, countParams);

  return successResponse({
    items: appointments,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

// 创建预约
async function createAppointment(request) {
  const body = await request.json();

  validateRequiredFields(body, [
    'client_name',
    'client_email',
    'client_phone',
    'consultant_id',
    'appointment_date',
    'appointment_time',
    'service_type'
  ]);

  const {
    client_name,
    client_email,
    client_phone,
    consultant_id,
    appointment_date,
    appointment_time,
    service_type,
    message,
    duration = 60
  } = body;

  validateEmail(client_email);
  validatePhone(client_phone);

  const db = await getDatabase();

  // 检查咨询师是否存在且可用
  const consultant = await db.get(
    'SELECT * FROM consultants WHERE id = ? AND status = ?',
    [consultant_id, 'active']
  );

  if (!consultant) {
    throw new Error('咨询师不存在或不可用');
  }

  // 检查时间冲突
  const conflictingAppointment = await db.get(`
    SELECT id FROM appointments
    WHERE consultant_id = ?
    AND appointment_date = ?
    AND appointment_time = ?
    AND status IN ('pending', 'confirmed')
  `, [consultant_id, appointment_date, appointment_time]);

  if (conflictingAppointment) {
    throw new Error('该时间段已被预约，请选择其他时间');
  }

  // 生成预约编号
  const appointmentNumber = `APT${Date.now().toString().slice(-8)}`;

  const appointment = await db.insert('appointments', {
    appointment_number: appointmentNumber,
    client_name,
    client_email,
    client_phone,
    consultant_id,
    appointment_date,
    appointment_time,
    duration,
    service_type,
    message,
    status: 'pending',
    total_amount: consultant.hourly_rate * (duration / 60)
  });

  // 更新咨询师预约统计
  await db.run(
    'UPDATE consultants SET total_appointments = total_appointments + 1 WHERE id = ?',
    [consultant_id]
  );

  return successResponse({
    id: appointment.id,
    appointment_number: appointmentNumber,
    consultant_name: consultant.name,
    appointment_date,
    appointment_time
  }, '预约申请已成功提交，我们会尽快与您确认！');
}

export const GET = withErrorHandling(getAppointments);
export const POST = withErrorHandling(createAppointment);
