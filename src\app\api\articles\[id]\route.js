import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import {
  successResponse,
  withErrorHandling,
  validateStatus,
  sanitizeHtml,
  generateSummary,
  processImageUrl
} from '@/lib/utils.js';

// 获取单篇文章
async function getArticleHandler(request, { params }) {
  const { id } = params;

  const db = await getDatabase();
  const articles = await db.getAll('articles');
  const articleIndex = articles.findIndex(a => a.id == id);

  if (articleIndex === -1) {
    throw new Error('文章不存在');
  }

  const article = articles[articleIndex];

  // 获取作者和分类信息
  const users = await db.getAll('users');
  const categories = await db.getAll('categories');
  const author = users.find(u => u.id === article.author_id);
  const category = categories.find(c => c.id === article.category_id);

  // 增加浏览量
  articles[articleIndex] = {
    ...article,
    views: (article.views || 0) + 1
  };
  await db.writeTable('articles', articles);

  return successResponse({
    ...article,
    views: (article.views || 0) + 1,
    author: author ? { id: author.id, name: author.name, username: author.username } : null,
    category: category ? { id: category.id, name: category.name, slug: category.slug } : null
  });
}

// 更新文章
async function updateArticleHandler(request, { params }) {
  const currentUser = await requireEditor(request);
  const { id } = params;
  const body = await request.json();

  const db = await getDatabase();
  const articles = await db.getAll('articles');
  const articleIndex = articles.findIndex(a => a.id == id);

  if (articleIndex === -1) {
    throw new Error('文章不存在');
  }

  const article = articles[articleIndex];

  // 只有管理员或文章作者可以修改文章
  if (currentUser.role !== 'admin' && currentUser.id !== article.author_id) {
    throw new Error('权限不足');
  }

  const {
    title,
    content,
    summary,
    slug,
    category_id,
    status,
    cover_image_url,
    published_at
  } = body;

  const updates = {};

  // 更新标题
  if (title !== undefined) {
    updates.title = title;
  }

  // 更新内容
  if (content !== undefined) {
    updates.content = sanitizeHtml(content);
    // 如果没有提供摘要，自动生成
    if (summary === undefined) {
      updates.summary = generateSummary(updates.content);
    }
  }

  // 更新摘要
  if (summary !== undefined) {
    updates.summary = summary;
  }

  // 更新slug
  if (slug !== undefined && slug !== article.slug) {
    // 检查新slug是否已存在
    const existingArticle = articles.find(a => a.slug === slug && a.id != article.id);
    if (existingArticle) {
      throw new Error('文章别名已存在');
    }
    updates.slug = slug;
  }

  // 更新分类
  if (category_id !== undefined) {
    if (category_id) {
      const categories = await db.getAll('categories');
      const category = categories.find(c => c.id == category_id);
      if (!category) {
        throw new Error('分类不存在');
      }
    }
    updates.category_id = category_id;
  }

  // 更新状态
  if (status !== undefined) {
    validateStatus(status, ['draft', 'published', 'archived']);
    updates.status = status;

    // 如果状态改为已发布，设置发布时间
    if (status === 'published' && !article.published_at) {
      updates.published_at = published_at || new Date().toISOString();
    }
  }

  // 更新封面图片
  if (cover_image_url !== undefined) {
    updates.cover_image_url = processImageUrl(cover_image_url);
  }

  // 更新发布时间
  if (published_at !== undefined) {
    updates.published_at = published_at;
  }

  // 执行更新
  const updatedArticle = {
    ...article,
    ...updates,
    updated_at: new Date().toISOString()
  };

  articles[articleIndex] = updatedArticle;
  await db.writeTable('articles', articles);

  // 记录日志
  await logActivity(currentUser.id, 'UPDATE_ARTICLE', 'content', {
    articleId: updatedArticle.id,
    title: updatedArticle.title,
    changes: Object.keys(updates)
  }, 'info', request);

  return successResponse(updatedArticle, '文章更新成功');
}

// 删除文章
async function deleteArticleHandler(request, { params }) {
  const currentUser = await requireEditor(request);
  const { id } = params;

  const db = await getDatabase();
  const articles = await db.getAll('articles');
  const articleIndex = articles.findIndex(a => a.id == id);

  if (articleIndex === -1) {
    throw new Error('文章不存在');
  }

  const article = articles[articleIndex];

  // 只有管理员或文章作者可以删除文章
  if (currentUser.role !== 'admin' && currentUser.id !== article.author_id) {
    throw new Error('权限不足');
  }

  // 删除文章
  articles.splice(articleIndex, 1);
  await db.writeTable('articles', articles);

  // 记录日志
  await logActivity(currentUser.id, 'DELETE_ARTICLE', 'content', {
    articleId: article.id,
    title: article.title
  }, 'warning', request);

  return successResponse(null, '文章删除成功');
}

export const GET = withErrorHandling(getArticleHandler);
export const PUT = withErrorHandling(updateArticleHandler);
export const DELETE = withErrorHandling(deleteArticleHandler);
