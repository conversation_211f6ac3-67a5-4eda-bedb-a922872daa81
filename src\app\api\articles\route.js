import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import { 
  successResponse, 
  paginatedResponse, 
  withErrorHandling, 
  validateRequiredFields,
  validatePaginationParams,
  validateSortParams,
  validateStatus,
  generateSlug,
  sanitizeHtml,
  generateSummary,
  processImageUrl
} from '@/lib/utils.js';

// 获取文章列表
async function getArticlesHandler(request) {
  const { searchParams } = new URL(request.url);
  
  // 验证分页参数
  const { page, limit, offset } = validatePaginationParams(searchParams);
  
  // 验证排序参数
  const { sortBy, sortOrder } = validateSortParams(searchParams, [
    'id', 'title', 'status', 'published_at', 'views', 'created_at', 'updated_at'
  ]);
  
  const db = await getDatabase();
  
  // 构建查询条件
  let conditions = {};
  const search = searchParams.get('search');
  const category = searchParams.get('category');
  const status = searchParams.get('status');
  const author = searchParams.get('author');
  
  if (category) conditions.category_id = parseInt(category);
  if (status) conditions.status = status;
  if (author) conditions.author_id = parseInt(author);
  
  // 获取所有文章
  let articles = await db.query('articles', conditions);
  
  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    articles = articles.filter(article => 
      article.title?.toLowerCase().includes(searchLower) ||
      article.summary?.toLowerCase().includes(searchLower) ||
      article.content?.toLowerCase().includes(searchLower)
    );
  }
  
  // 获取作者和分类信息
  const users = await db.getAll('users');
  const categories = await db.getAll('categories');
  
  // 关联作者和分类信息
  articles = articles.map(article => ({
    ...article,
    author: users.find(u => u.id === article.author_id),
    category: categories.find(c => c.id === article.category_id)
  }));
  
  // 排序
  articles.sort((a, b) => {
    const aVal = a[sortBy];
    const bVal = b[sortBy];
    
    if (sortOrder === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });
  
  const total = articles.length;
  
  // 分页
  const paginatedArticles = articles.slice(offset, offset + limit);
  
  return paginatedResponse(paginatedArticles, total, page, limit);
}

// 创建新文章
async function createArticleHandler(request) {
  const currentUser = await requireEditor(request);
  const body = await request.json();
  
  // 验证必填字段
  validateRequiredFields(body, ['title', 'content']);
  
  const { 
    title, 
    content, 
    summary, 
    slug, 
    category_id, 
    status = 'draft', 
    cover_image_url,
    published_at
  } = body;
  
  // 验证状态
  validateStatus(status, ['draft', 'published', 'archived']);
  
  const db = await getDatabase();
  
  // 生成或验证slug
  let finalSlug = slug;
  if (!finalSlug) {
    const existingArticles = await db.getAll('articles');
    const existingSlugs = existingArticles.map(a => a.slug);
    finalSlug = generateSlug(title, existingSlugs);
  } else {
    // 检查slug是否已存在
    const existingArticle = await db.query('articles', { slug: finalSlug });
    if (existingArticle.length > 0) {
      throw new Error('文章别名已存在');
    }
  }
  
  // 验证分类
  if (category_id) {
    const category = await db.get('categories', category_id);
    if (!category) {
      throw new Error('分类不存在');
    }
  }
  
  // 处理内容
  const sanitizedContent = sanitizeHtml(content);
  const finalSummary = summary || generateSummary(sanitizedContent);
  const processedImageUrl = processImageUrl(cover_image_url);
  
  // 创建文章
  const newArticle = await db.insert('articles', {
    title,
    slug: finalSlug,
    summary: finalSummary,
    content: sanitizedContent,
    cover_image_url: processedImageUrl,
    author_id: currentUser.id,
    category_id: category_id || null,
    status,
    published_at: status === 'published' ? (published_at || new Date().toISOString()) : null,
    views: 0
  });
  
  // 记录日志
  await logActivity(currentUser.id, 'CREATE_ARTICLE', 'content', { 
    articleId: newArticle.id, 
    title: newArticle.title 
  }, 'info', request);
  
  return successResponse(newArticle, '文章创建成功');
}

export const GET = withErrorHandling(getArticlesHandler);
export const POST = withErrorHandling(createArticleHandler);
