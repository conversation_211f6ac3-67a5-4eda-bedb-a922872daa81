import { authenticateUser, generateToken, logActivity } from '@/lib/auth.js';
import { successResponse, errorResponse, withErrorHandling, validateRequiredFields } from '@/lib/utils.js';

async function loginHandler(request) {
  let body;
  try {
    body = await request.json();
  } catch (error) {
    throw new Error('请求体格式错误');
  }

  // 验证必填字段
  validateRequiredFields(body, ['username', 'password']);

  const { username, password } = body;

  // 验证用户身份
  const user = await authenticateUser(username, password);

  // 生成JWT令牌
  const token = generateToken({
    id: user.id,
    username: user.username,
    role: user.role,
    name: user.name
  });

  // 记录登录日志
  await logActivity(user.id, 'USER_LOGIN', 'auth', { username }, 'info', request);

  // 返回用户信息和令牌
  return successResponse({
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      avatar_url: user.avatar_url,
      phone: user.phone,
      position: user.position,
      bio: user.bio
    },
    token
  }, '登录成功');
}

// 处理CORS预检请求
export async function OPTIONS(request) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

// 包装处理器以添加CORS头
async function corsHandler(request) {
  const response = await withErrorHandling(loginHandler)(request);

  // 添加CORS头
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  return response;
}

export const POST = corsHandler;
