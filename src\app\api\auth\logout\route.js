import { requireAuth, logActivity } from '@/lib/auth.js';
import { successResponse, withErrorHandling } from '@/lib/utils.js';

async function logoutHandler(request) {
  try {
    // 验证用户身份（可选，因为登出可能在令牌过期后进行）
    const user = await requireAuth(request);
    
    // 记录登出日志
    await logActivity(user.id, 'USER_LOGOUT', 'auth', { username: user.username }, 'info', request);
  } catch (error) {
    // 忽略认证错误，允许登出
  }

  // 返回成功响应（前端负责清除令牌）
  return successResponse(null, '登出成功');
}

export const POST = withErrorHandling(logoutHandler);
