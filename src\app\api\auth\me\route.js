import { requireAuth } from '@/lib/auth.js';
import { successResponse, withErrorHandling } from '@/lib/utils.js';

async function getMeHandler(request) {
  // 验证用户身份
  const user = await requireAuth(request);

  // 返回用户信息
  return successResponse({
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      avatar_url: user.avatar_url,
      phone: user.phone,
      position: user.position,
      bio: user.bio,
      status: user.status,
      created_at: user.created_at,
      updated_at: user.updated_at
    }
  });
}

// 处理CORS预检请求
export async function OPTIONS(request) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

// 包装处理器以添加CORS头
async function corsHandler(request) {
  const response = await withErrorHandling(getMeHandler)(request);

  // 添加CORS头
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  return response;
}

export const GET = corsHandler;
