import { requireAuth } from '@/lib/auth.js';
import { successResponse, withErrorHandling } from '@/lib/utils.js';

async function getMeHandler(request) {
  // 验证用户身份
  const user = await requireAuth(request);

  // 返回用户信息
  return successResponse({
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role,
      avatar_url: user.avatar_url,
      phone: user.phone,
      position: user.position,
      bio: user.bio,
      status: user.status,
      created_at: user.created_at,
      updated_at: user.updated_at
    }
  });
}

export const GET = withErrorHandling(getMeHandler);
