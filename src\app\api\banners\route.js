import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import { 
  successResponse, 
  paginatedResponse, 
  withErrorHandling, 
  validateRequiredFields,
  validatePaginationParams,
  validateSortParams,
  validateStatus,
  validateUrl,
  processImageUrl
} from '@/lib/utils.js';

// 获取Banner列表
async function getBannersHandler(request) {
  const { searchParams } = new URL(request.url);
  
  // 验证分页参数
  const { page, limit, offset } = validatePaginationParams(searchParams);
  
  // 验证排序参数
  const { sortBy, sortOrder } = validateSortParams(searchParams, [
    'id', 'title', 'status', 'sort_order', 'created_at', 'updated_at'
  ]);
  
  const db = await getDatabase();
  
  // 构建查询条件
  let conditions = {};
  const search = searchParams.get('search');
  const status = searchParams.get('status');
  
  if (status) conditions.status = status;
  
  // 获取所有Banner
  let banners = await db.query('banners', conditions);
  
  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    banners = banners.filter(banner => 
      banner.title?.toLowerCase().includes(searchLower) ||
      banner.subtitle?.toLowerCase().includes(searchLower) ||
      banner.description?.toLowerCase().includes(searchLower)
    );
  }
  
  // 排序
  banners.sort((a, b) => {
    const aVal = a[sortBy];
    const bVal = b[sortBy];
    
    if (sortOrder === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });
  
  const total = banners.length;
  
  // 分页
  const paginatedBanners = banners.slice(offset, offset + limit);
  
  return paginatedResponse(paginatedBanners, total, page, limit);
}

// 创建新Banner
async function createBannerHandler(request) {
  const currentUser = await requireEditor(request);
  const body = await request.json();
  
  // 验证必填字段
  validateRequiredFields(body, ['title']);
  
  const { 
    title, 
    subtitle,
    description,
    image_url,
    link_url,
    link_text,
    sort_order = 0,
    status = 'active'
  } = body;
  
  // 验证状态
  validateStatus(status, ['active', 'inactive']);
  
  // 验证URL
  if (link_url) validateUrl(link_url);
  
  const db = await getDatabase();
  
  // 处理图片URL
  const processedImageUrl = processImageUrl(image_url);
  
  // 创建Banner
  const newBanner = await db.insert('banners', {
    title,
    subtitle: subtitle || null,
    description: description || null,
    image_url: processedImageUrl,
    link_url: link_url || null,
    link_text: link_text || null,
    sort_order: parseInt(sort_order),
    status
  });
  
  // 记录日志
  await logActivity(currentUser.id, 'CREATE_BANNER', 'content', { 
    bannerId: newBanner.id, 
    title: newBanner.title 
  }, 'info', request);
  
  return successResponse(newBanner, 'Banner创建成功');
}

export const GET = withErrorHandling(getBannersHandler);
export const POST = withErrorHandling(createBannerHandler);
