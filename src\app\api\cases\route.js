import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import {
  successResponse,
  paginatedResponse,
  withErrorHandling,
  validateRequiredFields,
  validatePaginationParams,
  validateSortParams,
  validateStatus,
  generateSlug,
  sanitizeHtml,
  processImageUrl
} from '@/lib/utils.js';

// 获取案例列表
async function getCasesHandler(request) {
  const { searchParams } = new URL(request.url);

  // 验证分页参数
  const { page, limit, offset } = validatePaginationParams(searchParams);

  // 验证排序参数
  const { sortBy, sortOrder } = validateSortParams(searchParams, [
    'id', 'title', 'category', 'status', 'sort_order', 'is_featured', 'created_at', 'updated_at'
  ]);

  const db = await getDatabase();

  // 构建查询条件
  let conditions = {};
  const search = searchParams.get('search');
  const category = searchParams.get('category');
  const status = searchParams.get('status');
  const featured = searchParams.get('featured');

  if (category) conditions.category = category;
  if (status) conditions.status = status;
  if (featured !== null) conditions.is_featured = featured === 'true';

  // 获取所有案例
  let cases = await db.getAll('cases');

  // 应用条件过滤
  if (category) {
    cases = cases.filter(caseItem => caseItem.category === category);
  }
  if (status) {
    cases = cases.filter(caseItem => caseItem.status === status);
  }
  if (featured !== null) {
    cases = cases.filter(caseItem => caseItem.is_featured === (featured === 'true'));
  }

  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    cases = cases.filter(caseItem =>
      caseItem.title?.toLowerCase().includes(searchLower) ||
      caseItem.summary?.toLowerCase().includes(searchLower) ||
      caseItem.client_info?.toLowerCase().includes(searchLower)
    );
  }

  // 排序
  cases.sort((a, b) => {
    const aVal = a[sortBy];
    const bVal = b[sortBy];

    if (sortOrder === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  const total = cases.length;

  // 分页
  const paginatedCases = cases.slice(offset, offset + limit);

  return paginatedResponse(paginatedCases, total, page, limit);
}

// 创建新案例
async function createCaseHandler(request) {
  const currentUser = await requireEditor(request);
  const body = await request.json();

  // 验证必填字段
  validateRequiredFields(body, ['title']);

  const {
    title,
    category,
    thumbnail_url,
    summary,
    client_info,
    details,
    result,
    slug,
    sort_order = 0,
    status = 'draft',
    is_featured = false
  } = body;

  // 验证状态
  validateStatus(status, ['draft', 'published', 'archived']);

  const db = await getDatabase();

  // 生成或验证slug
  let finalSlug = slug;
  if (!finalSlug) {
    const existingCases = await db.getAll('cases');
    const existingSlugs = existingCases.map(c => c.slug);
    finalSlug = generateSlug(title, existingSlugs);
  } else {
    // 检查slug是否已存在
    const existingCases = await db.getAll('cases');
    const existingCase = existingCases.find(c => c.slug === finalSlug);
    if (existingCase) {
      throw new Error('案例别名已存在');
    }
  }

  // 处理内容
  const sanitizedDetails = details ? sanitizeHtml(details) : null;
  const sanitizedResult = result ? sanitizeHtml(result) : null;
  const processedThumbnailUrl = processImageUrl(thumbnail_url);

  // 创建案例
  const newCase = await db.insert('cases', {
    title,
    slug: finalSlug,
    category: category || null,
    thumbnail_url: processedThumbnailUrl,
    summary: summary || null,
    client_info: client_info || null,
    details: sanitizedDetails,
    result: sanitizedResult,
    sort_order: parseInt(sort_order),
    status,
    is_featured: Boolean(is_featured)
  });

  // 记录日志
  await logActivity(currentUser.id, 'CREATE_CASE', 'content', {
    caseId: newCase.id,
    title: newCase.title
  }, 'info', request);

  return successResponse(newCase, '案例创建成功');
}

export const GET = withErrorHandling(getCasesHandler);
export const POST = withErrorHandling(createCaseHandler);
