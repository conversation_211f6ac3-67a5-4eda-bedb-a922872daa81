import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import { 
  successResponse, 
  withErrorHandling, 
  validateRequiredFields,
  generateSlug,
  validateStatus
} from '@/lib/utils.js';

// 获取分类列表
async function getCategoriesHandler(request) {
  const { searchParams } = new URL(request.url);
  const status = searchParams.get('status');
  
  const db = await getDatabase();
  
  let conditions = {};
  if (status) conditions.status = status;
  
  const categories = await db.query('categories', conditions);
  
  // 按排序字段排序
  categories.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
  
  return successResponse(categories);
}

// 创建新分类
async function createCategoryHandler(request) {
  const currentUser = await requireEditor(request);
  const body = await request.json();
  
  // 验证必填字段
  validateRequiredFields(body, ['name']);
  
  const { name, description, slug, sort_order = 0, status = 'active' } = body;
  
  // 验证状态
  validateStatus(status, ['active', 'inactive']);
  
  const db = await getDatabase();
  
  // 生成或验证slug
  let finalSlug = slug;
  if (!finalSlug) {
    const existingCategories = await db.getAll('categories');
    const existingSlugs = existingCategories.map(c => c.slug);
    finalSlug = generateSlug(name, existingSlugs);
  } else {
    // 检查slug是否已存在
    const existingCategory = await db.query('categories', { slug: finalSlug });
    if (existingCategory.length > 0) {
      throw new Error('分类别名已存在');
    }
  }
  
  // 创建分类
  const newCategory = await db.insert('categories', {
    name,
    slug: finalSlug,
    description: description || null,
    sort_order: parseInt(sort_order),
    status
  });
  
  // 记录日志
  await logActivity(currentUser.id, 'CREATE_CATEGORY', 'content', { 
    categoryId: newCategory.id, 
    name: newCategory.name 
  }, 'info', request);
  
  return successResponse(newCategory, '分类创建成功');
}

export const GET = withErrorHandling(getCategoriesHandler);
export const POST = withErrorHandling(createCategoryHandler);
