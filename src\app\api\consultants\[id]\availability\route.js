import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  withErrorHandling
} from '@/lib/utils.js';

// 获取咨询师可用时间
async function getConsultantAvailability(request, { params }) {
  const { id } = params;
  const { searchParams } = new URL(request.url);
  const date = searchParams.get('date'); // YYYY-MM-DD格式
  const week = searchParams.get('week'); // 获取一周的可用时间

  const db = await getDatabase();

  // 获取所有咨询师数据
  const consultants = await db.getAll('consultants');
  console.log(`查找咨询师 ID: ${id}, 总咨询师数: ${consultants.length}`);
  console.log('前3个咨询师:', consultants.slice(0, 3).map(c => ({ id: c.id, name: c.name, status: c.status })));

  const consultant = consultants.find(c => c.id == id && c.status === 'active');
  console.log(`找到的咨询师:`, consultant ? { id: consultant.id, name: consultant.name } : '未找到');

  if (!consultant) {
    throw new Error('咨询师不存在或不可用');
  }

  let availableHours = {};
  try {
    availableHours = JSON.parse(consultant.available_hours || '{}');
  } catch (e) {
    availableHours = {};
  }

  if (date) {
    // 获取指定日期的可用时间
    const dayOfWeek = new Date(date).getDay(); // 0=Sunday, 1=Monday, etc.
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayName = dayNames[dayOfWeek];

    const dayAvailability = availableHours[dayName] || [];

    // 将时间段字符串转换为对象格式
    const processedSlots = dayAvailability.map(slot => {
      if (typeof slot === 'string') {
        // 处理 "14:00-18:00" 格式
        const [start, end] = slot.split('-');
        return { start, end };
      }
      return slot; // 已经是对象格式
    });

    // 获取该日期已预约的时间
    const appointments = await db.getAll('appointments');
    const bookedSlots = appointments.filter(apt =>
      apt.consultant_id == id &&
      apt.appointment_date === date &&
      (apt.status === 'pending' || apt.status === 'confirmed')
    ).map(apt => ({
      appointment_time: apt.appointment_time,
      duration: apt.duration
    }));

    // 过滤掉已预约的时间和过期时间
    const availableSlots = processedSlots.filter(slot => {
      // 检查是否与已预约时间冲突
      const hasConflict = bookedSlots.some(booked => {
        const bookedStart = booked.appointment_time;
        const bookedEnd = addMinutes(bookedStart, booked.duration);
        const slotStart = slot.start;
        const slotEnd = slot.end;

        // 检查时间冲突
        return (slotStart < bookedEnd && slotEnd > bookedStart);
      });

      // 检查时间是否已过期（只对今天的时间进行检查）
      const now = new Date();
      const today = now.toISOString().split('T')[0];
      const isToday = date === today;

      if (isToday) {
        const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
        const isExpired = slot.start <= currentTime;
        return !hasConflict && !isExpired;
      }

      return !hasConflict;
    });

    return successResponse({
      date,
      dayOfWeek: dayName,
      availableSlots,
      bookedSlots: bookedSlots.map(slot => ({
        time: slot.appointment_time,
        duration: slot.duration
      }))
    });

  } else if (week) {
    // 获取一周的可用时间
    const startDate = new Date(week);
    const weekAvailability = [];
    const appointments = await db.getAll('appointments');

    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      const dateStr = currentDate.toISOString().split('T')[0];

      const dayOfWeek = currentDate.getDay();
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const dayName = dayNames[dayOfWeek];

      const dayAvailability = availableHours[dayName] || [];

      // 将时间段字符串转换为对象格式
      const processedSlots = dayAvailability.map(slot => {
        if (typeof slot === 'string') {
          // 处理 "14:00-18:00" 格式
          const [start, end] = slot.split('-');
          return { start, end };
        }
        return slot; // 已经是对象格式
      });

      // 获取该日期已预约的时间
      const bookedSlots = appointments.filter(apt =>
        apt.consultant_id == id &&
        apt.appointment_date === dateStr &&
        (apt.status === 'pending' || apt.status === 'confirmed')
      ).map(apt => ({
        appointment_time: apt.appointment_time,
        duration: apt.duration
      }));

      // 过滤掉已预约的时间和过期时间
      const availableSlots = processedSlots.filter(slot => {
        // 检查是否与已预约时间冲突
        const hasConflict = bookedSlots.some(booked => {
          const bookedStart = booked.appointment_time;
          const bookedEnd = addMinutes(bookedStart, booked.duration);
          const slotStart = slot.start;
          const slotEnd = slot.end;

          return (slotStart < bookedEnd && slotEnd > bookedStart);
        });

        // 检查时间是否已过期（只对今天的时间进行检查）
        const now = new Date();
        const today = now.toISOString().split('T')[0];
        const isToday = dateStr === today;

        if (isToday) {
          const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
          const isExpired = slot.start <= currentTime;
          return !hasConflict && !isExpired;
        }

        return !hasConflict;
      });

      weekAvailability.push({
        date: dateStr,
        dayOfWeek: dayName,
        availableSlots,
        bookedCount: bookedSlots.length
      });
    }

    return successResponse({
      week,
      weekAvailability
    });

  } else {
    // 返回咨询师的基本可用时间设置
    return successResponse({
      consultantId: id,
      consultantName: consultant.name,
      availableHours
    });
  }
}

// 辅助函数：给时间字符串添加分钟
function addMinutes(timeStr, minutes) {
  const [hours, mins] = timeStr.split(':').map(Number);
  const totalMinutes = hours * 60 + mins + minutes;
  const newHours = Math.floor(totalMinutes / 60);
  const newMins = totalMinutes % 60;
  return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
}

// 处理CORS预检请求
export async function OPTIONS(request) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

// 包装处理器以添加CORS头
async function corsHandler(request, context) {
  const response = await withErrorHandling(getConsultantAvailability)(request, context);

  // 添加CORS头
  response.headers.set('Access-Control-Allow-Origin', '*');
  response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  return response;
}

export const GET = corsHandler;
