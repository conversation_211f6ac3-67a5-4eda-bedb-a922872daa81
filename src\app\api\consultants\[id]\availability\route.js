import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  withErrorHandling
} from '@/lib/utils.js';

// 获取咨询师可用时间
async function getConsultantAvailability(request, { params }) {
  const { id } = params;
  const { searchParams } = new URL(request.url);
  const date = searchParams.get('date'); // YYYY-MM-DD格式
  const week = searchParams.get('week'); // 获取一周的可用时间

  const db = await getDatabase();

  // 检查咨询师是否存在
  const consultant = await db.get(
    'SELECT id, name, available_hours FROM consultants WHERE id = ? AND status = ?',
    [id, 'active']
  );

  if (!consultant) {
    throw new Error('咨询师不存在或不可用');
  }

  let availableHours = {};
  try {
    availableHours = JSON.parse(consultant.available_hours || '{}');
  } catch (e) {
    availableHours = {};
  }

  if (date) {
    // 获取指定日期的可用时间
    const dayOfWeek = new Date(date).getDay(); // 0=Sunday, 1=Monday, etc.
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayName = dayNames[dayOfWeek];

    const dayAvailability = availableHours[dayName] || [];

    // 获取该日期已预约的时间
    const bookedSlots = await db.all(`
      SELECT appointment_time, duration 
      FROM appointments 
      WHERE consultant_id = ? 
      AND appointment_date = ? 
      AND status IN ('pending', 'confirmed')
    `, [id, date]);

    // 过滤掉已预约的时间
    const availableSlots = dayAvailability.filter(slot => {
      return !bookedSlots.some(booked => {
        const bookedStart = booked.appointment_time;
        const bookedEnd = addMinutes(bookedStart, booked.duration);
        const slotStart = slot.start;
        const slotEnd = slot.end;
        
        // 检查时间冲突
        return (slotStart < bookedEnd && slotEnd > bookedStart);
      });
    });

    return successResponse({
      date,
      dayOfWeek: dayName,
      availableSlots,
      bookedSlots: bookedSlots.map(slot => ({
        time: slot.appointment_time,
        duration: slot.duration
      }))
    });

  } else if (week) {
    // 获取一周的可用时间
    const startDate = new Date(week);
    const weekAvailability = [];

    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      const dateStr = currentDate.toISOString().split('T')[0];
      
      const dayOfWeek = currentDate.getDay();
      const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
      const dayName = dayNames[dayOfWeek];

      const dayAvailability = availableHours[dayName] || [];

      // 获取该日期已预约的时间
      const bookedSlots = await db.all(`
        SELECT appointment_time, duration 
        FROM appointments 
        WHERE consultant_id = ? 
        AND appointment_date = ? 
        AND status IN ('pending', 'confirmed')
      `, [id, dateStr]);

      // 过滤掉已预约的时间
      const availableSlots = dayAvailability.filter(slot => {
        return !bookedSlots.some(booked => {
          const bookedStart = booked.appointment_time;
          const bookedEnd = addMinutes(bookedStart, booked.duration);
          const slotStart = slot.start;
          const slotEnd = slot.end;
          
          return (slotStart < bookedEnd && slotEnd > bookedStart);
        });
      });

      weekAvailability.push({
        date: dateStr,
        dayOfWeek: dayName,
        availableSlots,
        bookedCount: bookedSlots.length
      });
    }

    return successResponse({
      week,
      weekAvailability
    });

  } else {
    // 返回咨询师的基本可用时间设置
    return successResponse({
      consultantId: id,
      consultantName: consultant.name,
      availableHours
    });
  }
}

// 辅助函数：给时间字符串添加分钟
function addMinutes(timeStr, minutes) {
  const [hours, mins] = timeStr.split(':').map(Number);
  const totalMinutes = hours * 60 + mins + minutes;
  const newHours = Math.floor(totalMinutes / 60);
  const newMins = totalMinutes % 60;
  return `${newHours.toString().padStart(2, '0')}:${newMins.toString().padStart(2, '0')}`;
}

export const GET = withErrorHandling(getConsultantAvailability);
