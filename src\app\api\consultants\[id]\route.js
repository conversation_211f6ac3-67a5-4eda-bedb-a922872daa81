import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  withE<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateRequiredFields,
  validateEmail,
  requireAuth
} from '@/lib/utils.js';

// 获取单个咨询师
async function getConsultant(request, { params }) {
  const { id } = params;
  const db = await getDatabase();

  const consultant = await db.get(
    'SELECT * FROM consultants WHERE id = ?',
    [id]
  );

  if (!consultant) {
    throw new Error('咨询师不存在');
  }

  // 解析JSON字段
  if (consultant.certifications) {
    consultant.certifications = JSON.parse(consultant.certifications);
  }
  if (consultant.available_hours) {
    consultant.available_hours = JSON.parse(consultant.available_hours);
  }
  if (consultant.languages) {
    consultant.languages = JSON.parse(consultant.languages);
  }

  // 获取咨询师的预约统计
  const stats = await db.get(`
    SELECT 
      COUNT(*) as total_appointments,
      AVG(rating) as avg_rating,
      COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_appointments
    FROM appointments 
    WHERE consultant_id = ?
  `, [id]);

  consultant.stats = stats;

  return successResponse(consultant);
}

// 更新咨询师
async function updateConsultant(request, { params }) {
  await requireAuth(request);
  
  const { id } = params;
  const body = await request.json();
  
  const {
    name,
    email,
    phone,
    specialty,
    experience_years,
    education,
    certifications,
    bio,
    avatar_url,
    available_hours,
    hourly_rate,
    languages,
    status
  } = body;

  if (email) {
    validateEmail(email);
  }

  const db = await getDatabase();

  // 检查咨询师是否存在
  const existingConsultant = await db.get(
    'SELECT id FROM consultants WHERE id = ?',
    [id]
  );

  if (!existingConsultant) {
    throw new Error('咨询师不存在');
  }

  // 如果更新邮箱，检查是否与其他咨询师冲突
  if (email) {
    const emailConflict = await db.get(
      'SELECT id FROM consultants WHERE email = ? AND id != ?',
      [email, id]
    );

    if (emailConflict) {
      throw new Error('该邮箱已被其他咨询师使用');
    }
  }

  const updateData = {};
  if (name !== undefined) updateData.name = name;
  if (email !== undefined) updateData.email = email;
  if (phone !== undefined) updateData.phone = phone;
  if (specialty !== undefined) updateData.specialty = specialty;
  if (experience_years !== undefined) updateData.experience_years = experience_years;
  if (education !== undefined) updateData.education = education;
  if (certifications !== undefined) updateData.certifications = JSON.stringify(certifications);
  if (bio !== undefined) updateData.bio = bio;
  if (avatar_url !== undefined) updateData.avatar_url = avatar_url;
  if (available_hours !== undefined) updateData.available_hours = JSON.stringify(available_hours);
  if (hourly_rate !== undefined) updateData.hourly_rate = hourly_rate;
  if (languages !== undefined) updateData.languages = JSON.stringify(languages);
  if (status !== undefined) updateData.status = status;

  updateData.updated_at = new Date().toISOString();

  await db.update('consultants', updateData, { id });

  return successResponse(null, '咨询师信息更新成功');
}

// 删除咨询师
async function deleteConsultant(request, { params }) {
  await requireAuth(request);
  
  const { id } = params;
  const db = await getDatabase();

  // 检查是否有未完成的预约
  const pendingAppointments = await db.get(
    'SELECT COUNT(*) as count FROM appointments WHERE consultant_id = ? AND status IN (?, ?)',
    [id, 'pending', 'confirmed']
  );

  if (pendingAppointments.count > 0) {
    throw new Error('该咨询师还有未完成的预约，无法删除');
  }

  await db.delete('consultants', { id });

  return successResponse(null, '咨询师删除成功');
}

export const GET = withErrorHandling(getConsultant);
export const PUT = withErrorHandling(updateConsultant);
export const DELETE = withErrorHandling(deleteConsultant);
