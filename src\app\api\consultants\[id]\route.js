import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  withE<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateRequiredFields,
  validateEmail,
  requireAuth
} from '@/lib/utils.js';

// 获取单个咨询师
async function getConsultant(request, { params }) {
  const { id } = params;
  const db = await getDatabase();

  // 获取所有咨询师数据
  const consultants = await db.getAll('consultants');
  const consultant = consultants.find(c => c.id == id);

  if (!consultant) {
    throw new Error('咨询师不存在');
  }

  // 解析JSON字段
  const processedConsultant = {
    ...consultant,
    certifications: consultant.certifications ? JSON.parse(consultant.certifications) : [],
    available_hours: consultant.available_hours ? JSON.parse(consultant.available_hours) : {},
    languages: consultant.languages ? JSON.parse(consultant.languages) : ['中文']
  };

  // 获取咨询师的预约统计
  const appointments = await db.getAll('appointments');
  const consultantAppointments = appointments.filter(apt => apt.consultant_id == id);

  const stats = {
    total_appointments: consultantAppointments.length,
    completed_appointments: consultantAppointments.filter(apt => apt.status === 'completed').length,
    avg_rating: consultantAppointments.filter(apt => apt.rating).length > 0
      ? consultantAppointments.filter(apt => apt.rating).reduce((sum, apt) => sum + apt.rating, 0) / consultantAppointments.filter(apt => apt.rating).length
      : 0
  };

  processedConsultant.stats = stats;

  return successResponse(processedConsultant);
}

// 更新咨询师
async function updateConsultant(request, { params }) {
  await requireAuth(request);

  const { id } = params;
  const body = await request.json();

  const {
    name,
    email,
    phone,
    specialty,
    experience_years,
    education,
    certifications,
    bio,
    avatar_url,
    available_hours,
    hourly_rate,
    languages,
    status
  } = body;

  if (email) {
    validateEmail(email);
  }

  const db = await getDatabase();

  // 获取所有咨询师数据
  const consultants = await db.getAll('consultants');
  const consultantIndex = consultants.findIndex(c => c.id == id);

  if (consultantIndex === -1) {
    throw new Error('咨询师不存在');
  }

  // 如果更新邮箱，检查是否与其他咨询师冲突
  if (email) {
    const emailConflict = consultants.find(c => c.email === email && c.id != id);
    if (emailConflict) {
      throw new Error('该邮箱已被其他咨询师使用');
    }
  }

  // 更新咨询师数据
  const existingConsultant = consultants[consultantIndex];
  const updatedConsultant = {
    ...existingConsultant,
    updated_at: new Date().toISOString()
  };

  if (name !== undefined) updatedConsultant.name = name;
  if (email !== undefined) updatedConsultant.email = email;
  if (phone !== undefined) updatedConsultant.phone = phone;
  if (specialty !== undefined) updatedConsultant.specialty = specialty;
  if (experience_years !== undefined) updatedConsultant.experience_years = experience_years;
  if (education !== undefined) updatedConsultant.education = education;
  if (certifications !== undefined) updatedConsultant.certifications = JSON.stringify(certifications);
  if (bio !== undefined) updatedConsultant.bio = bio;
  if (avatar_url !== undefined) updatedConsultant.avatar_url = avatar_url;
  if (available_hours !== undefined) updatedConsultant.available_hours = JSON.stringify(available_hours);
  if (hourly_rate !== undefined) updatedConsultant.hourly_rate = hourly_rate;
  if (languages !== undefined) updatedConsultant.languages = JSON.stringify(languages);
  if (status !== undefined) updatedConsultant.status = status;

  // 保存更新后的数据
  consultants[consultantIndex] = updatedConsultant;
  await db.writeTable('consultants', consultants);

  return successResponse(null, '咨询师信息更新成功');
}

// 删除咨询师
async function deleteConsultant(request, { params }) {
  await requireAuth(request);

  const { id } = params;
  const db = await getDatabase();

  // 获取所有数据
  const consultants = await db.getAll('consultants');
  const appointments = await db.getAll('appointments');

  // 检查咨询师是否存在
  const consultantIndex = consultants.findIndex(c => c.id == id);
  if (consultantIndex === -1) {
    throw new Error('咨询师不存在');
  }

  // 检查是否有未完成的预约
  const pendingAppointments = appointments.filter(apt =>
    apt.consultant_id == id && (apt.status === 'pending' || apt.status === 'confirmed')
  );

  if (pendingAppointments.length > 0) {
    throw new Error('该咨询师还有未完成的预约，无法删除');
  }

  // 删除咨询师
  consultants.splice(consultantIndex, 1);
  await db.writeTable('consultants', consultants);

  return successResponse(null, '咨询师删除成功');
}

export const GET = withErrorHandling(getConsultant);
export const PUT = withErrorHandling(updateConsultant);
export const DELETE = withErrorHandling(deleteConsultant);
