import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  withE<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateRequiredFields,
  validateEmail,
  requireAuth
} from '@/lib/utils.js';

// 获取咨询师列表
async function getConsultants(request) {
  const { searchParams } = new URL(request.url);
  const status = searchParams.get('status') || 'active';
  const specialty = searchParams.get('specialty');
  const page = parseInt(searchParams.get('page')) || 1;
  const limit = parseInt(searchParams.get('limit')) || 10;
  const offset = (page - 1) * limit;

  const db = await getDatabase();

  let query = 'SELECT * FROM consultants WHERE status = ?';
  let params = [status];

  if (specialty) {
    query += ' AND specialty LIKE ?';
    params.push(`%${specialty}%`);
  }

  query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
  params.push(limit, offset);

  const consultants = await db.all(query, params);

  // 获取总数
  let countQuery = 'SELECT COUNT(*) as total FROM consultants WHERE status = ?';
  let countParams = [status];
  
  if (specialty) {
    countQuery += ' AND specialty LIKE ?';
    countParams.push(`%${specialty}%`);
  }

  const { total } = await db.get(countQuery, countParams);

  return successResponse({
    items: consultants,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

// 创建咨询师
async function createConsultant(request) {
  await requireAuth(request);
  
  const body = await request.json();
  
  validateRequiredFields(body, ['name', 'email', 'specialty', 'experience_years']);
  
  const {
    name,
    email,
    phone,
    specialty,
    experience_years,
    education,
    certifications,
    bio,
    avatar_url,
    available_hours,
    hourly_rate,
    languages,
    status = 'active'
  } = body;

  validateEmail(email);

  const db = await getDatabase();

  // 检查邮箱是否已存在
  const existingConsultant = await db.get(
    'SELECT id FROM consultants WHERE email = ?',
    [email]
  );

  if (existingConsultant) {
    throw new Error('该邮箱已被使用');
  }

  const consultant = await db.insert('consultants', {
    name,
    email,
    phone,
    specialty,
    experience_years,
    education,
    certifications: JSON.stringify(certifications || []),
    bio,
    avatar_url,
    available_hours: JSON.stringify(available_hours || {}),
    hourly_rate,
    languages: JSON.stringify(languages || ['中文']),
    status,
    rating: 5.0,
    total_appointments: 0
  });

  return successResponse(consultant, '咨询师创建成功');
}

export const GET = withErrorHandling(getConsultants);
export const POST = withErrorHandling(createConsultant);
