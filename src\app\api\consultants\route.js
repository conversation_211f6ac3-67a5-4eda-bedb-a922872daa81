import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  withE<PERSON><PERSON><PERSON><PERSON><PERSON>,
  validateRequiredFields,
  validateEmail,
  requireAuth
} from '@/lib/utils.js';

// 获取咨询师列表
async function getConsultants(request) {
  const { searchParams } = new URL(request.url);
  const status = searchParams.get('status') || 'active';
  const specialty = searchParams.get('specialty');
  const page = parseInt(searchParams.get('page')) || 1;
  const limit = parseInt(searchParams.get('limit')) || 50; // 增加默认限制以显示更多数据

  const db = await getDatabase();

  // 获取所有咨询师数据
  let consultants = await db.getAll('consultants');

  // 按状态筛选
  if (status) {
    consultants = consultants.filter(consultant => consultant.status === status);
  }

  // 按专业领域筛选
  if (specialty) {
    consultants = consultants.filter(consultant =>
      consultant.specialty && consultant.specialty.toLowerCase().includes(specialty.toLowerCase())
    );
  }

  // 排序（按创建时间倒序）
  consultants.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

  // 分页
  const total = consultants.length;
  const offset = (page - 1) * limit;
  const paginatedConsultants = consultants.slice(offset, offset + limit);

  // 解析JSON字段
  const processedConsultants = paginatedConsultants.map(consultant => ({
    ...consultant,
    certifications: consultant.certifications ? JSON.parse(consultant.certifications) : [],
    languages: consultant.languages ? JSON.parse(consultant.languages) : ['中文'],
    available_hours: consultant.available_hours ? JSON.parse(consultant.available_hours) : {}
  }));

  return successResponse({
    items: processedConsultants,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}

// 创建咨询师
async function createConsultant(request) {
  await requireAuth(request);

  const body = await request.json();

  validateRequiredFields(body, ['name', 'email', 'specialty', 'experience_years']);

  const {
    name,
    email,
    phone,
    specialty,
    experience_years,
    education,
    certifications,
    bio,
    avatar_url,
    available_hours,
    hourly_rate,
    languages,
    status = 'active'
  } = body;

  validateEmail(email);

  const db = await getDatabase();

  // 获取现有咨询师数据
  const consultants = await db.getAll('consultants');

  // 检查邮箱是否已存在
  const existingConsultant = consultants.find(c => c.email === email);
  if (existingConsultant) {
    throw new Error('该邮箱已被使用');
  }

  // 生成新ID
  const newId = consultants.length > 0 ? Math.max(...consultants.map(c => c.id)) + 1 : 1;

  const newConsultant = {
    id: newId,
    name,
    email,
    phone,
    specialty,
    experience_years,
    education,
    certifications: JSON.stringify(certifications || []),
    bio,
    avatar_url,
    available_hours: JSON.stringify(available_hours || {}),
    hourly_rate,
    languages: JSON.stringify(languages || ['中文']),
    status,
    rating: 5.0,
    total_appointments: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  };

  // 添加到数组并保存
  consultants.push(newConsultant);
  await db.writeTable('consultants', consultants);

  const consultant = newConsultant;

  return successResponse(consultant, '咨询师创建成功');
}

export const GET = withErrorHandling(getConsultants);
export const POST = withErrorHandling(createConsultant);
