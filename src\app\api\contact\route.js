export async function POST(request) {
  try {
    // 解析请求体
    const data = await request.json();
    
    // 验证必填字段
    const requiredFields = ['name', 'email', 'subject', 'message'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return new Response(
          JSON.stringify({ success: false, message: `${field}字段是必填的` }),
          { status: 400, headers: { 'Content-Type': 'application/json' } }
        );
      }
    }
    
    // 在实际项目中，这里会有发送邮件、保存到数据库等操作
    // 例如：
    // await saveToDatabase(data);
    // await sendNotificationEmail(data);
    
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 返回成功响应
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: '您的信息已成功提交，我们将尽快与您联系！' 
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
    
  } catch (error) {
    console.error('处理联系表单时出错:', error);
    
    // 返回错误响应
    return new Response(
      JSON.stringify({ 
        success: false, 
        message: '提交失败，请稍后再试或直接联系我们。' 
      }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}