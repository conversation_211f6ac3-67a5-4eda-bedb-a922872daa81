import { getDatabase } from '@/lib/database.js';
import {
  successResponse,
  withErrorHandling,
  validateRequiredFields,
  validateEmail,
  validatePhone
} from '@/lib/utils.js';

async function contactHandler(request) {
  const body = await request.json();

  // 验证必填字段
  validateRequiredFields(body, ['name', 'email', 'subject', 'message']);

  const { name, email, phone, subject, message } = body;

  // 验证数据
  validateEmail(email);
  if (phone) validatePhone(phone);

  const db = await getDatabase();

  // 创建咨询记录
  const newInquiry = await db.insert('inquiries', {
    name,
    email,
    phone: phone || null,
    subject,
    message,
    service_type: 'contact',
    status: 'pending'
  });

  return successResponse(
    { id: newInquiry.id },
    '您的信息已成功提交，我们将尽快与您联系！'
  );
}

export const POST = withErrorHandling(contactHandler);