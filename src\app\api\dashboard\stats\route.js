import { getDatabase } from '@/lib/database.js';
import { requireEditor } from '@/lib/auth.js';
import { successResponse, withErrorHandling } from '@/lib/utils.js';

async function getDashboardStatsHandler(request) {
  await requireEditor(request);
  
  const db = await getDatabase();
  
  // 获取各种统计数据
  const [
    totalUsers,
    totalArticles,
    totalInquiries,
    totalCases,
    totalServices,
    totalTeamMembers,
    publishedArticles,
    pendingInquiries,
    repliedInquiries,
    closedInquiries
  ] = await Promise.all([
    db.count('users'),
    db.count('articles'),
    db.count('inquiries'),
    db.count('cases'),
    db.count('services'),
    db.count('team'),
    db.count('articles', { status: 'published' }),
    db.count('inquiries', { status: 'pending' }),
    db.count('inquiries', { status: 'replied' }),
    db.count('inquiries', { status: 'closed' })
  ]);
  
  // 计算文章总浏览量
  const articles = await db.getAll('articles');
  const totalViews = articles.reduce((sum, article) => sum + (article.views || 0), 0);
  
  // 获取最近7天的咨询数据
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  
  const allInquiries = await db.getAll('inquiries');
  const recentInquiries = allInquiries.filter(inquiry => 
    new Date(inquiry.created_at) >= sevenDaysAgo
  );
  
  // 按服务类型统计咨询
  const serviceStats = {};
  allInquiries.forEach(inquiry => {
    const serviceType = inquiry.service_type || '其他';
    serviceStats[serviceType] = (serviceStats[serviceType] || 0) + 1;
  });
  
  // 最近活动
  const recentActivities = await db.query('logs', {});
  const sortedActivities = recentActivities
    .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
    .slice(0, 10);
  
  return successResponse({
    overview: {
      totalUsers,
      totalArticles,
      totalInquiries,
      totalCases,
      totalServices,
      totalTeamMembers,
      totalViews,
      publishedArticles,
      pendingInquiries,
      repliedInquiries,
      closedInquiries
    },
    trends: {
      recentInquiriesCount: recentInquiries.length,
      inquiryGrowth: calculateGrowthRate(allInquiries, 'created_at', 7),
      articleGrowth: calculateGrowthRate(articles, 'created_at', 7)
    },
    serviceDistribution: Object.entries(serviceStats).map(([name, count]) => ({
      name,
      count,
      percentage: Math.round((count / totalInquiries) * 100)
    })),
    recentActivities: sortedActivities
  });
}

// 计算增长率
function calculateGrowthRate(data, dateField, days) {
  const now = new Date();
  const periodStart = new Date();
  periodStart.setDate(periodStart.getDate() - days);
  const previousPeriodStart = new Date();
  previousPeriodStart.setDate(previousPeriodStart.getDate() - days * 2);
  
  const currentPeriod = data.filter(item => {
    const date = new Date(item[dateField]);
    return date >= periodStart && date <= now;
  }).length;
  
  const previousPeriod = data.filter(item => {
    const date = new Date(item[dateField]);
    return date >= previousPeriodStart && date < periodStart;
  }).length;
  
  if (previousPeriod === 0) return currentPeriod > 0 ? 100 : 0;
  
  return Math.round(((currentPeriod - previousPeriod) / previousPeriod) * 100);
}

export const GET = withErrorHandling(getDashboardStatsHandler);
