import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import {
  successResponse,
  paginatedResponse,
  withErrorHandling,
  validateRequiredFields,
  validatePaginationParams,
  validateSortParams,
  validateStatus,
  sanitizeHtml
} from '@/lib/utils.js';

// 获取FAQ列表
async function getFaqsHandler(request) {
  const { searchParams } = new URL(request.url);

  // 验证分页参数
  const { page, limit, offset } = validatePaginationParams(searchParams);

  // 验证排序参数
  const { sortBy, sortOrder } = validateSortParams(searchParams, [
    'id', 'question', 'category', 'status', 'sort_order', 'created_at', 'updated_at'
  ]);

  const db = await getDatabase();

  // 构建查询条件
  let conditions = {};
  const search = searchParams.get('search');
  const category = searchParams.get('category');
  const status = searchParams.get('status');

  if (category) conditions.category = category;
  if (status) conditions.status = status;

  // 获取所有FAQ
  let faqs = await db.getAll('faqs');

  // 应用条件过滤
  if (category) {
    faqs = faqs.filter(faq => faq.category === category);
  }
  if (status) {
    faqs = faqs.filter(faq => faq.status === status);
  }

  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    faqs = faqs.filter(faq =>
      faq.question?.toLowerCase().includes(searchLower) ||
      faq.answer?.toLowerCase().includes(searchLower)
    );
  }

  // 排序
  faqs.sort((a, b) => {
    const aVal = a[sortBy];
    const bVal = b[sortBy];

    if (sortOrder === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });

  const total = faqs.length;

  // 分页
  const paginatedFaqs = faqs.slice(offset, offset + limit);

  return paginatedResponse(paginatedFaqs, total, page, limit);
}

// 创建新FAQ
async function createFaqHandler(request) {
  const currentUser = await requireEditor(request);
  const body = await request.json();

  // 验证必填字段
  validateRequiredFields(body, ['question', 'answer']);

  const {
    question,
    answer,
    category,
    sort_order = 0,
    status = 'active'
  } = body;

  // 验证状态
  validateStatus(status, ['active', 'inactive']);

  const db = await getDatabase();

  // 处理内容
  const sanitizedAnswer = sanitizeHtml(answer);

  // 创建FAQ
  const newFaq = await db.insert('faqs', {
    question,
    answer: sanitizedAnswer,
    category: category || null,
    sort_order: parseInt(sort_order),
    status
  });

  // 记录日志
  await logActivity(currentUser.id, 'CREATE_FAQ', 'content', {
    faqId: newFaq.id,
    question: newFaq.question
  }, 'info', request);

  return successResponse(newFaq, 'FAQ创建成功');
}

export const GET = withErrorHandling(getFaqsHandler);
export const POST = withErrorHandling(createFaqHandler);
