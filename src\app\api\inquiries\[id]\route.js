import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import { 
  successResponse, 
  withErrorHandling, 
  validateStatus
} from '@/lib/utils.js';

// 获取单个咨询
async function getInquiryHandler(request, { params }) {
  await requireEditor(request);
  const { id } = params;
  
  const db = await getDatabase();
  const inquiry = await db.get('inquiries', parseInt(id));
  
  if (!inquiry) {
    throw new Error('咨询记录不存在');
  }
  
  // 获取回复者信息
  let repliedByUser = null;
  if (inquiry.replied_by) {
    const user = await db.get('users', inquiry.replied_by);
    if (user) {
      repliedByUser = { id: user.id, name: user.name, username: user.username };
    }
  }
  
  return successResponse({
    ...inquiry,
    replied_by_user: repliedByUser
  });
}

// 更新咨询状态和回复
async function updateInquiryHandler(request, { params }) {
  const currentUser = await requireEditor(request);
  const { id } = params;
  const body = await request.json();
  
  const db = await getDatabase();
  const inquiry = await db.get('inquiries', parseInt(id));
  
  if (!inquiry) {
    throw new Error('咨询记录不存在');
  }
  
  const { status, reply } = body;
  const updates = {};
  
  // 更新状态
  if (status !== undefined) {
    validateStatus(status, ['pending', 'replied', 'closed']);
    updates.status = status;
  }
  
  // 更新回复
  if (reply !== undefined) {
    updates.reply = reply;
    updates.replied_at = new Date().toISOString();
    updates.replied_by = currentUser.id;
    
    // 如果添加了回复，自动将状态设为已回复
    if (reply && status !== 'closed') {
      updates.status = 'replied';
    }
  }
  
  // 执行更新
  const updatedInquiry = await db.update('inquiries', parseInt(id), updates);
  
  // 记录日志
  await logActivity(currentUser.id, 'UPDATE_INQUIRY', 'inquiry', { 
    inquiryId: updatedInquiry.id, 
    customerName: updatedInquiry.name,
    changes: Object.keys(updates)
  }, 'info', request);
  
  return successResponse(updatedInquiry, '咨询记录更新成功');
}

// 删除咨询记录
async function deleteInquiryHandler(request, { params }) {
  const currentUser = await requireEditor(request);
  const { id } = params;
  
  // 只有管理员可以删除咨询记录
  if (currentUser.role !== 'admin') {
    throw new Error('只有管理员可以删除咨询记录');
  }
  
  const db = await getDatabase();
  const inquiry = await db.get('inquiries', parseInt(id));
  
  if (!inquiry) {
    throw new Error('咨询记录不存在');
  }
  
  // 删除咨询记录
  await db.delete('inquiries', parseInt(id));
  
  // 记录日志
  await logActivity(currentUser.id, 'DELETE_INQUIRY', 'inquiry', { 
    inquiryId: inquiry.id, 
    customerName: inquiry.name 
  }, 'warning', request);
  
  return successResponse(null, '咨询记录删除成功');
}

export const GET = withErrorHandling(getInquiryHandler);
export const PUT = withErrorHandling(updateInquiryHandler);
export const DELETE = withErrorHandling(deleteInquiryHandler);
