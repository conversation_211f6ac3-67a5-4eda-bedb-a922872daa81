import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import { 
  successResponse, 
  paginatedResponse, 
  withErrorHandling, 
  validateRequiredFields,
  validatePaginationParams,
  validateSortParams,
  validateStatus,
  validateEmail,
  validatePhone
} from '@/lib/utils.js';

// 获取咨询列表
async function getInquiriesHandler(request) {
  await requireEditor(request);
  const { searchParams } = new URL(request.url);
  
  // 验证分页参数
  const { page, limit, offset } = validatePaginationParams(searchParams);
  
  // 验证排序参数
  const { sortBy, sortOrder } = validateSortParams(searchParams, [
    'id', 'name', 'email', 'status', 'created_at', 'updated_at'
  ]);
  
  const db = await getDatabase();
  
  // 构建查询条件
  let conditions = {};
  const search = searchParams.get('search');
  const status = searchParams.get('status');
  const serviceType = searchParams.get('serviceType');
  
  if (status) conditions.status = status;
  if (serviceType) conditions.service_type = serviceType;
  
  // 获取所有咨询
  let inquiries = await db.query('inquiries', conditions);
  
  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    inquiries = inquiries.filter(inquiry => 
      inquiry.name?.toLowerCase().includes(searchLower) ||
      inquiry.email?.toLowerCase().includes(searchLower) ||
      inquiry.phone?.toLowerCase().includes(searchLower) ||
      inquiry.subject?.toLowerCase().includes(searchLower) ||
      inquiry.message?.toLowerCase().includes(searchLower)
    );
  }
  
  // 排序
  inquiries.sort((a, b) => {
    const aVal = a[sortBy];
    const bVal = b[sortBy];
    
    if (sortOrder === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });
  
  const total = inquiries.length;
  
  // 分页
  const paginatedInquiries = inquiries.slice(offset, offset + limit);
  
  // 获取回复者信息
  const users = await db.getAll('users');
  const enrichedInquiries = paginatedInquiries.map(inquiry => ({
    ...inquiry,
    replied_by_user: inquiry.replied_by ? users.find(u => u.id === inquiry.replied_by) : null
  }));
  
  return paginatedResponse(enrichedInquiries, total, page, limit);
}

// 创建新咨询（公开接口，用于网站表单提交）
async function createInquiryHandler(request) {
  const body = await request.json();
  
  // 验证必填字段
  validateRequiredFields(body, ['name', 'email', 'message']);
  
  const { 
    name, 
    email, 
    phone, 
    subject, 
    message, 
    service_type,
    preferred_date,
    preferred_time
  } = body;
  
  // 验证数据
  validateEmail(email);
  if (phone) validatePhone(phone);
  
  const db = await getDatabase();
  
  // 创建咨询记录
  const newInquiry = await db.insert('inquiries', {
    name,
    email,
    phone: phone || null,
    subject: subject || '网站咨询',
    message,
    service_type: service_type || null,
    preferred_date: preferred_date || null,
    preferred_time: preferred_time || null,
    status: 'pending'
  });
  
  return successResponse(
    { id: newInquiry.id }, 
    '您的咨询已成功提交，我们将尽快与您联系！'
  );
}

export const GET = withErrorHandling(getInquiriesHandler);
export const POST = withErrorHandling(createInquiryHandler);
