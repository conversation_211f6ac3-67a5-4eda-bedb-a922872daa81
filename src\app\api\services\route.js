import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import { 
  successResponse, 
  paginatedResponse, 
  withErrorHandling, 
  validateRequiredFields,
  validatePaginationParams,
  validateSortParams,
  validateStatus,
  generateSlug,
  sanitizeHtml,
  processImageUrl
} from '@/lib/utils.js';

// 获取服务列表
async function getServicesHandler(request) {
  const { searchParams } = new URL(request.url);
  
  // 验证分页参数
  const { page, limit, offset } = validatePaginationParams(searchParams);
  
  // 验证排序参数
  const { sortBy, sortOrder } = validateSortParams(searchParams, [
    'id', 'title', 'status', 'sort_order', 'is_featured', 'created_at', 'updated_at'
  ]);
  
  const db = await getDatabase();
  
  // 构建查询条件
  let conditions = {};
  const search = searchParams.get('search');
  const status = searchParams.get('status');
  const featured = searchParams.get('featured');
  
  if (status) conditions.status = status;
  if (featured !== null) conditions.is_featured = featured === 'true';
  
  // 获取所有服务
  let services = await db.query('services', conditions);
  
  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    services = services.filter(service => 
      service.title?.toLowerCase().includes(searchLower) ||
      service.description?.toLowerCase().includes(searchLower) ||
      service.content?.toLowerCase().includes(searchLower)
    );
  }
  
  // 排序
  services.sort((a, b) => {
    const aVal = a[sortBy];
    const bVal = b[sortBy];
    
    if (sortOrder === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });
  
  const total = services.length;
  
  // 分页
  const paginatedServices = services.slice(offset, offset + limit);
  
  return paginatedResponse(paginatedServices, total, page, limit);
}

// 创建新服务
async function createServiceHandler(request) {
  const currentUser = await requireEditor(request);
  const body = await request.json();
  
  // 验证必填字段
  validateRequiredFields(body, ['title']);
  
  const { 
    title, 
    description, 
    content, 
    slug, 
    icon_url,
    cover_image_url,
    price_range,
    features,
    sort_order = 0,
    status = 'active',
    is_featured = false
  } = body;
  
  // 验证状态
  validateStatus(status, ['active', 'inactive']);
  
  const db = await getDatabase();
  
  // 生成或验证slug
  let finalSlug = slug;
  if (!finalSlug) {
    const existingServices = await db.getAll('services');
    const existingSlugs = existingServices.map(s => s.slug);
    finalSlug = generateSlug(title, existingSlugs);
  } else {
    // 检查slug是否已存在
    const existingService = await db.query('services', { slug: finalSlug });
    if (existingService.length > 0) {
      throw new Error('服务别名已存在');
    }
  }
  
  // 处理内容
  const sanitizedContent = content ? sanitizeHtml(content) : null;
  const processedIconUrl = processImageUrl(icon_url);
  const processedCoverUrl = processImageUrl(cover_image_url);
  
  // 创建服务
  const newService = await db.insert('services', {
    title,
    slug: finalSlug,
    description: description || null,
    content: sanitizedContent,
    icon_url: processedIconUrl,
    cover_image_url: processedCoverUrl,
    price_range: price_range || null,
    features: features || null,
    sort_order: parseInt(sort_order),
    status,
    is_featured: Boolean(is_featured)
  });
  
  // 记录日志
  await logActivity(currentUser.id, 'CREATE_SERVICE', 'content', { 
    serviceId: newService.id, 
    title: newService.title 
  }, 'info', request);
  
  return successResponse(newService, '服务创建成功');
}

export const GET = withErrorHandling(getServicesHandler);
export const POST = withErrorHandling(createServiceHandler);
