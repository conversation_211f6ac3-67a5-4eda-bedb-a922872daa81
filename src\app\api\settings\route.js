import { getDatabase } from '@/lib/database.js';
import { requireAdmin, logActivity } from '@/lib/auth.js';
import { 
  successResponse, 
  withErrorHandling, 
  validateEmail,
  validatePhone,
  validateUrl
} from '@/lib/utils.js';

// 获取系统设置
async function getSettingsHandler(request) {
  await requireAdmin(request);
  
  const db = await getDatabase();
  const settingsRecord = await db.get('settings', 1);
  
  if (!settingsRecord) {
    // 返回默认设置
    return successResponse({
      siteName: '武汉思立恒教育科技有限公司',
      siteDescription: '您的教育与生涯导航专家',
      contactEmail: '<EMAIL>',
      contactPhone: '027-12345678',
      address: '武汉市洪山区光谷大道123号',
      icp: '鄂ICP备12345678号',
      seoKeywords: '教育规划,生涯规划,升学规划,高考志愿填报,职业规划',
      seoDescription: '武汉思立恒教育科技有限公司提供专业的教育规划与生涯导航服务',
      logoUrl: '/images/logo.png',
      faviconUrl: '/favicon.ico',
      footerCopyright: '© 2024 武汉思立恒教育科技有限公司 版权所有',
      socialMedia: {
        weixin: 'slhgw_weixin',
        weibo: 'slhgw_weibo',
        zhihu: 'slhgw_zhihu'
      }
    });
  }
  
  const settings = JSON.parse(settingsRecord.settings);
  return successResponse(settings);
}

// 更新系统设置
async function updateSettingsHandler(request) {
  const currentUser = await requireAdmin(request);
  const body = await request.json();
  
  const { 
    siteName,
    siteDescription,
    contactEmail,
    contactPhone,
    address,
    icp,
    seoKeywords,
    seoDescription,
    logoUrl,
    faviconUrl,
    footerCopyright,
    socialMedia
  } = body;
  
  // 验证数据
  if (contactEmail) validateEmail(contactEmail);
  if (contactPhone) validatePhone(contactPhone);
  if (logoUrl) validateUrl(logoUrl);
  if (faviconUrl) validateUrl(faviconUrl);
  
  const db = await getDatabase();
  
  // 构建设置对象
  const settings = {
    siteName: siteName || '武汉思立恒教育科技有限公司',
    siteDescription: siteDescription || '您的教育与生涯导航专家',
    contactEmail: contactEmail || '<EMAIL>',
    contactPhone: contactPhone || '027-12345678',
    address: address || '武汉市洪山区光谷大道123号',
    icp: icp || '鄂ICP备12345678号',
    seoKeywords: seoKeywords || '教育规划,生涯规划,升学规划,高考志愿填报,职业规划',
    seoDescription: seoDescription || '武汉思立恒教育科技有限公司提供专业的教育规划与生涯导航服务',
    logoUrl: logoUrl || '/images/logo.png',
    faviconUrl: faviconUrl || '/favicon.ico',
    footerCopyright: footerCopyright || '© 2024 武汉思立恒教育科技有限公司 版权所有',
    socialMedia: socialMedia || {
      weixin: 'slhgw_weixin',
      weibo: 'slhgw_weibo',
      zhihu: 'slhgw_zhihu'
    }
  };
  
  // 检查设置记录是否存在
  const existingSettings = await db.get('settings', 1);
  
  let updatedSettings;
  if (existingSettings) {
    // 更新现有设置
    updatedSettings = await db.update('settings', 1, {
      settings: JSON.stringify(settings)
    });
  } else {
    // 创建新设置
    updatedSettings = await db.insert('settings', {
      id: 1,
      settings: JSON.stringify(settings)
    });
  }
  
  // 记录日志
  await logActivity(currentUser.id, 'UPDATE_SETTINGS', 'system', { 
    changes: Object.keys(body)
  }, 'info', request);
  
  return successResponse(settings, '系统设置更新成功');
}

export const GET = withErrorHandling(getSettingsHandler);
export const PUT = withErrorHandling(updateSettingsHandler);
