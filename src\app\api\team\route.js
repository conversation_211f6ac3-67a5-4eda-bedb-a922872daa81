import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import { 
  successResponse, 
  paginatedResponse, 
  withErrorHandling, 
  validateRequiredFields,
  validatePaginationParams,
  validateSortParams,
  validateStatus,
  validateEmail,
  validatePhone,
  processImageUrl
} from '@/lib/utils.js';

// 获取团队成员列表
async function getTeamMembersHandler(request) {
  const { searchParams } = new URL(request.url);
  
  // 验证分页参数
  const { page, limit, offset } = validatePaginationParams(searchParams);
  
  // 验证排序参数
  const { sortBy, sortOrder } = validateSortParams(searchParams, [
    'id', 'name', 'position', 'status', 'sort_order', 'created_at', 'updated_at'
  ]);
  
  const db = await getDatabase();
  
  // 构建查询条件
  let conditions = {};
  const search = searchParams.get('search');
  const status = searchParams.get('status');
  
  if (status) conditions.status = status;
  
  // 获取所有团队成员
  let teamMembers = await db.query('team', conditions);
  
  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    teamMembers = teamMembers.filter(member => 
      member.name?.toLowerCase().includes(searchLower) ||
      member.position?.toLowerCase().includes(searchLower) ||
      member.bio?.toLowerCase().includes(searchLower) ||
      member.expertise?.toLowerCase().includes(searchLower)
    );
  }
  
  // 排序
  teamMembers.sort((a, b) => {
    const aVal = a[sortBy];
    const bVal = b[sortBy];
    
    if (sortOrder === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });
  
  const total = teamMembers.length;
  
  // 分页
  const paginatedMembers = teamMembers.slice(offset, offset + limit);
  
  return paginatedResponse(paginatedMembers, total, page, limit);
}

// 创建新团队成员
async function createTeamMemberHandler(request) {
  const currentUser = await requireEditor(request);
  const body = await request.json();
  
  // 验证必填字段
  validateRequiredFields(body, ['name']);
  
  const { 
    name, 
    position,
    bio,
    avatar_url,
    email,
    phone,
    social_links,
    expertise,
    sort_order = 0,
    status = 'active'
  } = body;
  
  // 验证状态
  validateStatus(status, ['active', 'inactive']);
  
  // 验证邮箱和手机号
  if (email) validateEmail(email);
  if (phone) validatePhone(phone);
  
  const db = await getDatabase();
  
  // 处理头像URL
  const processedAvatarUrl = processImageUrl(avatar_url);
  
  // 创建团队成员
  const newMember = await db.insert('team', {
    name,
    position: position || null,
    bio: bio || null,
    avatar_url: processedAvatarUrl,
    email: email || null,
    phone: phone || null,
    social_links: social_links || null,
    expertise: expertise || null,
    sort_order: parseInt(sort_order),
    status
  });
  
  // 记录日志
  await logActivity(currentUser.id, 'CREATE_TEAM_MEMBER', 'content', { 
    memberId: newMember.id, 
    name: newMember.name 
  }, 'info', request);
  
  return successResponse(newMember, '团队成员创建成功');
}

export const GET = withErrorHandling(getTeamMembersHandler);
export const POST = withErrorHandling(createTeamMemberHandler);
