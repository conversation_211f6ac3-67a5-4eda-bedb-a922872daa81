import { getDatabase } from '@/lib/database.js';
import { successResponse, withErrorHandling } from '@/lib/utils.js';
import fs from 'fs/promises';
import path from 'path';

// 强制重新初始化数据库
async function forceInitDatabase(request) {
  try {
    const db = await getDatabase();

    // 读取现有数据
    let consultants = await db.getAll('consultants');
    let appointments = await db.getAll('appointments');

    console.log('当前咨询师数量:', consultants.length);
    console.log('当前预约数量:', appointments.length);

    // 如果没有咨询师数据，从JSON文件加载
    if (consultants.length === 0) {
      try {
        const consultantsPath = path.join(process.cwd(), 'data', 'consultants.json');
        const consultantsData = await fs.readFile(consultantsPath, 'utf8');
        consultants = JSON.parse(consultantsData);
        await db.writeTable('consultants', consultants);
        console.log('已从JSON文件加载咨询师数据:', consultants.length);
      } catch (error) {
        console.error('加载咨询师数据失败:', error);
      }
    }

    // 如果没有预约数据，从JSON文件加载
    if (appointments.length === 0) {
      try {
        const appointmentsPath = path.join(process.cwd(), 'data', 'appointments.json');
        const appointmentsData = await fs.readFile(appointmentsPath, 'utf8');
        appointments = JSON.parse(appointmentsData);
        await db.writeTable('appointments', appointments);
        console.log('已从JSON文件加载预约数据:', appointments.length);
      } catch (error) {
        console.error('加载预约数据失败:', error);
      }
    }

    return successResponse({
      consultants: consultants.length,
      appointments: appointments.length,
      message: '数据初始化完成'
    }, '数据库状态检查完成');
  } catch (error) {
    console.error('数据库检查失败:', error);
    throw error;
  }
}

export const GET = withErrorHandling(forceInitDatabase);
