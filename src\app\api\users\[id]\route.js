import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity, hashPassword, validateEmail, validatePassword } from '@/lib/auth.js';
import { 
  successResponse, 
  withErrorHandling, 
  validateRequiredFields,
  validateStatus
} from '@/lib/utils.js';

// 获取单个用户
async function getUserHandler(request, { params }) {
  await requireEditor(request);
  const { id } = params;
  
  const db = await getDatabase();
  const user = await db.get('users', parseInt(id));
  
  if (!user) {
    throw new Error('用户不存在');
  }
  
  // 移除密码字段
  const { password_hash, ...safeUser } = user;
  return successResponse(safeUser);
}

// 更新用户
async function updateUserHandler(request, { params }) {
  const currentUser = await requireEditor(request);
  const { id } = params;
  const body = await request.json();
  
  const db = await getDatabase();
  const user = await db.get('users', parseInt(id));
  
  if (!user) {
    throw new Error('用户不存在');
  }
  
  // 只有管理员可以修改其他用户，或者用户修改自己的信息
  if (currentUser.role !== 'admin' && currentUser.id !== user.id) {
    throw new Error('权限不足');
  }
  
  const { password, email, role, status, ...otherFields } = body;
  const updates = { ...otherFields };
  
  // 验证邮箱
  if (email) {
    validateEmail(email);
    // 检查邮箱是否已被其他用户使用
    const existingUsers = await db.getAll('users');
    if (existingUsers.some(u => u.email === email && u.id !== user.id)) {
      throw new Error('邮箱已被使用');
    }
    updates.email = email;
  }
  
  // 验证密码
  if (password) {
    validatePassword(password);
    updates.password_hash = await hashPassword(password);
  }
  
  // 验证角色（只有管理员可以修改角色）
  if (role && currentUser.role === 'admin') {
    if (!['admin', 'editor', 'viewer'].includes(role)) {
      throw new Error('无效的角色');
    }
    updates.role = role;
  }
  
  // 验证状态（只有管理员可以修改状态）
  if (status && currentUser.role === 'admin') {
    validateStatus(status, ['active', 'inactive', 'pending']);
    updates.status = status;
  }
  
  // 更新用户
  const updatedUser = await db.update('users', parseInt(id), updates);
  
  // 记录日志
  await logActivity(currentUser.id, 'UPDATE_USER', 'user', { 
    targetUserId: updatedUser.id, 
    username: updatedUser.username,
    changes: Object.keys(updates)
  }, 'info', request);
  
  // 返回用户信息（不包含密码）
  const { password_hash, ...safeUser } = updatedUser;
  return successResponse(safeUser, '用户更新成功');
}

// 删除用户
async function deleteUserHandler(request, { params }) {
  const currentUser = await requireEditor(request);
  const { id } = params;
  
  // 只有管理员可以删除用户
  if (currentUser.role !== 'admin') {
    throw new Error('只有管理员可以删除用户');
  }
  
  const db = await getDatabase();
  const user = await db.get('users', parseInt(id));
  
  if (!user) {
    throw new Error('用户不存在');
  }
  
  // 不能删除自己
  if (currentUser.id === user.id) {
    throw new Error('不能删除自己的账户');
  }
  
  // 删除用户
  await db.delete('users', parseInt(id));
  
  // 记录日志
  await logActivity(currentUser.id, 'DELETE_USER', 'user', { 
    targetUserId: user.id, 
    username: user.username 
  }, 'warning', request);
  
  return successResponse(null, '用户删除成功');
}

export const GET = withErrorHandling(getUserHandler);
export const PUT = withErrorHandling(updateUserHandler);
export const DELETE = withErrorHandling(deleteUserHandler);
