import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity } from '@/lib/auth.js';
import { 
  successResponse, 
  withErrorHandling, 
  validateRequiredFields,
  validateStatus
} from '@/lib/utils.js';

// 更新用户状态
async function updateUserStatusHandler(request, { params }) {
  const currentUser = await requireEditor(request);
  const { id } = params;
  const body = await request.json();
  
  // 验证必填字段
  validateRequiredFields(body, ['status']);
  
  const { status } = body;
  
  // 验证状态值
  validateStatus(status, ['active', 'inactive', 'pending']);
  
  const db = await getDatabase();
  const user = await db.get('users', parseInt(id));
  
  if (!user) {
    throw new Error('用户不存在');
  }
  
  // 只有管理员可以修改用户状态
  if (currentUser.role !== 'admin') {
    throw new Error('只有管理员可以修改用户状态');
  }
  
  // 不能修改自己的状态
  if (currentUser.id === user.id) {
    throw new Error('不能修改自己的状态');
  }
  
  // 更新用户状态
  const updatedUser = await db.update('users', parseInt(id), { status });
  
  // 记录日志
  await logActivity(currentUser.id, 'UPDATE_USER_STATUS', 'user', { 
    targetUserId: updatedUser.id, 
    username: updatedUser.username,
    oldStatus: user.status,
    newStatus: status
  }, 'info', request);
  
  // 返回用户信息（不包含密码）
  const { password_hash, ...safeUser } = updatedUser;
  return successResponse(safeUser, '用户状态更新成功');
}

export const PATCH = withErrorHandling(updateUserStatusHandler);
