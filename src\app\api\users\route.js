import { getDatabase } from '@/lib/database.js';
import { requireEditor, logActivity, hashPassword, validateEmail, validatePassword } from '@/lib/auth.js';
import { 
  successResponse, 
  paginatedResponse, 
  withErrorHandling, 
  validateRequiredFields,
  validatePaginationParams,
  validateSortParams,
  buildSearchCondition,
  validateStatus
} from '@/lib/utils.js';

// 获取用户列表
async function getUsersHandler(request) {
  const currentUser = await requireEditor(request);
  const { searchParams } = new URL(request.url);
  
  // 验证分页参数
  const { page, limit, offset } = validatePaginationParams(searchParams);
  
  // 验证排序参数
  const { sortBy, sortOrder } = validateSortParams(searchParams, [
    'id', 'username', 'email', 'name', 'role', 'status', 'created_at', 'updated_at'
  ]);
  
  const db = await getDatabase();
  
  // 构建查询条件
  let conditions = {};
  const search = searchParams.get('search');
  const role = searchParams.get('role');
  const status = searchParams.get('status');
  
  if (role) conditions.role = role;
  if (status) conditions.status = status;
  
  // 获取所有用户
  let users = await db.query('users', conditions);
  
  // 搜索过滤
  if (search) {
    const searchLower = search.toLowerCase();
    users = users.filter(user => 
      user.username?.toLowerCase().includes(searchLower) ||
      user.email?.toLowerCase().includes(searchLower) ||
      user.name?.toLowerCase().includes(searchLower)
    );
  }
  
  // 排序
  users.sort((a, b) => {
    const aVal = a[sortBy];
    const bVal = b[sortBy];
    
    if (sortOrder === 'asc') {
      return aVal > bVal ? 1 : -1;
    } else {
      return aVal < bVal ? 1 : -1;
    }
  });
  
  const total = users.length;
  
  // 分页
  const paginatedUsers = users.slice(offset, offset + limit);
  
  // 移除密码字段
  const safeUsers = paginatedUsers.map(user => {
    const { password_hash, ...safeUser } = user;
    return safeUser;
  });
  
  return paginatedResponse(safeUsers, total, page, limit);
}

// 创建新用户
async function createUserHandler(request) {
  const currentUser = await requireEditor(request);
  const body = await request.json();
  
  // 验证必填字段
  validateRequiredFields(body, ['username', 'email', 'password', 'name']);
  
  const { username, email, password, name, role = 'editor', status = 'active', ...otherFields } = body;
  
  // 验证数据
  validateEmail(email);
  validatePassword(password);
  validateStatus(status, ['active', 'inactive', 'pending']);
  
  if (!['admin', 'editor', 'viewer'].includes(role)) {
    throw new Error('无效的角色');
  }
  
  // 只有管理员可以创建管理员用户
  if (role === 'admin' && currentUser.role !== 'admin') {
    throw new Error('只有管理员可以创建管理员用户');
  }
  
  const db = await getDatabase();
  
  // 检查用户名和邮箱是否已存在
  const existingUsers = await db.getAll('users');
  if (existingUsers.some(u => u.username === username)) {
    throw new Error('用户名已存在');
  }
  if (existingUsers.some(u => u.email === email)) {
    throw new Error('邮箱已存在');
  }
  
  // 哈希密码
  const passwordHash = await hashPassword(password);
  
  // 创建用户
  const newUser = await db.insert('users', {
    username,
    email,
    password_hash: passwordHash,
    name,
    role,
    status,
    ...otherFields
  });
  
  // 记录日志
  await logActivity(currentUser.id, 'CREATE_USER', 'user', { 
    targetUserId: newUser.id, 
    username: newUser.username 
  }, 'info', request);
  
  // 返回用户信息（不包含密码）
  const { password_hash, ...safeUser } = newUser;
  return successResponse(safeUser, '用户创建成功');
}

export const GET = withErrorHandling(getUsersHandler);
export const POST = withErrorHandling(createUserHandler);
