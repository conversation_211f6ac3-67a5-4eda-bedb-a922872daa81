'use client';

import Link from 'next/link';
import Image from 'next/image';
import { FaCheckCircle, FaPhone, FaEnvelope, FaWeixin } from 'react-icons/fa';

export default function AppointmentSuccessPage() {
  return (
    <main className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          {/* 成功图标和标题 */}
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <FaCheckCircle className="text-4xl text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">预约申请已成功提交！</h1>
            <p className="text-lg text-gray-600">
              感谢您选择我们的咨询服务，我们会尽快与您联系确认预约详情。
            </p>
          </div>

          {/* 后续步骤 */}
          <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-6">接下来会发生什么？</h2>

            <div className="space-y-6">
              <div className="flex items-start">
                <div className="w-8 h-8 bg-sky-100 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span className="text-sm font-bold text-sky-600">1</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">预约确认</h3>
                  <p className="text-gray-600">我们的工作人员会在24小时内通过电话或邮件与您联系，确认预约时间和咨询方式。</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-8 h-8 bg-sky-100 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span className="text-sm font-bold text-sky-600">2</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">准备材料</h3>
                  <p className="text-gray-600">根据您的咨询需求，我们会提供一份准备清单，帮助您更好地准备咨询内容。</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-8 h-8 bg-sky-100 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span className="text-sm font-bold text-sky-600">3</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">开始咨询</h3>
                  <p className="text-gray-600">在约定的时间，您可以通过线上或线下的方式与咨询师进行一对一咨询。</p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="w-8 h-8 bg-sky-100 rounded-full flex items-center justify-center mr-4 mt-1">
                  <span className="text-sm font-bold text-sky-600">4</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">后续跟进</h3>
                  <p className="text-gray-600">咨询结束后，我们会提供咨询报告和后续建议，并安排必要的跟进服务。</p>
                </div>
              </div>
            </div>
          </div>

          {/* 顾问二维码区域 */}
          <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                <FaWeixin className="text-2xl text-green-500 mr-2" />
                <h2 className="text-xl font-bold text-gray-900">扫码添加专属顾问微信</h2>
              </div>
              <p className="text-gray-600 mb-6">获得一对一专业指导，随时解答您的疑问</p>

              <div className="flex justify-center mb-6">
                <div className="w-48 h-48 bg-gray-100 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300">
                  {/* 这里可以放置实际的二维码图片 */}
                  <div className="text-center">
                    <FaWeixin className="text-4xl text-gray-400 mb-2 mx-auto" />
                    <p className="text-sm text-gray-500">顾问微信二维码</p>
                    <p className="text-xs text-gray-400 mt-1">扫码添加好友</p>
                  </div>
                  {/* 如果有实际二维码图片，可以使用以下代码替换上面的占位内容：
                  <Image
                    src="/images/advisor-qrcode.png"
                    alt="顾问微信二维码"
                    width={192}
                    height={192}
                    className="rounded-lg"
                  />
                  */}
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-4">
                <h3 className="font-semibold text-green-800 mb-2">专属顾问服务包括：</h3>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• 预约确认和时间调整</li>
                  <li>• 咨询前准备指导</li>
                  <li>• 后续跟进服务</li>
                  <li>• 专业问题解答</li>
                </ul>
              </div>
            </div>
          </div>

          {/* 联系信息 */}
          <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
            <h2 className="text-xl font-bold text-gray-900 mb-6">需要帮助？</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-sky-100 rounded-lg flex items-center justify-center mr-4">
                  <FaPhone className="text-sky-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">电话咨询</h3>
                  <p className="text-gray-600">177-0272-0924</p>
                  <p className="text-sm text-gray-500">工作日 9:00-18:00</p>
                </div>
              </div>

              <div className="flex items-center">
                <div className="w-12 h-12 bg-sky-100 rounded-lg flex items-center justify-center mr-4">
                  <FaEnvelope className="text-sky-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">邮件咨询</h3>
                  <p className="text-gray-600"><EMAIL></p>
                  <p className="text-sm text-gray-500">24小时内回复</p>
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/consultants"
              className="bg-sky-600 hover:bg-sky-700 text-white px-8 py-3 rounded-lg font-semibold text-center transition-colors duration-300"
            >
              预约其他咨询师
            </Link>
            <Link
              href="/"
              className="bg-white hover:bg-gray-50 text-sky-600 border border-sky-600 px-8 py-3 rounded-lg font-semibold text-center transition-colors duration-300"
            >
              返回首页
            </Link>
          </div>

          {/* 温馨提示 */}
          <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="font-semibold text-yellow-800 mb-2">温馨提示</h3>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• 请保持电话畅通，我们会尽快与您联系确认预约</li>
              <li>• 如需修改或取消预约，请至少提前24小时联系我们</li>
              <li>• 首次咨询建议准备相关背景资料，以便咨询师更好地了解您的情况</li>
              <li>• 咨询过程中如有任何问题，请随时与我们的客服团队联系</li>
            </ul>
          </div>
        </div>
      </div>
    </main>
  );
}
