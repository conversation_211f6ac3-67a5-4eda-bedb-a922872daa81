'use client';

import { useState } from 'react';
import Image from 'next/image'; // 引入Image组件
import Link from 'next/link';

export default function AppointmentPage() {
  // 预约表单数据
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    serviceType: '',
    preferredDate: '',
    preferredTime: '',
    message: '',
  });

  // 表单状态
  const [formStatus, setFormStatus] = useState({
    submitted: false,
    success: false,
    message: '',
  });

  // 提交状态
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 可选服务类型
  const serviceTypes = [
    { id: 'k12', name: '学前及K12教育规划' },
    { id: 'high-school', name: '高中一体化升学规划' },
    { id: 'university', name: '大学发展与深造规划' },
    { id: 'career', name: '职业人士生涯进阶服务' },
    { id: 'other', name: '其他咨询' },
  ];

  // 可选时间段
  const timeSlots = [
    '09:00 - 10:00',
    '10:00 - 11:00',
    '11:00 - 12:00',
    '14:00 - 15:00',
    '15:00 - 16:00',
    '16:00 - 17:00',
  ];

  // 处理表单输入变化
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // 处理表单提交
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/appointment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const result = await response.json();
      
      setFormStatus({
        submitted: true,
        success: result.success,
        message: result.message
      });
      
      if (result.success) {
        // 重置表单
        setFormData({
          name: '',
          email: '',
          phone: '',
          serviceType: '',
          preferredDate: '',
          preferredTime: '',
          message: '',
        });
      }
    } catch (error) {
      console.error('提交预约表单时出错:', error);
      setFormStatus({
        submitted: true,
        success: false,
        message: '预约提交失败，请稍后再试或直接联系我们。'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 获取明天的日期作为最小可选日期
  const getTomorrowDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  return (
    <main className="min-h-screen bg-white">
      {/* 页面标题区 */}
      <section className="py-24 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-sky-500 opacity-90"></div>
        <div className="absolute inset-0 opacity-10" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-white to-transparent"></div>
        <div className="container mx-auto px-4 text-center relative z-10">
          <span className="inline-block px-4 py-1 bg-white/20 text-white rounded-full text-sm font-medium mb-6 backdrop-blur-sm">专业咨询</span>
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white">在线预约</h1>
          <p className="text-xl max-w-3xl mx-auto text-white/90 mb-8">预约我们的专业顾问，获取一对一的个性化咨询服务</p>
          <div className="flex flex-wrap justify-center gap-4 mt-8">
            <a href="#appointment-form" className="px-8 py-4 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group">
              立即预约 
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline-block ml-2 group-hover:ml-3 transition-all duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              </svg>
            </a>
            <Link href="/contact" className="px-8 py-4 border-2 border-white text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-300 backdrop-blur-sm">
              联系我们
            </Link>
          </div>
        </div>
      </section>

      {/* 预约表单区域 */}
      <section id="appointment-form" className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <span className="inline-block px-4 py-1 bg-blue-50 text-blue-600 rounded-full text-sm font-medium mb-4">填写信息</span>
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800 relative inline-block">
                预约咨询服务
                <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full"></span>
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">填写以下表单，我们的顾问将在1个工作日内与您联系确认详情</p>
            </div>
            
            <div className="bg-white rounded-xl shadow-xl p-8 md:p-10 border border-gray-100">
              {formStatus.submitted ? (
                <div className={`p-8 rounded-lg ${formStatus.success ? 'bg-green-50 text-green-700 border border-green-100' : 'bg-red-50 text-red-700 border border-red-100'}`}>
                  <div className="flex flex-col items-center text-center">
                    <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${formStatus.success ? 'bg-green-100' : 'bg-red-100'}`}>
                      {formStatus.success ? (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      )}
                    </div>
                    <p className="text-xl font-bold mb-2">{formStatus.success ? '预约提交成功！' : '提交失败'}</p>
                    <p className="text-lg mb-6">{formStatus.message}</p>
                    <button 
                      onClick={() => setFormStatus({submitted: false, success: false, message: ''})}
                      className={`px-6 py-3 rounded-lg font-medium transition-all duration-300 ${formStatus.success ? 'bg-gradient-to-r from-blue-600 to-sky-500 text-white hover:shadow-lg transform hover:-translate-y-1' : 'bg-white border border-red-200 text-red-600 hover:bg-red-50'}`}
                    >
                      {formStatus.success ? '再次预约' : '重新提交'}
                    </button>
                  </div>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="name" className="block text-gray-700 font-medium">姓名 <span className="text-red-500">*</span></label>
                      <div className="relative">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        <input 
                          type="text" 
                          id="name" 
                          name="name" 
                          value={formData.name} 
                          onChange={handleChange} 
                          required 
                          className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                          placeholder="您的姓名"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="email" className="block text-gray-700 font-medium">电子邮箱 <span className="text-red-500">*</span></label>
                      <div className="relative">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                        <input 
                          type="email" 
                          id="email" 
                          name="email" 
                          value={formData.email} 
                          onChange={handleChange} 
                          required 
                          className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                          placeholder="您的邮箱地址"
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="phone" className="block text-gray-700 font-medium">电话 <span className="text-red-500">*</span></label>
                      <div className="relative">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        <input 
                          type="tel" 
                          id="phone" 
                          name="phone" 
                          value={formData.phone} 
                          onChange={handleChange} 
                          required
                          className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                          placeholder="您的联系电话"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="serviceType" className="block text-gray-700 font-medium">咨询服务类型 <span className="text-red-500">*</span></label>
                      <div className="relative">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        <select 
                          id="serviceType" 
                          name="serviceType" 
                          value={formData.serviceType} 
                          onChange={handleChange} 
                          required 
                          className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm appearance-none bg-white"
                        >
                          <option value="">请选择服务类型</option>
                          {serviceTypes.map(type => (
                            <option key={type.id} value={type.id}>{type.name}</option>
                          ))}
                        </select>
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                          <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label htmlFor="preferredDate" className="block text-gray-700 font-medium">期望日期 <span className="text-red-500">*</span></label>
                      <div className="relative">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <input 
                          type="date" 
                          id="preferredDate" 
                          name="preferredDate" 
                          value={formData.preferredDate} 
                          onChange={handleChange} 
                          min={getTomorrowDate()}
                          required 
                          className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="preferredTime" className="block text-gray-700 font-medium">期望时间段 <span className="text-red-500">*</span></label>
                      <div className="relative">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <select 
                          id="preferredTime" 
                          name="preferredTime" 
                          value={formData.preferredTime} 
                          onChange={handleChange} 
                          required 
                          className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm appearance-none bg-white"
                        >
                          <option value="">请选择时间段</option>
                          {timeSlots.map(slot => (
                            <option key={slot} value={slot}>{slot}</option>
                          ))}
                        </select>
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                          <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label htmlFor="message" className="block text-gray-700 font-medium">补充说明</label>
                    <div className="relative">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 absolute left-3 top-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                      <textarea 
                        id="message" 
                        name="message" 
                        value={formData.message} 
                        onChange={handleChange} 
                        rows={4} 
                        className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-sm"
                        placeholder="请简要描述您的咨询需求或问题，以便我们更好地为您准备"
                      ></textarea>
                    </div>
                  </div>
                  
                  <div className="pt-4">
                    <button 
                      type="submit" 
                      disabled={isSubmitting}
                      className={`w-full px-6 py-4 rounded-lg font-medium flex items-center justify-center transition-all duration-300 ${isSubmitting ? 'bg-blue-400 cursor-not-allowed' : 'bg-gradient-to-r from-blue-600 to-sky-500 text-white hover:shadow-lg transform hover:-translate-y-1'}`}
                    >
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          提交中...
                        </>
                      ) : (
                        <>
                          提交预约申请
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </>
                      )}
                    </button>
                    <p className="text-sm text-gray-500 mt-4 text-center">提交即表示您同意我们的<Link href="/privacy" className="text-blue-600 hover:underline">隐私政策</Link></p>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* 咨询师展示区域 */}
      <section className="py-24 bg-gradient-to-b from-white to-blue-50 relative overflow-hidden">
        <div className="absolute inset-0 opacity-5" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1 bg-blue-50 text-blue-600 rounded-full text-sm font-medium mb-4">专业团队</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800 relative inline-block">
              我们的专业咨询师
              <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full"></span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">拥有丰富经验的专业团队，为您提供个性化的教育与职业规划咨询</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
            {[
              {
                id: 1,
                name: '王老师',
                title: '首席生涯规划师',
                specialties: ['K12教育规划', '新高考选科', '大学专业选择'],
                imageUrl: '/team/member-1.jpg', // 请替换为实际图片路径
                bio: '王老师拥有超过15年的教育咨询经验，成功指导上千名学生进入理想的学府与职业道路。'
              },
              {
                id: 2,
                name: '李博士',
                title: '资深心理咨询师',
                specialties: ['青少年心理辅导', '家庭关系调适', '情绪压力管理'],
                imageUrl: '/team/member-2.jpg', // 请替换为实际图片路径
                bio: '李博士专注于青少年心理健康领域，以其深深的专业知识和丰富的实践经验，帮助众多家庭和学生走出困境。'
              },
              {
                id: 3,
                name: '赵顾问',
                title: '职业发展规划专家',
                specialties: ['大学生就业指导', '职场进阶规划', '创业咨询'],
                imageUrl: '/team/member-3.jpg', // 请替换为实际图片路径
                bio: '赵顾问在人力资源和职业发展领域有独到见解，擅长为不同阶段的职场人士提供个性化的发展建议。'
              },
            ].map((consultant) => (
              <div key={consultant.id} className="bg-white rounded-xl shadow-xl overflow-hidden group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                <div className="relative h-64 w-full overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-blue-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>
                  <Image 
                    src={consultant.imageUrl}
                    alt={consultant.name}
                    layout="fill"
                    objectFit="cover"
                    className="transition-transform duration-500 group-hover:scale-110"
                  />
                </div>
                <div className="p-8">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-2xl font-bold mb-1 text-gray-800 group-hover:text-blue-600 transition-colors">{consultant.name}</h3>
                      <p className="text-blue-600 font-medium">{consultant.title}</p>
                    </div>
                    <div className="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center group-hover:bg-blue-600 transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600 group-hover:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  </div>
                  
                  <div className="mb-5">
                    {consultant.specialties.map(spec => (
                      <span key={spec} className="inline-block bg-blue-50 text-blue-700 text-xs px-3 py-1.5 rounded-full mr-2 mb-2 border border-blue-100 group-hover:bg-blue-600 group-hover:text-white group-hover:border-blue-500 transition-colors">{spec}</span>
                    ))}
                  </div>
                  
                  <p className="text-gray-600 mb-6 h-20 overflow-hidden">{consultant.bio}</p>
                  
                  <div className="pt-4 border-t border-gray-100">
                    <button className="w-full bg-gradient-to-r from-blue-600 to-sky-500 text-white px-6 py-3 rounded-lg font-medium transition-all duration-300 flex items-center justify-center group-hover:shadow-lg">
                      预约咨询
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 group-hover:ml-3 transition-all duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Link href="/about#team" className="inline-flex items-center text-blue-600 font-medium hover:text-blue-800 transition-colors">
              查看全部团队成员
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* 预约说明 */}
      <section className="py-24 bg-gradient-to-b from-blue-50 to-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-5" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
        <div className="container mx-auto px-4 max-w-6xl relative z-10">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1 bg-blue-50 text-blue-600 rounded-full text-sm font-medium mb-4">使用指南</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-800 relative inline-block">
              预约须知
              <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full"></span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">了解我们的预约流程和注意事项，让您的咨询体验更加顺畅</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
            <div className="bg-white p-8 rounded-xl shadow-xl border border-gray-100 relative overflow-hidden group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-blue-600 to-sky-400"></div>
              <div className="absolute top-0 right-0 w-24 h-24 -mt-8 -mr-8 bg-blue-50 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-300"></div>
              
              <h3 className="text-2xl font-bold mb-6 text-gray-800 flex items-center">
                <span className="w-10 h-10 rounded-full bg-blue-600 text-white flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                  </svg>
                </span>
                预约流程
              </h3>
              
              <ol className="space-y-5 relative z-10">
                {[
                  { step: 1, text: '填写并提交在线预约表单' },
                  { step: 2, text: '顾问电话确认预约详情（1个工作日内）' },
                  { step: 3, text: '按约定时间进行咨询服务' },
                  { step: 4, text: '咨询后跟进与服务评价' }
                ].map((item) => (
                  <li key={item.step} className="flex items-start group/item">
                    <span className="w-8 h-8 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-4 flex-shrink-0 font-bold group-hover/item:bg-blue-600 group-hover/item:text-white transition-colors duration-300">{item.step}</span>
                    <span className="text-gray-700 pt-1 group-hover/item:text-blue-600 transition-colors duration-300">{item.text}</span>
                  </li>
                ))}
              </ol>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-xl border border-gray-100 relative overflow-hidden group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="absolute top-0 left-0 w-2 h-full bg-gradient-to-b from-orange-400 to-yellow-400"></div>
              <div className="absolute top-0 right-0 w-24 h-24 -mt-8 -mr-8 bg-orange-50 rounded-full opacity-20 group-hover:opacity-40 transition-opacity duration-300"></div>
              
              <h3 className="text-2xl font-bold mb-6 text-gray-800 flex items-center">
                <span className="w-10 h-10 rounded-full bg-orange-400 text-white flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </span>
                温馨提示
              </h3>
              
              <ul className="space-y-5 relative z-10">
                {[
                  '预约时间需提前至少24小时',
                  '如需取消或变更预约，请提前12小时通知',
                  '初次咨询时长约30分钟',
                  '目前暂时只支持线上视频咨询'
                ].map((tip, index) => (
                  <li key={index} className="flex items-start group/item">
                    <span className="w-8 h-8 rounded-full bg-orange-100 text-orange-500 flex items-center justify-center mr-4 flex-shrink-0 group-hover/item:bg-orange-400 group-hover/item:text-white transition-colors duration-300">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </span>
                    <span className="text-gray-700 pt-1 group-hover/item:text-orange-500 transition-colors duration-300">{tip}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
          
          <div className="mt-16 text-center bg-gradient-to-r from-blue-600 to-sky-500 p-10 rounded-2xl shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 opacity-10" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
            <h3 className="text-2xl font-bold mb-4 text-white">紧急咨询需求？</h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">如有紧急咨询需求，请直接拨打我们的咨询热线，我们将优先为您安排专业顾问</p>
            <div className="text-3xl font-bold text-white mb-8 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
              177-0272-0924
            </div>
            <Link 
              href="/contact" 
              className="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group"
            >
              查看更多联系方式 
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 group-hover:ml-3 transition-all duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}