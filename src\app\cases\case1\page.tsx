'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiArrowLeft, FiCalendar, FiUser, FiShare2, FiMessageCircle, FiThumbsUp, FiArrowRight } from 'react-icons/fi';

export default function Case1DetailPage() {
  // 案例详情数据
  const caseDetail = {
    id: 'case1',
    title: '从普通高中到世界名校的逆袭之路',
    category: 'highschool',
    categoryName: '高中升学规划',
    summary: '来自普通高中的学生通过系统规划和努力，最终获得多所世界名校的录取，实现了教育梦想。',
    image: '/images/cases/case1-detail.svg',
    tags: ['高中规划', '留学申请', '名校录取'],
    date: '2023-05-15',
    author: '王教育顾问',
    views: 1280,
    likes: 326,
    comments: 48,
    content: [
      {
        type: 'paragraph',
        text: '小王是一名来自普通高中的学生，虽然成绩优秀，但由于学校资源有限，他在国际名校申请方面缺乏经验和指导。高二下学期，小王和家长意识到需要专业的规划和指导，于是找到了我们。'
      },
      {
        type: 'subtitle',
        text: '面临的挑战'
      },
      {
        type: 'list',
        items: [
          '学校国际教育资源有限，缺乏系统的留学申请指导',
          '对国外大学申请流程和要求了解不足',
          '标准化考试准备时间紧张',
          '缺乏有竞争力的课外活动和领导力经历',
          '英语口语和写作能力需要提升'
        ]
      },
      {
        type: 'subtitle',
        text: '我们的解决方案'
      },
      {
        type: 'paragraph',
        text: '针对小王的情况，我们制定了全面的规划方案：'
      },
      {
        type: 'list',
        items: [
          '进行详细的学术和兴趣评估，确定最适合的申请方向和目标院校',
          '制定个性化的标准化考试准备计划，包括SAT和托福考试',
          '设计并指导实施有特色的课外项目，突显个人优势和领导力',
          '提供一对一的英语口语和写作培训，提升语言表达能力',
          '指导准备高质量的申请文书和面试技巧'
        ]
      },
      {
        type: 'image',
        src: '/images/cases/case1-chart.svg',
        alt: '小王的成绩提升图表',
        caption: '小王在规划实施过程中的能力提升情况'
      },
      {
        type: 'subtitle',
        text: '实施过程'
      },
      {
        type: 'paragraph',
        text: '在为期一年半的合作中，小王按照规划方案稳步推进：'
      },
      {
        type: 'timeline',
        events: [
          { time: '第1-3个月', event: '完成评估和规划，开始标准化考试准备' },
          { time: '第4-6个月', event: '参加首次SAT和托福考试，同时开展特色课外项目' },
          { time: '第7-9个月', event: '提升考试成绩，深化课外项目成果，开始准备申请材料' },
          { time: '第10-12个月', event: '完成申请文书，提交早申请，准备面试' },
          { time: '第13-18个月', event: '提交常规申请，参加面试，获得录取结果' }
        ]
      },
      {
        type: 'subtitle',
        text: '取得的成果'
      },
      {
        type: 'paragraph',
        text: '通过系统的规划和努力，小王取得了令人瞩目的成果：'
      },
      {
        type: 'list',
        items: [
          'SAT成绩从初次1380分提升至1520分',
          '托福成绩从初次98分提升至112分',
          '成功创办校内英语演讲俱乐部，并组织了校际演讲比赛',
          '参与并领导了一项社区环保项目，获得市级表彰',
          '最终获得包括康奈尔大学、加州大学伯克利分校、密歇根大学安娜堡分校等多所世界名校的录取'
        ]
      },
      {
        type: 'quote',
        text: '思立恒教育的专业规划和指导让我看到了更广阔的可能性，他们不仅帮助我提升了学术成绩，更重要的是培养了我的领导力和国际视野。没有他们的帮助，我不可能获得这么多优秀大学的录取。',
        author: '小王，案例主人公'
      },
      {
        type: 'subtitle',
        text: '经验总结'
      },
      {
        type: 'paragraph',
        text: '小王的案例证明，即使来自普通高中，只要有正确的规划和指导，加上自身的努力，也能实现进入世界名校的梦想。关键因素包括：'
      },
      {
        type: 'list',
        items: [
          '尽早开始规划，留出充足的准备时间',
          '全面评估个人优势和不足，制定有针对性的提升计划',
          '注重学术成绩与课外活动的均衡发展',
          '发掘并深化个人特色，打造申请亮点',
          '专业的指导能够事半功倍，避免走弯路'
        ]
      },
      {
        type: 'cta',
        text: '如果您也希望为孩子的教育规划获得专业指导，欢迎联系我们的顾问团队。'
      }
    ],
    relatedCases: [
      { id: 'case5', title: '高考失利后的成功转折' },
      { id: 'case6', title: '从国内本科到海外名校硕士的申请之旅' }
    ]
  };

  return (
    <main className="min-h-screen bg-gray-50">
      {/* 案例详情头部 */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-sky-400 opacity-90"></div>
        <div className="absolute inset-0 opacity-20" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <Link 
              href="/cases" 
              className="inline-flex items-center text-white/90 hover:text-white mb-6 transition-colors group"
            >
              <FiArrowLeft className="mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
              返回案例列表
            </Link>
            <div className="flex items-center space-x-4 mb-4">
              <span className="px-3 py-1 bg-white/20 text-white rounded-full text-sm font-medium backdrop-blur-sm">
                {caseDetail.categoryName}
              </span>
              <span className="flex items-center text-white/90">
                <FiCalendar className="mr-1" /> {caseDetail.date}
              </span>
              <span className="flex items-center text-white/90">
                <FiUser className="mr-1" /> {caseDetail.author}
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">{caseDetail.title}</h1>
            <p className="text-xl text-white/90 mb-6">{caseDetail.summary}</p>
            <div className="flex flex-wrap gap-2 mb-8">
              {caseDetail.tags.map((tag, index) => (
                <span key={index} className="px-3 py-1 bg-white/20 text-white text-sm rounded-full backdrop-blur-sm">
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 案例详情内容 */}
      <section className="py-16 -mt-10">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-xl overflow-hidden">
            <div className="relative h-80 w-full">
              <Image 
                src={caseDetail.image}
                alt={caseDetail.title}
                layout="fill"
                objectFit="cover"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-8 md:p-12">
              <div className="prose prose-lg max-w-none prose-blue">
                {caseDetail.content.map((block, index) => {
                  switch (block.type) {
                    case 'paragraph':
                      return <p key={index} className="text-gray-700 mb-6">{block.text}</p>;
                    case 'subtitle':
                      return <h2 key={index} className="text-2xl font-bold text-gray-800 mt-10 mb-6">{block.text}</h2>;
                    case 'list':
                      return (
                        <ul key={index} className="list-disc pl-6 mb-6 space-y-2">
                          {block.items.map((item, i) => (
                            <li key={i} className="text-gray-700">{item}</li>
                          ))}
                        </ul>
                      );
                    case 'image':
                      return (
                        <div key={index} className="my-10">
                          <div className="relative h-80 w-full rounded-lg overflow-hidden">
                            <Image 
                              src={block.src}
                              alt={block.alt}
                              layout="fill"
                              objectFit="cover"
                              className="w-full h-full object-cover"
                            />
                          </div>
                          {block.caption && (
                            <p className="text-center text-gray-500 mt-3 text-sm">{block.caption}</p>
                          )}
                        </div>
                      );
                    case 'timeline':
                      return (
                        <div key={index} className="my-10 relative border-l-2 border-blue-500 pl-6 py-2 ml-4">
                          {block.events.map((event, i) => (
                            <div key={i} className="mb-8 relative">
                              <div className="absolute -left-10 w-4 h-4 rounded-full bg-blue-500"></div>
                              <h3 className="text-lg font-bold text-blue-600 mb-1">{event.time}</h3>
                              <p className="text-gray-700">{event.event}</p>
                            </div>
                          ))}
                        </div>
                      );
                    case 'quote':
                      return (
                        <blockquote key={index} className="border-l-4 border-blue-500 pl-6 py-2 my-8 bg-blue-50 rounded-r-lg">
                          <p className="text-gray-700 italic mb-2">"{block.text}"</p>
                          <p className="text-gray-500 text-sm">— {block.author}</p>
                        </blockquote>
                      );
                    case 'cta':
                      return (
                        <div key={index} className="bg-gradient-to-r from-blue-50 to-sky-50 p-6 rounded-lg my-10 border-l-4 border-blue-500">
                          <p className="text-gray-700 font-medium">{block.text}</p>
                          <div className="mt-4">
                            <Link 
                              href="/appointment" 
                              className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-md hover:shadow-lg"
                            >
                              预约咨询
                            </Link>
                          </div>
                        </div>
                      );
                    default:
                      return null;
                  }
                })}
              </div>

              {/* 社交分享和互动 */}
              <div className="mt-12 pt-8 border-t border-gray-100">
                <div className="flex flex-wrap justify-between items-center">
                  <div className="flex items-center space-x-6 mb-4 md:mb-0">
                    <span className="flex items-center text-gray-500">
                      <FiThumbsUp className="mr-1" /> {caseDetail.likes} 人觉得有用
                    </span>
                    <span className="flex items-center text-gray-500">
                      <FiMessageCircle className="mr-1" /> {caseDetail.comments} 条评论
                    </span>
                    <span className="flex items-center text-gray-500">
                      <FiShare2 className="mr-1" /> 分享
                    </span>
                  </div>
                  <div className="flex items-center">
                    <button className="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                      <FiThumbsUp className="mr-2" /> 点赞
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 相关案例 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold mb-8 text-gray-800">相关案例</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {caseDetail.relatedCases.map((relatedCase) => (
                <Link 
                  key={relatedCase.id} 
                  href={`/cases/${relatedCase.id}`}
                  className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100 group"
                >
                  <h3 className="text-xl font-bold mb-2 text-gray-800 group-hover:text-blue-600 transition-colors">
                    {relatedCase.title}
                  </h3>
                  <div className="flex items-center justify-between mt-4">
                    <span className="text-gray-500">查看案例</span>
                    <FiArrowRight className="text-blue-500 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 咨询预约 */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-sky-400 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">需要专业的教育规划指导？</h2>
            <p className="text-xl text-white/90 mb-8">我们的专业顾问团队随时为您提供个性化的咨询服务</p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link 
                href="/appointment" 
                className="px-8 py-4 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                立即预约
              </Link>
              <Link 
                href="/contact" 
                className="px-8 py-4 border-2 border-white text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-300"
              >
                联系我们
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}