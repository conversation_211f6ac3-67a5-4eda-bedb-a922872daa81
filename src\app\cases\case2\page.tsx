'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiArrowLeft, FiCalendar, FiUser, FiShare2, FiMessageCircle, FiThumbsUp, FiArrowRight } from 'react-icons/fi';

export default function Case2DetailPage() {
  // 案例详情数据
  const caseDetail = {
    id: 'case2',
    title: '国际学校学生的综合素质培养与名校申请',
    category: 'international',
    categoryName: '国际学校升学规划',
    summary: '国际学校学生如何通过全面发展和特色培养，在激烈的名校申请中脱颖而出，获得理想大学录取。',
    image: '/images/cases/case2-detail.svg',
    tags: ['国际学校', '综合素质', '名校申请', '特长发展'],
    date: '2023-06-22',
    author: '李教育顾问',
    views: 1560,
    likes: 412,
    comments: 56,
    content: [
      {
        type: 'paragraph',
        text: '小李是一名国际学校的高中生，学术成绩优秀，但在申请顶尖大学的过程中面临着与众多同样优秀的国际学校学生的激烈竞争。他和家长意识到，仅有良好的学术成绩已经不足以在名校申请中脱颖而出，需要更全面的规划和特色培养。'
      },
      {
        type: 'subtitle',
        text: '面临的挑战'
      },
      {
        type: 'list',
        items: [
          '国际学校学生普遍学术成绩优秀，申请竞争激烈',
          '缺乏个人特色和亮点，难以在申请中脱颖而出',
          '课外活动参与广泛但缺乏深度和领导力体现',
          '对目标大学的专业和文化契合度认识不足',
          '申请材料准备缺乏系统性和针对性'
        ]
      },
      {
        type: 'subtitle',
        text: '我们的解决方案'
      },
      {
        type: 'paragraph',
        text: '针对小李的情况，我们制定了以下综合规划方案：'
      },
      {
        type: 'list',
        items: [
          '深入评估个人兴趣和优势，确定最具发展潜力的特长方向',
          '精选并深化参与2-3个核心课外活动，培养领导力和影响力',
          '设计并指导实施具有社会影响力的个人项目，打造申请亮点',
          '针对目标大学特点，定制学术和活动发展计划',
          '系统化准备申请材料，确保每一部分都能突显个人特色和成长历程'
        ]
      },
      {
        type: 'image',
        src: '/images/cases/case2-chart.svg',
        alt: '小李的综合素质发展雷达图',
        caption: '小李在规划前后的综合素质发展对比'
      },
      {
        type: 'subtitle',
        text: '实施过程'
      },
      {
        type: 'paragraph',
        text: '在为期两年的合作中，小李按照规划方案稳步推进：'
      },
      {
        type: 'timeline',
        events: [
          { time: '第1-3个月', event: '完成全面评估，确定以环保科技创新为核心特长方向' },
          { time: '第4-6个月', event: '组建校内环保科技创新团队，开始研发可降解塑料替代品' },
          { time: '第7-12个月', event: '项目取得初步成果，在校内推广应用，同时参加区域科技创新比赛' },
          { time: '第13-18个月', event: '扩大项目影响力，与当地企业合作推广，获得市级媒体报道' },
          { time: '第19-24个月', event: '完善申请材料，参加大学面试，获得录取结果' }
        ]
      },
      {
        type: 'subtitle',
        text: '取得的成果'
      },
      {
        type: 'paragraph',
        text: '通过系统的规划和努力，小李取得了以下成果：'
      },
      {
        type: 'list',
        items: [
          '环保科技创新项目获得市级青少年科技创新大赛一等奖',
          '作为创始人领导的环保团队发展至40余人，影响校内外500余人',
          '与两家本地企业建立合作，推动环保材料在实际场景中的应用',
          '相关研究成果发表在青少年科技期刊上',
          '最终获得包括宾夕法尼亚大学、杜克大学、加州大学伯克利分校等多所顶尖大学的录取，并获得两所大学的奖学金'
        ]
      },
      {
        type: 'quote',
        text: '思立恒教育帮助我找到了自己真正感兴趣且有潜力的发展方向，让我的大学申请不再是简单地堆砌成绩和活动，而是展现一个有深度、有特色、有影响力的真实自我。这种差异化的申请策略让我在激烈的竞争中脱颖而出。',
        author: '小李，案例主人公'
      },
      {
        type: 'subtitle',
        text: '经验总结'
      },
      {
        type: 'paragraph',
        text: '小李的案例展示了国际学校学生如何在激烈的名校申请中脱颖而出的关键因素：'
      },
      {
        type: 'list',
        items: [
          '找准个人兴趣和优势，发展有深度的特长',
          '不追求活动数量，而注重活动质量和影响力',
          '将个人兴趣与社会价值相结合，展现社会责任感',
          '注重长期规划和持续发展，而非短期突击',
          '申请材料要讲好个人成长故事，展现真实且有特色的自我'
        ]
      },
      {
        type: 'cta',
        text: '如果您的孩子就读国际学校，希望在名校申请中获得竞争优势，欢迎联系我们的专业顾问团队。'
      }
    ],
    relatedCases: [
      { id: 'case3', title: 'AP/IB课程规划与考试提升策略' },
      { id: 'case4', title: '艺术特长生的留学申请之路' }
    ]
  };

  return (
    <main className="min-h-screen bg-gray-50">
      {/* 案例详情头部 */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-600 to-purple-500 opacity-90"></div>
        <div className="absolute inset-0 opacity-20" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <Link 
              href="/cases" 
              className="inline-flex items-center text-white/90 hover:text-white mb-6 transition-colors group"
            >
              <FiArrowLeft className="mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
              返回案例列表
            </Link>
            <div className="flex items-center space-x-4 mb-4">
              <span className="px-3 py-1 bg-white/20 text-white rounded-full text-sm font-medium backdrop-blur-sm">
                {caseDetail.categoryName}
              </span>
              <span className="flex items-center text-white/90">
                <FiCalendar className="mr-1" /> {caseDetail.date}
              </span>
              <span className="flex items-center text-white/90">
                <FiUser className="mr-1" /> {caseDetail.author}
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">{caseDetail.title}</h1>
            <p className="text-xl text-white/90 mb-6">{caseDetail.summary}</p>
            <div className="flex flex-wrap gap-2 mb-8">
              {caseDetail.tags.map((tag, index) => (
                <span key={index} className="px-3 py-1 bg-white/20 text-white text-sm rounded-full backdrop-blur-sm">
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 案例详情内容 */}
      <section className="py-16 -mt-10">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-xl overflow-hidden">
            <div className="relative h-80 w-full">
              <Image 
                src={caseDetail.image}
                alt={caseDetail.title}
                layout="fill"
                objectFit="cover"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-8 md:p-12">
              <div className="prose prose-lg max-w-none prose-indigo">
                {caseDetail.content.map((block, index) => {
                  switch (block.type) {
                    case 'paragraph':
                      return <p key={index} className="text-gray-700 mb-6">{block.text}</p>;
                    case 'subtitle':
                      return <h2 key={index} className="text-2xl font-bold text-gray-800 mt-10 mb-6">{block.text}</h2>;
                    case 'list':
                      return (
                        <ul key={index} className="list-disc pl-6 mb-6 space-y-2">
                          {block.items.map((item, i) => (
                            <li key={i} className="text-gray-700">{item}</li>
                          ))}
                        </ul>
                      );
                    case 'image':
                      return (
                        <div key={index} className="my-10">
                          <div className="relative h-80 w-full rounded-lg overflow-hidden">
                            <Image 
                              src={block.src}
                              alt={block.alt}
                              layout="fill"
                              objectFit="cover"
                              className="w-full h-full object-cover"
                            />
                          </div>
                          {block.caption && (
                            <p className="text-center text-gray-500 mt-3 text-sm">{block.caption}</p>
                          )}
                        </div>
                      );
                    case 'timeline':
                      return (
                        <div key={index} className="my-10 relative border-l-2 border-indigo-500 pl-6 py-2 ml-4">
                          {block.events.map((event, i) => (
                            <div key={i} className="mb-8 relative">
                              <div className="absolute -left-10 w-4 h-4 rounded-full bg-indigo-500"></div>
                              <h3 className="text-lg font-bold text-indigo-600 mb-1">{event.time}</h3>
                              <p className="text-gray-700">{event.event}</p>
                            </div>
                          ))}
                        </div>
                      );
                    case 'quote':
                      return (
                        <blockquote key={index} className="border-l-4 border-indigo-500 pl-6 py-2 my-8 bg-indigo-50 rounded-r-lg">
                          <p className="text-gray-700 italic mb-2">"{block.text}"</p>
                          <p className="text-gray-500 text-sm">— {block.author}</p>
                        </blockquote>
                      );
                    case 'cta':
                      return (
                        <div key={index} className="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-lg my-10 border-l-4 border-indigo-500">
                          <p className="text-gray-700 font-medium">{block.text}</p>
                          <div className="mt-4">
                            <Link 
                              href="/appointment" 
                              className="inline-flex items-center justify-center px-6 py-3 bg-indigo-600 text-white font-medium rounded-lg hover:bg-indigo-700 transition-all duration-300 shadow-md hover:shadow-lg"
                            >
                              预约咨询
                            </Link>
                          </div>
                        </div>
                      );
                    default:
                      return null;
                  }
                })}
              </div>

              {/* 社交分享和互动 */}
              <div className="mt-12 pt-8 border-t border-gray-100">
                <div className="flex flex-wrap justify-between items-center">
                  <div className="flex items-center space-x-6 mb-4 md:mb-0">
                    <span className="flex items-center text-gray-500">
                      <FiThumbsUp className="mr-1" /> {caseDetail.likes} 人觉得有用
                    </span>
                    <span className="flex items-center text-gray-500">
                      <FiMessageCircle className="mr-1" /> {caseDetail.comments} 条评论
                    </span>
                    <span className="flex items-center text-gray-500">
                      <FiShare2 className="mr-1" /> 分享
                    </span>
                  </div>
                  <div className="flex items-center">
                    <button className="flex items-center px-4 py-2 bg-indigo-50 text-indigo-600 rounded-lg hover:bg-indigo-100 transition-colors">
                      <FiThumbsUp className="mr-2" /> 点赞
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 相关案例 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold mb-8 text-gray-800">相关案例</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {caseDetail.relatedCases.map((relatedCase) => (
                <Link 
                  key={relatedCase.id} 
                  href={`/cases/${relatedCase.id}`}
                  className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100 group"
                >
                  <h3 className="text-xl font-bold mb-2 text-gray-800 group-hover:text-indigo-600 transition-colors">
                    {relatedCase.title}
                  </h3>
                  <div className="flex items-center justify-between mt-4">
                    <span className="text-gray-500">查看案例</span>
                    <FiArrowRight className="text-indigo-500 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 咨询预约 */}
      <section className="py-16 bg-gradient-to-r from-indigo-600 to-purple-500 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">需要专业的国际教育规划指导？</h2>
            <p className="text-xl text-white/90 mb-8">我们的专业顾问团队随时为您提供个性化的咨询服务</p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link 
                href="/appointment" 
                className="px-8 py-4 bg-white text-indigo-600 font-medium rounded-lg hover:bg-indigo-50 transition-all duration-300 shadow-lg hover:shadow-xl"
              >
                立即预约
              </Link>
              <Link 
                href="/contact" 
                className="px-8 py-4 border-2 border-white text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-300"
              >
                联系我们
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}