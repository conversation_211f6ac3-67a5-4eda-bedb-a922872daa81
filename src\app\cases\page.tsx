'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { FiArrowRight, FiCalendar, FiTag, FiUser, FiMessageCircle, FiThumbsUp } from 'react-icons/fi';

// 案例分类数据
const caseCategories = [
  { id: 'all', name: '全部案例' },
  { id: 'k12', name: 'K12教育规划' },
  { id: 'highschool', name: '高中升学规划' },
  { id: 'university', name: '大学发展规划' },
  { id: 'career', name: '职业发展规划' },
];

// 案例数据
const cases = [
  {
    id: 'case1',
    title: '从普通高中到世界名校的逆袭之路',
    category: 'highschool',
    summary: '来自普通高中的学生通过系统规划和努力，最终获得多所世界名校的录取，实现了教育梦想。',
    image: '/images/case1.jpg',
    tags: ['高中规划', '留学申请', '名校录取'],
    date: '2023-05-15',
    author: '王教育顾问',
  },
  {
    id: 'case2',
    title: '职场瓶颈突破：从技术岗到管理层的转型',
    category: 'career',
    summary: '一位工作5年的技术人员面临职业瓶颈，通过职业规划和能力提升，成功转型为管理岗位，薪资提升50%。',
    image: '/images/case2.jpg',
    tags: ['职业转型', '管理能力', '薪资提升'],
    date: '2023-04-20',
    author: '李职业顾问',
  },
  {
    id: 'case3',
    title: '小学生学习习惯养成与兴趣培养',
    category: 'k12',
    summary: '一位注意力不集中的小学生通过科学的学习习惯培养和兴趣引导，学习成绩显著提升，并发现了自己的特长。',
    image: '/images/case3.jpg',
    tags: ['学习习惯', '兴趣培养', '注意力提升'],
    date: '2023-03-10',
    author: '张教育顾问',
  },
  {
    id: 'case4',
    title: '大学生专业调整与职业规划',
    category: 'university',
    summary: '一位对所学专业失去兴趣的大二学生，通过评估和规划，成功调整专业方向并明确了职业目标。',
    image: '/images/case4.jpg',
    tags: ['专业调整', '职业规划', '目标设定'],
    date: '2023-02-25',
    author: '刘职业顾问',
  },
  {
    id: 'case5',
    title: '高考失利后的成功转折',
    category: 'highschool',
    summary: '一位高考成绩不理想的学生，通过重新规划和指导，选择了适合的教育路径，最终实现了职业理想。',
    image: '/images/case5.jpg',
    tags: ['高考规划', '教育路径', '目标重设'],
    date: '2023-01-18',
    author: '陈教育顾问',
  },
  {
    id: 'case6',
    title: '从国内本科到海外名校硕士的申请之旅',
    category: 'university',
    summary: '一位普通大学的本科生，通过系统的规划和准备，成功申请到了海外名校的硕士项目，开启了国际化职业生涯。',
    image: '/images/case6.jpg',
    tags: ['留学申请', '硕士规划', '职业发展'],
    date: '2022-12-05',
    author: '赵教育顾问',
  },
];

export default function CasesPage() {
  const [activeCategory, setActiveCategory] = useState('all');
  
  const filteredCases = activeCategory === 'all' 
    ? cases 
    : cases.filter(caseItem => caseItem.category === activeCategory);

  return (
    <main className="min-h-screen">
      {/* 页面标题区 */}
      <section className="relative py-24 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-sky-400 opacity-90"></div>
        <div className="absolute inset-0 opacity-20" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block px-4 py-1 bg-white/20 text-white rounded-full text-sm font-medium mb-6 backdrop-blur-sm">真实案例</span>
            <h1 className="text-5xl font-bold mb-6 text-white">成功案例</h1>
            <p className="text-xl text-white/90 mb-10">探索我们如何帮助客户实现教育和职业目标的真实案例</p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Link 
                href="/appointment" 
                className="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group"
              >
                预约咨询 <FiArrowRight className="ml-2 group-hover:ml-3 transition-all duration-300" />
              </Link>
              <Link 
                href="/contact" 
                className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-300 backdrop-blur-sm group"
              >
                联系我们 <FiArrowRight className="ml-2 opacity-0 group-hover:opacity-100 group-hover:ml-3 transition-all duration-300" />
              </Link>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-white to-transparent"></div>
      </section>

      {/* 案例分类与列表 */}
      <section className="py-20 bg-gradient-to-b from-white via-sky-50 to-white">
        <div className="container mx-auto px-4">
          {/* 分类导航 */}
          <div className="flex flex-wrap justify-center mb-12">
            {caseCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-6 py-3 m-2 rounded-full transition-all duration-300 font-medium ${activeCategory === category.id 
                  ? 'bg-gradient-to-r from-blue-600 to-sky-500 text-white shadow-md shadow-blue-200' 
                  : 'bg-white text-gray-700 hover:bg-gray-50 hover:shadow-md border border-gray-100'}`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* 案例列表 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredCases.map((caseItem) => (
              <div 
                key={caseItem.id} 
                className="bg-white rounded-xl shadow-lg shadow-blue-100/50 overflow-hidden transition-all duration-500 hover:shadow-xl hover:-translate-y-2 border border-gray-100 group"
              >
                <div className="relative h-56 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-blue-900/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>
                  <Image 
                    src={caseItem.image} 
                    alt={caseItem.title}
                    width={400}
                    height={300}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute bottom-0 left-0 right-0 p-4 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300 z-20">
                    <div className="flex gap-2 flex-wrap">
                      {caseItem.tags.map((tag, index) => (
                        <span key={index} className="px-3 py-1 bg-white/80 backdrop-blur-sm text-blue-700 text-xs rounded-full font-medium">{tag}</span>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <div className="flex items-center text-sm text-gray-500 mb-3">
                    <span className="flex items-center mr-4 text-blue-500">
                      <FiCalendar className="mr-1" /> {caseItem.date}
                    </span>
                    <span className="flex items-center text-blue-500">
                      <FiTag className="mr-1" /> {caseCategories.find(c => c.id === caseItem.category)?.name}
                    </span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-gray-800 group-hover:text-blue-600 transition-colors">{caseItem.title}</h3>
                  <p className="text-gray-600 mb-5">{caseItem.summary}</p>
                  <div className="flex justify-between items-center">
                    <span className="flex items-center text-sm text-gray-500">
                      <FiUser className="mr-1 text-blue-500" /> {caseItem.author}
                    </span>
                    <Link 
                      href={`/cases/${caseItem.id}`} 
                      className="inline-flex items-center text-blue-600 font-medium hover:text-blue-800 group/link"
                    >
                      查看详情 <FiArrowRight className="ml-1 group-hover/link:ml-2 transition-all duration-300" />
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 客户评价 */}
      <section className="py-20 bg-gradient-to-b from-white to-sky-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1 bg-blue-50 text-blue-600 rounded-full text-sm font-medium mb-4">客户反馈</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800 relative inline-block">
              客户评价
              <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full"></span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">听听我们的客户如何评价我们的服务</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-xl shadow-blue-100/50 border border-gray-100 transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl relative">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 bg-gradient-to-r from-blue-600 to-sky-400 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
                <FiMessageCircle size={20} />
              </div>
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full flex items-center justify-center text-white mr-4 shadow-md">
                  <FiUser size={24} />
                </div>
                <div>
                  <h4 className="font-bold text-gray-800 text-lg">张先生</h4>
                  <p className="text-blue-500">高中生家长</p>
                </div>
              </div>
              <p className="text-gray-600 mb-6 italic">{`"感谢顾问的专业指导，我的孩子成功考入了理想的大学。整个过程中，顾问不仅提供了学业规划，还关注孩子的心理健康。"`}</p>
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <FiThumbsUp key={i} className="mr-1 transform transition-transform hover:scale-125" />
                ))}
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-xl shadow-blue-100/50 border border-gray-100 transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl relative">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 bg-gradient-to-r from-blue-600 to-sky-400 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
                <FiMessageCircle size={20} />
              </div>
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full flex items-center justify-center text-white mr-4 shadow-md">
                  <FiUser size={24} />
                </div>
                <div>
                  <h4 className="font-bold text-gray-800 text-lg">李女士</h4>
                  <p className="text-blue-500">职场人士</p>
                </div>
              </div>
              <p className="text-gray-600 mb-6 italic">{`"职业转型是一个艰难的决定，但在顾问的帮助下，我找到了适合自己的新方向，并成功进入了理想的行业。非常感谢！"`}</p>
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <FiThumbsUp key={i} className="mr-1 transform transition-transform hover:scale-125" />
                ))}
              </div>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-xl shadow-blue-100/50 border border-gray-100 transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl relative">
              <div className="absolute top-0 right-0 -mt-4 -mr-4 bg-gradient-to-r from-blue-600 to-sky-400 text-white rounded-full w-12 h-12 flex items-center justify-center shadow-lg">
                <FiMessageCircle size={20} />
              </div>
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full flex items-center justify-center text-white mr-4 shadow-md">
                  <FiUser size={24} />
                </div>
                <div>
                  <h4 className="font-bold text-gray-800 text-lg">王同学</h4>
                  <p className="text-blue-500">大学生</p>
                </div>
              </div>
              <p className="text-gray-600 mb-6 italic">{`"大学期间的专业调整和留学申请得到了顾问的全程指导，最终我获得了多所海外名校的录取，非常感谢！"`}</p>
              <div className="flex text-yellow-400">
                {[...Array(5)].map((_, i) => (
                  <FiThumbsUp key={i} className="mr-1 transform transition-transform hover:scale-125" />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 咨询指引 */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-sky-500 opacity-90"></div>
        <div className="absolute inset-0 opacity-10" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <span className="inline-block px-4 py-1 bg-white/20 text-white rounded-full text-sm font-medium mb-6 backdrop-blur-sm">专业指导</span>
          <h2 className="text-4xl font-bold mb-6 text-white">需要专业的教育与职业规划指导？</h2>
          <p className="text-xl mb-10 max-w-3xl mx-auto text-white/90">我们的顾问团队随时准备为您提供个性化的解决方案</p>
          <div className="flex flex-col sm:flex-row justify-center gap-6">
            <Link 
              href="/appointment" 
              className="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group"
            >
              立即预约 <FiArrowRight className="ml-2 group-hover:ml-3 transition-all duration-300" />
            </Link>
            <Link 
              href="/services" 
              className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-300 backdrop-blur-sm group"
            >
              了解服务 <FiArrowRight className="ml-2 opacity-0 group-hover:opacity-100 group-hover:ml-3 transition-all duration-300" />
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}