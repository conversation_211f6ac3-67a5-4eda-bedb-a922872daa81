'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import { getConsultant, getConsultantAvailability, submitAppointment } from '@/lib/api-client';
import { FaGraduationCap, FaClock, FaLanguage, FaUserTie, FaCalendarAlt, FaCheck } from 'react-icons/fa';

interface Consultant {
  id: number;
  name: string;
  specialty: string;
  experience_years: number;
  education: string;
  certifications: string;
  bio: string;
  avatar_url: string;
  hourly_rate: number;
  languages: string;

}

interface TimeSlot {
  start: string;
  end: string;
}

interface DayAvailability {
  date: string;
  dayOfWeek: string;
  availableSlots: TimeSlot[];
}

// 解析JSON字符串为数组
const parseJsonArray = (jsonString: string | undefined): string[] => {
  if (!jsonString) return [];
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('解析JSON字符串失败:', error);
    return [];
  }
};

export default function BookConsultantPage() {
  const params = useParams();
  const router = useRouter();
  const consultantId = params.id as string;

  const [consultant, setConsultant] = useState<Consultant | null>(null);
  const [weekAvailability, setWeekAvailability] = useState<DayAvailability[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState('');
  const [selectedTime, setSelectedTime] = useState('');
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const [formData, setFormData] = useState({
    client_name: '',
    client_email: '',
    client_phone: '',
    service_type: '',
    message: '',
    duration: 60
  });

  useEffect(() => {
    if (consultantId) {
      fetchConsultant();
      fetchAvailability();
    }
  }, [consultantId, currentWeek]);

  const fetchConsultant = async () => {
    try {
      const response = await getConsultant(consultantId);
      if (response.success) {
        setConsultant(response.data);
      }
    } catch (error) {
      console.error('获取咨询师信息失败:', error);
    }
  };

  const fetchAvailability = async () => {
    try {
      setLoading(true);
      const weekStart = getWeekStart(currentWeek);
      const response = await getConsultantAvailability(consultantId, {
        week: weekStart.toISOString().split('T')[0]
      });

      if (response.success) {
        setWeekAvailability(response.data.weekAvailability || []);
      }
    } catch (error) {
      console.error('获取可用时间失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getWeekStart = (date: Date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
    return new Date(d.setDate(diff));
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return {
      month: date.getMonth() + 1,
      day: date.getDate(),
      weekday: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][date.getDay()]
    };
  };

  const handleTimeSelect = (date: string, time: string) => {
    setSelectedDate(date);
    setSelectedTime(time);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedDate || !selectedTime) {
      alert('请选择预约时间');
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const appointmentData = {
        ...formData,
        consultant_id: parseInt(consultantId),
        appointment_date: selectedDate,
        appointment_time: selectedTime
      };

      const response = await submitAppointment(appointmentData);

      if (response.success) {
        setSubmitStatus('success');
        // 3秒后跳转到成功页面
        setTimeout(() => {
          router.push('/appointment-success');
        }, 3000);
      } else {
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('预约提交失败:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextWeek = () => {
    const next = new Date(currentWeek);
    next.setDate(next.getDate() + 7);
    setCurrentWeek(next);
  };

  const prevWeek = () => {
    const prev = new Date(currentWeek);
    prev.setDate(prev.getDate() - 7);
    setCurrentWeek(prev);
  };

  if (!consultant) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* 返回按钮 */}
          <button
            onClick={() => router.back()}
            className="mb-6 text-sky-600 hover:text-sky-700 flex items-center"
          >
            ← 返回咨询师列表
          </button>

          {/* 咨询师信息卡片 */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
              <div className="relative w-24 h-24 rounded-full overflow-hidden bg-gradient-to-br from-sky-400 to-blue-500 flex-shrink-0">
                {consultant.avatar_url ? (
                  <Image
                    src={consultant.avatar_url}
                    alt={consultant.name}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <FaUserTie className="text-3xl text-white" />
                  </div>
                )}
              </div>

              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-2xl font-bold text-gray-900">{consultant.name}</h1>
                  <div className="flex items-center bg-sky-50 px-2 py-1 rounded">
                    <FaClock className="text-sky-500 mr-1" />
                    <span className="text-sm font-medium">{consultant.experience_years}年经验</span>
                  </div>
                </div>

                <p className="text-sky-600 font-medium mb-3">{consultant.specialty}</p>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                  <div className="flex items-center">
                    <FaGraduationCap className="mr-2 text-sky-500" />
                    <span>{consultant.education}</span>
                  </div>
                  <div className="flex items-center">
                    <FaClock className="mr-2 text-sky-500" />
                    <span>{consultant.experience_years}年经验</span>
                  </div>
                  <div className="flex items-center">
                    <FaLanguage className="mr-2 text-sky-500" />
                    <span>{parseJsonArray(consultant.languages).join(', ') || '中文'}</span>
                  </div>
                </div>
              </div>

              <div className="text-right">
                <div className="text-2xl font-bold text-sky-600">¥{consultant.hourly_rate}</div>
                <div className="text-sm text-gray-500">每小时</div>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-gray-200">
              <p className="text-gray-600">{consultant.bio}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 时间选择 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                <FaCalendarAlt className="mr-2 text-sky-500" />
                选择预约时间
              </h2>

              {/* 周导航 */}
              <div className="flex items-center justify-between mb-6">
                <button
                  onClick={prevWeek}
                  className="px-3 py-1 text-sky-600 hover:text-sky-700"
                >
                  ← 上一周
                </button>
                <span className="font-medium">
                  {getWeekStart(currentWeek).toLocaleDateString()} - {
                    new Date(getWeekStart(currentWeek).getTime() + 6 * 24 * 60 * 60 * 1000).toLocaleDateString()
                  }
                </span>
                <button
                  onClick={nextWeek}
                  className="px-3 py-1 text-sky-600 hover:text-sky-700"
                >
                  下一周 →
                </button>
              </div>

              {/* 可用时间网格 */}
              {loading ? (
                <div className="text-center py-8">加载中...</div>
              ) : (
                <div className="space-y-4">
                  {weekAvailability.map((day) => {
                    const { month, day: dayNum, weekday } = formatDate(day.date);

                    return (
                      <div key={day.date} className="border border-gray-200 rounded-lg p-4">
                        <div className="font-medium text-gray-900 mb-3">
                          {month}月{dayNum}日 {weekday}
                        </div>

                        {day.availableSlots.length === 0 ? (
                          <div className="text-gray-500 text-sm">暂无可用时间</div>
                        ) : (
                          <div className="grid grid-cols-3 gap-2">
                            {day.availableSlots.map((slot, index) => (
                              <button
                                key={index}
                                onClick={() => handleTimeSelect(day.date, slot.start)}
                                className={`px-3 py-2 text-sm rounded border transition-colors ${
                                  selectedDate === day.date && selectedTime === slot.start
                                    ? 'bg-sky-600 text-white border-sky-600'
                                    : 'bg-white text-gray-700 border-gray-300 hover:border-sky-500 hover:text-sky-600'
                                }`}
                              >
                                {slot.start}
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* 预约表单 */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">预约信息</h2>

              {submitStatus === 'success' && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center">
                  <FaCheck className="text-green-600 mr-2" />
                  <span className="text-green-800">预约申请已成功提交！我们会尽快与您联系确认。</span>
                </div>
              )}

              {submitStatus === 'error' && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <span className="text-red-800">预约提交失败，请稍后重试或联系客服。</span>
                </div>
              )}

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      姓名 *
                    </label>
                    <input
                      type="text"
                      name="client_name"
                      value={formData.client_name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                      placeholder="请输入您的姓名"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      邮箱 *
                    </label>
                    <input
                      type="email"
                      name="client_email"
                      value={formData.client_email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                      placeholder="请输入您的邮箱"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      电话 *
                    </label>
                    <input
                      type="tel"
                      name="client_phone"
                      value={formData.client_phone}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                      placeholder="请输入您的电话号码"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      咨询类型 *
                    </label>
                    <select
                      name="service_type"
                      value={formData.service_type}
                      onChange={handleInputChange}
                      required
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                    >
                      <option value="">请选择咨询类型</option>
                      <option value="education_planning">教育规划</option>
                      <option value="career_planning">职业规划</option>
                      <option value="study_abroad">留学咨询</option>
                      <option value="psychological_counseling">心理咨询</option>
                      <option value="learning_improvement">学习能力提升</option>
                      <option value="other">其他</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    咨询时长
                  </label>
                  <select
                    name="duration"
                    value={formData.duration}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  >
                    <option value={60}>1小时 (¥{consultant.hourly_rate})</option>
                    <option value={90}>1.5小时 (¥{Math.round(consultant.hourly_rate * 1.5)})</option>
                    <option value={120}>2小时 (¥{consultant.hourly_rate * 2})</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    详细需求
                  </label>
                  <textarea
                    name="message"
                    value={formData.message}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                    placeholder="请详细描述您的咨询需求，以便咨询师更好地为您准备..."
                  />
                </div>

                {/* 选中的时间显示 */}
                {selectedDate && selectedTime && (
                  <div className="bg-sky-50 border border-sky-200 rounded-lg p-4">
                    <h4 className="font-medium text-sky-800 mb-2">已选择时间:</h4>
                    <p className="text-sky-700">
                      {formatDate(selectedDate).month}月{formatDate(selectedDate).day}日 {formatDate(selectedDate).weekday} {selectedTime}
                    </p>
                    <p className="text-sm text-sky-600 mt-1">
                      咨询时长: {formData.duration}分钟 | 费用: ¥{Math.round(consultant.hourly_rate * (formData.duration / 60))}
                    </p>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isSubmitting || !selectedDate || !selectedTime}
                  className="w-full bg-sky-600 hover:bg-sky-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? '提交中...' : '确认预约'}
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
