'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getConsultants } from '@/lib/api-client';
import { FaStar, FaGraduationCap, FaClock, FaLanguage, FaUserTie } from 'react-icons/fa';

interface Consultant {
  id: number;
  name: string;
  email: string;
  phone: string;
  specialty: string;
  experience_years: number;
  education: string;
  certifications: string[];
  bio: string;
  avatar_url: string;
  hourly_rate: number;
  languages: string[];
  rating: number;
  total_appointments: number;
  status: string;
}

export default function ConsultantsPage() {
  const [consultants, setConsultants] = useState<Consultant[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSpecialty, setSelectedSpecialty] = useState('');

  useEffect(() => {
    fetchConsultants();
  }, [selectedSpecialty]);

  const fetchConsultants = async () => {
    try {
      setLoading(true);
      const params: any = {};
      if (selectedSpecialty) {
        params.specialty = selectedSpecialty;
      }
      
      const response = await getConsultants(params);
      if (response.success) {
        setConsultants(response.data.items || response.data);
      }
    } catch (error) {
      console.error('获取咨询师列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const specialties = [
    { value: '', label: '全部专业' },
    { value: '教育规划', label: '教育规划' },
    { value: '升学指导', label: '升学指导' },
    { value: '职业规划', label: '职业规划' },
    { value: '留学咨询', label: '留学咨询' },
    { value: '心理咨询', label: '心理咨询' },
    { value: '学习能力提升', label: '学习能力提升' }
  ];

  return (
    <main className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-sky-800 mb-4">专业咨询师团队</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            我们的专业咨询师团队拥有丰富的教育和职业规划经验，为您提供个性化的专业指导
          </p>
        </div>

        {/* 筛选器 */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-4 justify-center">
            <div className="flex items-center">
              <label className="text-sm font-medium text-gray-700 mr-2">专业领域:</label>
              <select
                value={selectedSpecialty}
                onChange={(e) => setSelectedSpecialty(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
              >
                {specialties.map((specialty) => (
                  <option key={specialty.value} value={specialty.value}>
                    {specialty.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 咨询师列表 */}
        {loading ? (
          <div className="text-center py-12">
            <div className="text-lg text-gray-600">加载中...</div>
          </div>
        ) : consultants.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-lg text-gray-600">暂无咨询师信息</div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {consultants.map((consultant) => (
              <div key={consultant.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                {/* 咨询师头像 */}
                <div className="relative h-64 bg-gradient-to-br from-sky-400 to-blue-500">
                  {consultant.avatar_url ? (
                    <Image
                      src={consultant.avatar_url}
                      alt={consultant.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <FaUserTie className="text-6xl text-white opacity-80" />
                    </div>
                  )}
                  
                  {/* 评分标签 */}
                  <div className="absolute top-4 right-4 bg-white bg-opacity-90 rounded-full px-3 py-1 flex items-center">
                    <FaStar className="text-yellow-400 mr-1" />
                    <span className="text-sm font-medium">{consultant.rating}</span>
                  </div>
                </div>

                {/* 咨询师信息 */}
                <div className="p-6">
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{consultant.name}</h3>
                    <p className="text-sky-600 font-medium">{consultant.specialty}</p>
                  </div>

                  {/* 基本信息 */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <FaGraduationCap className="mr-2 text-sky-500" />
                      <span>{consultant.education}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <FaClock className="mr-2 text-sky-500" />
                      <span>{consultant.experience_years}年经验</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <FaLanguage className="mr-2 text-sky-500" />
                      <span>{consultant.languages?.join(', ') || '中文'}</span>
                    </div>
                  </div>

                  {/* 简介 */}
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {consultant.bio}
                  </p>

                  {/* 资质认证 */}
                  {consultant.certifications && consultant.certifications.length > 0 && (
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">专业资质:</h4>
                      <div className="flex flex-wrap gap-1">
                        {consultant.certifications.slice(0, 2).map((cert, index) => (
                          <span
                            key={index}
                            className="inline-block bg-sky-100 text-sky-800 text-xs px-2 py-1 rounded"
                          >
                            {cert}
                          </span>
                        ))}
                        {consultant.certifications.length > 2 && (
                          <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                            +{consultant.certifications.length - 2}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 价格和预约按钮 */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                    <div className="text-lg font-bold text-sky-600">
                      ¥{consultant.hourly_rate}/小时
                    </div>
                    <Link
                      href={`/consultants/${consultant.id}/book`}
                      className="bg-sky-600 hover:bg-sky-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-300"
                    >
                      立即预约
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 预约流程说明 */}
        <div className="mt-16 bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-center text-sky-800 mb-8">预约流程</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-sky-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-sky-600">1</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">选择咨询师</h3>
              <p className="text-sm text-gray-600">根据专业领域和经验选择合适的咨询师</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-sky-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-sky-600">2</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">选择时间</h3>
              <p className="text-sm text-gray-600">查看咨询师可用时间并选择合适的时段</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-sky-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-sky-600">3</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">填写信息</h3>
              <p className="text-sm text-gray-600">提供联系方式和咨询需求详情</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-sky-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-sky-600">4</span>
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">确认预约</h3>
              <p className="text-sm text-gray-600">我们会联系您确认预约并安排咨询</p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
