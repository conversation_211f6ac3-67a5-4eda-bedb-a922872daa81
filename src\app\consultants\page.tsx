'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getConsultants } from '@/lib/api-client';
import { FaStar, FaGraduationCap, FaClock, FaLanguage, FaUserTie, FaSearch, FaFilter, FaCalendarCheck } from 'react-icons/fa';

interface Consultant {
  id: number;
  name: string;
  email: string;
  phone: string;
  specialty: string;
  experience_years: number;
  education: string;
  certifications: string;
  bio: string;
  avatar_url: string;
  hourly_rate: number;
  languages: string;
  rating: number;
  total_appointments: number;
  status: string;
}

export default function ConsultantsPage() {
  const [consultants, setConsultants] = useState<Consultant[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSpecialty, setSelectedSpecialty] = useState('');

  useEffect(() => {
    fetchConsultants();
  }, [selectedSpecialty]);

  const fetchConsultants = async () => {
    try {
      setLoading(true);
      const params: Record<string, string> = {};
      if (selectedSpecialty) {
        params.specialty = selectedSpecialty;
      }

      const response = await getConsultants(params);
      if (response.success) {
        setConsultants(response.data.items || response.data);
      }
    } catch (error) {
      console.error('获取咨询师列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const specialties = [
    { value: '', label: '全部专业' },
    { value: '教育规划', label: '教育规划' },
    { value: '升学指导', label: '升学指导' },
    { value: '职业规划', label: '职业规划' },
    { value: '留学咨询', label: '留学咨询' },
    { value: '心理咨询', label: '心理咨询' },
    { value: '学习能力提升', label: '学习能力提升' },
    { value: 'STEM教育', label: 'STEM教育' },
    { value: '创业指导', label: '创业指导' },
    { value: '家庭教育', label: '家庭教育' },
    { value: '特殊教育', label: '特殊教育' },
    { value: '体育特长', label: '体育特长' },
    { value: '艺术教育', label: '艺术教育' },
    { value: '医学升学', label: '医学升学' },
    { value: '法律教育', label: '法律教育' },
    { value: '文学创作', label: '文学创作' },
    { value: '技术教育', label: '技术教育' }
  ];

  // 解析JSON字符串为数组
  const parseJsonArray = (jsonString: string | undefined): string[] => {
    if (!jsonString) return [];
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('解析JSON字符串失败:', error);
      return [];
    }
  };

  return (
    <main className="min-h-screen bg-gradient-to-b from-sky-50 to-white">
      {/* 顶部Banner区域 */}
      <section className="relative h-[300px] overflow-hidden bg-gradient-to-r from-sky-600 via-blue-600 to-sky-700">
        {/* 渐变遮罩层 */}
        <div className="absolute inset-0 bg-gradient-to-r from-sky-700/90 via-sky-600/70 to-blue-500/50 z-10"></div>

        {/* 装饰元素 */}
        <div className="absolute inset-0 opacity-10 z-0">
          <div className="absolute top-10 left-10 w-40 h-40 rounded-full bg-white"></div>
          <div className="absolute bottom-10 right-10 w-60 h-60 rounded-full bg-white"></div>
          <div className="absolute top-40 right-40 w-20 h-20 rounded-full bg-white"></div>
        </div>

        {/* 内容区域 */}
        <div className="container mx-auto px-4 h-full flex flex-col justify-center items-center relative z-20 text-white text-center">
          <h1 className="text-4xl sm:text-5xl font-extrabold mb-6 leading-tight tracking-tight">
            专业咨询师团队
          </h1>
          <p className="text-lg md:text-xl max-w-2xl mx-auto text-sky-100">
            我们的专业咨询师团队拥有丰富的教育和职业规划经验，为您提供个性化的专业指导
          </p>
        </div>
      </section>

      <div className="container mx-auto px-4 py-12">
        {/* 筛选器 */}
        <div className="mb-12 bg-white rounded-xl shadow-lg p-6 transform -mt-16 relative z-20">
          <div className="flex flex-wrap gap-4 justify-center items-center">
            <div className="flex items-center">
              <FaFilter className="mr-3 text-sky-600" />
              <label className="text-sm font-medium text-gray-700 mr-3">按专业领域筛选:</label>
              <select
                value={selectedSpecialty}
                onChange={(e) => setSelectedSpecialty(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 bg-white shadow-sm"
              >
                {specialties.map((specialty) => (
                  <option key={specialty.value} value={specialty.value}>
                    {specialty.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 咨询师列表 */}
        {loading ? (
          <div className="text-center py-12">
            <div className="inline-block h-12 w-12 animate-spin rounded-full border-4 border-solid border-sky-600 border-r-transparent align-[-0.125em]"></div>
            <div className="mt-4 text-lg text-gray-600">正在加载咨询师信息...</div>
          </div>
        ) : consultants.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-xl shadow-lg">
            <FaSearch className="mx-auto text-5xl text-sky-300 mb-4" />
            <div className="text-lg text-gray-600 mb-2">暂无符合条件的咨询师</div>
            <div className="text-sm text-gray-500">请尝试更改筛选条件</div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {consultants.map((consultant) => {
              // 解析认证和语言
              const certifications = parseJsonArray(consultant.certifications);
              const languages = parseJsonArray(consultant.languages);

              return (
                <div key={consultant.id} className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                  {/* 咨询师头像 */}
                  <div className="relative h-64 bg-gradient-to-br from-sky-400 to-blue-600 overflow-hidden">
                    {consultant.avatar_url ? (
                      <Image
                        src={consultant.avatar_url}
                        alt={consultant.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <FaUserTie className="text-6xl text-white opacity-80 group-hover:scale-110 transition-transform duration-300" />
                      </div>
                    )}

                    {/* 评分标签 */}
                    <div className="absolute top-4 right-4 bg-white bg-opacity-90 rounded-full px-3 py-1 flex items-center shadow-md">
                      <FaStar className="text-yellow-400 mr-1" />
                      <span className="text-sm font-medium">{consultant.rating}</span>
                    </div>
                  </div>

                  {/* 咨询师信息 */}
                  <div className="p-6">
                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-sky-800 mb-2 group-hover:text-sky-600 transition-colors duration-300">{consultant.name}</h3>
                      <p className="text-sky-600 font-medium inline-block px-3 py-1 bg-sky-50 rounded-full">{consultant.specialty}</p>
                    </div>

                    {/* 基本信息 */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <FaGraduationCap className="mr-2 text-sky-500" />
                        <span>{consultant.education}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <FaClock className="mr-2 text-sky-500" />
                        <span>{consultant.experience_years}年经验</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <FaLanguage className="mr-2 text-sky-500" />
                        <span>{languages.join(', ') || '中文'}</span>
                      </div>
                    </div>

                    {/* 简介 */}
                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {consultant.bio}
                    </p>

                    {/* 资质认证 */}
                    {certifications && certifications.length > 0 && (
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">专业资质:</h4>
                        <div className="flex flex-wrap gap-1">
                          {certifications.slice(0, 2).map((cert, index) => (
                            <span
                              key={index}
                              className="inline-block bg-sky-100 text-sky-800 text-xs px-2 py-1 rounded"
                            >
                              {cert}
                            </span>
                          ))}
                          {certifications.length > 2 && (
                            <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                              +{certifications.length - 2}
                            </span>
                          )}
                        </div>
                      </div>
                    )}

                    {/* 价格和预约按钮 */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                      <div className="text-lg font-bold text-sky-600">
                        ¥{consultant.hourly_rate}/小时
                      </div>
                      <Link
                        href={`/consultants/${consultant.id}/book`}
                        className="bg-sky-600 hover:bg-sky-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-300 flex items-center"
                      >
                        <FaCalendarCheck className="mr-2" /> 立即预约
                      </Link>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* 预约流程说明 */}
        <div className="mt-16 bg-white rounded-xl shadow-lg p-8 bg-gradient-to-br from-white to-sky-50">
          <h2 className="text-2xl font-bold text-center text-sky-800 mb-8">预约流程</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-sky-400 to-sky-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110">
                <span className="text-2xl font-bold text-white">1</span>
              </div>
              <h3 className="font-semibold text-sky-700 mb-2 group-hover:text-sky-600 transition-colors duration-300">选择咨询师</h3>
              <p className="text-sm text-gray-600">根据专业领域和经验选择合适的咨询师</p>
            </div>
            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-sky-400 to-sky-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110">
                <span className="text-2xl font-bold text-white">2</span>
              </div>
              <h3 className="font-semibold text-sky-700 mb-2 group-hover:text-sky-600 transition-colors duration-300">选择时间</h3>
              <p className="text-sm text-gray-600">查看咨询师可用时间并选择合适的时段</p>
            </div>
            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-sky-400 to-sky-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110">
                <span className="text-2xl font-bold text-white">3</span>
              </div>
              <h3 className="font-semibold text-sky-700 mb-2 group-hover:text-sky-600 transition-colors duration-300">填写信息</h3>
              <p className="text-sm text-gray-600">提供联系方式和咨询需求详情</p>
            </div>
            <div className="text-center group">
              <div className="w-20 h-20 bg-gradient-to-br from-sky-400 to-sky-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110">
                <span className="text-2xl font-bold text-white">4</span>
              </div>
              <h3 className="font-semibold text-sky-700 mb-2 group-hover:text-sky-600 transition-colors duration-300">确认预约</h3>
              <p className="text-sm text-gray-600">我们会联系您确认预约并安排咨询</p>
            </div>
          </div>
        </div>

        {/* 咨询师团队优势 */}
        <div className="mt-16">
          <h2 className="text-2xl font-bold text-center text-sky-800 mb-8">我们的团队优势</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-16 h-16 bg-sky-100 rounded-full flex items-center justify-center mb-4">
                <FaGraduationCap className="text-2xl text-sky-600" />
              </div>
              <h3 className="text-lg font-bold text-sky-800 mb-3">专业背景</h3>
              <p className="text-gray-600">
                我们的咨询师团队拥有国内外知名高校的教育背景，以及各类专业资质认证，确保为您提供最专业的指导。
              </p>
            </div>
            <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-16 h-16 bg-sky-100 rounded-full flex items-center justify-center mb-4">
                <FaClock className="text-2xl text-sky-600" />
              </div>
              <h3 className="text-lg font-bold text-sky-800 mb-3">丰富经验</h3>
              <p className="text-gray-600">
                团队成员平均拥有10年以上的教育咨询和职业规划经验，已成功帮助数千名学生和职场人士实现自己的目标。
              </p>
            </div>
            <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="w-16 h-16 bg-sky-100 rounded-full flex items-center justify-center mb-4">
                <FaLanguage className="text-2xl text-sky-600" />
              </div>
              <h3 className="text-lg font-bold text-sky-800 mb-3">国际视野</h3>
              <p className="text-gray-600">
                多位咨询师拥有海外学习和工作经历，熟悉国际教育体系和职场环境，能为有国际发展需求的客户提供全球化视角。
              </p>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
