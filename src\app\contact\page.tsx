'use client';

import { useState } from 'react';
import { submitInquiry } from '@/lib/api-client';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });

  const [formStatus, setFormStatus] = useState({
    submitted: false,
    success: false,
    message: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const result = await submitInquiry(formData);

      setFormStatus({
        submitted: true,
        success: result.success,
        message: result.success ? '感谢您的咨询！我们会尽快与您联系。' : result.message || '提交失败，请稍后再试。'
      });

      if (result.success) {
        // 重置表单
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: '',
        });
      }
    } catch (error) {
      console.error('提交表单时出错:', error);
      setFormStatus({
        submitted: true,
        success: false,
        message: '提交失败，请稍后再试或直接联系我们。'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // 办公地点数据
  const officeLocations = [
    {
      name: '武汉总部',
      address: '湖北省武汉市武昌区中南国际汇18层',
      phone: '177-0272-0924',
      email: '<EMAIL>',
      hours: '周一至周五 9:00-18:00',
    },
  ];

  return (
    <main className="min-h-screen">
      {/* 页面标题区 */}
      <section className="relative py-28 text-white flex items-center justify-center overflow-hidden" style={{minHeight:'380px'}}>
        <div className="absolute inset-0 z-0 bg-gradient-to-r from-blue-700 via-indigo-600 to-purple-700">
          <div className="absolute inset-0 opacity-20" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
          <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-white opacity-10"></div>
        </div>
        <div className="absolute inset-0 z-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-96 h-96 bg-blue-500 rounded-full opacity-20 blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-purple-500 rounded-full opacity-20 blur-3xl"></div>
        </div>
        <div className="relative z-10 container mx-auto px-4 text-center">
          <span className="inline-block px-4 py-1 bg-white/10 backdrop-blur-sm text-white rounded-full text-sm font-medium mb-6 animate-fadeIn">随时为您服务</span>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 animate-fadeInUp">
            联系我们
            <span className="block h-1.5 w-24 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full mx-auto mt-4"></span>
          </h1>
          <p className="text-xl md:text-2xl max-w-3xl mx-auto text-white/90 animate-fadeInUp animation-delay-200">无论您有任何问题或需求，我们都随时准备为您提供帮助和支持</p>
          <div className="mt-10 flex flex-wrap justify-center gap-4 animate-fadeInUp animation-delay-300">
            <a href="#contact-form" className="px-8 py-3 bg-white text-blue-700 font-medium rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
              发送信息
            </a>
            <a href="tel:17702720924" className="px-8 py-3 bg-transparent border-2 border-white/30 backdrop-blur-sm text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-300">
              立即咨询
            </a>
          </div>
        </div>
      </section>

      {/* 联系方式与表单 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* 左侧联系信息 */}
            <div>
              <h2 className="text-3xl font-bold mb-8 text-gray-800">联系方式</h2>

              <div className="space-y-6 mb-10">
                <div className="flex items-start">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">电话咨询</h3>
                    <p className="text-gray-600 mb-1">总机: 177-0272-0924</p>
                    <p className="text-gray-600">咨询热线: 177-0272-0924</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">电子邮件</h3>
                    <p className="text-gray-600 mb-1">咨询: <EMAIL></p>
                    <p className="text-gray-600">商务合作: <EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4 flex-shrink-0">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">总部地址</h3>
                    <p className="text-gray-600">湖北省武汉市武昌区中南国际汇18层</p>
                  </div>
                </div>
              </div>

              <div className="bg-gray-100 p-6 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">工作时间</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">周一至周五:</span>
                    <span className="text-gray-800 font-medium">9:00 - 18:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">周六:</span>
                    <span className="text-gray-800 font-medium">10:00 - 16:00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">周日:</span>
                    <span className="text-gray-800 font-medium">休息</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 右侧联系表单 */}
            <div>
              <h2 className="text-3xl font-bold mb-8 text-gray-800">发送信息</h2>

              {formStatus.submitted ? (
                <div className={`p-6 rounded-lg ${formStatus.success ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'}`}>
                  <p className="text-lg font-medium">{formStatus.message}</p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-gray-700 font-medium mb-2">姓名</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-gray-700 font-medium mb-2">电子邮箱</label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">电话</label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div>
                      <label htmlFor="subject" className="block text-gray-700 font-medium mb-2">咨询主题</label>
                      <select
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleChange}
                        required
                        className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">请选择咨询主题</option>
                        <option value="k12">学前及K12教育规划</option>
                        <option value="high-school">高中一体化升学规划</option>
                        <option value="university">大学发展与深造规划</option>
                        <option value="career">职业人士生涯进阶服务</option>
                        <option value="other">其他咨询</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-gray-700 font-medium mb-2">咨询内容</label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={6}
                      className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    ></textarea>
                  </div>

                  <div>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`px-6 py-3 text-white rounded-md font-medium flex items-center justify-center ${isSubmitting ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 transition-colors'}`}
                    >
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          处理中...
                        </>
                      ) : '提交信息'}
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* 地图区域 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">我们的办公地点</h2>

          <div className="flex flex-col items-center gap-8 mb-12">
            {officeLocations.map((office, index) => (
              <div key={index} className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-shadow w-full max-w-md mx-auto border border-blue-100">
                <h3 className="text-2xl font-bold mb-4 text-blue-700 text-center">{office.name}</h3>
                <div className="space-y-3 text-gray-600 text-base">
                  <p className="flex items-start justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {office.address}
                  </p>
                  <p className="flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    {office.phone}
                  </p>
                  <p className="flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    {office.email}
                  </p>
                  <p className="flex items-center justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {office.hours}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* 高德地图集成 */}
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="w-full h-96 rounded-lg overflow-hidden">
              <iframe
                src="https://uri.amap.com/marker?position=114.334373,30.531212&name=武汉总部&callout=1"
                width="100%"
                height="100%"
                frameBorder="0"
                scrolling="no"
                allowFullScreen={true}
                title="思立恒教育办公地点地图"
                style={{borderRadius:'1rem'}}
              ></iframe>
            </div>
            <div className="mt-4 text-center text-sm text-gray-500">
              <p>点击标记查看详细地址和联系方式</p>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>

          <div className="space-y-6">
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">如何预约咨询服务？</h3>
              <p className="text-gray-600">您可以通过填写本页面的联系表单、拨打咨询热线或发送电子邮件的方式预约咨询服务。我们的客服团队会在24小时内与您取得联系，安排专业顾问为您提供一对一咨询。</p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">咨询服务是否收费？</h3>
              <p className="text-gray-600">我们提供30分钟的免费初步咨询，帮助您了解我们的服务内容和方式。根据您的具体需求，我们会为您定制个性化的服务方案，并提供相应的收费标准。</p>
            </div>

            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">是否提供线上咨询服务？</h3>
              <p className="text-gray-600">是的，我们提供线上视频咨询服务，特别适合异地客户。您可以选择通过Zoom、腾讯会议等平台与我们的顾问进行一对一视频咨询，获得与线下同等专业的服务体验。</p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}