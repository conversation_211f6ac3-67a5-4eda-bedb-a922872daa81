@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  /* 主色调与品牌色 */
  --color-primary: #2563eb; /* blue-600 */
  --color-primary-light: #38bdf8; /* sky-400 */
  --color-primary-dark: #1e40af; /* blue-800 */
  --color-accent: #f59e42; /* orange-400 */
  --color-accent-light: #fde68a; /* yellow-400 */
  --color-success: #10b981; /* emerald-500 */
  --color-success-dark: #047857; /* emerald-700 */
  --color-purple: #6366f1; /* indigo-500 */
  --color-purple-dark: #6d28d9; /* purple-700 */
  --color-gradient-blue: linear-gradient(90deg, #38bdf8 0%, #2563eb 100%);
  --color-gradient-green: linear-gradient(90deg, #34d399 0%, #059669 100%);
  --color-gradient-purple: linear-gradient(90deg, #a78bfa 0%, #6366f1 100%);
  --color-gradient-orange: linear-gradient(90deg, #fbbf24 0%, #f59e42 100%);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
