'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiCalendar, FiUser, FiShare2 } from 'react-icons/fi';

export default function ArticlePage() {
  return (
    <main className="min-h-screen bg-gray-50">
      {/* 文章头部 */}
      <section className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="mb-6">
              <Link href="/insights" className="text-blue-600 hover:text-blue-800 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回媒体中心
              </Link>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">新高考选科策略：如何做出最优选择</h1>
            <div className="flex flex-wrap items-center text-gray-500 text-sm mb-6">
              <div className="flex items-center mr-6">
                <FiCalendar className="mr-2" />
                <span>2023-12-15</span>
              </div>
              <div className="flex items-center mr-6">
                <FiUser className="mr-2" />
                <span>张教授 | 高考政策研究专家</span>
              </div>
              <div className="flex items-center">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">教育博客</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 文章内容 */}
      <section className="py-10">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto bg-white p-8 rounded-lg shadow-sm">
            {/* 文章导语 */}
            <div className="mb-8 bg-blue-50 p-6 rounded-lg border-l-4 border-blue-600">
              <p className="text-gray-700 italic">
                新高考改革已在全国多个省份实施，选科组合直接影响学生的高中学习体验、高考竞争力和未来专业发展路径。
                本文将从多个维度分析如何基于个人特点和目标做出科学的选科决策，帮助学生和家长避免常见误区，找到最适合的选科组合。
              </p>
            </div>

            {/* 文章主体 */}
            <div className="prose prose-lg max-w-none">
              <h2>一、新高考选科的重要性</h2>
              <p>
                在新高考改革背景下，高中生需要从物理、化学、生物、政治、历史、地理六门学科中选择三门作为选考科目。
                这一选择将直接影响：
              </p>
              <ul>
                <li><strong>高中学习体验</strong>：选择适合自己的科目可以提高学习效率和学习兴趣，减轻学习压力。</li>
                <li><strong>高考竞争力</strong>：不同的选科组合面临的竞争压力不同，热门组合如物理+化学+生物、历史+政治+地理的竞争往往更为激烈。</li>
                <li><strong>专业选择范围</strong>：不同的选科组合对应不同的可报考专业范围，选科决策直接影响未来的专业选择空间。</li>
                <li><strong>职业发展路径</strong>：长远来看，选科决策会间接影响未来的职业发展方向和可能性。</li>
              </ul>

              <h2>二、科学选科的四大维度</h2>
              
              <h3>1. 个人特质维度</h3>
              <p>
                选科首先应考虑学生的个人特质，包括：
              </p>
              <ul>
                <li><strong>学科兴趣</strong>：对学科的兴趣是持续学习的重要动力，应优先考虑学生感兴趣的学科。</li>
                <li><strong>学科能力</strong>：客观评估在各学科的学习能力和表现，找出优势学科。</li>
                <li><strong>思维特点</strong>：分析型思维更适合理科，整合型思维可能更适合文科，但这不是绝对的。</li>
                <li><strong>学习习惯</strong>：不同学科对学习方法和习惯有不同要求，应考虑与自身学习习惯的匹配度。</li>
              </ul>

              <div className="bg-gray-50 p-6 rounded-lg my-8">
                <h4 className="text-blue-700 mb-3">案例分享：基于个人特质的选科决策</h4>
                <p>
                  小王在初中阶段表现出对科学实验的浓厚兴趣，逻辑思维能力强，但语言表达能力一般。
                  经过测评和分析，我们建议他选择物理+化学+生物的组合，充分发挥他的实验操作能力和逻辑分析能力。
                  小王在高中阶段学习积极性高，最终以优异成绩考入了理想的医学院校。
                </p>
              </div>

              <h3>2. 目标院校与专业维度</h3>
              <p>
                如果学生已有明确的目标院校或专业方向，选科决策应考虑：
              </p>
              <ul>
                <li><strong>专业选科要求</strong>：了解目标专业对选考科目的具体要求，确保选科组合满足报考条件。</li>
                <li><strong>院校录取偏好</strong>：部分高校对某些选科组合可能有隐性偏好，应进行深入研究。</li>
                <li><strong>专业竞争情况</strong>：分析不同选科组合报考目标专业的竞争情况，选择相对优势路径。</li>
              </ul>

              <p>
                值得注意的是，不同高校对同一专业的选科要求可能不同，应详细查阅各目标院校的招生章程。
                同时，部分专业虽然对选科没有硬性要求，但某些科目对专业学习有重要帮助，也应予以考虑。
              </p>

              <h3>3. 未来发展维度</h3>
              <p>
                选科决策应具有前瞻性，考虑未来发展趋势：
              </p>
              <ul>
                <li><strong>学科交叉趋势</strong>：未来的发展越来越强调学科交叉融合，选科组合应尽可能保持一定的多样性。</li>
                <li><strong>新兴专业需求</strong>：人工智能、大数据、生物科技等新兴领域对选科组合有特定要求，值得关注。</li>
                <li><strong>职业发展路径</strong>：不同职业对知识结构有不同要求，应考虑选科组合与未来职业发展的匹配度。</li>
              </ul>

              <h3>4. 区域政策维度</h3>
              <p>
                不同省份的新高考政策存在差异，选科决策应考虑区域特点：
              </p>
              <ul>
                <li><strong>赋分政策</strong>：了解所在省份对各科目的赋分政策，选择有利于提高总分的科目组合。</li>
                <li><strong>录取政策</strong>：分析区域内高校的录取政策和选科偏好，把握区域特点。</li>
                <li><strong>考试难度</strong>：不同地区各科目的考试难度和竞争情况不同，应进行针对性分析。</li>
              </ul>

              <h2>三、常见选科组合分析</h2>
              
              <h3>1. 物理+化学+生物</h3>
              <p>
                <strong>优势</strong>：覆盖面广，适合报考医学、理工类专业，未来发展空间大。<br />
                <strong>挑战</strong>：学习难度较大，需要较强的理科思维和实验能力。<br />
                <strong>适合人群</strong>：理科思维强，对自然科学有浓厚兴趣，目标是医学或理工类专业的学生。
              </p>

              <h3>2. 物理+化学+政治</h3>
              <p>
                <strong>优势</strong>：兼顾理工和经管，选择面较广，可报考工科、经济学类专业。<br />
                <strong>挑战</strong>：需要同时具备理科思维和文科表达能力。<br />
                <strong>适合人群</strong>：理科基础好但也具备一定文科能力，对经济管理类专业有兴趣的学生。
              </p>

              <h3>3. 历史+政治+地理</h3>
              <p>
                <strong>优势</strong>：传统文科组合，适合报考人文社科类专业。<br />
                <strong>挑战</strong>：需要较强的记忆力和文字表达能力。<br />
                <strong>适合人群</strong>：文科思维强，对人文社会科学有浓厚兴趣的学生。
              </p>

              <h3>4. 物理+历史+政治/地理</h3>
              <p>
                <strong>优势</strong>：跨文理组合，保持较广的专业选择空间。<br />
                <strong>挑战</strong>：需要同时适应不同学科的学习方法。<br />
                <strong>适合人群</strong>：文理兼长，尚未确定明确发展方向的学生。
              </p>

              <div className="bg-yellow-50 p-6 rounded-lg border-l-4 border-yellow-500 my-8">
                <h4 className="text-yellow-700 mb-3">选科误区警示</h4>
                <ul className="list-disc pl-5 space-y-2">
                  <li><strong>盲目跟风</strong>：不考虑个人特点，盲目选择热门组合。</li>
                  <li><strong>过度依赖成绩</strong>：仅根据初中或高一的学科成绩选科，忽视兴趣和潜力。</li>
                  <li><strong>短视决策</strong>：只考虑高考难度，不考虑未来发展。</li>
                  <li><strong>忽视交叉学科</strong>：过于极端地选择全理或全文组合，限制未来发展可能性。</li>
                </ul>
              </div>

              <h2>四、科学选科的步骤与方法</h2>
              
              <h3>1. 全面自我评估</h3>
              <p>
                通过科学的测评工具和方法，全面评估自身的学科兴趣、能力、思维特点和学习习惯。
                可以结合过往的学习经历、兴趣爱好和性格特点进行分析，也可以寻求专业机构的测评服务。
              </p>

              <h3>2. 明确目标定位</h3>
              <p>
                思考未来的发展方向和职业规划，明确目标院校和专业范围，了解相关选科要求。
                如果尚未确定具体方向，可以保持选科的多样性和灵活性。
              </p>

              <h3>3. 多方案比较</h3>
              <p>
                制定2-3个备选方案，从多个维度进行比较分析，评估每个方案的优势、挑战和适配度。
                可以咨询专业教师、学长学姐和教育顾问的意见，获取多角度的建议。
              </p>

              <h3>4. 试探性学习</h3>
              <p>
                在正式选科前，可以通过自学或选修课的形式，对潜在的选科进行试探性学习，
                了解学科内容和学习难度，检验自己的兴趣和适应性。
              </p>

              <h3>5. 制定学习规划</h3>
              <p>
                选科决策后，应制定详细的学习规划，包括学习目标、方法和资源配置，
                为选科后的学习做好充分准备，确保选科决策能够有效落地。
              </p>

              <h2>五、结语</h2>
              <p>
                新高考选科是一项复杂的决策，需要综合考虑个人特质、目标定位、未来发展和区域政策等多个因素。
                科学的选科决策不仅关乎高考成绩，更关乎学生的长期发展和人生规划。
                希望本文的分析和建议能够帮助学生和家长做出更加科学、合理的选科决策，
                找到最适合自己的发展路径。
              </p>
            </div>

            {/* 分享与评论 */}
            <div className="mt-12 pt-8 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <span className="text-gray-600 mr-4">分享文章：</span>
                  <div className="flex space-x-3">
                    <button className="w-10 h-10 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center hover:bg-blue-200 transition-colors">
                      <FiShare2 />
                    </button>
                  </div>
                </div>
                <div>
                  <Link href="/contact" className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors">
                    咨询选科服务
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 相关文章推荐 */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold mb-8 text-gray-800">相关文章推荐</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <h3 className="text-xl font-bold mb-3 text-gray-800">
                  <Link href="/insights/article-2" className="hover:text-blue-600 transition-colors">
                    大学生如何规划学术与就业双赢之路
                  </Link>
                </h3>
                <p className="text-gray-600 mb-4">大学阶段是人生关键期，如何平衡学术发展与就业准备，打造核心竞争力？本文提供实用建议...</p>
                <div className="flex items-center text-sm text-gray-500">
                  <FiCalendar className="mr-2" />
                  <span>2023-11-20</span>
                </div>
              </div>
              <div className="bg-white p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <h3 className="text-xl font-bold mb-3 text-gray-800">
                  <Link href="/insights/article-3" className="hover:text-blue-600 transition-colors">
                    职场转型指南：如何成功跨界到新领域
                  </Link>
                </h3>
                <p className="text-gray-600 mb-4">随着产业变革加速，职业转型成为常态。本文分享如何评估自身能力，制定有效的转型策略...</p>
                <div className="flex items-center text-sm text-gray-500">
                  <FiCalendar className="mr-2" />
                  <span>2023-10-08</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}