'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiArrowLeft, FiCalendar, FiUser, FiShare2, FiMessageCircle, FiThumbsUp, FiArrowRight, FiBookmark } from 'react-icons/fi';

export default function Insight1DetailPage() {
  // 媒体内容详情数据
  const insightDetail = {
    id: 'insight1',
    title: '2023年全球顶尖大学申请趋势分析',
    category: 'trends',
    categoryName: '留学趋势',
    summary: '深度分析2023年全球顶尖大学申请的最新趋势、录取标准变化以及未来发展方向，为计划申请的学生提供重要参考。',
    image: '/images/insights/insight1-detail.svg',
    date: '2023-07-10',
    author: '张教授',
    views: 2450,
    likes: 568,
    comments: 87,
    bookmarks: 326,
    content: [
      {
        type: 'paragraph',
        text: '随着全球高等教育的不断发展和国际学生流动性的增加，顶尖大学的申请竞争日益激烈。本文基于最新数据和一线申请经验，深入分析2023年全球顶尖大学申请的关键趋势和变化，为计划申请的学生和家长提供专业指导。'
      },
      {
        type: 'subtitle',
        text: '录取率变化趋势'
      },
      {
        type: 'paragraph',
        text: '2023年，全球顶尖大学的整体录取率继续呈下降趋势，但降幅较2022年有所放缓。以美国常春藤盟校为例，平均录取率从2022年的4.6%微降至4.3%。值得注意的是，部分院校开始更加注重申请质量而非数量，通过提高申请门槛来筛选更匹配的申请者。'
      },
      {
        type: 'image',
        src: '/images/insights/insight1-chart.svg',
        alt: '2019-2023年顶尖大学录取率变化趋势图',
        caption: '2019-2023年全球顶尖大学录取率变化趋势'
      },
      {
        type: 'subtitle',
        text: '录取标准的变化'
      },
      {
        type: 'paragraph',
        text: '2023年，顶尖大学的录取标准呈现以下几个明显变化：'
      },
      {
        type: 'list',
        items: [
          '学术成绩仍是基础，但评价更加全面化。除GPA外，课程挑战度、学术深度和知识应用能力受到更多关注',
          '标准化考试政策继续调整，更多院校采用Test Optional或Test Flexible政策，但提交优秀成绩仍有明显优势',
          '课外活动评价标准从"广度"转向"深度+影响力"，长期持续且有实质性成果的活动更受青睐',
          '社会责任感和全球视野成为重要考量因素，与可持续发展、社会公平等议题相关的经历更具竞争力',
          '数字素养和跨学科能力日益重要，能够在不同领域之间建立联系的申请者更受欢迎'
        ]
      },
      {
        type: 'subtitle',
        text: '申请人群特点变化'
      },
      {
        type: 'paragraph',
        text: '2023年申请季呈现出几个明显的申请人群特点变化：'
      },
      {
        type: 'list',
        items: [
          '国际学生构成更加多元化，除传统的中国、印度申请者外，来自东南亚、拉美和非洲的申请人数显著增加',
          '申请者年龄结构变化，Gap Year和非传统年龄段申请者比例上升',
          '跨学科申请增多，传统学科界限逐渐模糊，如计算机与人文、商科与社会科学的交叉申请',
          '职业转换型申请者增加，已有工作经验后再申请相关或跨领域研究生项目的趋势明显'
        ]
      },
      {
        type: 'subtitle',
        text: '热门专业趋势'
      },
      {
        type: 'paragraph',
        text: '2023年全球范围内的热门专业呈现出新的变化趋势：'
      },
      {
        type: 'list',
        items: [
          '人工智能与数据科学继续领跑，申请人数和竞争程度均创新高',
          '可持续发展相关专业快速崛起，包括环境科学、可再生能源、绿色金融等',
          '公共卫生与全球健康领域持续升温，疫情后效应仍在延续',
          '数字媒体与创意产业专业需求增长，尤其是结合技术与创意的交叉学科',
          '传统商科专业如金融、会计的申请竞争有所缓和，而创业、创新管理等新兴方向更受青睐'
        ]
      },
      {
        type: 'image',
        src: '/images/insights/insight1-majors.svg',
        alt: '2023年全球热门专业申请增长率对比图',
        caption: '2023年全球热门专业申请增长率对比'
      },
      {
        type: 'subtitle',
        text: '区域差异分析'
      },
      {
        type: 'paragraph',
        text: '不同地区顶尖大学的申请趋势也呈现出明显差异：'
      },
      {
        type: 'list',
        items: [
          '美国：常春藤盟校和顶尖文理学院竞争最为激烈，早申请(Early Decision/Action)优势进一步扩大',
          '英国：受脱欧影响，来自欧盟国家的申请者减少，但亚洲申请者明显增加；医学、法律等传统强势专业竞争加剧',
          '加拿大：整体申请人数大幅增长，尤其是多伦多大学和英属哥伦比亚大学等顶尖院校',
          '澳洲：国际学生申请强势回升，八大名校中悉尼大学和墨尔本大学竞争最为激烈',
          '亚洲：新加坡国立大学、香港大学等亚洲顶尖院校国际声誉提升，国际学生申请数量显著增加'
        ]
      },
      {
        type: 'subtitle',
        text: '申请策略建议'
      },
      {
        type: 'paragraph',
        text: '基于2023年的申请趋势，我们为计划申请全球顶尖大学的学生提供以下策略建议：'
      },
      {
        type: 'list',
        items: [
          '提前规划，至少在申请前1-2年开始系统性准备，尤其是特色活动和领导力经历的培养',
          '合理定位，根据个人背景和优势选择适合的目标院校和专业，避免盲目追求名校而忽视匹配度',
          '深化特色，在1-2个核心领域建立深度和影响力，而非追求活动数量',
          '关注新兴交叉学科，这些领域通常竞争相对较小但发展前景广阔',
          '提升软实力，包括批判性思维、团队协作、跨文化沟通等能力，这些在申请材料和面试中日益重要',
          '差异化定位，清晰展现个人独特价值和贡献潜力，避免成为"完美但平淡"的申请者'
        ]
      },
      {
        type: 'quote',
        text: '2023年的申请趋势表明，顶尖大学越来越注重学生的整体素质和长期发展潜力，而非仅关注短期成绩。成功的申请者不仅要有优秀的学术表现，更要展现出独特的思考能力、解决问题的创新方法以及对所选领域的热情与贡献。',
        author: '张教授，国际教育专家'
      },
      {
        type: 'subtitle',
        text: '未来展望'
      },
      {
        type: 'paragraph',
        text: '展望未来，我们预计全球顶尖大学的申请趋势将继续朝以下方向发展：'
      },
      {
        type: 'list',
        items: [
          '录取过程将更加注重"整体人才"评价，学术与非学术因素的权重更加平衡',
          '技术在申请和评估过程中的应用将增加，包括AI辅助评估和虚拟面试等',
          '国际学生多元化将继续推进，新兴市场国家的申请者比例将上升',
          '跨学科和新兴交叉领域的重要性将进一步提升',
          '可持续发展、社会责任等价值观将在申请评估中扮演更重要角色'
        ]
      },
      {
        type: 'cta',
        text: '如果您正在规划全球顶尖大学申请，欢迎联系我们的专业顾问团队，获取个性化的申请策略和指导。'
      }
    ],
    relatedInsights: [
      { id: 'insight5', title: '如何准备一份脱颖而出的大学申请文书' },
      { id: 'insight8', title: '国际学生如何提升软实力，增强名校申请竞争力' }
    ]
  };

  return (
    <main className="min-h-screen bg-gray-50">
      {/* 文章详情头部 */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-sky-500 opacity-90"></div>
        <div className="absolute inset-0 opacity-20" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto">
            <Link 
              href="/insights" 
              className="inline-flex items-center text-white/90 hover:text-white mb-6 transition-colors group"
            >
              <FiArrowLeft className="mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
              返回媒体中心
            </Link>
            <div className="flex items-center space-x-4 mb-4">
              <span className="px-3 py-1 bg-white/20 text-white rounded-full text-sm font-medium backdrop-blur-sm">
                {insightDetail.categoryName}
              </span>
              <span className="flex items-center text-white/90">
                <FiCalendar className="mr-1" /> {insightDetail.date}
              </span>
              <span className="flex items-center text-white/90">
                <FiUser className="mr-1" /> {insightDetail.author}
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">{insightDetail.title}</h1>
            <p className="text-xl text-white/90 mb-6">{insightDetail.summary}</p>
          </div>
        </div>
      </section>

      {/* 文章详情内容 */}
      <section className="py-16 -mt-10">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-xl overflow-hidden">
            <div className="relative h-80 w-full">
              <Image 
                src={insightDetail.image}
                alt={insightDetail.title}
                layout="fill"
                objectFit="cover"
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-8 md:p-12">
              <div className="prose prose-lg max-w-none prose-blue">
                {insightDetail.content.map((block, index) => {
                  switch (block.type) {
                    case 'paragraph':
                      return <p key={index} className="text-gray-700 mb-6">{block.text}</p>;
                    case 'subtitle':
                      return <h2 key={index} className="text-2xl font-bold text-gray-800 mt-10 mb-6">{block.text}</h2>;
                    case 'list':
                      return (
                        <ul key={index} className="list-disc pl-6 mb-6 space-y-2">
                          {block.items.map((item, i) => (
                            <li key={i} className="text-gray-700">{item}</li>
                          ))}
                        </ul>
                      );
                    case 'image':
                      return (
                        <div key={index} className="my-10">
                          <div className="relative h-80 w-full rounded-lg overflow-hidden">
                            <Image 
                              src={block.src}
                              alt={block.alt}
                              layout="fill"
                              objectFit="cover"
                              className="w-full h-full object-cover"
                            />
                          </div>
                          {block.caption && (
                            <p className="text-center text-gray-500 mt-3 text-sm">{block.caption}</p>
                          )}
                        </div>
                      );
                    case 'quote':
                      return (
                        <blockquote key={index} className="border-l-4 border-blue-500 pl-6 py-2 my-8 bg-blue-50 rounded-r-lg">
                          <p className="text-gray-700 italic mb-2">{`"${block.text}"`}</p>
                          <p className="text-gray-500 text-sm">— {block.author}</p>
                        </blockquote>
                      );
                    case 'cta':
                      return (
                        <div key={index} className="bg-gradient-to-r from-blue-50 to-sky-50 p-6 rounded-lg my-10 border-l-4 border-blue-500">
                          <p className="text-gray-700 font-medium">{block.text}</p>
                          <div className="mt-4">
                            <Link 
                              href="/appointment" 
                              className="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-md hover:shadow-lg"
                            >
                              预约咨询
                            </Link>
                          </div>
                        </div>
                      );
                    default:
                      return null;
                  }
                })}
              </div>

              {/* 社交分享和互动 */}
              <div className="mt-12 pt-8 border-t border-gray-100">
                <div className="flex flex-wrap justify-between items-center">
                  <div className="flex items-center space-x-6 mb-4 md:mb-0">
                    <span className="flex items-center text-gray-500">
                      <FiThumbsUp className="mr-1" /> {insightDetail.likes} 人觉得有用
                    </span>
                    <span className="flex items-center text-gray-500">
                      <FiMessageCircle className="mr-1" /> {insightDetail.comments} 条评论
                    </span>
                    <span className="flex items-center text-gray-500">
                      <FiBookmark className="mr-1" /> {insightDetail.bookmarks} 人收藏
                    </span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <button className="flex items-center px-4 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors">
                      <FiThumbsUp className="mr-2" /> 点赞
                    </button>
                    <button className="flex items-center px-4 py-2 bg-gray-50 text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                      <FiBookmark className="mr-2" /> 收藏
                    </button>
                    <button className="flex items-center px-4 py-2 bg-gray-50 text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                      <FiShare2 className="mr-2" /> 分享
                    </button>
                  </div>
                </div>
              </div>

              {/* 作者信息 */}
              <div className="mt-10 p-6 bg-gray-50 rounded-xl">
                <div className="flex items-center">
                  <div className="relative w-16 h-16 rounded-full overflow-hidden mr-4">
                    <Image 
                      src="/team/member-1.svg"
                      alt={insightDetail.author}
                      layout="fill"
                      objectFit="cover"
                    />
                  </div>
                  <div>
                    <h3 className="text-lg font-bold text-gray-800">{insightDetail.author}</h3>
                    <p className="text-gray-600">国际教育专家，拥有20年顶尖大学申请指导经验</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 相关文章 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold mb-8 text-gray-800">相关文章</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {insightDetail.relatedInsights.map((relatedInsight) => (
                <Link 
                  key={relatedInsight.id} 
                  href={`/insights/${relatedInsight.id}`}
                  className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border border-gray-100 group"
                >
                  <h3 className="text-xl font-bold mb-2 text-gray-800 group-hover:text-blue-600 transition-colors">
                    {relatedInsight.title}
                  </h3>
                  <div className="flex items-center justify-between mt-4">
                    <span className="text-gray-500">阅读更多</span>
                    <FiArrowRight className="text-blue-500 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 订阅更新 */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-sky-400 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">获取最新教育资讯和专家观点</h2>
            <p className="text-xl text-white/90 mb-8">订阅我们的电子通讯，定期获取国际教育领域的最新动态和专业指导</p>
            <div className="max-w-xl mx-auto">
              <div className="flex flex-col sm:flex-row gap-4">
                <input 
                  type="email" 
                  placeholder="您的邮箱地址" 
                  className="flex-1 px-6 py-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 text-gray-800"
                />
                <button className="px-8 py-4 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl">
                  订阅
                </button>
              </div>
              <p className="text-sm text-white/80 mt-4">我们尊重您的隐私，绝不会向第三方分享您的信息</p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}