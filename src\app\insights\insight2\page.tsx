'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FaCalendarAlt, FaUser, FaEye, FaThumbsUp, FaComment, FaFacebookF, FaTwitter, FaLinkedinIn, FaWeixin } from 'react-icons/fa';

const InsightDetailPage = () => {
  const [likes, setLikes] = useState(142);
  const [hasLiked, setHasLiked] = useState(false);
  
  const insightData = {
    id: 'insight2',
    title: '2024年留学申请趋势分析：新兴专业与申请策略',
    category: '留学趋势',
    summary: '随着全球教育环境的变化，2024年留学申请呈现出新的趋势和特点。本文深入分析新兴热门专业、申请策略变化以及未来发展方向。',
    imageUrl: '/images/insights/insight2-detail.svg',
    date: '2023-12-15',
    author: '王教授',
    authorTitle: '国际教育专家',
    authorImage: '/team/member-3.svg',
    views: 3245,
    likes: 142,
    comments: 38,
    content: [
      {
        type: 'paragraph',
        content: '随着全球教育环境的持续变化和发展，2024年的留学申请呈现出一系列新的趋势和特点。本文将深入分析当前留学申请的热门专业、申请策略的变化以及未来的发展方向，为计划在未来一年申请留学的学生提供有价值的参考。'
      },
      {
        type: 'subheading',
        content: '一、新兴热门专业分析'
      },
      {
        type: 'paragraph',
        content: '近年来，随着科技的快速发展和全球经济结构的调整，一些新兴专业逐渐成为留学申请的热门选择。根据我们的最新数据分析，以下专业在2024年的申请中表现出强劲的增长势头：'
      },
      {
        type: 'image',
        url: '/images/insights/insight2-majors.svg',
        caption: '2024年全球热门专业申请增长率对比'
      },
      {
        type: 'list',
        items: [
          '人工智能与机器学习：随着AI技术在各行业的广泛应用，相关专业的需求持续攀升，申请人数同比增长约35%。',
          '可持续发展与环境科学：在全球气候变化背景下，环境相关专业吸引了大量关注，申请增长率达到28%。',
          '数字健康与生物技术：疫情后，医疗科技领域的创新需求激增，相关专业申请增长约25%。',
          '量子计算：作为计算机科学的前沿领域，量子计算专业虽然基数较小，但增长率高达40%。',
          '跨学科项目：结合多个领域知识的跨学科项目（如人工智能+医学、数据科学+商业等）成为新趋势。'
        ]
      },
      {
        type: 'subheading',
        content: '二、申请策略的变化'
      },
      {
        type: 'paragraph',
        content: '2024年的留学申请策略相比往年有了明显的变化，主要体现在以下几个方面：'
      },
      {
        type: 'list',
        items: [
          '全面评估取代单一标准：越来越多的顶尖院校减少对标准化考试的依赖，转而采用更全面的评估体系，包括学术表现、课外活动、个人陈述等多方面因素。',
          '数字化申请体验：申请流程的数字化程度进一步提高，包括虚拟校园参观、在线面试和数字作品集的广泛应用。',
          '早期规划的重要性增加：竞争加剧使得早期规划变得更加重要，许多学生从高中一年级就开始为留学申请做准备。',
          '个性化申请材料：标准化的申请材料已不足以在激烈的竞争中脱颖而出，个性化、有深度的申请材料成为关键。',
          '多元文化能力的强调：国际视野和跨文化交流能力在申请中的权重明显提升。'
        ]
      },
      {
        type: 'quote',
        content: '未来的成功申请者不仅需要优秀的学术成绩，还需要展示其独特的思维方式、解决问题的能力以及对所选领域的热情和洞察力。',
        author: '哈佛大学招生办公室'
      },
      {
        type: 'subheading',
        content: '三、区域趋势分析'
      },
      {
        type: 'paragraph',
        content: '不同留学目的地国家和地区的申请趋势也呈现出新的特点：'
      },
      {
        type: 'list',
        items: [
          '美国：顶尖院校竞争更加激烈，STEM专业依然是中国学生的首选，但文理学院受到的关注度也在提升。',
          '英国：后脱欧时代，英国大学对国际学生的吸引力有所恢复，尤其是在商科、法律和艺术领域。',
          '加拿大：凭借相对宽松的移民政策和高质量的教育，加拿大成为越来越多学生的首选。',
          '亚洲：新加坡、香港和日本的顶尖大学国际化程度提高，吸引了更多寻求亚洲教育体验的国际学生。',
          '欧洲大陆：提供英语授课项目的欧洲大学（如荷兰、德国、北欧国家）受欢迎程度上升，尤其是在可持续发展、设计和工程领域。'
        ]
      },
      {
        type: 'subheading',
        content: '四、未来展望与建议'
      },
      {
        type: 'paragraph',
        content: '基于当前趋势，我们对未来留学申请提出以下建议：'
      },
      {
        type: 'list',
        items: [
          '提前规划，制定长期发展战略：建议学生至少提前18-24个月开始规划留学申请，系统性地提升各方面能力。',
          '注重实践经验和研究能力：实习、科研项目和实际问题解决经验将成为申请中的重要加分项。',
          '培养独特的个人叙事：在申请材料中展现个人成长历程和独特视角，避免模板化表达。',
          '关注新兴交叉学科：传统学科边界正在模糊，具有跨学科背景的申请者将更具竞争力。',
          '提升数字素养和适应能力：在快速变化的环境中，适应能力和持续学习的能力变得尤为重要。'
        ]
      },
      {
        type: 'paragraph',
        content: '总的来说，2024年的留学申请环境既充满挑战也蕴含机遇。申请者需要更加全面地提升自己，同时保持对教育本质的关注，而不仅仅是追逐热门趋势。成功的留学申请不仅是进入理想院校的通行证，更是个人成长和职业发展的重要里程碑。'
      },
      {
        type: 'cta',
        title: '需要专业的留学申请指导？',
        content: '我们的专家团队可以为您提供个性化的留学规划和申请指导，助您实现留学梦想。',
        buttonText: '预约咨询',
        buttonLink: '/contact'
      }
    ],
    relatedInsights: [
      {
        id: 'insight1',
        title: '全球顶尖大学申请趋势分析：2023年最新数据解读',
        imageUrl: '/images/insights/insight1.svg',
        category: '留学趋势'
      },
      {
        id: 'insight3',
        title: '如何打造完美的大学申请文书：专家建议与案例分析',
        imageUrl: '/images/insights/insight3.svg',
        category: '申请技巧'
      },
      {
        id: 'insight4',
        title: '国际学生奖学金申请全攻略：机会、策略与成功案例',
        imageUrl: '/images/insights/insight4.svg',
        category: '奖学金'
      }
    ]
  };

  const handleLike = () => {
    if (!hasLiked) {
      setLikes(likes + 1);
      setHasLiked(true);
    } else {
      setLikes(likes - 1);
      setHasLiked(false);
    }
  };

  return (
    <div className="bg-gray-50">
      {/* 头部区域 */}
      <div className="relative bg-gradient-to-r from-blue-900 to-blue-700 py-20">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <div className="text-blue-200 mb-2">{insightData.category}</div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">{insightData.title}</h1>
            <p className="text-xl text-blue-100 mb-8">{insightData.summary}</p>
            <div className="flex items-center justify-center text-blue-100 space-x-6">
              <div className="flex items-center">
                <FaCalendarAlt className="mr-2" />
                <span>{insightData.date}</span>
              </div>
              <div className="flex items-center">
                <FaUser className="mr-2" />
                <span>{insightData.author}</span>
              </div>
              <div className="flex items-center">
                <FaEye className="mr-2" />
                <span>{insightData.views} 阅读</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* 文章主体 */}
          <div className="lg:w-2/3">
            <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
              <div className="relative h-96 w-full">
                <Image 
                  src={insightData.imageUrl} 
                  alt={insightData.title}
                  fill
                  style={{objectFit: 'cover'}}
                  priority
                />
              </div>
              
              <div className="p-8">
                {insightData.content.map((block, index) => {
                  switch (block.type) {
                    case 'paragraph':
                      return <p key={index} className="text-gray-700 mb-6 leading-relaxed">{block.content}</p>;
                    case 'subheading':
                      return <h2 key={index} className="text-2xl font-bold text-blue-800 mb-4 mt-8">{block.content}</h2>;
                    case 'list':
                      return (
                        <ul key={index} className="list-disc pl-6 mb-6 space-y-2">
                          {block.items.map((item, i) => (
                            <li key={i} className="text-gray-700">{item}</li>
                          ))}
                        </ul>
                      );
                    case 'image':
                      return (
                        <div key={index} className="my-8">
                          <div className="relative h-80 w-full">
                            <Image 
                              src={block.url} 
                              alt={block.caption || 'Article image'}
                              fill
                              style={{objectFit: 'contain'}}
                            />
                          </div>
                          {block.caption && (
                            <p className="text-center text-gray-500 mt-2">{block.caption}</p>
                          )}
                        </div>
                      );
                    case 'quote':
                      return (
                        <blockquote key={index} className="border-l-4 border-blue-500 pl-4 py-2 my-6 bg-blue-50 rounded-r-lg">
                          <p className="text-gray-700 italic">{block.content}</p>
                          {block.author && <p className="text-gray-500 mt-2">— {block.author}</p>}
                        </blockquote>
                      );
                    case 'cta':
                      return (
                        <div key={index} className="bg-gradient-to-r from-blue-100 to-blue-50 p-6 rounded-lg my-8 border-l-4 border-blue-600">
                          <h3 className="text-xl font-bold text-blue-800 mb-2">{block.title}</h3>
                          <p className="text-gray-700 mb-4">{block.content}</p>
                          <Link href={block.buttonLink}>
                            <span className="inline-block bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 transition-colors">
                              {block.buttonText}
                            </span>
                          </Link>
                        </div>
                      );
                    default:
                      return null;
                  }
                })}
              </div>
              
              {/* 文章底部互动区 */}
              <div className="border-t border-gray-200 p-6">
                <div className="flex justify-between items-center">
                  <div className="flex space-x-4">
                    <button 
                      onClick={handleLike}
                      className={`flex items-center space-x-1 ${hasLiked ? 'text-blue-600' : 'text-gray-600'} hover:text-blue-600`}
                    >
                      <FaThumbsUp />
                      <span>{likes}</span>
                    </button>
                    <div className="flex items-center space-x-1 text-gray-600">
                      <FaComment />
                      <span>{insightData.comments}</span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-3">
                    <span className="text-gray-600 mr-2">分享：</span>
                    <a href="#" className="text-blue-600 hover:text-blue-800"><FaFacebookF /></a>
                    <a href="#" className="text-blue-400 hover:text-blue-600"><FaTwitter /></a>
                    <a href="#" className="text-blue-700 hover:text-blue-900"><FaLinkedinIn /></a>
                    <a href="#" className="text-green-600 hover:text-green-800"><FaWeixin /></a>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 作者信息 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden p-6 mb-8">
              <div className="flex items-center">
                <div className="relative h-20 w-20 rounded-full overflow-hidden">
                  <Image 
                    src={insightData.authorImage} 
                    alt={insightData.author}
                    fill
                    style={{objectFit: 'cover'}}
                  />
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-bold text-gray-800">{insightData.author}</h3>
                  <p className="text-gray-600">{insightData.authorTitle}</p>
                </div>
              </div>
              <p className="mt-4 text-gray-700">
                王教授是国际教育领域的资深专家，拥有超过15年的留学咨询和教育研究经验。他专注于全球高等教育趋势分析和留学申请策略研究，曾帮助数百名学生成功申请世界顶尖大学。
              </p>
            </div>
          </div>
          
          {/* 侧边栏 */}
          <div className="lg:w-1/3">
            {/* 相关文章 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-800">相关文章</h3>
              </div>
              <div className="p-6 space-y-6">
                {insightData.relatedInsights.map((item, index) => (
                  <div key={index} className="flex space-x-4">
                    <div className="relative h-20 w-20 flex-shrink-0 rounded-md overflow-hidden">
                      <Image 
                        src={item.imageUrl} 
                        alt={item.title}
                        fill
                        style={{objectFit: 'cover'}}
                      />
                    </div>
                    <div>
                      <span className="text-sm text-blue-600">{item.category}</span>
                      <Link href={`/insights/${item.id}`}>
                        <h4 className="text-gray-800 font-medium hover:text-blue-600 transition-colors">{item.title}</h4>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* 订阅更新 */}
            <div className="bg-gradient-to-br from-blue-800 to-blue-600 rounded-xl shadow-md overflow-hidden mb-8 text-white">
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">订阅我们的更新</h3>
                <p className="mb-4 text-blue-100">获取最新的留学资讯、申请技巧和成功案例分析。</p>
                <form className="space-y-3">
                  <input 
                    type="email" 
                    placeholder="您的邮箱地址" 
                    className="w-full px-4 py-2 rounded-md text-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-300"
                  />
                  <button 
                    type="submit" 
                    className="w-full bg-white text-blue-700 font-medium px-4 py-2 rounded-md hover:bg-blue-50 transition-colors"
                  >
                    立即订阅
                  </button>
                </form>
                <p className="mt-3 text-xs text-blue-200">我们尊重您的隐私，绝不会分享您的信息。</p>
              </div>
            </div>
            
            {/* 热门标签 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-800">热门标签</h3>
              </div>
              <div className="p-6">
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">留学申请</span>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">美国大学</span>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">英国大学</span>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">奖学金</span>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">申请文书</span>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">面试技巧</span>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">STEM专业</span>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">商科</span>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">艺术留学</span>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">语言考试</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsightDetailPage;