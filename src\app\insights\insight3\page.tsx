'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FaCalendarAlt, FaUser, FaEye, FaThumbsUp, FaComment, FaFacebookF, FaTwitter, FaLinkedinIn, FaWeixin } from 'react-icons/fa';

const InsightDetailPage = () => {
  const [likes, setLikes] = useState(98);
  const [hasLiked, setHasLiked] = useState(false);
  
  const insightData = {
    id: 'insight3',
    title: '如何打造完美的大学申请文书：专家建议与案例分析',
    category: '申请技巧',
    summary: '申请文书是展示个人特质和潜力的重要窗口，本文提供专业指导和实用技巧，帮助学生创作出打动招生官的优秀文书。',
    imageUrl: '/images/insights/insight3-detail.svg',
    date: '2023-11-20',
    author: '李博士',
    authorTitle: '文书指导专家',
    authorImage: '/team/member-2.svg',
    views: 2876,
    likes: 98,
    comments: 27,
    content: [
      {
        type: 'paragraph',
        content: '在竞争激烈的大学申请过程中，申请文书往往成为决定录取结果的关键因素。一篇出色的文书能够让招生官了解申请人的个性、价值观和成长历程，从而在众多优秀申请者中脱颖而出。本文将分享专业文书写作指导和实用技巧，并通过成功案例分析，帮助学生创作出真实、有深度且富有个人特色的申请文书。'
      },
      {
        type: 'subheading',
        content: '一、理解申请文书的本质与目的'
      },
      {
        type: 'paragraph',
        content: '申请文书本质上是一种自我介绍和个人陈述，其核心目的是让招生官了解到简历和成绩单无法展示的个人特质。优秀的申请文书应当：'
      },
      {
        type: 'list',
        items: [
          '展示申请人的个性、价值观和思维方式',
          '讲述有意义的个人经历和成长故事',
          '表达对所申请专业或学校的理解和热情',
          '展现申请人的写作能力和逻辑思维',
          '让招生官记住申请人，并对其产生兴趣'
        ]
      },
      {
        type: 'quote',
        content: '招生官阅读文书时最希望看到的是真实的你，而不是你认为他们想看到的那个人。',
        author: '斯坦福大学前招生官'
      },
      {
        type: 'subheading',
        content: '二、文书写作前的准备工作'
      },
      {
        type: 'paragraph',
        content: '在开始撰写申请文书前，充分的准备工作能够帮助你更清晰地构思内容，提高写作效率：'
      },
      {
        type: 'list',
        items: [
          '深入研究目标院校：了解学校的价值观、教育理念和特色项目，思考自己与学校的契合点。',
          '自我反思与梳理：回顾个人经历，找出能体现自己特质的关键事件、挑战和成长时刻。',
          '确定核心主题：选择一个能够贯穿全文的中心思想或主题，避免内容过于分散。',
          '收集素材：记录相关的细节、对话、感受和思考，为文书提供丰富的素材。',
          '了解文书要求：仔细阅读各校的文书题目和要求，包括字数限制、格式等具体规定。'
        ]
      },
      {
        type: 'subheading',
        content: '三、文书写作的核心技巧'
      },
      {
        type: 'paragraph',
        content: '以下是创作优秀申请文书的关键技巧：'
      },
      {
        type: 'list',
        items: [
          '开头要吸引人：用独特的故事、引人深思的问题或生动的场景描写开篇，立即抓住读者注意力。',
          '展示而非陈述：通过具体事例和细节展示你的特质，避免空洞的自我评价。',
          '聚焦深度而非广度：深入探讨一两个主题，而不是浅尝辄止地罗列多个成就。',
          '保持真实性：展现真实的自我，包括挑战、失败和成长，避免过度包装或虚构。',
          '注重结构与逻辑：确保文书有清晰的结构和自然的逻辑流程，使读者易于理解。',
          '精炼语言：每个词都应有其价值，删除冗余内容，在有限字数内最大化信息传递。',
          '结尾要有力：以深刻的反思、未来展望或呼应开头的方式结束，给读者留下深刻印象。'
        ]
      },
      {
        type: 'image',
        url: '/images/insights/insight3-structure.svg',
        caption: '优秀申请文书的结构与要素'
      },
      {
        type: 'subheading',
        content: '四、常见文书主题与案例分析'
      },
      {
        type: 'paragraph',
        content: '以下是几种常见的文书主题类型及其成功案例分析：'
      },
      {
        type: 'subheading',
        content: '1. 挑战与成长类'
      },
      {
        type: 'paragraph',
        content: '这类文书聚焦于申请人面对的重大挑战或困难，以及从中获得的成长和领悟。'
      },
      {
        type: 'paragraph',
        content: '案例分析：小张在文书中描述了他克服口吃参加全国演讲比赛的经历。他没有仅仅陈述"我克服了口吃"，而是通过生动的场景描写，展示了他如何从每晚对着镜子练习到第一次在小组面前发言的全过程。文书重点不是最终的成功，而是过程中的坚持、自我怀疑和突破，展现了他的毅力和成长思维。这篇文书成功地让招生官看到了一个真实、立体且有韧性的申请人。'
      },
      {
        type: 'subheading',
        content: '2. 文化认同与价值观类'
      },
      {
        type: 'paragraph',
        content: '这类文书探讨申请人的文化背景、家庭价值观或重要信念如何塑造了其身份和世界观。'
      },
      {
        type: 'paragraph',
        content: '案例分析：小李的文书围绕她作为中西文化交融家庭中的一员的独特经历展开。她通过描述春节和圣诞节的家庭聚会，以及两种文化在日常生活中的碰撞与融合，展示了她如何从中培养出跨文化思维和包容精神。文书不仅有具体的文化细节描写，还深入探讨了这种背景如何影响她的思维方式和未来目标，展现了她的自我认知和文化敏感性。'
      },
      {
        type: 'subheading',
        content: '3. 学术热情与探索类'
      },
      {
        type: 'paragraph',
        content: '这类文书聚焦于申请人对特定学术领域的热情、探索历程和独特见解。'
      },
      {
        type: 'paragraph',
        content: '案例分析：小王在文书中分享了他对天体物理学的痴迷是如何从小时候阁楼上的一台旧望远镜开始的。他没有堆砌专业术语或研究成果，而是通过讲述几个关键时刻——第一次看到土星环的震撼、高中时期自学相对论的困惑与突破、参与天文俱乐部的社区观星活动等，展示了他对这一领域持续且深入的热情。文书还巧妙地将他的学术兴趣与对宇宙奥秘和人类位置的哲学思考相结合，展现了他的思维深度和学术潜力。'
      },
      {
        type: 'subheading',
        content: '五、常见误区与避免方法'
      },
      {
        type: 'paragraph',
        content: '在申请文书写作过程中，以下是一些常见误区及其避免方法：'
      },
      {
        type: 'list',
        items: [
          '过度强调成就：避免将文书变成成就清单，招生官更关注你的思考过程和个人特质。',
          '使用陈词滥调：避免使用"我一直梦想"、"这次经历改变了我的一生"等老套表达。',
          '话题过于普通：避免选择过于常见的话题，如体育比赛胜利、志愿者经历等，除非你能提供独特视角。',
          '过度修饰语言：避免使用过多华丽辞藻或复杂句式，清晰自然的表达更有效。',
          '缺乏个人声音：避免模仿他人风格或使用网上模板，保持自己独特的语言风格和思维方式。',
          '忽视细节检查：避免语法错误、拼写错误或格式问题，这些细节会影响整体印象。'
        ]
      },
      {
        type: 'quote',
        content: '最糟糕的文书不是写得不好的文书，而是那些看起来可以被任何人写出的文书。',
        author: '耶鲁大学招生委员会'
      },
      {
        type: 'subheading',
        content: '六、文书修改与完善'
      },
      {
        type: 'paragraph',
        content: '文书写作是一个反复修改和完善的过程，以下是有效的修改策略：'
      },
      {
        type: 'list',
        items: [
          '多次修改：完成初稿后，至少进行3-5次修改，每次关注不同方面（内容、结构、语言等）。',
          '寻求反馈：请老师、顾问或信任的朋友阅读你的文书，获取客观意见和建议。',
          '大声朗读：朗读文书有助于发现语言不自然或逻辑不顺的地方。',
          '间隔修改：写完初稿后，放置几天再回来修改，以获得新的视角。',
          '检查一致性：确保文书的主题、语调和风格始终一致。',
          '最终校对：仔细检查拼写、语法、标点和格式，确保无误。'
        ]
      },
      {
        type: 'paragraph',
        content: '总之，一篇优秀的申请文书需要时间、思考和多次修改。通过展示真实的自我、独特的视角和深刻的反思，你的文书将能够在激烈的申请竞争中脱颖而出，向招生官展示你作为一个独特个体的价值和潜力。'
      },
      {
        type: 'cta',
        title: '需要专业的文书指导？',
        content: '我们的专家团队提供一对一的文书咨询和修改服务，帮助你创作出最能展现个人特色的申请文书。',
        buttonText: '预约咨询',
        buttonLink: '/contact'
      }
    ],
    relatedInsights: [
      {
        id: 'insight1',
        title: '全球顶尖大学申请趋势分析：2023年最新数据解读',
        imageUrl: '/images/insights/insight1.svg',
        category: '留学趋势'
      },
      {
        id: 'insight2',
        title: '2024年留学申请趋势分析：新兴专业与申请策略',
        imageUrl: '/images/insights/insight2.svg',
        category: '留学趋势'
      },
      {
        id: 'insight4',
        title: '国际学生奖学金申请全攻略：机会、策略与成功案例',
        imageUrl: '/images/insights/insight4.svg',
        category: '奖学金'
      }
    ]
  };

  const handleLike = () => {
    if (!hasLiked) {
      setLikes(likes + 1);
      setHasLiked(true);
    } else {
      setLikes(likes - 1);
      setHasLiked(false);
    }
  };

  return (
    <div className="bg-gray-50">
      {/* 头部区域 */}
      <div className="relative bg-gradient-to-r from-indigo-900 to-indigo-700 py-20">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <div className="text-indigo-200 mb-2">{insightData.category}</div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">{insightData.title}</h1>
            <p className="text-xl text-indigo-100 mb-8">{insightData.summary}</p>
            <div className="flex items-center justify-center text-indigo-100 space-x-6">
              <div className="flex items-center">
                <FaCalendarAlt className="mr-2" />
                <span>{insightData.date}</span>
              </div>
              <div className="flex items-center">
                <FaUser className="mr-2" />
                <span>{insightData.author}</span>
              </div>
              <div className="flex items-center">
                <FaEye className="mr-2" />
                <span>{insightData.views} 阅读</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* 文章主体 */}
          <div className="lg:w-2/3">
            <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
              <div className="relative h-96 w-full">
                <Image 
                  src={insightData.imageUrl} 
                  alt={insightData.title}
                  fill
                  style={{objectFit: 'cover'}}
                  priority
                />
              </div>
              
              <div className="p-8">
                {insightData.content.map((block, index) => {
                  switch (block.type) {
                    case 'paragraph':
                      return <p key={index} className="text-gray-700 mb-6 leading-relaxed">{block.content}</p>;
                    case 'subheading':
                      return <h2 key={index} className="text-2xl font-bold text-indigo-800 mb-4 mt-8">{block.content}</h2>;
                    case 'list':
                      return (
                        <ul key={index} className="list-disc pl-6 mb-6 space-y-2">
                          {block.items.map((item, i) => (
                            <li key={i} className="text-gray-700">{item}</li>
                          ))}
                        </ul>
                      );
                    case 'image':
                      return (
                        <div key={index} className="my-8">
                          <div className="relative h-80 w-full">
                            <Image 
                              src={block.url} 
                              alt={block.caption || 'Article image'}
                              fill
                              style={{objectFit: 'contain'}}
                            />
                          </div>
                          {block.caption && (
                            <p className="text-center text-gray-500 mt-2">{block.caption}</p>
                          )}
                        </div>
                      );
                    case 'quote':
                      return (
                        <blockquote key={index} className="border-l-4 border-indigo-500 pl-4 py-2 my-6 bg-indigo-50 rounded-r-lg">
                          <p className="text-gray-700 italic">{block.content}</p>
                          {block.author && <p className="text-gray-500 mt-2">— {block.author}</p>}
                        </blockquote>
                      );
                    case 'cta':
                      return (
                        <div key={index} className="bg-gradient-to-r from-indigo-100 to-indigo-50 p-6 rounded-lg my-8 border-l-4 border-indigo-600">
                          <h3 className="text-xl font-bold text-indigo-800 mb-2">{block.title}</h3>
                          <p className="text-gray-700 mb-4">{block.content}</p>
                          <Link href={block.buttonLink}>
                            <span className="inline-block bg-indigo-600 text-white px-6 py-2 rounded-md font-medium hover:bg-indigo-700 transition-colors">
                              {block.buttonText}
                            </span>
                          </Link>
                        </div>
                      );
                    default:
                      return null;
                  }
                })}
              </div>
              
              {/* 文章底部互动区 */}
              <div className="border-t border-gray-200 p-6">
                <div className="flex justify-between items-center">
                  <div className="flex space-x-4">
                    <button 
                      onClick={handleLike}
                      className={`flex items-center space-x-1 ${hasLiked ? 'text-indigo-600' : 'text-gray-600'} hover:text-indigo-600`}
                    >
                      <FaThumbsUp />
                      <span>{likes}</span>
                    </button>
                    <div className="flex items-center space-x-1 text-gray-600">
                      <FaComment />
                      <span>{insightData.comments}</span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-3">
                    <span className="text-gray-600 mr-2">分享：</span>
                    <a href="#" className="text-blue-600 hover:text-blue-800"><FaFacebookF /></a>
                    <a href="#" className="text-blue-400 hover:text-blue-600"><FaTwitter /></a>
                    <a href="#" className="text-blue-700 hover:text-blue-900"><FaLinkedinIn /></a>
                    <a href="#" className="text-green-600 hover:text-green-800"><FaWeixin /></a>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 作者信息 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden p-6 mb-8">
              <div className="flex items-center">
                <div className="relative h-20 w-20 rounded-full overflow-hidden">
                  <Image 
                    src={insightData.authorImage} 
                    alt={insightData.author}
                    fill
                    style={{objectFit: 'cover'}}
                  />
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-bold text-gray-800">{insightData.author}</h3>
                  <p className="text-gray-600">{insightData.authorTitle}</p>
                </div>
              </div>
              <p className="mt-4 text-gray-700">
                李博士是国际知名的文书指导专家，拥有超过10年的留学申请指导经验。她曾帮助数百名学生成功申请世界顶尖大学，并在文书写作领域发表多篇研究文章。她擅长挖掘学生的独特故事，并指导他们创作出真实、有深度且富有个人特色的申请文书。
              </p>
            </div>
          </div>
          
          {/* 侧边栏 */}
          <div className="lg:w-1/3">
            {/* 相关文章 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-800">相关文章</h3>
              </div>
              <div className="p-6 space-y-6">
                {insightData.relatedInsights.map((item, index) => (
                  <div key={index} className="flex space-x-4">
                    <div className="relative h-20 w-20 flex-shrink-0 rounded-md overflow-hidden">
                      <Image 
                        src={item.imageUrl} 
                        alt={item.title}
                        fill
                        style={{objectFit: 'cover'}}
                      />
                    </div>
                    <div>
                      <span className="text-sm text-indigo-600">{item.category}</span>
                      <Link href={`/insights/${item.id}`}>
                        <h4 className="text-gray-800 font-medium hover:text-indigo-600 transition-colors">{item.title}</h4>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* 订阅更新 */}
            <div className="bg-gradient-to-br from-indigo-800 to-indigo-600 rounded-xl shadow-md overflow-hidden mb-8 text-white">
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">订阅我们的更新</h3>
                <p className="mb-4 text-indigo-100">获取最新的留学资讯、申请技巧和成功案例分析。</p>
                <form className="space-y-3">
                  <input 
                    type="email" 
                    placeholder="您的邮箱地址" 
                    className="w-full px-4 py-2 rounded-md text-gray-800 focus:outline-none focus:ring-2 focus:ring-indigo-300"
                  />
                  <button 
                    type="submit" 
                    className="w-full bg-white text-indigo-700 font-medium px-4 py-2 rounded-md hover:bg-indigo-50 transition-colors"
                  >
                    立即订阅
                  </button>
                </form>
                <p className="mt-3 text-xs text-indigo-200">我们尊重您的隐私，绝不会分享您的信息。</p>
              </div>
            </div>
            
            {/* 热门标签 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-800">热门标签</h3>
              </div>
              <div className="p-6">
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">申请文书</span>
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">个人陈述</span>
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">文书修改</span>
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">留学申请</span>
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">美国大学</span>
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">英国大学</span>
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">面试技巧</span>
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">申请策略</span>
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">成功案例</span>
                  <span className="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm">文书范例</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsightDetailPage;