'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { FaCalendarAlt, FaUser, FaEye, FaThumbsUp, FaComment, FaFacebookF, FaTwitter, FaLinkedinIn, FaWeixin } from 'react-icons/fa';

const InsightDetailPage = () => {
  const [likes, setLikes] = useState(87);
  const [hasLiked, setHasLiked] = useState(false);
  
  const insightData = {
    id: 'insight4',
    title: '国际学生奖学金申请全攻略：机会、策略与成功案例',
    category: '奖学金',
    summary: '本文全面介绍国际学生可申请的各类奖学金，分享申请策略和技巧，并通过成功案例分析，帮助学生最大化获取奖学金的机会。',
    imageUrl: '/images/insights/insight4-detail.svg',
    date: '2023-10-25',
    author: '张教授',
    authorTitle: '留学规划专家',
    authorImage: '/team/member-1.svg',
    views: 2453,
    likes: 87,
    comments: 19,
    content: [
      {
        type: 'paragraph',
        content: '随着国际教育成本的不断上升，奖学金对于许多计划出国留学的学生来说变得尤为重要。然而，国际学生奖学金申请竞争激烈，信息繁杂，许多学生不知从何入手。本文将全面介绍国际学生可申请的各类奖学金，分享申请策略和技巧，并通过成功案例分析，帮助学生最大化获取奖学金的机会。'
      },
      {
        type: 'subheading',
        content: '一、国际学生奖学金类型概览'
      },
      {
        type: 'paragraph',
        content: '国际学生可申请的奖学金种类繁多，了解不同类型的奖学金及其特点是制定申请策略的第一步：'
      },
      {
        type: 'list',
        items: [
          '大学提供的奖学金：包括学术成就奖学金、多元化奖学金、特定专业奖学金等，通常由学校直接提供给国际学生。',
          '政府奖学金：如中国国家留学基金委奖学金、美国富布莱特奖学金、英国志奋领奖学金等，通常覆盖全部或大部分留学费用。',
          '私人基金会奖学金：如罗德奖学金、盖茨剑桥奖学金等，通常基于学术成就、领导力和社会贡献等因素。',
          '特定国家/地区奖学金：针对特定国家或地区学生的奖学金，如MEXT日本政府奖学金、德国学术交流中心(DAAD)奖学金等。',
          '特定学科奖学金：针对特定学科或研究领域的奖学金，如STEM领域、商科、艺术等专业奖学金。',
          '少数族裔/特殊群体奖学金：针对少数族裔、女性、残障人士等特定群体的奖学金。'
        ]
      },
      {
        type: 'image',
        url: '/images/insights/insight4-types.svg',
        caption: '国际学生奖学金类型及平均资助金额对比'
      },
      {
        type: 'subheading',
        content: '二、奖学金申请准备工作'
      },
      {
        type: 'paragraph',
        content: '成功的奖学金申请需要充分的准备和规划：'
      },
      {
        type: 'list',
        items: [
          '提前规划：大多数奖学金申请截止日期在入学前6-12个月，建议提前至少一年开始准备。',
          '研究目标奖学金：深入了解各奖学金的申请条件、评选标准、覆盖范围和申请流程。',
          '评估自身条件：客观评估自己的学术成绩、标准化考试成绩、课外活动、领导经验和社区服务等。',
          '准备基本申请材料：包括成绩单、推荐信、个人陈述、简历、语言成绩证明等。',
          '定制申请材料：根据不同奖学金的要求和评选标准，定制个人陈述和申请文书。',
          '寻求指导：咨询学校顾问、留学顾问或已获得奖学金的学长学姐。'
        ]
      },
      {
        type: 'quote',
        content: '奖学金申请不仅是展示你的成就，更是展示你的潜力和你将如何利用这些资源来实现更大的影响力。',
        author: '哈佛大学奖学金委员会'
      },
      {
        type: 'subheading',
        content: '三、奖学金申请策略与技巧'
      },
      {
        type: 'paragraph',
        content: '以下是提高奖学金申请成功率的关键策略和技巧：'
      },
      {
        type: 'list',
        items: [
          '申请多个奖学金：不要把所有希望寄托在一个奖学金上，同时申请多个符合条件的奖学金以增加成功机会。',
          '突出个人特色：在申请材料中强调自己独特的经历、视角和贡献，与其他申请者区分开来。',
          '量身定制申请文书：针对每个奖学金的具体要求和价值观定制申请文书，避免使用通用模板。',
          '展示领导力和影响力：通过具体例子展示你的领导能力、团队合作精神和对社区的积极影响。',
          '强调未来规划：清晰阐述你的学术和职业目标，以及获得奖学金将如何帮助你实现这些目标。',
          '准备充分的面试：许多高额奖学金需要面试环节，提前准备常见问题和个人故事。',
          '注重细节和截止日期：确保申请材料无误，并在截止日期前提交，最好提前几天完成。'
        ]
      },
      {
        type: 'subheading',
        content: '四、成功案例分析'
      },
      {
        type: 'paragraph',
        content: '以下是几个国际学生成功获得奖学金的案例分析，从中我们可以总结出一些有价值的经验：'
      },
      {
        type: 'subheading',
        content: '案例一：李同学 - 获得美国常春藤盟校全额奖学金'
      },
      {
        type: 'paragraph',
        content: '背景：李同学是一名来自普通高中的学生，GPA 3.9/4.0，托福110分，SAT 1550分。除了优秀的学术成绩外，他创立了一个环保社团，开发了一款减少校园食物浪费的应用程序，并在全国科技竞赛中获奖。'
      },
      {
        type: 'paragraph',
        content: '成功因素：李同学的申请材料突出展示了他将学术知识应用于解决实际问题的能力，以及他对环境可持续发展的持续承诺。他的个人陈述不仅讲述了他的成就，更重要的是分享了这些经历如何塑造了他的价值观和未来目标。此外，他的推荐信强调了他的创新思维和领导能力，为他的申请增添了有力的佐证。'
      },
      {
        type: 'subheading',
        content: '案例二：王同学 - 获得英国罗素集团大学奖学金'
      },
      {
        type: 'paragraph',
        content: '背景：王同学本科就读于国内211大学，GPA 3.7/4.0，雅思7.5分。她在大学期间参与了多个社会研究项目，发表了两篇学术论文，并有半年的NGO实习经验。'
      },
      {
        type: 'paragraph',
        content: '成功因素：王同学的申请成功在于她明确的研究方向和丰富的相关经验。她的研究计划书详细阐述了她想要研究的社会问题，以及这些研究如何能够产生实际影响。她还主动联系了目标大学的教授，讨论研究兴趣并获得了支持。此外，她的申请文书展示了她对英国文化和教育体系的了解，以及为什么该大学是她研究的最佳选择。'
      },
      {
        type: 'subheading',
        content: '案例三：张同学 - 获得多个小额奖学金组合资助'
      },
      {
        type: 'paragraph',
        content: '背景：张同学来自三线城市，家庭经济条件一般，GPA 3.5/4.0，托福100分。他在高中期间创办了一个为农村学生提供免费英语教学的公益项目，并坚持了三年。'
      },
      {
        type: 'paragraph',
        content: '成功因素：张同学没有获得单一的全额奖学金，而是采取了申请多个小额奖学金的策略。他针对不同奖学金的特点定制申请材料，强调自己的公益经历和对教育平等的热情。他还积极寻找针对特定背景学生（如首代大学生、经济条件有限的学生）的奖学金机会。通过组合多个奖学金，他最终获得了接近全额的资助。'
      },
      {
        type: 'subheading',
        content: '五、常见误区与注意事项'
      },
      {
        type: 'paragraph',
        content: '在奖学金申请过程中，以下是一些常见误区和需要注意的事项：'
      },
      {
        type: 'list',
        items: [
          '只关注知名奖学金：许多学生只申请知名的大型奖学金，忽略了小额奖学金和专项奖学金，实际上后者竞争可能较小，累积起来也能提供可观的资助。',
          '忽视申请要求：每个奖学金都有特定的申请要求和评选标准，确保你完全理解并满足这些要求。',
          '过度依赖学术成绩：虽然学术成绩重要，但许多奖学金也看重领导力、社区服务、创新能力等非学术因素。',
          '申请材料千篇一律：使用同一套申请材料申请不同奖学金，而不是根据具体奖学金的特点和要求进行定制。',
          '错过截止日期：奖学金申请通常有严格的截止日期，错过就意味着失去机会。',
          '低估准备时间：许多学生低估了准备高质量申请材料所需的时间，导致匆忙完成申请。',
          '忽视后续要求：获得奖学金后可能需要满足一定的学术表现要求、参加特定活动或提交报告，确保了解并遵守这些要求。'
        ]
      },
      {
        type: 'subheading',
        content: '六、奖学金申请时间规划'
      },
      {
        type: 'paragraph',
        content: '以下是奖学金申请的理想时间规划表，以秋季入学为例：'
      },
      {
        type: 'list',
        items: [
          '入学前12-18个月：开始研究奖学金机会，评估自身条件，准备标准化考试。',
          '入学前10-12个月：确定目标奖学金列表，准备基本申请材料，联系推荐人。',
          '入学前8-10个月：撰写并修改申请文书，准备作品集或其他补充材料。',
          '入学前6-8个月：提交早期奖学金申请，准备可能的面试。',
          '入学前4-6个月：提交常规截止日期的奖学金申请，跟进申请状态。',
          '入学前2-4个月：接受奖学金面试，评估奖学金offer，申请后期截止的奖学金。',
          '入学前1-2个月：确认奖学金接受程序，了解入学后的奖学金要求。'
        ]
      },
      {
        type: 'paragraph',
        content: '总之，奖学金申请是一个需要精心规划和充分准备的过程。通过了解不同类型的奖学金，制定有针对性的申请策略，准备高质量的申请材料，并从成功案例中学习经验，国际学生可以显著提高获得奖学金的机会，减轻留学经济负担，实现自己的留学梦想。'
      },
      {
        type: 'cta',
        title: '需要专业的奖学金申请指导？',
        content: '我们的专家团队可以为您提供个性化的奖学金申请规划和指导，最大化您获得奖学金的机会。',
        buttonText: '预约咨询',
        buttonLink: '/contact'
      }
    ],
    relatedInsights: [
      {
        id: 'insight1',
        title: '全球顶尖大学申请趋势分析：2023年最新数据解读',
        imageUrl: '/images/insights/insight1.svg',
        category: '留学趋势'
      },
      {
        id: 'insight2',
        title: '2024年留学申请趋势分析：新兴专业与申请策略',
        imageUrl: '/images/insights/insight2.svg',
        category: '留学趋势'
      },
      {
        id: 'insight3',
        title: '如何打造完美的大学申请文书：专家建议与案例分析',
        imageUrl: '/images/insights/insight3.svg',
        category: '申请技巧'
      }
    ]
  };

  const handleLike = () => {
    if (!hasLiked) {
      setLikes(likes + 1);
      setHasLiked(true);
    } else {
      setLikes(likes - 1);
      setHasLiked(false);
    }
  };

  return (
    <div className="bg-gray-50">
      {/* 头部区域 */}
      <div className="relative bg-gradient-to-r from-amber-900 to-amber-700 py-20">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <div className="text-amber-200 mb-2">{insightData.category}</div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">{insightData.title}</h1>
            <p className="text-xl text-amber-100 mb-8">{insightData.summary}</p>
            <div className="flex items-center justify-center text-amber-100 space-x-6">
              <div className="flex items-center">
                <FaCalendarAlt className="mr-2" />
                <span>{insightData.date}</span>
              </div>
              <div className="flex items-center">
                <FaUser className="mr-2" />
                <span>{insightData.author}</span>
              </div>
              <div className="flex items-center">
                <FaEye className="mr-2" />
                <span>{insightData.views} 阅读</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="container mx-auto px-4 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* 文章主体 */}
          <div className="lg:w-2/3">
            <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
              <div className="relative h-96 w-full">
                <Image 
                  src={insightData.imageUrl} 
                  alt={insightData.title}
                  fill
                  style={{objectFit: 'cover'}}
                  priority
                />
              </div>
              
              <div className="p-8">
                {insightData.content.map((block, index) => {
                  switch (block.type) {
                    case 'paragraph':
                      return <p key={index} className="text-gray-700 mb-6 leading-relaxed">{block.content}</p>;
                    case 'subheading':
                      return <h2 key={index} className="text-2xl font-bold text-amber-800 mb-4 mt-8">{block.content}</h2>;
                    case 'list':
                      return (
                        <ul key={index} className="list-disc pl-6 mb-6 space-y-2">
                          {block.items.map((item, i) => (
                            <li key={i} className="text-gray-700">{item}</li>
                          ))}
                        </ul>
                      );
                    case 'image':
                      return (
                        <div key={index} className="my-8">
                          <div className="relative h-80 w-full">
                            <Image 
                              src={block.url} 
                              alt={block.caption || 'Article image'}
                              fill
                              style={{objectFit: 'contain'}}
                            />
                          </div>
                          {block.caption && (
                            <p className="text-center text-gray-500 mt-2">{block.caption}</p>
                          )}
                        </div>
                      );
                    case 'quote':
                      return (
                        <blockquote key={index} className="border-l-4 border-amber-500 pl-4 py-2 my-6 bg-amber-50 rounded-r-lg">
                          <p className="text-gray-700 italic">{block.content}</p>
                          {block.author && <p className="text-gray-500 mt-2">— {block.author}</p>}
                        </blockquote>
                      );
                    case 'cta':
                      return (
                        <div key={index} className="bg-gradient-to-r from-amber-100 to-amber-50 p-6 rounded-lg my-8 border-l-4 border-amber-600">
                          <h3 className="text-xl font-bold text-amber-800 mb-2">{block.title}</h3>
                          <p className="text-gray-700 mb-4">{block.content}</p>
                          <Link href={block.buttonLink}>
                            <span className="inline-block bg-amber-600 text-white px-6 py-2 rounded-md font-medium hover:bg-amber-700 transition-colors">
                              {block.buttonText}
                            </span>
                          </Link>
                        </div>
                      );
                    default:
                      return null;
                  }
                })}
              </div>
              
              {/* 文章底部互动区 */}
              <div className="border-t border-gray-200 p-6">
                <div className="flex justify-between items-center">
                  <div className="flex space-x-4">
                    <button 
                      onClick={handleLike}
                      className={`flex items-center space-x-1 ${hasLiked ? 'text-amber-600' : 'text-gray-600'} hover:text-amber-600`}
                    >
                      <FaThumbsUp />
                      <span>{likes}</span>
                    </button>
                    <div className="flex items-center space-x-1 text-gray-600">
                      <FaComment />
                      <span>{insightData.comments}</span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-3">
                    <span className="text-gray-600 mr-2">分享：</span>
                    <a href="#" className="text-blue-600 hover:text-blue-800"><FaFacebookF /></a>
                    <a href="#" className="text-blue-400 hover:text-blue-600"><FaTwitter /></a>
                    <a href="#" className="text-blue-700 hover:text-blue-900"><FaLinkedinIn /></a>
                    <a href="#" className="text-green-600 hover:text-green-800"><FaWeixin /></a>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 作者信息 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden p-6 mb-8">
              <div className="flex items-center">
                <div className="relative h-20 w-20 rounded-full overflow-hidden">
                  <Image 
                    src={insightData.authorImage} 
                    alt={insightData.author}
                    fill
                    style={{objectFit: 'cover'}}
                  />
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-bold text-gray-800">{insightData.author}</h3>
                  <p className="text-gray-600">{insightData.authorTitle}</p>
                </div>
              </div>
              <p className="mt-4 text-gray-700">
                张教授是国际教育领域的资深专家，拥有超过20年的留学咨询和教育研究经验。他专注于国际学生奖学金申请策略研究，曾帮助数百名学生成功获得全球顶尖大学的奖学金，总金额超过1000万美元。他定期在国际教育论坛发表演讲，并出版了多本关于留学规划和奖学金申请的著作。
              </p>
            </div>
          </div>
          
          {/* 侧边栏 */}
          <div className="lg:w-1/3">
            {/* 相关文章 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden mb-8">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-800">相关文章</h3>
              </div>
              <div className="p-6 space-y-6">
                {insightData.relatedInsights.map((item, index) => (
                  <div key={index} className="flex space-x-4">
                    <div className="relative h-20 w-20 flex-shrink-0 rounded-md overflow-hidden">
                      <Image 
                        src={item.imageUrl} 
                        alt={item.title}
                        fill
                        style={{objectFit: 'cover'}}
                      />
                    </div>
                    <div>
                      <span className="text-sm text-amber-600">{item.category}</span>
                      <Link href={`/insights/${item.id}`}>
                        <h4 className="text-gray-800 font-medium hover:text-amber-600 transition-colors">{item.title}</h4>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* 订阅更新 */}
            <div className="bg-gradient-to-br from-amber-800 to-amber-600 rounded-xl shadow-md overflow-hidden mb-8 text-white">
              <div className="p-6">
                <h3 className="text-xl font-bold mb-2">订阅我们的更新</h3>
                <p className="mb-4 text-amber-100">获取最新的留学资讯、申请技巧和成功案例分析。</p>
                <form className="space-y-3">
                  <input 
                    type="email" 
                    placeholder="您的邮箱地址" 
                    className="w-full px-4 py-2 rounded-md text-gray-800 focus:outline-none focus:ring-2 focus:ring-amber-300"
                  />
                  <button 
                    type="submit" 
                    className="w-full bg-white text-amber-700 font-medium px-4 py-2 rounded-md hover:bg-amber-50 transition-colors"
                  >
                    立即订阅
                  </button>
                </form>
                <p className="mt-3 text-xs text-amber-200">我们尊重您的隐私，绝不会分享您的信息。</p>
              </div>
            </div>
            
            {/* 热门标签 */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-800">热门标签</h3>
              </div>
              <div className="p-6">
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm">奖学金申请</span>
                  <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm">留学资助</span>
                  <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm">申请文书</span>
                  <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm">面试技巧</span>
                  <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm">美国大学</span>
                  <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm">英国大学</span>
                  <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm">政府奖学金</span>
                  <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm">基金会奖学金</span>
                  <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm">成功案例</span>
                  <span className="px-3 py-1 bg-amber-100 text-amber-800 rounded-full text-sm">留学规划</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InsightDetailPage;