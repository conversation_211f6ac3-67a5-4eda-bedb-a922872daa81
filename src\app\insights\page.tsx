'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { FiArrowRight, FiCalendar, FiUser, FiSearch, FiMail } from 'react-icons/fi';

// 媒体内容分类数据
const insightCategories = [
  { id: 'all', name: '全部内容' },
  { id: 'news', name: '新闻动态' },
  { id: 'articles', name: '教育文章' },
  { id: 'research', name: '研究报告' },
  { id: 'events', name: '活动预告' },
];

// 媒体内容数据
const insights = [
  {
    id: 'insight1',
    title: '2023年高考志愿填报策略与分析',
    category: 'articles',
    summary: '本文深入分析2023年高考形势，提供科学的志愿填报策略和方法，帮助考生和家长做出明智的选择。',
    image: '/images/insight1.jpg',
    date: '2023-06-10',
    author: '王教育顾问',
  },
  {
    id: 'insight2',
    title: '人工智能时代的职业规划新思路',
    category: 'research',
    summary: '随着AI技术的发展，传统职业面临巨大变革，本报告探讨了未来就业趋势和个人职业规划的新思路。',
    image: '/images/insight2.jpg',
    date: '2023-05-25',
    author: '李职业顾问',
  },
  {
    id: 'insight3',
    title: '思立恒教育荣获"年度最具影响力教育机构"奖项',
    category: 'news',
    summary: '在近日举办的2023教育行业峰会上，思立恒教育凭借专业的服务和显著的社会影响力获得重要奖项。',
    image: '/images/insight3.jpg',
    date: '2023-04-18',
    author: '思立恒新闻部',
  },
  {
    id: 'insight4',
    title: '如何培养孩子的学习兴趣与自主学习能力',
    category: 'articles',
    summary: '本文分享了培养孩子学习兴趣和自主学习能力的实用方法，帮助家长更好地引导孩子的教育成长。',
    image: '/images/insight4.jpg',
    date: '2023-03-30',
    author: '张教育顾问',
  },
  {
    id: 'insight5',
    title: '2023年留学申请趋势报告',
    category: 'research',
    summary: '本报告基于大量数据分析，揭示了2023年国际留学市场的最新趋势、热门专业和申请策略。',
    image: '/images/insight5.jpg',
    date: '2023-02-15',
    author: '刘留学顾问',
  },
  {
    id: 'insight6',
    title: '职场软技能提升工作坊即将举办',
    category: 'events',
    summary: '思立恒教育将于下月举办职场软技能提升工作坊，帮助职场人士提升沟通、领导力和团队协作能力。',
    image: '/images/insight6.jpg',
    date: '2023-01-20',
    author: '思立恒活动部',
  },
];

export default function InsightsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');

  // 根据当前选中的分类和搜索词筛选内容
  const filteredInsights = insights.filter(item => {
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    const matchesSearch = searchQuery === '' || 
      item.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      item.summary.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <main className="min-h-screen">
      {/* 页面标题区 */}
      <section className="relative py-24 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-sky-400 opacity-90"></div>
        <div className="absolute inset-0 opacity-20" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block px-4 py-1 bg-white/20 text-white rounded-full text-sm font-medium mb-6 backdrop-blur-sm">资讯中心</span>
            <h1 className="text-5xl font-bold mb-6 text-white">媒体中心</h1>
            <p className="text-xl text-white/90 mb-10">获取最新的教育资讯、行业动态和专业观点</p>
            <div className="relative max-w-xl mx-auto">
              <input 
                type="text" 
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="搜索文章、新闻或研究报告..." 
                className="w-full px-6 py-4 pl-12 rounded-full border-2 border-white/30 bg-white/10 backdrop-blur-sm text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent transition-all duration-300"
              />
              <FiSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/70" size={20} />
              <button className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white text-blue-600 px-4 py-2 rounded-full hover:bg-blue-50 transition-all duration-300 font-medium">
                搜索
              </button>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-white to-transparent"></div>
      </section>

      {/* 内容分类与列表 */}
      <section className="py-20 bg-gradient-to-b from-white via-sky-50 to-white">
        <div className="container mx-auto px-4">
          {/* 分类导航 */}
          <div className="flex flex-wrap justify-center mb-12">
            {insightCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-6 py-3 m-2 rounded-full transition-all duration-300 font-medium ${activeCategory === category.id 
                  ? 'bg-gradient-to-r from-blue-600 to-sky-500 text-white shadow-md shadow-blue-200' 
                  : 'bg-white text-gray-700 hover:bg-gray-50 hover:shadow-md border border-gray-100'}`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* 内容列表 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredInsights.length > 0 ? (
              filteredInsights.map((item) => (
                <div 
                  key={item.id} 
                  className="bg-white rounded-xl shadow-lg shadow-blue-100/50 overflow-hidden transition-all duration-500 hover:shadow-xl hover:-translate-y-2 border border-gray-100 group"
                >
                  <div className="relative h-56 overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-t from-blue-900/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>
                    <Image 
                      src={item.image} 
                      alt={item.title}
                      width={400}
                      height={300}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                    <div className="absolute top-4 right-4 bg-blue-600 text-white text-xs font-medium px-3 py-1 rounded-full z-20">
                      {insightCategories.find(c => c.id === item.category)?.name}
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center text-sm text-gray-500 mb-3">
                      <span className="flex items-center mr-4 text-blue-500">
                        <FiCalendar className="mr-1" /> {item.date}
                      </span>
                      <span className="flex items-center text-blue-500">
                        <FiUser className="mr-1" /> {item.author}
                      </span>
                    </div>
                    <h3 className="text-xl font-bold mb-3 text-gray-800 group-hover:text-blue-600 transition-colors">{item.title}</h3>
                    <p className="text-gray-600 mb-5">{item.summary}</p>
                    <Link 
                      href={`/insights/${item.id}`} 
                      className="inline-flex items-center text-blue-600 font-medium hover:text-blue-800 group/link"
                    >
                      阅读更多 <FiArrowRight className="ml-1 group-hover/link:ml-2 transition-all duration-300" />
                    </Link>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-3 py-16 text-center">
                <div className="bg-blue-50 rounded-xl p-8 max-w-2xl mx-auto">
                  <FiSearch className="mx-auto text-blue-500 mb-4" size={48} />
                  <h3 className="text-xl font-bold mb-2 text-gray-800">未找到相关内容</h3>
                  <p className="text-gray-600 mb-4">尝试使用不同的搜索词或浏览其他分类</p>
                  <button 
                    onClick={() => {
                      setSearchQuery('');
                      setActiveCategory('all');
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    查看全部内容
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* 订阅区域 */}
      <section className="py-20 bg-gradient-to-b from-white to-sky-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block px-4 py-1 bg-blue-50 text-blue-600 rounded-full text-sm font-medium mb-4">定期更新</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800 relative inline-block">
              订阅我们的教育资讯
              <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full"></span>
            </h2>
            <p className="text-xl text-gray-600 mb-10 max-w-3xl mx-auto">定期获取最新的教育趋势、规划方法和成功案例，助力您做出更明智的教育决策</p>
            <div className="flex flex-col md:flex-row gap-4 max-w-2xl mx-auto">
              <div className="relative flex-1">
                <FiMail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-blue-400" size={20} />
                <input 
                  type="email" 
                  placeholder="您的邮箱地址" 
                  className="w-full px-6 py-4 pl-12 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent shadow-md"
                />
              </div>
              <button className="px-8 py-4 bg-gradient-to-r from-blue-600 to-sky-500 text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300 flex items-center justify-center transform hover:-translate-y-1 group">
                立即订阅 <FiArrowRight className="ml-2 group-hover:ml-3 transition-all duration-300" />
              </button>
            </div>
            <p className="text-sm text-gray-500 mt-4">我们尊重您的隐私，绝不会向第三方分享您的信息</p>
          </div>
        </div>
      </section>

      {/* 常见问题快速入口 */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-700 to-sky-500 opacity-90"></div>
        <div className="absolute inset-0 opacity-10" style={{ backgroundImage: 'url(/images/pattern-bg.png)', backgroundRepeat: 'repeat' }}></div>
        <div className="container mx-auto px-4 relative z-10 text-center">
          <span className="inline-block px-4 py-1 bg-white/20 text-white rounded-full text-sm font-medium mb-6 backdrop-blur-sm">常见问题</span>
          <h2 className="text-4xl font-bold mb-6 text-white">有问题需要解答？</h2>
          <p className="text-xl mb-10 max-w-3xl mx-auto text-white/90">浏览我们的常见问题解答，或直接联系我们获取专业指导</p>
          <div className="flex flex-col sm:flex-row justify-center gap-6">
            <Link 
              href="/insights?category=faq" 
              className="inline-flex items-center justify-center px-8 py-4 bg-white text-blue-600 font-medium rounded-lg hover:bg-blue-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 group"
            >
              常见问题 <FiArrowRight className="ml-2 group-hover:ml-3 transition-all duration-300" />
            </Link>
            <Link 
              href="/contact" 
              className="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-300 backdrop-blur-sm group"
            >
              联系我们 <FiArrowRight className="ml-2 opacity-0 group-hover:opacity-100 group-hover:ml-3 transition-all duration-300" />
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}