'use client';

import { Inter } from "next/font/google";
import type { Metadata } from "next";
import "./globals.css";
import Link from "next/link";
import { useState } from "react";

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
});

// export const metadata: Metadata = {
//   title: "武汉思立恒教育科技有限公司 - 您的教育与生涯导航专家",
//   description: "武汉思立恒教育科技有限公司提供专业的教育规划与生涯导航服务，覆盖学前及K12教育规划、高中一体化升学规划、大学发展与深造规划、职业人士生涯进阶服务。",
//   keywords: "教育规划,生涯规划,升学规划,高考志愿填报,职业规划,武汉思立恒"
// };

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  return (
    <html lang="zh-CN">
      <body className={inter.className} suppressHydrationWarning>
        <header className="bg-white shadow-sm sticky top-0 z-50">
          <div className="container mx-auto px-4 py-4">
            <div className="flex justify-between items-center">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xl">SLH</div>
                <div className="text-2xl font-bold text-blue-800">思立恒教育</div>
              </Link>
              <nav className="hidden md:flex space-x-1">
                <Link href="/" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200">首页</Link>
                <Link href="/about" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200">关于我们</Link>
                <Link href="/services" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200">服务体系</Link>
                <Link href="/consultants" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200">专业咨询师</Link>
                <Link href="/cases" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200">成功案例</Link>
                <Link href="/insights" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200">媒体中心</Link>
                <Link href="/appointment" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200">在线预约</Link>
                <Link href="/contact" className="ml-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">联系我们</Link>
              </nav>
              <div className="md:hidden">
                <button className="p-2 rounded-md bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors" onClick={() => setMobileMenuOpen(!mobileMenuOpen)} aria-label="打开菜单">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
          {/* 移动端菜单抽屉 */}
          {mobileMenuOpen && (
            <div className="fixed inset-0 z-50 bg-black bg-opacity-40 md:hidden" onClick={() => setMobileMenuOpen(false)}>
              <div className="absolute top-0 right-0 w-64 h-full bg-white shadow-lg p-6 flex flex-col" onClick={e => e.stopPropagation()}>
                <button className="self-end mb-6 p-2 rounded-md bg-gray-100 text-gray-800 hover:bg-gray-200 transition-colors" onClick={() => setMobileMenuOpen(false)} aria-label="关闭菜单">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <nav className="flex flex-col space-y-4">
                  <Link href="/" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200" onClick={() => setMobileMenuOpen(false)}>首页</Link>
                  <Link href="/about" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200" onClick={() => setMobileMenuOpen(false)}>关于我们</Link>
                  <Link href="/services" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200" onClick={() => setMobileMenuOpen(false)}>服务体系</Link>
                  <Link href="/consultants" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200" onClick={() => setMobileMenuOpen(false)}>专业咨询师</Link>
                  <Link href="/cases" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200" onClick={() => setMobileMenuOpen(false)}>成功案例</Link>
                  <Link href="/insights" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200" onClick={() => setMobileMenuOpen(false)}>媒体中心</Link>
                  <Link href="/appointment" className="px-4 py-2 text-gray-800 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors duration-200" onClick={() => setMobileMenuOpen(false)}>在线预约</Link>
                  <Link href="/contact" className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200" onClick={() => setMobileMenuOpen(false)}>联系我们</Link>
                </nav>
              </div>
            </div>
          )}
        </header>
        {children}
        <footer className="bg-gradient-to-b from-gray-800 to-gray-900 text-white py-12">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div className="md:pr-6">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xl">SLH</div>
                  <h3 className="text-xl font-bold">思立恒教育</h3>
                </div>
                <p className="mb-4 text-gray-300">您的教育与生涯导航专家，致力于为每一位学生和职场人士提供专业的规划指导。</p>
                <div className="space-y-2 text-gray-300">
                  <p className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    177-0272-0924
                  </p>
                  <p className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <EMAIL>
                  </p>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-bold mb-4 text-blue-300">快速链接</h3>
                <ul className="space-y-2">
                  <li><Link href="/" className="text-gray-300 hover:text-blue-300 transition-colors duration-200">首页</Link></li>
                  <li><Link href="/about" className="text-gray-300 hover:text-blue-300 transition-colors duration-200">关于我们</Link></li>
                  <li><Link href="/services" className="text-gray-300 hover:text-blue-300 transition-colors duration-200">服务体系</Link></li>
                  <li><Link href="/cases" className="text-gray-300 hover:text-blue-300 transition-colors duration-200">成功案例</Link></li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-bold mb-4 text-blue-300">服务项目</h3>
                <ul className="space-y-2">
                  <li><Link href="/services/k12" className="text-gray-300 hover:text-blue-300 transition-colors duration-200">学前及K12教育规划</Link></li>
                  <li><Link href="/services/high-school" className="text-gray-300 hover:text-blue-300 transition-colors duration-200">高中一体化升学规划</Link></li>
                  <li><Link href="/services/university" className="text-gray-300 hover:text-blue-300 transition-colors duration-200">大学发展与深造规划</Link></li>
                  <li><Link href="/services/career" className="text-gray-300 hover:text-blue-300 transition-colors duration-200">职业人士生涯进阶服务</Link></li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-bold mb-4 text-blue-300">关注我们</h3>
                <p className="mb-3 text-gray-300">扫描下方二维码联系我们</p>
                <div className="w-32 h-32 bg-white rounded-lg p-2 shadow-lg">
                  <img src="/images/qrcode.svg" alt="思立恒教育微信二维码" className="w-full h-full" />
                </div>
              </div>
            </div>
            <div className="mt-10 pt-6 border-t border-gray-700 text-center">
              <div className="flex flex-col md:flex-row justify-between items-center">
                <div className="text-gray-400 text-sm">
                  <p>© {new Date().getFullYear()} 武汉思立恒教育科技有限公司 版权所有</p>
                  <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank" rel="noopener noreferrer" className="hover:text-blue-300 transition-colors duration-200">鄂ICP备2023003821号-1</a>
                </div>
                <div className="flex space-x-4 mt-4 md:mt-0">
                  <a href="#" className="text-gray-400 hover:text-blue-300 transition-colors duration-200">
                    <span className="sr-only">隐私政策</span>
                    隐私政策
                  </a>
                  <a href="#" className="text-gray-400 hover:text-blue-300 transition-colors duration-200">
                    <span className="sr-only">使用条款</span>
                    使用条款
                  </a>
                </div>
              </div>
            </div>
          </div>
        </footer>
      </body>
    </html>
  );
}
