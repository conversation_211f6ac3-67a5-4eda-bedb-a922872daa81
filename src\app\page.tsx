'use client';

import { useState, useEffect } from 'react';
import Image from "next/image";
import Link from "next/link";
import { FaChalkboardTeacher, FaUserGraduate, FaUniversity, FaBriefcase } from 'react-icons/fa';
import { getServices, getPublishedArticles } from '@/lib/api-client';

export default function Home() {
  const [services, setServices] = useState([]);
  const [articles, setArticles] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [servicesRes, articlesRes] = await Promise.all([
          getServices(),
          getPublishedArticles({ limit: 3 })
        ]);

        if (servicesRes.success) setServices(servicesRes.data.items || servicesRes.data);
        if (articlesRes.success) setArticles(articlesRes.data.items || articlesRes.data);
      } catch (error) {
        console.error('获取首页数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);
  return (
    <main className="min-h-screen">
      {/* 顶部Banner区域 - 更新后 */}
      <section className="relative h-[550px] md:h-[600px] overflow-hidden">
        {/* 背景图片 - 请替换为实际图片路径 */}
        <Image
          src="/hero/modern-education-banner.svg" // 使用SVG矢量图作为首页背景
          alt="现代教育与科技融合"
          layout="fill"
          objectFit="cover"
          quality={85}
          className="z-0"
        />
        {/* 渐变遮罩层，增强文字可读性 - 调整渐变色 */}
        <div className="absolute inset-0 bg-gradient-to-r from-sky-700/90 via-sky-600/70 to-blue-500/50 z-10"></div>

        {/* 内容区域 */}
        <div className="container mx-auto px-4 h-full flex flex-col justify-center items-start relative z-20 text-white">
          <div className="max-w-3xl">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold mb-6 leading-tight tracking-tight">
              <span className="block">解码教育新生态，</span>
              <span className="block text-sky-300">赋能个体卓越成长</span>
            </h1>
            <p className="text-lg sm:text-xl md:text-2xl mb-10 text-gray-100 max-w-2xl">
              思立恒教育，您身边的教育与生涯导航专家，用专业与智慧点亮您的未来之路。
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <a
                href="/appointment"
                className="bg-sky-500 hover:bg-sky-400 text-white px-8 py-4 rounded-lg font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-sky-300 focus:ring-offset-2 focus:ring-offset-sky-700"
              >
                立即预约咨询
              </a>
              <a
                href="/about"
                className="bg-white/25 hover:bg-white/35 backdrop-blur-sm text-white px-8 py-4 rounded-lg font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-sky-200 focus:ring-offset-2 focus:ring-offset-sky-700"
              >
                了解我们
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* 公司简介/引言区域 - 调整背景和文字颜色 */}
      <section className="py-16 md:py-20 bg-gradient-to-b from-sky-50 to-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-sky-800">解码教育新生态，赋能个体卓越成长</h2>
            <p className="text-lg text-slate-700 mb-8">
              武汉思立恒教育科技有限公司致力于为学生和职场人士提供全方位的教育规划与生涯导航服务。
              我们以专业的态度、科学的方法和个性化的方案，帮助每一位客户找到最适合自己的发展路径。
            </p>
            <a href="/about" className="inline-block border-2 border-sky-600 text-sky-600 hover:bg-sky-600 hover:text-white px-8 py-3 rounded-lg font-medium transition-colors duration-300 text-lg transform hover:scale-105">
              了解更多
            </a>
          </div>
        </div>
      </section>

      {/* 核心服务概览 - 统一卡片背景渐变，调整文字和图标颜色 */}
      <section className="py-16 md:py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center text-sky-800">我们的核心服务</h2>
          {loading ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">加载中...</div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {services.slice(0, 4).map((service, index) => {
                const icons = [FaChalkboardTeacher, FaUserGraduate, FaUniversity, FaBriefcase];
                const IconComponent = icons[index] || FaChalkboardTeacher;

                return (
                  <div key={service.id} className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div className="h-52 bg-gradient-to-br from-sky-500 via-sky-600 to-blue-600 flex items-center justify-center p-6 shadow-inner group-hover:shadow-lg transition-shadow duration-300">
                      {service.icon_url ? (
                        <Image
                          src={service.icon_url}
                          alt={service.title}
                          width={96}
                          height={96}
                          className="text-white opacity-90 drop-shadow-lg group-hover:opacity-100 group-hover:scale-110 transition-all duration-300"
                        />
                      ) : (
                        <IconComponent className="text-6xl text-white opacity-90 drop-shadow-lg group-hover:opacity-100 group-hover:scale-110 transition-all duration-300" />
                      )}
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-3 text-sky-700 group-hover:text-sky-600 transition-colors duration-300">
                        {service.title}
                      </h3>
                      <p className="text-slate-600 text-sm mb-4 h-20 overflow-hidden">
                        {service.description || '专业的服务，为您提供最优质的解决方案。'}
                      </p>
                      <a
                        href={`/services/${service.slug || service.id}`}
                        className="inline-block text-sky-600 hover:text-sky-500 font-medium group-hover:underline transition-all duration-300"
                      >
                        了解详情 →
                      </a>
                    </div>
                  </div>
                );
              })}

              {/* 如果服务数量不足4个，显示默认服务 */}
              {services.length === 0 && (
                <>
                  <div className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div className="h-52 bg-gradient-to-br from-sky-500 via-sky-600 to-blue-600 flex items-center justify-center p-6 shadow-inner group-hover:shadow-lg transition-shadow duration-300">
                      <FaChalkboardTeacher className="text-6xl text-white opacity-90 drop-shadow-lg group-hover:opacity-100 group-hover:scale-110 transition-all duration-300" />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-3 text-sky-700 group-hover:text-sky-600 transition-colors duration-300">学前及K12教育规划</h3>
                      <p className="text-slate-600 text-sm mb-4 h-20 overflow-hidden">为孩子提供全面的教育评估与规划，包括学校选择、课程设置、学习方法指导和能力培养，助力孩子在关键成长阶段打造坚实基础，培养核心素养与学习能力。</p>
                      <Link href="/services" className="inline-block text-sky-600 hover:text-sky-500 font-medium group-hover:underline transition-all duration-300">了解详情 →</Link>
                    </div>
                  </div>
                  <div className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div className="h-52 bg-gradient-to-br from-sky-500 via-sky-600 to-blue-600 flex items-center justify-center p-6 shadow-inner group-hover:shadow-lg transition-shadow duration-300">
                      <FaUserGraduate className="text-6xl text-white opacity-90 drop-shadow-lg group-hover:opacity-100 group-hover:scale-110 transition-all duration-300" />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-3 text-sky-700 group-hover:text-sky-600 transition-colors duration-300">高中一体化升学规划</h3>
                      <p className="text-slate-600 text-sm mb-4 h-20 overflow-hidden">提供科学选科指导、学业规划、背景提升和志愿填报全流程服务，结合学生个人特质与高校专业需求，制定个性化升学策略，最大化录取机会，助力学生进入理想大学。</p>
                      <Link href="/services" className="inline-block text-sky-600 hover:text-sky-500 font-medium group-hover:underline transition-all duration-300">了解详情 →</Link>
                    </div>
                  </div>
                  <div className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div className="h-52 bg-gradient-to-br from-sky-500 via-sky-600 to-blue-600 flex items-center justify-center p-6 shadow-inner group-hover:shadow-lg transition-shadow duration-300">
                      <FaUniversity className="text-6xl text-white opacity-90 drop-shadow-lg group-hover:opacity-100 group-hover:scale-110 transition-all duration-300" />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-3 text-sky-700 group-hover:text-sky-600 transition-colors duration-300">大学发展与深造规划</h3>
                      <p className="text-slate-600 text-sm mb-4 h-20 overflow-hidden">为大学生提供专业发展指导、学术能力提升、科研竞赛参与策略和考研留学规划，结合学生兴趣与职业发展方向，制定全面的大学生涯发展路径，助力实现学术与职业价值。</p>
                      <Link href="/services" className="inline-block text-sky-600 hover:text-sky-500 font-medium group-hover:underline transition-all duration-300">了解详情 →</Link>
                    </div>
                  </div>
                  <div className="group bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">
                    <div className="h-52 bg-gradient-to-br from-sky-500 via-sky-600 to-blue-600 flex items-center justify-center p-6 shadow-inner group-hover:shadow-lg transition-shadow duration-300">
                      <FaBriefcase className="text-6xl text-white opacity-90 drop-shadow-lg group-hover:opacity-100 group-hover:scale-110 transition-all duration-300" />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-3 text-sky-700 group-hover:text-sky-600 transition-colors duration-300">职业人士生涯进阶服务</h3>
                      <p className="text-slate-600 text-sm mb-4 h-20 overflow-hidden">为职场人士提供职业定位与规划、核心竞争力提升、行业转型指导和领导力发展服务，结合个人优势与市场需求，制定切实可行的职业发展策略，助力实现职场突破与持续成长。</p>
                      <Link href="/services" className="inline-block text-sky-600 hover:text-sky-500 font-medium group-hover:underline transition-all duration-300">了解详情 →</Link>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </section>

      {/* 为什么选择思立恒 - 调整背景渐变和卡片样式 */}
      <section className="py-20 bg-gradient-to-b from-sky-100 to-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-16 text-center text-sky-800">思立恒的独特价值主张</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
            {/* 优势1 */}
            <div className="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 flex flex-col items-center text-center transform hover:scale-105">
              <div className="w-24 h-24 rounded-full bg-sky-100 flex items-center justify-center mb-6 border-4 border-sky-200 shadow-md">
                <FaChalkboardTeacher className="text-5xl text-sky-600" />
              </div>
              <h3 className="text-2xl font-semibold mb-4 text-sky-700">深度个性化</h3>
              <p className="text-slate-600 leading-relaxed">根据每位客户的独特特质、兴趣爱好、能力优势和未来目标，量身打造专属的发展规划方案。</p>
            </div>

            {/* 优势2 */}
            <div className="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 flex flex-col items-center text-center transform hover:scale-105">
              <div className="w-24 h-24 rounded-full bg-sky-100 flex items-center justify-center mb-6 border-4 border-sky-200 shadow-md">
                <FaUserGraduate className="text-5xl text-sky-600" />
              </div>
              <h3 className="text-2xl font-semibold mb-4 text-sky-700">专业数据驱动</h3>
              <p className="text-slate-600 leading-relaxed">运用科学的测评工具和行业大数据分析，提供客观、精准、前瞻性的规划建议与决策支持。</p>
            </div>

            {/* 优势3 */}
            <div className="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 flex flex-col items-center text-center transform hover:scale-105">
              <div className="w-24 h-24 rounded-full bg-sky-100 flex items-center justify-center mb-6 border-4 border-sky-200 shadow-md">
                <FaBriefcase className="text-5xl text-sky-600" />
              </div>
              <h3 className="text-2xl font-semibold mb-4 text-sky-700">全程陪伴式服务</h3>
              <p className="text-slate-600 leading-relaxed">从初步咨询、规划制定到方案执行与动态调整，我们提供持续的、全方位的指导与支持。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 客户见证/成功案例摘要 - 调整卡片样式和颜色 */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-16 text-center text-sky-800">来自客户的真实声音</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
            {/* 见证1 */}
            <div className="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 flex flex-col transform hover:scale-105">
              <div className="flex-grow mb-6">
                <p className="text-slate-700 italic text-lg leading-relaxed before:content-['“'] before:mr-1 before:text-sky-500 before:font-serif before:text-3xl after:content-['”'] after:ml-1 after:text-sky-500 after:font-serif after:text-3xl">
                  思立恒的选科指导非常专业，帮助我儿子找到了最适合他的科目组合，最终顺利考入了理想的大学计算机专业。感谢思立恒！
                </p>
              </div>
              <div className="flex items-center mt-auto pt-6 border-t border-sky-100">
                <div className="w-14 h-14 rounded-full bg-gradient-to-br from-sky-400 to-blue-500 text-white flex items-center justify-center text-2xl font-semibold mr-4 shadow-md">张</div>
                <div>
                  <p className="font-semibold text-sky-700 text-lg">张先生</p>
                  <p className="text-sm text-sky-600">高中生家长</p>
                </div>
              </div>
            </div>

            {/* 见证2 */}
            <div className="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 flex flex-col transform hover:scale-105">
              <div className="flex-grow mb-6">
                <p className="text-slate-700 italic text-lg leading-relaxed before:content-['“'] before:mr-1 before:text-sky-500 before:font-serif before:text-3xl after:content-['”'] after:ml-1 after:text-sky-500 after:font-serif after:text-3xl">
                  通过思立恒的大学生涯规划，我不仅明确了学术发展方向，还成功保研至北京某知名高校。他们的指导非常实用。
                </p>
              </div>
              <div className="flex items-center mt-auto pt-6 border-t border-sky-100">
                <div className="w-14 h-14 rounded-full bg-gradient-to-br from-sky-400 to-blue-500 text-white flex items-center justify-center text-2xl font-semibold mr-4 shadow-md">李</div>
                <div>
                  <p className="font-semibold text-sky-700 text-lg">李同学</p>
                  <p className="text-sm text-sky-600">大学生</p>
                </div>
              </div>
            </div>

            {/* 见证3 */}
            <div className="bg-white p-8 rounded-xl shadow-xl hover:shadow-2xl transition-shadow duration-300 flex flex-col transform hover:scale-105">
              <div className="flex-grow mb-6">
                <p className="text-slate-700 italic text-lg leading-relaxed before:content-['“'] before:mr-1 before:text-sky-500 before:font-serif before:text-3xl after:content-['”'] after:ml-1 after:text-sky-500 after:font-serif after:text-3xl">
                  在职业发展的十字路口，思立恒的职业规划咨询帮助我成功实现了行业转型，找到了更适合自己的职业发展路径，非常感谢！
                </p>
              </div>
              <div className="flex items-center mt-auto pt-6 border-t border-sky-100">
                <div className="w-14 h-14 rounded-full bg-gradient-to-br from-sky-400 to-blue-500 text-white flex items-center justify-center text-2xl font-semibold mr-4 shadow-md">王</div>
                <div>
                  <p className="font-semibold text-sky-700 text-lg">王女士</p>
                  <p className="text-sm text-sky-600">职场人士</p>
                </div>
              </div>
            </div>
          </div>
           <div className="text-center mt-16">
             <Link href="/cases" className="inline-block bg-sky-600 hover:bg-sky-700 text-white px-10 py-4 rounded-lg font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-sky-400 focus:ring-offset-2 focus:ring-offset-slate-50">
               查看更多成功案例
             </Link>
           </div>
        </div>
      </section>

      {/* 最新洞察/博客摘要 - 调整背景、卡片和按钮样式 */}
      <section className="py-20 bg-gradient-to-b from-white to-sky-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl md:text-4xl font-bold mb-16 text-center text-sky-800">教育前沿观察</h2>
          {loading ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">加载中...</div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {articles.slice(0, 4).map((article) => (
                <div key={article.id} className="group bg-white rounded-xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                  {article.cover_image_url ? (
                    <Image
                      src={article.cover_image_url}
                      alt={article.title}
                      width={600}
                      height={400}
                      className="w-full h-56 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  ) : (
                    <div className="w-full h-56 bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
                      <span className="text-white text-lg font-medium">教育观察</span>
                    </div>
                  )}
                  <div className="p-6">
                    <p className="text-sm text-sky-600 mb-2">
                      {new Date(article.published_at || article.created_at).toLocaleDateString()}
                    </p>
                    <h3 className="text-xl font-semibold mb-3 text-sky-700 group-hover:text-sky-600 transition-colors duration-300">
                      {article.title}
                    </h3>
                    <p className="text-slate-600 text-sm mb-4 h-20 overflow-hidden">
                      {article.summary || '深入探讨教育发展趋势，为您提供前沿的教育洞察。'}
                    </p>
                    <a
                      href={`/articles/${article.slug || article.id}`}
                      className="inline-block text-sky-600 hover:text-sky-500 font-medium group-hover:underline transition-all duration-300"
                    >
                      阅读更多 →
                    </a>
                  </div>
                </div>
              ))}

              {/* 如果没有文章，显示默认内容 */}
              {articles.length === 0 && (
                <>
                  <div className="group bg-white rounded-xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <div className="w-full h-56 bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
                      <FaChalkboardTeacher className="text-5xl text-white opacity-90" />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-3 text-sky-700 group-hover:text-sky-600 transition-colors duration-300">学前及K12教育规划趋势</h3>
                      <p className="text-slate-600 text-sm mb-4 h-20 overflow-hidden">当今K12教育强调个性化学习路径和综合素质培养，科技辅助教学和跨学科整合成为主流，家校协同和早期生涯规划意识日益重要。</p>
                      <a href="/articles" className="inline-block text-sky-600 hover:text-sky-500 font-medium group-hover:underline transition-all duration-300">阅读更多 →</a>
                    </div>
                  </div>
                  <div className="group bg-white rounded-xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <div className="w-full h-56 bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
                      <FaUserGraduate className="text-5xl text-white opacity-90" />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-3 text-sky-700 group-hover:text-sky-600 transition-colors duration-300">高中升学规划新思路</h3>
                      <p className="text-slate-600 text-sm mb-4 h-20 overflow-hidden">高考综合改革背景下，科学选科与生涯规划紧密结合，学科特长与综合素质并重，多元评价体系要求学生全面发展，个性化升学方案成为制胜关键。</p>
                      <a href="/articles" className="inline-block text-sky-600 hover:text-sky-500 font-medium group-hover:underline transition-all duration-300">阅读更多 →</a>
                    </div>
                  </div>
                  <div className="group bg-white rounded-xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <div className="w-full h-56 bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
                      <FaUniversity className="text-5xl text-white opacity-90" />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-3 text-sky-700 group-hover:text-sky-600 transition-colors duration-300">大学发展与深造新动向</h3>
                      <p className="text-slate-600 text-sm mb-4 h-20 overflow-hidden">跨学科融合成为学术发展主流，产学研一体化培养模式日益普及，国际交流与合作机会增多，数字化能力与创新思维成为核心竞争力，多元化深造路径助力个性发展。</p>
                      <a href="/articles" className="inline-block text-sky-600 hover:text-sky-500 font-medium group-hover:underline transition-all duration-300">阅读更多 →</a>
                    </div>
                  </div>
                  <div className="group bg-white rounded-xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
                    <div className="w-full h-56 bg-gradient-to-br from-sky-400 to-blue-500 flex items-center justify-center">
                      <FaBriefcase className="text-5xl text-white opacity-90" />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold mb-3 text-sky-700 group-hover:text-sky-600 transition-colors duration-300">职业人士生涯进阶策略</h3>
                      <p className="text-slate-600 text-sm mb-4 h-20 overflow-hidden">数字化转型催生新职业与技能需求，终身学习成为职场常态，跨界复合型人才备受青睐，灵活工作方式与多元职业身份兴起，主动规划与持续成长是职业发展核心。</p>
                      <a href="/articles" className="inline-block text-sky-600 hover:text-sky-500 font-medium group-hover:underline transition-all duration-300">阅读更多 →</a>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
          <div className="text-center mt-16">
            <a href="/articles" className="inline-block bg-sky-600 hover:bg-sky-700 text-white px-10 py-4 rounded-lg font-semibold text-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-sky-400 focus:ring-offset-2 focus:ring-offset-sky-50">
              探索更多洞察
            </a>
          </div>
        </div>
      </section>

      {/* 联系我们/CTA区域 - 调整背景、文字和按钮样式 */}
      <section className="py-20 bg-gradient-to-r from-sky-600 via-blue-600 to-sky-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">准备好开启您的卓越成长之旅了吗？</h2>
          <p className="text-lg md:text-xl mb-10 max-w-2xl mx-auto text-sky-100">
            立即联系思立恒教育的专业顾问团队，让我们一同为您量身定制个性化的教育与生涯发展方案。
          </p>
          <a
            href="/appointment"
            className="bg-white hover:bg-sky-50 text-sky-700 px-10 py-4 rounded-lg font-semibold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-sky-600"
          >
            免费预约咨询
          </a>
        </div>
      </section>
    </main>
  );
}
