'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiTarget, FiBarChart2, FiUsers, FiCheck, FiBook, FiActivity, FiHeart, FiStar, FiCompass } from 'react-icons/fi';

export default function CareerAssessmentPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">职业定位与发展规划</h1>
            <p className="text-xl text-blue-100">
              科学评估职业能力与潜力，明确职业定位，制定个性化职业发展路径，助力职场人士实现长期职业目标。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                我们的职业定位与发展规划服务，由资深职业顾问和行业专家团队提供，通过科学的评估工具和专业的咨询方法，帮助职场人士清晰认识自我，明确职业定位，制定切实可行的职业发展规划。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                无论您是刚刚步入职场的新人，还是寻求突破的资深职场人士，我们都能为您提供个性化的职业发展解决方案，助您在职场中找到最适合的发展路径，实现长期职业目标。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiCompass className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">职业能力与潜力评估</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>多维度职业能力测评与分析</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>职业性格与工作风格评估</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>职业兴趣与价值观探索</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">职业定位与目标设定</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>个人优势与职业匹配分析</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>职业发展方向探索与确定</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>短期、中期、长期职业目标设定</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBarChart2 className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">职业发展路径规划</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>行业与职位发展趋势分析</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>个性化职业发展路径设计</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>关键能力提升计划制定</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目4 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiActivity className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">行动计划与执行指导</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>详细行动计划制定与分解</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>职业发展障碍分析与应对策略</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>定期跟进与调整优化</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">科学评估，精准定位</h3>
              </div>
              <p className="text-gray-700">采用国际认证的职业评估工具，结合大数据分析，科学评估个人能力与潜力，精准定位最适合的职业发展方向。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">个性化规划，切实可行</h3>
              </div>
              <p className="text-gray-700">根据个人特点、行业背景和职业目标，量身定制个性化职业发展规划，确保方案的针对性和可执行性。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">行业洞察，前瞻布局</h3>
              </div>
              <p className="text-gray-700">依托丰富的行业资源和专业研究，提供前瞻性的行业发展趋势分析，助您提前布局，把握职业发展先机。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">持续跟进，动态调整</h3>
              </div>
              <p className="text-gray-700">提供长期职业发展顾问服务，定期跟进规划执行情况，根据实际情况和环境变化及时调整优化发展策略。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          
          <div className="max-w-5xl mx-auto">
            <div className="relative">
              {/* 连接线 */}
              <div className="hidden md:block absolute left-[7.5rem] top-10 bottom-10 w-1 bg-blue-200 z-0"></div>
              
              <div className="space-y-12 relative z-10">
                {/* 流程1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">初步咨询</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">通过面谈或线上方式，了解您的职业背景、现状和期望，确定服务需求和目标。</p>
                  </div>
                </div>
                
                {/* 流程2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">能力评估</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">通过专业测评工具，全面评估您的职业能力、性格特质、兴趣倾向和价值观。</p>
                  </div>
                </div>
                
                {/* 流程3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">深度分析</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">结合评估结果和行业洞察，分析您的职业优势、发展潜力和最佳发展方向。</p>
                  </div>
                </div>
                
                {/* 流程4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">规划制定</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">制定个性化的职业发展规划，包括短期、中期和长期目标，以及详细的行动计划。</p>
                  </div>
                </div>
                
                {/* 流程5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">5</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">执行指导</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">提供规划执行的具体指导和资源支持，帮助您有效实施职业发展计划。</p>
                  </div>
                </div>
                
                {/* 流程6 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">6</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">跟进调整</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">定期跟进规划执行情况，根据反馈和环境变化及时调整优化发展策略。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          
          <div className="space-y-6">
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">什么样的人适合进行职业定位与发展规划？</h3>
              <p className="text-gray-600">无论是刚毕业的大学生、职场新人，还是工作多年想要突破瓶颈的职场人士，或是考虑转行的专业人才，都适合进行职业定位与发展规划。只要您对自己的职业发展方向有疑惑，或希望更有计划地推进职业发展，都可以从我们的服务中受益。</p>
            </div>
            
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">职业规划服务需要多长时间？</h3>
              <p className="text-gray-600">基础的职业定位与规划服务通常需要2-4周时间完成，包括初步咨询、能力评估、深度分析和规划制定等环节。如果您选择长期的职业发展顾问服务，我们会根据您的需求提供3个月、6个月或12个月的持续跟进与指导。</p>
            </div>
            
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">如何确保职业规划的有效性？</h3>
              <p className="text-gray-600">我们的职业规划建立在科学评估和专业分析的基础上，同时结合您的实际情况和行业发展趋势，确保规划的针对性和可行性。此外，我们提供的不仅是规划方案，还包括详细的行动计划和执行指导，以及定期的跟进与调整，确保规划能够有效落地实施。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务保障 */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务保障</h2>
          
          <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-sm text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiUsers className="text-blue-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">专业团队</h3>
              <p className="text-gray-600">由资深职业顾问、行业专家和心理咨询师组成的专业团队，确保服务的专业性和权威性。</p>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-sm text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiStar className="text-blue-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">定制方案</h3>
              <p className="text-gray-600">根据个人特点和需求量身定制职业发展方案，确保规划的针对性和实用性。</p>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-sm text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiHeart className="text-blue-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">持续支持</h3>
              <p className="text-gray-600">提供长期的职业发展支持和指导，帮助您有效应对职业发展中的各种挑战。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 咨询预约 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl text-center">
          <h2 className="text-3xl font-bold mb-6 text-gray-800">开启您的职业发展新篇章</h2>
          <p className="text-xl text-gray-600 mb-10">立即预约专业顾问，获取个性化的职业定位与发展规划服务</p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/appointment" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              预约咨询
            </Link>
            <Link href="/contact" className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              了解更多
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}