'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiTarget, FiBarChart2, FiUsers, FiCheck, FiBook, FiActivity, FiHeart, FiStar, FiRefreshCw } from 'react-icons/fi';

export default function CareerTransitionPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">职业转型与跨界指导</h1>
            <p className="text-xl text-blue-100">
              专业指导职业转型与跨界发展，评估转型可行性，制定转型策略，助您顺利实现职业领域的成功转换。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                我们的职业转型与跨界指导服务，专为希望在不同行业或职能领域寻求发展机会的职场人士设计。由具有丰富跨行业经验的职业顾问团队提供，帮助您评估转型可行性，识别可迁移的核心能力，制定科学的转型策略。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                无论您是因行业变革被迫转型，还是主动寻求新的职业挑战，我们都能为您提供专业的指导和支持，帮助您在新的职业领域建立竞争优势，实现成功转型。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiRefreshCw className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">转型可行性评估</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>个人能力与目标行业要求匹配分析</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>可迁移技能与经验识别</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>转型风险与机会评估</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">目标行业与职位分析</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>目标行业发展趋势与前景分析</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>职位要求与能力差距识别</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>行业文化与工作方式适应性评估</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBarChart2 className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">转型策略与路径规划</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>个性化转型路径设计</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>能力提升与资质获取计划</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>阶段性目标与里程碑设定</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目4 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiActivity className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">转型实施与支持</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>求职材料优化与面试准备</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>行业人脉拓展与资源对接</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>转型过程中的心理支持与调适</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">跨界经验，精准指导</h3>
              </div>
              <p className="text-gray-700">我们的顾问团队拥有丰富的跨行业经验和人脉资源，能够提供基于实践的转型指导和行业洞察。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">能力迁移，优势重构</h3>
              </div>
              <p className="text-gray-700">帮助您识别和提炼可迁移的核心能力，重构个人优势，在新领域建立独特的竞争力。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">风险管控，稳健转型</h3>
              </div>
              <p className="text-gray-700">科学评估转型风险，制定稳健的转型策略，降低转型过程中的不确定性和潜在风险。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">全程陪伴，持续支持</h3>
              </div>
              <p className="text-gray-700">提供转型全过程的指导和支持，包括心理调适、能力提升和资源对接，确保转型的顺利实施。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          
          <div className="max-w-5xl mx-auto">
            <div className="relative">
              {/* 连接线 */}
              <div className="hidden md:block absolute left-[7.5rem] top-10 bottom-10 w-1 bg-blue-200 z-0"></div>
              
              <div className="space-y-12 relative z-10">
                {/* 流程1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">需求评估</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">深入了解您的职业背景、转型动机和目标，评估转型的必要性和可行性。</p>
                  </div>
                </div>
                
                {/* 流程2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">能力分析</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">全面评估您的技能、经验和特质，识别可迁移的核心能力和需要提升的关键能力。</p>
                  </div>
                </div>
                
                {/* 流程3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">行业研究</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">深入分析目标行业的发展趋势、准入门槛和职位要求，评估您的适配度和竞争力。</p>
                  </div>
                </div>
                
                {/* 流程4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">方案制定</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">制定个性化的转型策略和实施路径，包括能力提升计划、资源获取策略和阶段性目标。</p>
                  </div>
                </div>
                
                {/* 流程5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">5</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">执行支持</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">提供转型过程中的具体指导和支持，包括求职准备、人脉拓展和心理调适。</p>
                  </div>
                </div>
                
                {/* 流程6 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">6</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">跟进调整</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">定期跟进转型进展，根据反馈和实际情况调整优化转型策略，确保转型的顺利实现。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          
          <div className="space-y-6">
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">如何判断是否适合进行职业转型？</h3>
              <p className="text-gray-600">判断是否适合转型需要考虑多方面因素，包括您的职业满意度、行业发展前景、个人兴趣和能力等。我们的专业评估可以帮助您客观分析转型的必要性和可行性，做出明智的决策。如果您对当前职业感到倦怠、所在行业面临衰退、或对新领域有强烈兴趣和一定基础，可能是考虑转型的适当时机。</p>
            </div>
            
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">职业转型需要多长时间？</h3>
              <p className="text-gray-600">职业转型的时间因人而异，取决于转型的跨度、您的准备程度和目标行业的要求等因素。一般来说，相近领域的转型可能需要3-6个月，而跨度较大的转型可能需要1-2年的时间。我们会根据您的具体情况制定合理的时间规划，并提供分阶段的转型指导，帮助您高效实现转型目标。</p>
            </div>
            
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">转型过程中如何降低风险？</h3>
              <p className="text-gray-600">降低转型风险的关键是科学规划和稳步实施。我们建议采取渐进式转型策略，如在现有工作的基础上逐步向目标领域靠拢、通过兼职或项目制工作积累经验、参与相关培训和认证等。同时，我们会帮助您建立财务缓冲，拓展行业人脉，并提供心理支持，全方位降低转型过程中的不确定性和风险。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务保障 */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务保障</h2>
          
          <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-sm text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiUsers className="text-blue-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">专业团队</h3>
              <p className="text-gray-600">由具有丰富跨行业经验的职业顾问和行业专家组成，确保转型指导的专业性和针对性。</p>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-sm text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiStar className="text-blue-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">科学方法</h3>
              <p className="text-gray-600">采用科学的评估工具和方法，确保转型决策和规划的客观性和可行性。</p>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-sm text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiHeart className="text-blue-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">全程支持</h3>
              <p className="text-gray-600">提供转型全过程的指导和支持，确保您能够顺利度过转型期，实现职业目标。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 咨询预约 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl text-center">
          <h2 className="text-3xl font-bold mb-6 text-gray-800">开启您的职业转型之旅</h2>
          <p className="text-xl text-gray-600 mb-10">立即预约专业顾问，获取个性化的职业转型指导服务</p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/appointment" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              预约咨询
            </Link>
            <Link href="/contact" className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              了解更多
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}