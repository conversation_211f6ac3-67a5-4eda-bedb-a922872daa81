'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiTarget, FiBarChart2, FiUsers, FiCheck, FiBook, FiActivity, FiHeart, FiStar, FiAward } from 'react-icons/fi';

export default function LeadershipDevelopmentPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">领导力提升与职场晋升</h1>
            <p className="text-xl text-blue-100">
              系统培养领导力素养，提供职场晋升策略指导，助力职场人士突破瓶颈，实现职业生涯的跨越式发展。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                我们的领导力提升与职场晋升服务，由资深管理顾问和职业发展专家团队提供，针对希望在职场中获得更大发展空间和晋升机会的职场人士设计。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                通过科学的领导力评估、系统的能力培养和个性化的晋升策略指导，帮助您突破职业发展瓶颈，提升领导影响力，增强晋升竞争力，实现职业生涯的跨越式发展。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiAward className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">领导力评估与诊断</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>多维度领导力素质评估</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>领导风格与效能分析</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>领导力优势与发展空间识别</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiUsers className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">核心领导力培养</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>战略思维与决策能力提升</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>团队管理与激励技能培养</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>沟通影响力与冲突管理能力发展</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBarChart2 className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">职场晋升策略指导</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>职业发展路径规划与晋升机会分析</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>晋升准备与竞争力提升策略</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>职场政治智慧与人际关系管理</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目4 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiActivity className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">高管素养与形象塑造</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>高管思维与视野培养</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>专业形象与个人品牌建设</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>演讲与表达能力提升</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">科学评估，精准诊断</h3>
              </div>
              <p className="text-gray-700">采用国际认证的领导力评估工具，结合360度反馈，全面评估领导力现状，精准识别发展需求。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">个性化培养，系统提升</h3>
              </div>
              <p className="text-gray-700">根据个人特点和发展需求，量身定制领导力培养方案，系统提升核心领导力素养。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">实战导向，注重应用</h3>
              </div>
              <p className="text-gray-700">强调领导力在实际工作中的应用，提供具体的工具和方法，帮助您在实践中提升领导效能。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">资源赋能，加速发展</h3>
              </div>
              <p className="text-gray-700">提供丰富的学习资源和人脉对接，帮助您拓展视野，获取更多发展机会，加速职业晋升。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          
          <div className="max-w-5xl mx-auto">
            <div className="relative">
              {/* 连接线 */}
              <div className="hidden md:block absolute left-[7.5rem] top-10 bottom-10 w-1 bg-blue-200 z-0"></div>
              
              <div className="space-y-12 relative z-10">
                {/* 流程1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">需求分析</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">深入了解您的职业背景、发展目标和领导力提升需求，明确服务方向和重点。</p>
                  </div>
                </div>
                
                {/* 流程2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">领导力评估</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">通过专业工具和方法，全面评估您的领导力现状，识别优势和发展空间。</p>
                  </div>
                </div>
                
                {/* 流程3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">方案制定</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">根据评估结果和发展需求，制定个性化的领导力提升和职场晋升方案。</p>
                  </div>
                </div>
                
                {/* 流程4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">能力培养</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">通过一对一辅导、专题培训和实战演练，系统提升核心领导力素养。</p>
                  </div>
                </div>
                
                {/* 流程5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">5</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">晋升指导</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">提供具体的晋升策略和实施指导，帮助您把握晋升机会，提高晋升成功率。</p>
                  </div>
                </div>
                
                {/* 流程6 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="md:w-32 flex-shrink-0 flex flex-col items-center">
                    <div className="bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">6</div>
                    <div className="text-blue-600 font-bold mt-2 text-center">效果评估</div>
                  </div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <p className="text-gray-700">定期评估领导力提升和职场发展效果，根据反馈调整优化发展策略。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          
          <div className="space-y-6">
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">什么样的人适合领导力提升与职场晋升服务？</h3>
              <p className="text-gray-600">我们的服务适合各类希望提升领导能力和职场竞争力的职场人士，包括：即将晋升或已晋升为管理者的职场新人、希望突破职业瓶颈的中层管理者、寻求更高层级发展的高潜人才，以及希望提升团队管理能力的企业领导者等。无论您处于职业发展的哪个阶段，只要有提升领导力和加速职场晋升的需求，都可以从我们的服务中受益。</p>
            </div>
            
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">领导力提升需要多长时间？</h3>
              <p className="text-gray-600">领导力的提升是一个持续发展的过程，没有固定的时间表。我们的基础服务周期通常为3-6个月，在此期间，您将获得系统的领导力评估、培养和指导。根据个人起点和发展目标的不同，可能需要不同的时间投入。我们会根据您的实际情况和进展，调整服务内容和周期，确保您能够持续有效地提升领导力。</p>
            </div>
            
            <div className="bg-gray-50 p-6 rounded-lg">
              <h3 className="text-xl font-bold mb-3 text-gray-800">如何衡量领导力提升的效果？</h3>
              <p className="text-gray-600">我们采用多维度的方法评估领导力提升效果，包括：定期的领导力评估对比、360度反馈收集、实际工作表现和成果分析、团队绩效和满意度变化等。此外，我们也会关注您在职场中的实际晋升情况和影响力提升，作为衡量服务效果的重要指标。我们的目标是帮助您在领导能力和职场发展上取得可见的、可衡量的进步。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务保障 */}
      <section className="py-16 bg-blue-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务保障</h2>
          
          <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-sm text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiUsers className="text-blue-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">专业团队</h3>
              <p className="text-gray-600">由资深管理顾问和职业发展专家组成，拥有丰富的领导力培养和职场晋升指导经验。</p>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-sm text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiStar className="text-blue-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">定制方案</h3>
              <p className="text-gray-600">根据个人特点和发展需求，量身定制领导力提升和职场晋升方案，确保服务的针对性和有效性。</p>
            </div>
            
            <div className="bg-white p-8 rounded-xl shadow-sm text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FiHeart className="text-blue-600 text-2xl" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">全程支持</h3>
              <p className="text-gray-600">提供全程的指导和支持，帮助您有效应对领导力发展和职场晋升中的各种挑战。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 咨询预约 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl text-center">
          <h2 className="text-3xl font-bold mb-6 text-gray-800">提升您的领导力，加速职场晋升</h2>
          <p className="text-xl text-gray-600 mb-10">立即预约专业顾问，开启您的领导力发展之旅</p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/appointment" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              预约咨询
            </Link>
            <Link href="/contact" className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              了解更多
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}