'use client';

import React from 'react';
import Link from 'next/link';
import { Fi<PERSON>rrowLeft, FiTarget, FiBarChart2, FiUsers, <PERSON>C<PERSON>ck, FiBook } from 'react-icons/fi';

export default function ChildPotentialAnalysisPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">儿童潜能测评与分析</h1>
            <p className="text-xl text-blue-100">
              通过科学测评与专业分析，全面挖掘儿童潜能，为成长与学习规划提供坚实基础。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                依托国际标准化测评工具与专业心理学理论，系统评估儿童在认知、情感、社交、创造力等多维度的潜能表现，帮助家长和教师科学了解孩子的优势与成长空间。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                专业团队为每位儿童定制个性化成长报告，提出针对性的培养建议，助力孩子健康成长、全面发展。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">多维度潜能测评</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>认知能力、学习能力、创造力、社交情感等多维度测评</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>采用权威测评工具，结果科学可靠</span></li>
                    </ul>
                  </div>
                </div>
              </div>
              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBarChart2 className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">个性化成长报告</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>详细分析测评结果，挖掘儿童优势与潜力</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>为家长和教师提供个性化培养建议</span></li>
                    </ul>
                  </div>
                </div>
              </div>
              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiUsers className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">成长路径规划</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>结合测评结果，规划科学的成长路径</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>动态跟踪成长进展，持续优化培养方案</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">科学测评，数据驱动</h3>
              </div>
              <p className="text-gray-700">采用国际标准化测评工具，结合大数据分析，确保测评结果的科学性和权威性。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">个性化报告，实用建议</h3>
              </div>
              <p className="text-gray-700">每位儿童均有专属成长报告，内容详实，建议具体可操作。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">专家团队，专业保障</h3>
              </div>
              <p className="text-gray-700">由资深心理学专家与教育顾问全程参与，确保服务质量。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">持续跟踪，动态优化</h3>
              </div>
              <p className="text-gray-700">服务后期持续跟踪，动态调整成长方案，助力孩子持续进步。</p>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}