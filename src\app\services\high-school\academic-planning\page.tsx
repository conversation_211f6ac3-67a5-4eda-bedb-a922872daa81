'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiCheck, FiTarget, FiBook, FiBarChart2, FiUsers, FiLayers, FiStar, FiCompass, FiAward } from 'react-icons/fi';

export default function AcademicPlanningPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">高中学业规划</h1>
            <p className="text-xl text-blue-100">
              科学规划高中学习路径，平衡学科发展与特长培养，助力学生全面提升竞争力。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                高中阶段是学生学业发展的关键期，科学合理的学业规划对于提升学习效率、培养核心竞争力至关重要。我们的高中学业规划服务，基于对学生学科能力、学习特点和发展目标的全面评估，为学生量身定制系统化的学习方案。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                通过专业的学科诊断、学习策略指导和目标管理，帮助学生建立科学的学习体系，优化时间管理，平衡学科发展与特长培养，全面提升学业表现和综合素质，为升学和未来发展奠定坚实基础。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">学科能力诊断</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>全面评估各学科能力水平和学习特点</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>分析学科优势和短板，找出提升空间</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>识别学习障碍和瓶颈，制定突破策略</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBook className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">学习策略优化</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>针对不同学科特点，提供高效学习方法指导</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>优化笔记系统和知识体系构建</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>提升解题思路和应试技巧</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBarChart2 className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">目标管理与规划</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>制定科学合理的学业目标体系</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>分解长期目标为阶段性任务和行动计划</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>建立有效的目标追踪和调整机制</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目4 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiLayers className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">综合素质提升</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>规划课外活动和社会实践，丰富学生经历</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>指导特长培养和竞赛准备</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>培养领导力、创新思维等核心素养</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">个性化定制，精准规划</h3>
              </div>
              <p className="text-gray-700">基于学生个体特点和需求，量身定制学业规划方案，避免千篇一律的学习模式。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">科学方法，系统指导</h3>
              </div>
              <p className="text-gray-700">运用科学的学习理论和方法，提供系统化的学习策略和技巧指导。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">目标导向，过程管理</h3>
              </div>
              <p className="text-gray-700">注重目标设定和过程管理，培养学生的自主学习能力和时间管理能力。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">全面发展，均衡提升</h3>
              </div>
              <p className="text-gray-700">兼顾学科学习与综合素质培养，促进学生全面均衡发展。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute left-[50px] top-0 h-full w-1 bg-blue-200 md:hidden"></div>
              <div className="space-y-12">
                {/* 步骤1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">1</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">初步评估</h3>
                    <p className="text-gray-700">全面了解学生的学习现状、能力特点和发展目标</p>
                  </div>
                </div>
                {/* 步骤2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">2</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">学科诊断</h3>
                    <p className="text-gray-700">通过专业测评和分析，评估各学科能力水平和学习特点</p>
                  </div>
                </div>
                {/* 步骤3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">3</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">方案制定</h3>
                    <p className="text-gray-700">基于评估结果，制定个性化的学业规划方案</p>
                  </div>
                </div>
                {/* 步骤4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">4</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">执行指导</h3>
                    <p className="text-gray-700">提供具体的学习策略和方法指导，帮助学生有效执行规划</p>
                  </div>
                </div>
                {/* 步骤5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">5</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">跟踪调整</h3>
                    <p className="text-gray-700">定期跟踪学习效果，根据反馈及时调整优化规划方案</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 适用人群 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">适用人群</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">年级阶段</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>高一新生（学习规划起步）</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>高二学生（学业提升关键期）</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>高三学生（冲刺备考阶段）</span></li>
              </ul>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">特别适合</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>学习方法不当，效率低下的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>学科发展不均衡，需要优化学习策略的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>目标明确，希望提升竞争力的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>时间管理能力弱，需要系统规划的学生</span></li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                高中学业规划与普通的学习辅导有什么区别？
              </div>
              <div className="p-5 text-gray-600">
                学业规划更注重系统性和全局性，不仅关注具体学科知识的掌握，更强调学习方法的优化、学习习惯的培养、时间管理的提升以及目标设定与执行能力的培养。我们提供的是一套完整的学习体系和成长方案，而非单纯的知识讲解和题目训练。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                什么时候开始高中学业规划最合适？
              </div>
              <div className="p-5 text-gray-600">
                理想的时间是高一开学初期，这样可以尽早建立科学的学习体系和良好的学习习惯。但无论何时开始，我们都会根据学生当前的学习阶段和需求，制定适合的规划方案。高二和高三的学生也可以通过学业规划，优化学习策略，提高学习效率。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                如何平衡各学科的学习与特长发展？
              </div>
              <div className="p-5 text-gray-600">
                我们的学业规划会根据学生的实际情况，合理分配各学科学习和特长发展的时间和资源。原则上，我们建议在保证主要学科学习质量的前提下，给予学生适当的时间发展特长和兴趣，形成个人特色和竞争优势。具体的时间分配和侧重点会根据学生的学科基础、特长领域和发展目标进行个性化设计。
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 成功案例 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">成功案例</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例一：学习效率提升</h3>
              <p className="text-gray-700 mb-4">
                高二学生通过学习策略优化和时间管理训练，在保持学习时间不变的情况下，月考成绩提升了15%，尤其是弱势学科进步显著。
              </p>
              <div className="text-blue-600 font-medium">规划价值：方法优化，效率倍增</div>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例二：均衡发展</h3>
              <p className="text-gray-700 mb-4">
                理科优势的高一学生，通过系统规划和方法指导，文科成绩显著提升，同时保持理科优势，最终实现了各学科的均衡发展。
              </p>
              <div className="text-blue-600 font-medium">规划价值：扬长补短，全面发展</div>
            </div>
          </div>
        </div>
      </section>

      {/* 客户见证 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">客户见证</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "通过学业规划，孩子找到了适合自己的学习方法，学习效率大大提高，不仅成绩有了明显提升，更重要的是学习的主动性和自信心都增强了。"
              </div>
              <div className="text-gray-600 font-medium">— 赵女士，高二学生家长</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "以前总是埋头苦学但效果不佳，通过学业规划，我学会了如何科学安排时间，如何高效学习，现在的学习更有条理，也更轻松了。"
              </div>
              <div className="text-gray-600 font-medium">— 小王，高三学生</div>
            </div>
          </div>
        </div>
      </section>

      {/* 开启服务 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6 text-gray-800">开启规划之旅</h2>
            <p className="text-gray-600 mb-8">
              让我们一起制定科学的学业规划，提升学习效率，实现学业目标
            </p>
            <Link href="/contact" className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              立即预约
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}