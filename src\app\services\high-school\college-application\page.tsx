'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiCheck, FiTarget, FiBook, FiBarChart2, FiUsers, FiLayers, FiStar, FiCompass, FiAward } from 'react-icons/fi';

export default function CollegeApplicationPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">高考升学申请指导</h1>
            <p className="text-xl text-blue-100">
              提供专业的高考志愿填报和大学申请指导，助力学生进入理想院校和专业。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                高考升学申请是学生人生中的重要选择，科学合理的志愿填报和申请策略对于进入理想院校和专业至关重要。我们的高考升学申请指导服务，基于对学生学业表现、兴趣特长和职业倾向的全面评估，结合最新的高校招生政策和专业发展趋势，为学生提供个性化的升学规划和申请指导。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                通过专业的院校和专业匹配分析、志愿填报策略指导和申请材料准备，帮助学生最大化录取机会，进入最适合自己的大学和专业，为未来的学业和职业发展奠定坚实基础。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">学生综合评估</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>全面评估学生的学业表现、兴趣特长和职业倾向</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>分析学生的优势和特点，明确升学定位</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>评估学生的竞争力和发展潜力</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiCompass className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">院校与专业匹配</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>提供全面的院校和专业信息库</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>分析各院校和专业的录取要求、特色和就业前景</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>根据学生特点和需求，推荐最适合的院校和专业选择</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBook className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">志愿填报策略</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>解读最新高考政策和招生规则</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>制定科学的志愿填报方案，平衡冲稳保</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>提供专业的志愿填报技巧和策略指导</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目4 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiAward className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">申请材料准备</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>指导准备自主招生、综合评价等特殊类型招生的申请材料</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>提供个人陈述、自荐信等文书的写作指导</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>准备面试和考核的技巧指导</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">专业的数据分析</h3>
              </div>
              <p className="text-gray-700">基于大数据分析和历年录取数据，提供科学准确的院校和专业匹配建议。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">个性化的升学规划</h3>
              </div>
              <p className="text-gray-700">根据学生的特点和需求，量身定制升学规划方案，避免千篇一律的志愿填报。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">全面的院校资源</h3>
              </div>
              <p className="text-gray-700">拥有丰富的院校资源和招生信息，提供最新、最全面的院校和专业信息。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">专业的指导团队</h3>
              </div>
              <p className="text-gray-700">由经验丰富的升学指导专家和前招生官组成的专业团队，提供权威、实用的申请指导。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute left-[50px] top-0 h-full w-1 bg-blue-200 md:hidden"></div>
              <div className="space-y-12">
                {/* 步骤1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">1</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">初步咨询</h3>
                    <p className="text-gray-700">了解学生的学业情况、兴趣特长和升学目标</p>
                  </div>
                </div>
                {/* 步骤2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">2</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">综合评估</h3>
                    <p className="text-gray-700">全面评估学生的学业表现、兴趣特长和职业倾向</p>
                  </div>
                </div>
                {/* 步骤3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">3</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">方案制定</h3>
                    <p className="text-gray-700">制定个性化的升学规划和志愿填报方案</p>
                  </div>
                </div>
                {/* 步骤4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">4</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">方案执行</h3>
                    <p className="text-gray-700">指导学生准备申请材料，进行志愿填报</p>
                  </div>
                </div>
                {/* 步骤5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">5</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">录取跟进</h3>
                    <p className="text-gray-700">跟进录取结果，提供调剂和后续指导</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 适用人群 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">适用人群</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">学生类型</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>高三备考学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>高考成绩已出，准备填报志愿的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>参加自主招生、综合评价等特殊类型招生的学生</span></li>
              </ul>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">特别适合</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>对院校和专业选择有困惑的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>希望进入理想院校和专业的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>需要专业志愿填报指导的家庭</span></li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                什么时候开始准备高考升学申请最合适？
              </div>
              <div className="p-5 text-gray-600">
                理想的时间是高三上学期开始，这样有充足的时间了解院校和专业信息，明确目标，有针对性地进行备考。但即使是高考成绩出来后，我们也能提供专业的志愿填报指导，帮助学生做出最优选择。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                如何选择适合自己的大学和专业？
              </div>
              <div className="p-5 text-gray-600">
                选择适合的大学和专业需要考虑多方面因素：学生的学业表现和竞争力、兴趣特长和职业倾向、院校的实力和特色、专业的培养目标和就业前景、地理位置和学费等实际因素。我们会通过科学评估和专业分析，帮助学生找到最适合自己的选择。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                志愿填报有哪些策略和技巧？
              </div>
              <div className="p-5 text-gray-600">
                志愿填报的核心策略是平衡冲稳保，即合理安排冲刺院校、稳妥院校和保底院校。具体技巧包括：了解各院校的录取规则和历年分数线，分析自身竞争力和定位，合理利用平行志愿的梯度设置，关注专业冷热度和就业前景，考虑地域和学费等因素。我们会根据学生的具体情况，提供个性化的志愿填报建议。
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 成功案例 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">成功案例</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例一：精准定位，成功录取</h3>
              <p className="text-gray-700 mb-4">
                高考成绩处于中等水平的学生，通过科学的院校和专业匹配分析，精准定位适合的院校和专业，最终被第一志愿院校录取，专业符合兴趣和职业规划。
              </p>
              <div className="text-blue-600 font-medium">服务价值：精准定位，理想录取</div>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例二：特长突破，名校录取</h3>
              <p className="text-gray-700 mb-4">
                具有艺术特长的学生，通过特殊类型招生指导，准备了高质量的申请材料和作品集，成功获得重点高校的艺术类专业录取资格。
              </p>
              <div className="text-blue-600 font-medium">服务价值：特长突破，名校梦圆</div>
            </div>
          </div>
        </div>
      </section>

      {/* 客户见证 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">客户见证</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "感谢专业的升学指导，让我们在志愿填报时不再迷茫。通过科学的分析和建议，孩子顺利进入了理想的大学和专业，为未来的发展奠定了良好基础。"
              </div>
              <div className="text-gray-600 font-medium">— 李女士，高考生家长</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "原本对专业选择很困惑，通过升学指导服务，我更清楚地了解了自己的兴趣和优势，找到了最适合的专业方向，现在在大学学习很有动力。"
              </div>
              <div className="text-gray-600 font-medium">— 小张，大一学生</div>
            </div>
          </div>
        </div>
      </section>

      {/* 开启服务 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6 text-gray-800">开启升学之旅</h2>
            <p className="text-gray-600 mb-8">
              让我们一起规划理想的升学路径，助你进入心仪的大学和专业
            </p>
            <Link href="/contact" className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              立即预约
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}