'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiCheck, FiHelpCircle, FiTarget, FiBook, FiBarChart2, FiUsers } from 'react-icons/fi';

export default function SubjectSelectionPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 使用渐变背景替代纯色背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">新高考选科深度指导</h1>
            <p className="text-xl text-blue-100">
              基于学生兴趣、能力和目标院校专业要求，提供科学的选科建议，助力学生做出最优选择。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 - 使用卡片式设计增强内容层次感 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                新高考改革背景下，选科决策直接影响学生的高中学习体验、高考竞争力和未来专业发展路径。我们的新高考选科深度指导服务，通过科学的测评工具、专业的数据分析和个性化的咨询指导，帮助学生和家长做出科学、合理的选科决策，避免因选科不当而限制未来发展空间。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                我们的专业团队由具备丰富高考政策研究经验的教育专家、了解各学科特点的一线教师和熟悉大学专业要求的升学顾问组成，能够提供全方位、多角度的选科指导，确保每位学生都能找到最适合自己的选科组合。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 - 重新设计服务内容卡片，添加序号和图标 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                  1
                </div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">科学测评与自我认知</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>通过性格、兴趣、能力、价值观等多维度测评，帮助学生深入了解自我</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>分析学生在各学科的学习表现、思维特点和潜力空间</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>识别学生的优势学科和发展潜力，为选科决策提供科学依据</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              
              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                  2
                </div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBook className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">高校专业与选科匹配</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>精细解读全国高校各专业（组）的选科要求，建立专业-选科匹配数据库</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>分析历年各选科组合的录取数据和趋势，评估不同选科组合的竞争优势</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>根据学生的目标院校和专业倾向，提供最优的选科建议</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              
              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                  3
                </div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBarChart2 className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">未来职业路径分析</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>引入行业专家视角，分析不同选科组合的未来大学专业和职业发展的影响</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>提供至少3套备选方案，并详细剖析其利弊与适配度</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>结合学生的职业兴趣和发展愿景，评估各选科方案的长期价值</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
              
              {/* 服务项目4 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                  4
                </div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiUsers className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">个性化决策支持</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>模拟选科后的学业挑战与升学优势，提供具体的学习规划建议</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>与学生和家长深入沟通，解答疑虑，协助做出最终决策</span>
                      </li>
                      <li className="flex items-start">
                        <span className="text-blue-500 mr-2">•</span>
                        <span>提供选科后的学习适应与提升指导，确保选科决策落地实施</span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 - 优化网格布局，提高信息密度 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">科学测评，数据驱动</h3>
              </div>
              <p className="text-gray-700">采用国际标准化测评工具，结合大数据分析，为选科决策提供科学依据，确保选科方案的准确性和可靠性。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">多维度评估，全面分析</h3>
              </div>
              <p className="text-gray-700">从能力、兴趣、性格、职业倾向和院校专业要求等多个维度进行综合评估，确保选科决策的全面性和科学性。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">个性化方案，精准指导</h3>
              </div>
              <p className="text-gray-700">根据学生的个体差异，提供个性化的选科方案和学习建议，避免千篇一律的选科模式，最大化学生的发展潜力。</p>
            </div>
            
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">长期规划，持续跟进</h3>
              </div>
              <p className="text-gray-700">不仅提供选科建议，还为学生制定长期学习规划，并提供持续的学业指导和调整建议，确保选科决策的有效落实。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 - 重新设计服务流程的展示方式 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          
          <div className="max-w-5xl mx-auto relative">
            {/* 连接线 - 桌面版 */}
            <div className="hidden md:block absolute left-[7.5rem] top-16 bottom-16 w-1 bg-blue-200 z-0"></div>
            
            <div className="space-y-12 relative z-10">
              {/* 步骤1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                  1
                </div>
                <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-xl font-bold mb-3 text-gray-800">初步咨询与需求分析</h3>
                  <p className="text-gray-700">通过面谈或线上会议，了解学生的学习情况、兴趣爱好和未来规划，明确选科需求和目标。</p>
                </div>
              </div>
              
              {/* 步骤2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                  2
                </div>
                <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-xl font-bold mb-3 text-gray-800">综合测评与数据收集</h3>
                  <p className="text-gray-700">进行学科能力测评、兴趣倾向测评和性格特质测评，收集学生的学业成绩和学习情况数据。</p>
                </div>
              </div>
              
              {/* 步骤3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                  3
                </div>
                <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-xl font-bold mb-3 text-gray-800">数据分析与方案制定</h3>
                  <p className="text-gray-700">专业团队对测评数据进行分析，结合学生的目标院校和专业要求，制定个性化的选科方案。</p>
                </div>
              </div>
              
              {/* 步骤4 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                  4
                </div>
                <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-xl font-bold mb-3 text-gray-800">方案讲解与调整</h3>
                  <p className="text-gray-700">向学生和家长详细讲解选科方案和理由，根据反馈进行必要的调整和优化。</p>
                </div>
              </div>
              
              {/* 步骤5 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">
                  5
                </div>
                <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                  <h3 className="text-xl font-bold mb-3 text-gray-800">学习规划与持续跟进</h3>
                  <p className="text-gray-700">根据选定的科目组合，制定详细的学习规划，并提供持续的学业指导和调整建议。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 适用人群 - 新增区域 */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center">适用人群</h2>
          
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white/10 p-6 rounded-xl backdrop-blur-sm">
              <h3 className="text-xl font-bold mb-4">高一学生及家长</h3>
              <p className="text-white/90">即将面临选科决策，希望做出科学、合理选择，为高中学习和高考升学奠定良好基础的学生和家长。</p>
            </div>
            <div className="bg-white/10 p-6 rounded-xl backdrop-blur-sm">
              <h3 className="text-xl font-bold mb-4">初三学生及家长</h3>
              <p className="text-white/90">提前规划高中学习路径，希望在升入高中前就对选科有清晰认识，做好充分准备的学生和家长。</p>
            </div>
            <div className="bg-white/10 p-6 rounded-xl backdrop-blur-sm">
              <h3 className="text-xl font-bold mb-4">已选科但存在疑虑的高中生</h3>
              <p className="text-white/90">已经做出选科决策，但对自己的选择存在疑虑，希望获得专业评估和建议的学生。</p>
            </div>
            <div className="bg-white/10 p-6 rounded-xl backdrop-blur-sm">
              <h3 className="text-xl font-bold mb-4">考虑调整选科的高中生</h3>
              <p className="text-white/90">在学习过程中发现当前选科组合不适合自己，希望评估调整可能性和方案的学生。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 - 改进FAQ的展示方式 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-4xl">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          
          <div className="space-y-6">
            <div className="bg-gray-50 p-6 rounded-xl">
              <div className="flex items-start">
                <FiHelpCircle className="text-blue-600 text-xl mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-xl font-bold mb-3 text-gray-800">什么时候开始进行选科规划最合适？</h3>
                  <p className="text-gray-700">建议在高一上学期末或高一下学期初开始进行选科规划，这个时间点学生已经对各学科有了初步了解，同时也留有充足的时间进行测评、分析和决策，以及为选定的科目做好准备。</p>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 p-6 rounded-xl">
              <div className="flex items-start">
                <FiHelpCircle className="text-blue-600 text-xl mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-xl font-bold mb-3 text-gray-800">如何平衡兴趣与能力在选科中的权重？</h3>
                  <p className="text-gray-700">兴趣和能力都是选科决策中的重要因素。一般而言，我们建议在学生有一定能力基础的前提下，优先考虑兴趣因素，因为兴趣是持续学习的动力。但如果某一学科能力明显不足，即使有兴趣，也需要慎重考虑，或者制定针对性的提升计划。</p>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 p-6 rounded-xl">
              <div className="flex items-start">
                <FiHelpCircle className="text-blue-600 text-xl mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-xl font-bold mb-3 text-gray-800">选科后如果发现不适合，可以调整吗？</h3>
                  <p className="text-gray-700">大多数学校允许在高二上学期开始前进行一次选科调整，但调整过程可能会面临一些挑战，如学习进度跟不上、错过重要内容等。因此，我们的服务会尽可能确保初次选科的科学性和合理性，同时也会为可能的调整提供指导和支持。</p>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 p-6 rounded-xl">
              <div className="flex items-start">
                <FiHelpCircle className="text-blue-600 text-xl mr-3 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-xl font-bold mb-3 text-gray-800">选科与未来专业和职业的关系是怎样的？</h3>
                  <p className="text-gray-700">选科会直接影响高考后可选择的专业范围，进而影响未来的职业发展。不同专业对选科组合有不同要求，我们的服务会根据学生的职业倾向和目标专业，提供相应的选科建议，确保选科决策与长期规划相一致。</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 成功案例 - 展示真实案例增强可信度 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">成功案例</h2>
          
          <div className="max-w-5xl mx-auto">
            {/* 案例1 */}
            <div className="mb-12 bg-gray-50 rounded-xl overflow-hidden shadow-sm">
              <div className="md:flex">
                <div className="md:w-1/3 bg-blue-100 p-6 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-24 h-24 rounded-full bg-white mx-auto mb-4 flex items-center justify-center">
                      <span className="text-3xl">👨‍🎓</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-800">李同学</h3>
                    <p className="text-gray-600">某重点高中学生</p>
                  </div>
                </div>
                <div className="md:w-2/3 p-6">
                  <h4 className="text-xl font-bold mb-4 text-blue-700">从迷茫到清晰：找到最适合的选科组合</h4>
                  <p className="text-gray-700 mb-4">李同学在高一时对自己的学科优势和未来发展方向感到迷茫。通过我们的选科指导服务，发现他在理科方面有较强的逻辑思维能力，但同时也对经济和管理有浓厚兴趣。</p>
                  <div className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
                    <h5 className="font-bold text-gray-800 mb-2">我们的方案</h5>
                    <p className="text-gray-700">根据测评结果和目标分析，为李同学推荐了物理+化学+政治的选科组合，兼顾了理科思维培养和未来报考经管类专业的可能性。</p>
                  </div>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h5 className="font-bold text-gray-800 mb-2">最终结果</h5>
                    <p className="text-gray-700">李同学在高考中取得优异成绩，成功被北京某知名高校的经济学专业录取，实现了理想的升学目标。</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 案例2 */}
            <div className="mb-12 bg-gray-50 rounded-xl overflow-hidden shadow-sm">
              <div className="md:flex">
                <div className="md:w-1/3 bg-purple-100 p-6 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-24 h-24 rounded-full bg-white mx-auto mb-4 flex items-center justify-center">
                      <span className="text-3xl">👩‍🎓</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-800">张同学</h3>
                    <p className="text-gray-600">某普通高中学生</p>
                  </div>
                </div>
                <div className="md:w-2/3 p-6">
                  <h4 className="text-xl font-bold mb-4 text-purple-700">突破传统选择：发掘潜能开辟新路</h4>
                  <p className="text-gray-700 mb-4">张同学在初中阶段文科成绩优秀，但对生物学科有特别的兴趣。家长和老师都建议她选择传统文科组合，但她对医学和生命科学领域充满向往。</p>
                  <div className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
                    <h5 className="font-bold text-gray-800 mb-2">我们的方案</h5>
                    <p className="text-gray-700">通过深入测评和分析，我们发现张同学在生物学习上有特殊天赋。为她定制了物理+化学+生物的选科组合，并制定了针对性的学习提升计划。</p>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <h5 className="font-bold text-gray-800 mb-2">最终结果</h5>
                    <p className="text-gray-700">张同学克服了学习挑战，在高考中生物成绩尤为突出，被上海某医学院校录取，成功踏入医学领域。</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 案例3 */}
            <div className="bg-gray-50 rounded-xl overflow-hidden shadow-sm">
              <div className="md:flex">
                <div className="md:w-1/3 bg-green-100 p-6 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-24 h-24 rounded-full bg-white mx-auto mb-4 flex items-center justify-center">
                      <span className="text-3xl">👨‍🎓</span>
                    </div>
                    <h3 className="text-xl font-bold text-gray-800">王同学</h3>
                    <p className="text-gray-600">某示范高中学生</p>
                  </div>
                </div>
                <div className="md:w-2/3 p-6">
                  <h4 className="text-xl font-bold mb-4 text-green-700">选科调整：及时纠正，重获信心</h4>
                  <p className="text-gray-700 mb-4">王同学高一时在未经专业指导的情况下选择了物理+化学+生物组合，但在学习过程中发现化学学习非常吃力，成绩持续下滑，严重影响了学习信心。</p>
                  <div className="bg-white p-4 rounded-lg border border-gray-200 mb-4">
                    <h5 className="font-bold text-gray-800 mb-2">我们的方案</h5>
                    <p className="text-gray-700">在高一下学期，我们对王同学进行了全面评估，发现他在地理学科有较强的学习能力和兴趣。建议他调整为物理+生物+地理的组合，并制定了化学到地理的过渡学习计划。</p>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <h5 className="font-bold text-gray-800 mb-2">最终结果</h5>
                    <p className="text-gray-700">调整选科后，王同学学习积极性明显提高，成绩稳步上升，最终被心仪的环境科学专业录取，找到了适合自己的发展方向。</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 客户见证 - 增加真实感和可信度 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">客户见证</h2>
          
          <div className="max-w-5xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* 见证1 */}
            <div className="bg-white p-6 rounded-xl shadow-sm relative">
              <div className="text-blue-500 text-5xl absolute -top-4 left-4 opacity-20">❝</div>
              <div className="relative z-10">
                <p className="text-gray-700 italic mb-6">"思立恒教育的选科指导服务非常专业，通过科学的测评和详细的分析，帮助我女儿找到了最适合她的选科组合，避免了盲目跟风。现在她学习很有动力，成绩也稳步提升。"</p>
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                    <span className="text-blue-600 font-bold">L</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-800">刘女士</p>
                    <p className="text-sm text-gray-500">高一学生家长</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 见证2 */}
            <div className="bg-white p-6 rounded-xl shadow-sm relative">
              <div className="text-blue-500 text-5xl absolute -top-4 left-4 opacity-20">❝</div>
              <div className="relative z-10">
                <p className="text-gray-700 italic mb-6">"作为一名高中教师，我向很多学生推荐了思立恒的选科指导服务。他们的方案不是简单地推荐热门组合，而是真正基于学生的特点和未来规划，提供个性化的建议，非常值得信赖。"</p>
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                    <span className="text-blue-600 font-bold">Z</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-800">赵老师</p>
                    <p className="text-sm text-gray-500">高中班主任</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 见证3 */}
            <div className="bg-white p-6 rounded-xl shadow-sm relative">
              <div className="text-blue-500 text-5xl absolute -top-4 left-4 opacity-20">❝</div>
              <div className="relative z-10">
                <p className="text-gray-700 italic mb-6">"高一时我对选什么科目很纠结，通过思立恒的指导，我不仅明确了选科方向，还得到了详细的学习规划和方法指导，让我在高中阶段少走了很多弯路，非常感谢！"</p>
                <div className="flex items-center">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mr-4">
                    <span className="text-blue-600 font-bold">W</span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-800">吴同学</p>
                    <p className="text-sm text-gray-500">已录取大学新生</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA区域 - 增强行动召唤区域 */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">开启科学选科之旅</h2>
          <p className="text-xl max-w-3xl mx-auto mb-10">选择决定未来，让我们的专业团队助您做出最优选择</p>
          <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-4 rounded-full font-medium text-lg inline-block transition-colors">
            立即预约咨询
          </Link>
        </div>
      </section>
    </main>
  );
}