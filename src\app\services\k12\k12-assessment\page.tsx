'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiTarget, FiBarChart2, FiUsers, FiCheck, FiBook, FiActivity, FiHeart, FiStar } from 'react-icons/fi';

export default function ChildrenAssessmentPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">儿童潜能测评与分析</h1>
            <p className="text-xl text-blue-100">
              通过科学测评工具，全面评估孩子的认知能力、学习特点和发展潜力，为教育规划提供科学依据。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                我们的儿童潜能测评服务采用国际标准化的测评工具，从多个维度评估孩子的认知能力、学习风格、性格特质和兴趣倾向，帮助家长和教育者更全面地了解孩子的天赋潜能和发展特点。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                通过专业的测评分析和个性化报告，为每个孩子制定科学的发展建议，助力家长做出更明智的教育决策。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiActivity className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">多维度能力测评</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>认知能力评估（记忆力、注意力、逻辑思维等）</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>多元智能测评（语言、数理、空间、音乐等）</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>学习能力分析（学习方式、学习效率、知识迁移等）</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiHeart className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">性格与情商测评</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>性格特质分析（内外向、独立性、适应性等）</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>情绪管理能力评估</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>人际交往能力测评</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiStar className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">兴趣与天赋探索</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>兴趣倾向测评</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>天赋潜能分析</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>特长发展建议</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">科学专业的测评体系</h3>
              </div>
              <p className="text-gray-700">采用国际认可的儿童发展测评工具，确保测评结果的科学性和权威性。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">个性化的分析报告</h3>
              </div>
              <p className="text-gray-700">提供详细的测评分析报告，清晰呈现孩子的能力特点和发展建议。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">专家解读与指导</h3>
              </div>
              <p className="text-gray-700">由儿童发展专家对测评结果进行专业解读，并提供针对性的教育建议。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute left-[50px] top-0 h-full w-1 bg-blue-200 md:hidden"></div>
              <div className="space-y-12">
                {/* 步骤1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">1</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">初步沟通</h3>
                    <p className="text-gray-700">了解孩子基本情况和家长需求，确定测评方向和内容</p>
                  </div>
                </div>
                {/* 步骤2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">2</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">专业测评</h3>
                    <p className="text-gray-700">采用科学的测评工具，进行多维度能力测评</p>
                  </div>
                </div>
                {/* 步骤3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">3</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">数据分析</h3>
                    <p className="text-gray-700">专业团队对测评数据进行深入分析，生成个性化报告</p>
                  </div>
                </div>
                {/* 步骤4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">4</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">结果解读</h3>
                    <p className="text-gray-700">专家详细解读测评报告，提供针对性的教育建议</p>
                  </div>
                </div>
                {/* 步骤5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">5</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">跟踪指导</h3>
                    <p className="text-gray-700">定期回访，评估发展进展，适时调整教育策略</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 适用人群 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">适用人群</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">年龄范围</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>3-6岁学前儿童</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>7-12岁小学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>13-15岁初中生</span></li>
              </ul>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">特别适合</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>希望了解孩子天赋潜能的家长</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>处于教育关键选择期的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>需要特殊教育指导的儿童</span></li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                测评需要多长时间？
              </div>
              <div className="p-5 text-gray-600">
                完整的测评过程通常需要2-3小时，会根据孩子的年龄和状态适当调整。测评后的分析和报告生成需要3-5个工作日。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                多久建议进行一次测评？
              </div>
              <div className="p-5 text-gray-600">
                建议每1-2年进行一次综合测评，以跟踪孩子的发展变化。在关键成长节点（如学前、小升初等）可适当增加测评频率。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                测评结果如何应用？
              </div>
              <div className="p-5 text-gray-600">
                测评结果将用于指导个性化学习方案制定、特长培养方向选择、学校选择等教育决策，我们的专家团队会提供具体的应用建议。
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 成功案例 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">成功案例</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例一：发现艺术天赋</h3>
              <p className="text-gray-700 mb-4">
                通过综合测评发现6岁小朋友在视觉艺术方面的突出天赋，及时进行艺术教育干预，现已在多个少儿美术比赛中获奖。
              </p>
              <div className="text-blue-600 font-medium">测评价值：及早发现天赋，精准培养方向</div>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例二：解决学习困扰</h3>
              <p className="text-gray-700 mb-4">
                通过学习能力测评发现10岁学生注意力分散的原因，制定针对性训练方案，学习效率显著提升。
              </p>
              <div className="text-blue-600 font-medium">测评价值：找准问题根源，精准改进方案</div>
            </div>
          </div>
        </div>
      </section>

      {/* 客户见证 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">客户见证</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "通过测评，我们更清楚地了解了孩子的特点和潜力，为制定学习计划提供了很好的参考。专家的指导也非常专业和有针对性。"
              </div>
              <div className="text-gray-600 font-medium">— 张女士，8岁孩子家长</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "测评报告很详细，解读也很清晰。最重要的是通过测评发现了孩子在音乐方面的天赋，现在正在接受专业的音乐教育。"
              </div>
              <div className="text-gray-600 font-medium">— 李先生，5岁孩子家长</div>
            </div>
          </div>
        </div>
      </section>

      {/* 开启服务 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6 text-gray-800">开启测评之旅</h2>
            <p className="text-gray-600 mb-8">
              让我们一起探索孩子的无限潜能，为其未来发展指明方向
            </p>
            <Link href="/contact" className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              立即预约
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}