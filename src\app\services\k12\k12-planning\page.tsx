'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiCheck, FiTarget, FiBook, FiBarChart2, FiUsers, FiLayers, FiStar, FiTrendingUp } from 'react-icons/fi';

export default function K12PlanningPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">个性化学习路径规划</h1>
            <p className="text-xl text-blue-100">
              根据孩子的特点和家庭期望，制定适合的学习计划和能力培养方案，助力全面发展。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                我们的个性化学习路径规划服务，基于对孩子全面的了解，结合家庭教育理念和期望，为孩子量身定制科学的学习发展路径，包括学科学习规划、能力培养计划、兴趣发展方向和综合素质提升策略。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                通过专业的评估工具和个性化的规划方案，帮助每个孩子找到最适合自己的学习方式和发展路径，激发学习潜能，培养核心素养，为未来的成长奠定坚实基础。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">学习能力与风格评估</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>认知能力测评（记忆力、注意力、逻辑思维等）</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>学习风格分析（视觉型、听觉型、动觉型等）</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>学习习惯与方法评估</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBook className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">学科学习规划</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>学科能力诊断与目标设定</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>个性化学习策略与方法指导</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>学科知识体系构建与关键点突破</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiStar className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">能力培养方案</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>核心素养培养计划（思维能力、创新能力、沟通能力等）</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>自主学习能力培养</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>问题解决能力训练</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目4 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiLayers className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">兴趣特长发展</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>兴趣探索与特长发现</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>课外活动与实践项目规划</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>特长培养资源对接</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">个性化定制，精准规划</h3>
              </div>
              <p className="text-gray-700">根据每个孩子的独特特点和需求，量身定制学习发展方案，避免千篇一律的学习模式。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">全面发展，均衡成长</h3>
              </div>
              <p className="text-gray-700">注重学科学习与能力培养的平衡，促进知识、能力、素养的协调发展。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">科学方法，持续优化</h3>
              </div>
              <p className="text-gray-700">采用科学的学习方法和策略，定期评估学习效果，持续优化学习路径。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">家校协同，共育共赢</h3>
              </div>
              <p className="text-gray-700">促进家庭和学校的有效沟通与协作，形成教育合力，共同支持孩子的成长。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute left-[50px] top-0 h-full w-1 bg-blue-200 md:hidden"></div>
              <div className="space-y-12">
                {/* 步骤1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">1</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">需求沟通</h3>
                    <p className="text-gray-700">深入了解孩子的学习现状、家庭教育理念和期望目标</p>
                  </div>
                </div>
                {/* 步骤2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">2</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">综合评估</h3>
                    <p className="text-gray-700">通过科学工具评估孩子的学习能力、风格、习惯和兴趣特长</p>
                  </div>
                </div>
                {/* 步骤3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">3</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">方案制定</h3>
                    <p className="text-gray-700">基于评估结果，制定个性化的学习路径规划方案</p>
                  </div>
                </div>
                {/* 步骤4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">4</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">方案实施</h3>
                    <p className="text-gray-700">指导家长和孩子落实学习规划，提供具体的实施建议和资源支持</p>
                  </div>
                </div>
                {/* 步骤5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">5</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">跟踪调整</h3>
                    <p className="text-gray-700">定期跟踪学习效果，根据反馈及时调整优化学习方案</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 适用人群 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">适用人群</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">年龄阶段</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>3-6岁学前儿童</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>7-12岁小学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>13-15岁初中生</span></li>
              </ul>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">特别适合</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>学习方法不当，效率低下的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>希望全面发展，培养综合素质的家庭</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>处于学习关键期，需要科学规划的学生</span></li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                什么年龄段的孩子适合进行个性化学习路径规划？
              </div>
              <div className="p-5 text-gray-600">
                我们的服务适合3-18岁的儿童和青少年，不同年龄段的规划重点有所不同。学前阶段重点是能力培养和习惯养成，小学阶段注重学习兴趣和方法培养，初中阶段关注学科能力和特长发展。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                学习路径规划需要多长时间？如何实施？
              </div>
              <div className="p-5 text-gray-600">
                完整的规划过程通常需要2-4周，包括测评、分析、方案制定和沟通反馈。规划完成后，我们会提供详细的实施指导，并定期进行跟进和调整，确保规划有效落地。家长和学校老师也会参与实施过程，形成教育合力。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                如何平衡学科学习和兴趣特长发展？
              </div>
              <div className="p-5 text-gray-600">
                我们的规划会根据孩子的实际情况，合理分配学科学习和兴趣特长发展的时间和资源。原则上，我们建议在保证学科学习质量的前提下，给予孩子充分探索和发展兴趣特长的机会，避免单一的学习模式，促进全面发展。
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 成功案例 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">成功案例</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例一：学习效率提升</h3>
              <p className="text-gray-700 mb-4">
                10岁小学生通过学习风格评估，发现是视觉型学习者，调整学习方法后，学习效率显著提升，成绩从班级中等提升至前列。
              </p>
              <div className="text-blue-600 font-medium">规划价值：找准学习方式，事半功倍</div>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例二：全面均衡发展</h3>
              <p className="text-gray-700 mb-4">
                8岁小学生通过能力评估和兴趣探索，制定科学的时间规划，在保持优异学业成绩的同时，培养了编程和音乐特长，实现全面发展。
              </p>
              <div className="text-blue-600 font-medium">规划价值：均衡发展，多元成长</div>
            </div>
          </div>
        </div>
      </section>

      {/* 客户见证 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">客户见证</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "通过个性化学习路径规划，我们找到了适合孩子的学习方法，不仅学习成绩提高了，更重要的是孩子学习的主动性和自信心都增强了。"
              </div>
              <div className="text-gray-600 font-medium">— 王女士，9岁孩子家长</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "专业的评估和规划让我们对孩子的教育有了更清晰的方向，不再盲目跟风。孩子现在的学习更有针对性，也更快乐。"
              </div>
              <div className="text-gray-600 font-medium">— 张先生，12岁孩子家长</div>
            </div>
          </div>
        </div>
      </section>

      {/* 开启服务 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6 text-gray-800">开启规划之旅</h2>
            <p className="text-gray-600 mb-8">
              让我们一起为孩子设计科学的学习发展路径，激发潜能，成就未来
            </p>
            <Link href="/contact" className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              立即预约
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}