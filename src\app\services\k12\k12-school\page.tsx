'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiCheck, FiTarget, FiBook, FiBarChart2, FiUsers, FiLayers, FiStar, FiCompass, FiAward } from 'react-icons/fi';

export default function K12SchoolPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">学校选择与入学指导</h1>
            <p className="text-xl text-blue-100">
              提供学校信息分析、申请策略和面试准备，助力孩子进入理想学校，开启美好未来。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                我们的学校选择与入学指导服务，为家庭提供全面的学校信息和分析，帮助家长根据孩子的特点和需求选择最适合的学校，并提供专业的申请指导和面试准备，提高入学成功率。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                我们拥有丰富的学校资源网络和专业的教育顾问团队，能够为每个家庭提供个性化的入学规划和全程指导，确保孩子顺利进入理想学校，开启成功的学习之旅。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiCompass className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">学校资源库与信息分析</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>提供全面的幼儿园、小学、初中、高中资源库</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>分析各类学校的教学特色、师资力量和录取要求</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>提供学校最新招生政策和录取趋势分析</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">个性化学校匹配</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>评估孩子的学习能力、性格特点和发展需求</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>结合家庭教育理念和期望，推荐最适合的学校选择</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>提供科学的学校匹配分析报告</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBook className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">申请策略与材料准备</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>制定详细的申请时间表和策略</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>指导准备申请材料，包括个人陈述、作品集和推荐信等</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>优化申请材料，突出孩子的优势和特点</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目4 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiAward className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">面试与考试准备</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>提供针对性的面试技巧指导和模拟训练</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>分析入学考试内容，提供备考建议和资料</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>心理调适指导，帮助孩子以最佳状态应对面试和考试</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">全面的学校资源网络</h3>
              </div>
              <p className="text-gray-700">拥有丰富的学校资源库和深入的学校关系网络，能够提供最新、最全面的学校信息和内部见解。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">精准的学校匹配系统</h3>
              </div>
              <p className="text-gray-700">通过科学的评估体系和匹配算法，为每个孩子找到最适合的学校，实现个性化教育资源配置。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">专业的申请指导团队</h3>
              </div>
              <p className="text-gray-700">由经验丰富的教育顾问和前招生官组成的专业团队，提供权威、实用的申请和面试指导。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">持续的入学后支持</h3>
              </div>
              <p className="text-gray-700">提供入学后的适应性指导和学业跟踪服务，确保孩子顺利融入新环境并充分发挥潜能。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute left-[50px] top-0 h-full w-1 bg-blue-200 md:hidden"></div>
              <div className="space-y-12">
                {/* 步骤1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">1</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">需求评估</h3>
                    <p className="text-gray-700">了解孩子的学习情况、性格特点和家庭教育期望，明确入学目标</p>
                  </div>
                </div>
                {/* 步骤2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">2</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">学校匹配</h3>
                    <p className="text-gray-700">根据评估结果，推荐最适合的学校选择，并安排实地考察</p>
                  </div>
                </div>
                {/* 步骤3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">3</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">申请规划</h3>
                    <p className="text-gray-700">制定详细的申请时间表和策略，指导准备申请材料</p>
                  </div>
                </div>
                {/* 步骤4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">4</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">面试考试准备</h3>
                    <p className="text-gray-700">提供面试和考试的针对性指导和训练，帮助孩子充分展示自己</p>
                  </div>
                </div>
                {/* 步骤5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">5</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">录取后指导</h3>
                    <p className="text-gray-700">提供入学前准备和入学后适应指导，确保顺利过渡</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 适用人群 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">适用人群</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">入学阶段</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>幼升小家庭</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>小升初家庭</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>初升高家庭</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>转学需求家庭</span></li>
              </ul>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">特别适合</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>对学校选择有困惑的家庭</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>希望进入优质学校的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>需要专业入学指导的家庭</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>对入学考试和面试有压力的学生</span></li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                什么时候开始规划学校选择最合适？
              </div>
              <div className="p-5 text-gray-600">
                建议提前1-2年开始规划学校选择，特别是对于热门学校或国际学校。幼升小、小升初、初升高的关键节点前，都应提前做好充分准备。提前规划可以给家庭更多时间了解学校、准备申请材料，并为孩子做好相应的能力培养。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                如何判断一所学校是否适合自己的孩子？
              </div>
              <div className="p-5 text-gray-600">
                判断学校是否适合需要考虑多方面因素：学校的教育理念是否与家庭教育观念一致；教学方式是否符合孩子的学习风格；学校的特色项目是否能满足孩子的兴趣发展；学校的环境和氛围是否适合孩子的性格特点；以及学校的地理位置、费用等实际因素。我们会通过科学评估和实地考察，帮助家庭做出全面判断。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                面对激烈的入学竞争，如何提高录取几率？
              </div>
              <div className="p-5 text-gray-600">
                提高录取几率需要综合策略：首先，选择适合度高且录取可能性较大的学校组合；其次，提前培养孩子的核心能力和特长，打造个人亮点；再次，精心准备申请材料，突出孩子的独特优势；最后，进行充分的面试和考试准备，包括模拟训练和心理调适。我们的服务会在每个环节提供专业指导，最大化录取成功率。
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 成功案例 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">成功案例</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例一：小升初成功录取</h3>
              <p className="text-gray-700 mb-4">
                通过全面评估和精准匹配，为性格内向但学习能力强的学生选择了注重个性化教育的重点学校，并针对性准备面试，最终成功录取该校实验班。
              </p>
              <div className="text-blue-600 font-medium">服务价值：精准定位，扬长避短</div>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例二：特长生入学指导</h3>
              <p className="text-gray-700 mb-4">
                为具有音乐特长的初中生制定特长生申请策略，指导准备作品集和面试，成功获得三所重点高中的录取通知，最终选择了最适合的学校。
              </p>
              <div className="text-blue-600 font-medium">服务价值：特长突破，多校选择</div>
            </div>
          </div>
        </div>
      </section>

      {/* 客户见证 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">客户见证</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "感谢专业的学校选择指导，让我们避免了盲目跟风，找到了真正适合孩子的学校。面试准备也非常有针对性，孩子顺利通过了心仪学校的面试。"
              </div>
              <div className="text-gray-600 font-medium">— 刘女士，小升初家长</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "整个申请过程得到了专业团队的全程指导，从学校选择到材料准备再到面试训练，每一步都很细致。最终孩子被理想的学校录取，非常感谢！"
              </div>
              <div className="text-gray-600 font-medium">— 陈先生，初升高家长</div>
            </div>
          </div>
        </div>
      </section>

      {/* 开启服务 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6 text-gray-800">开启入学之旅</h2>
            <p className="text-gray-600 mb-8">
              让我们一起为孩子选择最适合的学校，开启精彩的学习旅程
            </p>
            <Link href="/contact" className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              立即预约
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}