'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowRight, FiCheck, FiTarget, FiUsers, FiBook, FiBarChart2, FiTrendingUp, FiDatabase, FiClock } from 'react-icons/fi';

export default function ServicesPage() {
  // 服务分类数据
  const serviceCategories = [
    {
      id: 'k12',
      title: '学前及K12教育规划',
      description: '为孩子的成长制定科学的教育路径，培养核心素养与能力。',
      icon: <FiUsers className="text-white text-2xl" />,
      services: [
        {
          id: 'k12-assessment',
          title: '儿童潜能测评与分析',
          description: '通过科学测评工具，发现孩子的天赋潜能和学习特点，为教育规划提供基础。',
          image: '/services/k12-assessment.svg'
        },
        {
          id: 'k12-planning',
          title: '个性化学习路径规划',
          description: '根据孩子的特点和家庭期望，制定适合的学习计划和能力培养方案。',
          image: '/services/k12-planning.svg'
        },
        {
          id: 'k12-school',
          title: '学校选择与入学指导',
          description: '提供学校信息分析、申请策略和面试准备，助力孩子进入理想学校。',
          image: '/services/k12-school.svg'
        }
      ]
    },
    {
      id: 'high-school',
      title: '高中一体化升学规划',
      description: '科学选科、学业规划、志愿填报，助力高考升学全程指导。',
      icon: <FiBook className="text-white text-2xl" />, 
      services: [
        {
          id: 'subject-selection',
          title: '新高考选科深度指导',
          description: '基于学生兴趣、能力和目标院校专业要求，提供科学的选科建议。',
          image: '/services/subject-selection.svg'
        },
        {
          id: 'academic-planning',
          title: '高中学业与素质综合规划',
          description: '制定学业提升计划，指导学科竞赛、科研活动和社会实践，全面提升综合素质。',
          image: '/services/academic-planning.svg'
        },
        {
          id: 'college-application',
          title: '高考志愿填报精准辅导',
          description: '基于大数据分析和专业经验，提供精准的院校和专业选择建议。',
          image: '/services/college-application.svg'
        }
      ]
    },
    {
      id: 'university',
      title: '大学发展与深造规划',
      description: '专业发展、学术规划、考研留学，助力大学生涯全面发展。',
      icon: <FiTarget className="text-white text-2xl" />, 
      services: [
        {
          id: 'major-development',
          title: '专业学习与发展规划',
          description: '帮助大学生明确专业学习目标，制定学术发展路径。',
          image: '/services/major-development.svg'
        },
        {
          id: 'graduate-planning',
          title: '考研与保研规划指导',
          description: '提供考研院校专业选择、复习计划和保研策略指导。',
          image: '/services/graduate-planning.svg'
        },
        {
          id: 'overseas-study',
          title: '海外留学申请指导',
          description: '提供留学国家和院校选择、申请材料准备和面试辅导等全方位服务。',
          image: '/services/overseas-study.svg'
        }
      ]
    },
    {
      id: 'career',
      title: '职业人士生涯进阶服务',
      description: '职业规划、能力提升、转型指导，助力职场人士持续成长。',
      icon: <FiBarChart2 className="text-white text-2xl" />, 
      services: [
        {
          id: 'career-assessment',
          title: '职业定位与发展规划',
          description: '通过专业测评和分析，明确职业发展方向和目标。',
          image: '/services/career-assessment.svg'
        },
        {
          id: 'career-transition',
          title: '职业转型与跨界指导',
          description: '为有转型需求的职场人士提供能力评估、行业分析和转型策略。',
          image: '/services/career-transition.svg'
        },
        {
          id: 'leadership-development',
          title: '领导力提升与职场晋升',
          description: '针对管理者和有晋升需求的职场人士，提供领导力培养和晋升策略指导。',
          image: '/services/leadership-development.svg'
        }
      ]
    }
  ];

  return (
    <main className="min-h-screen">
      {/* 服务总览页面标题 - 使用现代渐变背景 */}
      <section className="py-24 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-sky-400 z-0">
          <div className="absolute inset-0 bg-[url('/hero/services-bg.svg')] bg-center bg-no-repeat bg-cover opacity-20"></div>
          <div className="absolute inset-0 bg-blue-900/30"></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white drop-shadow-sm">思立恒精细化服务体系</h1>
            <p className="text-xl text-sky-100 mb-10 leading-relaxed">
              覆盖教育与生涯全链路，以专业的态度、科学的方法和个性化的方案，助力每一位客户实现人生目标。
            </p>
            <Link 
              href="/contact" 
              className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-4 rounded-full font-medium text-lg inline-block transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 focus:outline-none"
            >
              立即咨询
            </Link>
          </div>
        </div>
      </section>

      {/* 服务分类导航与列表 */}
      <section className="py-20 bg-gradient-to-b from-white to-sky-50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-10">
            {/* 左侧导航 */}
            <div className="lg:w-1/4">
              <div className="bg-white p-6 rounded-xl shadow-md border border-sky-100 sticky top-24">
                <h2 className="text-2xl font-bold mb-6 text-gray-800 border-l-4 border-sky-500 pl-4">服务分类</h2>
                <ul className="space-y-3">
                  {serviceCategories.map((category) => (
                    <li key={category.id}>
                      <a 
                        href={`#${category.id}`} 
                        className="flex items-center p-3 rounded-lg hover:bg-sky-50 text-gray-700 hover:text-sky-600 transition-all duration-300 group"
                      >
                        <span className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 flex items-center justify-center mr-3 shadow-sm group-hover:shadow transform group-hover:scale-110 transition-all duration-300">
                          {category.icon}
                        </span>
                        <span className="font-medium">{category.title}</span>
                      </a>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* 右侧服务列表 */}
            <div className="lg:w-3/4">
              {serviceCategories.map((category, index) => (
                <div key={category.id} id={category.id} className="mb-24">
                  <div className="flex flex-col md:flex-row md:items-center mb-10 bg-white p-6 rounded-xl shadow-sm border-l-4 border-sky-500">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 flex items-center justify-center mr-6 shadow-md mb-4 md:mb-0">
                      {category.icon}
                    </div>
                    <div>
                      <h2 className="text-3xl font-bold text-gray-800 mb-2">{category.title}</h2>
                      <p className="text-gray-600">{category.description}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {category.services.map((service) => (
                      <div key={service.id} className="bg-white border border-sky-100 rounded-xl overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2 flex flex-col group">
                        <div className="relative w-full h-48 bg-sky-50 flex items-center justify-center overflow-hidden">
                          <img
                            src={service.image || '/images/default-service.jpg'}
                            alt={service.title}
                            className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
                          />
                          <div className="absolute inset-0 bg-gradient-to-t from-blue-900/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                        <div className="p-6 flex-1 flex flex-col">
                          <h3 className="text-xl font-bold mb-3 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">{service.title}</h3>
                          <p className="text-gray-600 mb-6 flex-1">{service.description}</p>
                          <Link 
                            href={`/services/${category.id}/${service.id}`} 
                            className="inline-flex items-center text-sky-600 hover:text-sky-800 font-medium group-hover:font-semibold mt-auto group-hover:translate-x-1 transition-all duration-300"
                          >
                            了解详情 
                            <FiArrowRight className="ml-2 group-hover:ml-3 transition-all" />
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-20 bg-gradient-to-b from-sky-50 to-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1 bg-sky-100 text-sky-700 rounded-full text-sm font-medium mb-4">专业流程</span>
            <h2 className="text-4xl font-bold mb-6 text-gray-800 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-sky-500">我们的服务流程</h2>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">我们提供专业、系统、高效的服务流程，确保每位客户都能获得最佳的规划方案和服务体验</p>
          </div>

          <div className="relative">
            {/* 连接线 - 仅在中等屏幕以上显示 */}
            <div className="hidden md:block absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-blue-200 via-sky-300 to-blue-200 -translate-y-1/2 z-0"></div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 relative z-10">
              {/* 步骤1 */}
              <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 flex flex-col items-center text-center transform transition-all duration-300 hover:-translate-y-2 hover:shadow-lg group">
                <div className="w-20 h-20 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 text-white flex items-center justify-center text-2xl font-bold mb-6 shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-110">
                  <span className="transform transition-all duration-500 group-hover:rotate-12">1</span>
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">需求咨询</h3>
                <p className="text-gray-600">深入了解您的背景、目标和需求，为您提供初步的规划建议和服务方向</p>
              </div>

              {/* 步骤2 */}
              <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 flex flex-col items-center text-center transform transition-all duration-300 hover:-translate-y-2 hover:shadow-lg group">
                <div className="w-20 h-20 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 text-white flex items-center justify-center text-2xl font-bold mb-6 shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-110">
                  <span className="transform transition-all duration-500 group-hover:rotate-12">2</span>
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">方案定制</h3>
                <p className="text-gray-600">根据您的具体情况，我们的专家团队将为您量身定制专业的规划方案</p>
              </div>

              {/* 步骤3 */}
              <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 flex flex-col items-center text-center transform transition-all duration-300 hover:-translate-y-2 hover:shadow-lg group">
                <div className="w-20 h-20 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 text-white flex items-center justify-center text-2xl font-bold mb-6 shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-110">
                  <span className="transform transition-all duration-500 group-hover:rotate-12">3</span>
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">执行实施</h3>
                <p className="text-gray-600">我们将协助您执行规划方案，提供全程指导和支持，确保方案顺利实施</p>
              </div>

              {/* 步骤4 */}
              <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 flex flex-col items-center text-center transform transition-all duration-300 hover:-translate-y-2 hover:shadow-lg group">
                <div className="w-20 h-20 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 text-white flex items-center justify-center text-2xl font-bold mb-6 shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-110">
                  <span className="transform transition-all duration-500 group-hover:rotate-12">4</span>
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">跟踪调整</h3>
                <p className="text-gray-600">定期跟进方案执行情况，根据实际效果和反馈进行必要的调整和优化</p>
              </div>
            </div>
          </div>

          <div className="mt-16 text-center">
            <Link 
              href="/appointment" 
              className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-sky-500 text-white font-medium rounded-lg hover:from-blue-700 hover:to-sky-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1 group"
            >
              预约咨询 <FiArrowRight className="ml-2 group-hover:ml-3 transition-all duration-300" />
            </Link>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1 bg-sky-100 text-sky-700 rounded-full text-sm font-medium mb-4">核心优势</span>
            <h2 className="text-4xl font-bold mb-6 text-gray-800 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-sky-500">我们的服务特色</h2>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">我们致力于提供最专业、最全面、最个性化的规划服务，以下是我们的核心服务特色</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* 特色1 */}
            <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
              <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-sky-400 text-white flex items-center justify-center mb-6 shadow-md group-hover:shadow-lg transform group-hover:scale-110 transition-all duration-300">
                <FiUsers size={28} className="group-hover:rotate-12 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">专业团队</h3>
              <p className="text-gray-600 mb-5">由资深教育专家、职业规划师和行业顾问组成的专业团队，拥有丰富的实践经验和专业知识</p>
              <ul className="text-gray-600 space-y-3">
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>多年教育与职业规划经验</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>国内外知名院校背景</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>持续的专业培训与更新</span>
                </li>
              </ul>
            </div>

            {/* 特色2 */}
            <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
              <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-sky-400 text-white flex items-center justify-center mb-6 shadow-md group-hover:shadow-lg transform group-hover:scale-110 transition-all duration-300">
                <FiTarget size={28} className="group-hover:rotate-12 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">个性化方案</h3>
              <p className="text-gray-600 mb-5">根据每位客户的独特背景、需求和目标，量身定制个性化的规划方案，而非千篇一律的标准化服务</p>
              <ul className="text-gray-600 space-y-3">
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>深入了解个人情况</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>科学的测评与分析</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>定制专属发展路径</span>
                </li>
              </ul>
            </div>

            {/* 特色3 */}
            <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
              <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-sky-400 text-white flex items-center justify-center mb-6 shadow-md group-hover:shadow-lg transform group-hover:scale-110 transition-all duration-300">
                <FiTrendingUp size={28} className="group-hover:rotate-12 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">全程支持</h3>
              <p className="text-gray-600 mb-5">从初步咨询到方案执行，再到长期跟踪，我们提供全程支持和指导，确保规划方案的有效实施</p>
              <ul className="text-gray-600 space-y-3">
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>一对一专属顾问</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>定期回访与调整</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>长期成长支持计划</span>
                </li>
              </ul>
            </div>

            {/* 特色4 */}
            <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
              <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-sky-400 text-white flex items-center justify-center mb-6 shadow-md group-hover:shadow-lg transform group-hover:scale-110 transition-all duration-300">
                <FiDatabase size={28} className="group-hover:rotate-12 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">资源网络</h3>
              <p className="text-gray-600 mb-5">拥有广泛的教育资源和行业资源网络，为客户提供全方位的支持和机会对接</p>
              <ul className="text-gray-600 space-y-3">
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>国内外院校资源</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>行业企业合作伙伴</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>专业社群与活动</span>
                </li>
              </ul>
            </div>

            {/* 特色5 */}
            <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
              <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-sky-400 text-white flex items-center justify-center mb-6 shadow-md group-hover:shadow-lg transform group-hover:scale-110 transition-all duration-300">
                <FiBarChart2 size={28} className="group-hover:rotate-12 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">数据驱动</h3>
              <p className="text-gray-600 mb-5">基于科学的测评工具和数据分析，为规划决策提供客观依据，提高方案的科学性和有效性</p>
              <ul className="text-gray-600 space-y-3">
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>专业测评工具</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>数据分析与解读</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>基于数据的方案优化</span>
                </li>
              </ul>
            </div>

            {/* 特色6 */}
            <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 group">
              <div className="w-16 h-16 rounded-lg bg-gradient-to-br from-blue-500 to-sky-400 text-white flex items-center justify-center mb-6 shadow-md group-hover:shadow-lg transform group-hover:scale-110 transition-all duration-300">
                <FiClock size={28} className="group-hover:rotate-12 transition-transform duration-300" />
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300">持续更新</h3>
              <p className="text-gray-600 mb-5">持续关注教育政策、行业趋势和市场变化，不断更新服务内容和方法，确保规划的前瞻性和实用性</p>
              <ul className="text-gray-600 space-y-3">
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>政策动态跟踪</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>行业趋势研究</span>
                </li>
                <li className="flex items-start">
                  <span className="text-sky-500 mt-1 mr-3 flex-shrink-0"><FiCheck className="w-5 h-5" /></span>
                  <span>服务方法创新</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-20 bg-gradient-to-b from-white to-sky-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1 bg-sky-100 text-sky-700 rounded-full text-sm font-medium mb-4">解答疑惑</span>
            <h2 className="text-4xl font-bold mb-6 text-gray-800 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-sky-500">常见问题</h2>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">以下是我们的客户经常咨询的问题，如果您有其他疑问，欢迎随时联系我们</p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="space-y-6">
              {/* 问题1 */}
              <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 group">
                <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300 flex items-center">
                  <span className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 text-white flex items-center justify-center mr-3 text-sm font-bold">Q</span>
                  你们的服务适合哪些人群？
                </h3>
                <p className="text-gray-600 pl-11">我们的服务适合各个阶段的学生和职业人士，包括学前及K12阶段的学生、高中生、大学生以及职业发展中的各类人士。无论您是需要学业规划、升学指导、职业发展还是转行规划，我们都能提供专业的支持和指导。</p>
              </div>

              {/* 问题2 */}
              <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 group">
                <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300 flex items-center">
                  <span className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 text-white flex items-center justify-center mr-3 text-sm font-bold">Q</span>
                  服务流程是怎样的？需要准备什么？
                </h3>
                <p className="text-gray-600 pl-11">我们的服务流程通常包括初步咨询、需求评估、方案制定、执行支持和跟踪调整等环节。在初次咨询前，您可以准备一些基本信息，如教育背景、职业经历、兴趣爱好、目标等，这将有助于我们更好地了解您的需求和情况。</p>
              </div>

              {/* 问题3 */}
              <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 group">
                <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300 flex items-center">
                  <span className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 text-white flex items-center justify-center mr-3 text-sm font-bold">Q</span>
                  服务费用是如何计算的？
                </h3>
                <p className="text-gray-600 pl-11">我们的服务费用根据服务类型、内容和周期等因素而定。我们提供不同层次的服务套餐，以满足不同客户的需求和预算。具体费用请在初步咨询时与我们的顾问沟通，我们会根据您的具体需求提供详细的报价。</p>
              </div>

              {/* 问题4 */}
              <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 group">
                <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300 flex items-center">
                  <span className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 text-white flex items-center justify-center mr-3 text-sm font-bold">Q</span>
                  如何评估服务的效果？
                </h3>
                <p className="text-gray-600 pl-11">我们会根据客户的具体目标和需求，设定明确的评估指标和里程碑。在服务过程中，我们会定期与客户沟通，了解方案执行情况和效果，并根据反馈进行必要的调整。我们的成功取决于客户目标的实现和满意度。</p>
              </div>

              {/* 问题5 */}
              <div className="bg-white p-8 rounded-xl shadow-md border border-sky-100 hover:shadow-lg transition-all duration-300 group">
                <h3 className="text-xl font-bold mb-4 text-gray-800 group-hover:text-sky-600 transition-colors duration-300 flex items-center">
                  <span className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 text-white flex items-center justify-center mr-3 text-sm font-bold">Q</span>
                  可以随时终止服务吗？
                </h3>
                <p className="text-gray-600 pl-11">我们理解客户的需求和情况可能会发生变化。在签订服务协议时，我们会明确约定服务期限和终止条件。如果您需要终止服务，请提前与我们沟通，我们会根据协议条款处理相关事宜，并尽力为您提供最大的灵活性。</p>
              </div>
            </div>

            <div className="mt-16 text-center">
              <p className="text-gray-600 mb-8 text-lg">还有其他问题？欢迎随时联系我们</p>
              <Link 
                href="/contact" 
                className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-sky-500 text-white font-medium rounded-lg hover:from-blue-700 hover:to-sky-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1 group"
              >
                联系我们 <FiArrowRight className="ml-2 group-hover:ml-3 transition-all duration-300" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}