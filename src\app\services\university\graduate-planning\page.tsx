'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiTarget, FiBarChart2, FiUsers, FiCheck, FiBook, FiActivity, FiHeart, FiStar } from 'react-icons/fi';

export default function GraduatePlanningPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">考研与保研规划指导</h1>
            <p className="text-xl text-blue-100">
              科学规划考研或保研路径，提供专业选择、院校匹配和备考策略指导，助力学子实现理想升学目标。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                我们的考研与保研规划指导服务，由资深升学顾问和名校导师团队提供，基于对学生个人情况和目标院校要求的深入分析，制定科学合理的升学规划方案。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                从专业选择、院校匹配到备考策略，全方位指导学生高效备考，提高录取成功率，助力实现理想的学术深造目标。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">个人评估与目标定位</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>学业成绩与能力评估</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>研究兴趣与职业规划分析</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>考研与保研可行性评估</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBarChart2 className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">院校与专业选择指导</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>目标院校分析与匹配</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>专业方向选择建议</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>历年录取数据分析</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiActivity className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">备考策略与学习规划</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>个性化备考时间规划</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>科目学习方法指导</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>模拟测试与能力提升</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">名校导师团队</h3>
              </div>
              <p className="text-gray-700">由知名高校教授和招生委员会成员组成的专业团队，提供内部视角和专业指导。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">个性化规划方案</h3>
              </div>
              <p className="text-gray-700">根据学生个人情况和目标，量身定制升学规划方案，避免盲目备考。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">全程跟踪指导</h3>
              </div>
              <p className="text-gray-700">从规划制定到最终录取，提供全程跟踪指导和调整，确保计划有效执行。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute left-[50px] top-0 h-full w-1 bg-blue-200 md:hidden"></div>
              <div className="space-y-12">
                {/* 步骤1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">1</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">初步咨询</h3>
                    <p className="text-gray-700">了解学生基本情况、学业成绩和升学意向</p>
                  </div>
                </div>
                {/* 步骤2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">2</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">深度评估</h3>
                    <p className="text-gray-700">全面评估学生能力、兴趣和目标，分析考研/保研可行性</p>
                  </div>
                </div>
                {/* 步骤3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">3</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">方案制定</h3>
                    <p className="text-gray-700">制定个性化升学规划方案，包括院校选择、备考计划等</p>
                  </div>
                </div>
                {/* 步骤4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">4</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">执行指导</h3>
                    <p className="text-gray-700">提供备考指导、学习方法建议和定期模拟测试</p>
                  </div>
                </div>
                {/* 步骤5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">5</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">调整优化</h3>
                    <p className="text-gray-700">根据备考进展和模拟测试结果，调整优化备考策略</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 适用人群 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">适用人群</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">考研人群</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>计划考研的大三、大四学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>已毕业但希望再次考研的往届生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>希望跨专业考研的学生</span></li>
              </ul>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">保研人群</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>成绩优异有保研资格的大三学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>希望通过夏令营提前锁定保研名额的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>希望跨校或跨专业保研的学生</span></li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                什么时候开始准备考研/保研最合适？
              </div>
              <div className="p-5 text-gray-600">
                考研建议至少提前一年开始准备，大三下学期是理想的启动时间；保研则需要从大一开始注重学业成绩，大三上学期开始积极准备夏令营和预推免。越早规划，越能从容应对。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                如何选择适合自己的院校和专业？
              </div>
              <div className="p-5 text-gray-600">
                选择院校和专业需综合考虑个人兴趣、职业规划、学术能力和院校实力。我们会通过深入评估和数据分析，帮助您找到最匹配的选择，平衡理想与现实。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                跨专业考研/保研有哪些挑战？
              </div>
              <div className="p-5 text-gray-600">
                跨专业面临的主要挑战包括专业基础知识差距、学科思维转换和竞争压力增大。我们会提供针对性的学习计划和专业知识补充方案，帮助您顺利跨越专业壁垒。
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 成功案例 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">成功案例</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例一：普通院校到顶尖名校</h3>
              <p className="text-gray-700 mb-4">
                来自二本院校的学生，通过科学规划和系统备考，最终成功考入清华大学计算机专业，实现了学术深造的飞跃。
              </p>
              <div className="text-blue-600 font-medium">关键因素：精准定位、系统备考、心理调适</div>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例二：成功跨专业保研</h3>
              <p className="text-gray-700 mb-4">
                机械专业学生通过早期规划和有针对性的能力培养，成功保研至北京大学人工智能专业，实现了专业转型。
              </p>
              <div className="text-blue-600 font-medium">关键因素：提前规划、专业知识补充、导师沟通</div>
            </div>
          </div>
        </div>
      </section>

      {/* 客户见证 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">客户见证</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "从大三开始接受规划指导，顾问老师不仅帮我制定了详细的备考计划，还在心理调适上给予了很大帮助。最终我成功考入了梦想中的学校，非常感谢！"
              </div>
              <div className="text-gray-600 font-medium">— 王同学，成功考入北京大学</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "作为跨专业保研的学生，我面临很多挑战。顾问团队不仅帮我分析了各个目标院校的情况，还针对性地提供了专业知识补充方案，最终成功获得了理想院校的录取。"
              </div>
              <div className="text-gray-600 font-medium">— 李同学，成功保研至复旦大学</div>
            </div>
          </div>
        </div>
      </section>

      {/* 开启服务 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6 text-gray-800">开启升学规划之旅</h2>
            <p className="text-gray-600 mb-8">
              让我们一起规划您的学术深造之路，实现理想院校的录取目标
            </p>
            <Link href="/contact" className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              立即预约
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}