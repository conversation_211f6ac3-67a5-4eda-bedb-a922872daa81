'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiCheck, FiTarget, FiBook, FiBarChart2, FiUsers, FiLayers, FiStar, FiCompass, FiAward } from 'react-icons/fi';

export default function MajorDevelopmentPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">大学专业发展规划</h1>
            <p className="text-xl text-blue-100">
              帮助大学生明确专业发展方向，制定学业与职业规划，提升核心竞争力。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                大学阶段是专业知识学习和职业能力培养的关键期，科学合理的专业发展规划对于提升学业表现和职业竞争力至关重要。我们的大学专业发展规划服务，基于对学生专业兴趣、能力特点和职业目标的全面评估，为大学生量身定制系统化的专业发展方案。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                通过专业的学业指导、能力培养规划和职业发展建议，帮助大学生明确发展方向，优化学习路径，培养核心竞争力，为未来的职业发展和深造奠定坚实基础。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiTarget className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">专业发展评估</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>评估专业兴趣、能力特点和职业倾向</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>分析专业课程体系和发展方向</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>明确专业学习目标和发展定位</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBook className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">学业规划指导</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>制定科学的课程选择和学习计划</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>提供专业学习方法和技巧指导</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>规划学术研究和科研项目参与</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiLayers className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">能力培养规划</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>专业核心能力培养方案</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>职场通用能力提升计划</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>领导力和创新能力培养</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目4 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">4</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiCompass className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">职业发展规划</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>分析专业相关职业发展路径</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>规划实习和实践经历积累</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>提供就业准备和求职指导</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">专业导向，精准规划</h3>
              </div>
              <p className="text-gray-700">基于专业特点和行业需求，提供精准的发展规划，避免盲目学习和发展。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">理论实践，双轨并行</h3>
              </div>
              <p className="text-gray-700">注重理论学习与实践经验的结合，培养全面的专业能力和实践技能。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">能力为本，全面发展</h3>
              </div>
              <p className="text-gray-700">以能力培养为核心，促进专业能力与通用能力的协调发展。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">前瞻视野，职业导向</h3>
              </div>
              <p className="text-gray-700">关注行业发展趋势和职业需求变化，提供前瞻性的发展建议。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute left-[50px] top-0 h-full w-1 bg-blue-200 md:hidden"></div>
              <div className="space-y-12">
                {/* 步骤1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">1</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">初步咨询</h3>
                    <p className="text-gray-700">了解学生的专业背景、学习情况和发展目标</p>
                  </div>
                </div>
                {/* 步骤2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">2</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">专业评估</h3>
                    <p className="text-gray-700">评估专业兴趣、能力特点和职业倾向，分析专业发展方向</p>
                  </div>
                </div>
                {/* 步骤3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">3</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">方案制定</h3>
                    <p className="text-gray-700">制定个性化的专业发展规划方案</p>
                  </div>
                </div>
                {/* 步骤4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">4</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">执行指导</h3>
                    <p className="text-gray-700">提供具体的学习和能力培养指导，帮助学生有效执行规划</p>
                  </div>
                </div>
                {/* 步骤5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">5</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">跟踪调整</h3>
                    <p className="text-gray-700">定期跟踪发展进展，根据反馈及时调整优化规划方案</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 适用人群 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">适用人群</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">学习阶段</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>大一新生（专业学习起步阶段）</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>大二大三学生（专业深入学习阶段）</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>大四学生（就业或深造准备阶段）</span></li>
              </ul>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">特别适合</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>对专业发展方向不明确的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>希望提升专业竞争力的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>有明确职业目标需要系统规划的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>准备考研或出国深造的学生</span></li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                什么时候开始专业发展规划最合适？
              </div>
              <div className="p-5 text-gray-600">
                理想的时间是大一开学初期，这样可以尽早明确发展方向，系统规划学习路径。但无论何时开始，我们都会根据学生当前的学习阶段和需求，制定适合的规划方案。大二、大三甚至大四的学生也可以通过专业发展规划，优化学习策略，提高竞争力。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                如何平衡专业学习与实践经验积累？
              </div>
              <div className="p-5 text-gray-600">
                平衡专业学习与实践经验需要科学规划时间和资源。我们建议在保证专业课程学习质量的前提下，有计划地参与实习、科研项目和社会实践，将理论知识应用于实际场景。具体的时间分配和侧重点会根据专业特点、学习阶段和发展目标进行个性化设计。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                专业发展规划如何应对就业市场的变化？
              </div>
              <div className="p-5 text-gray-600">
                我们的专业发展规划注重培养学生的核心竞争力和适应能力，而不仅仅是针对当前就业市场的需求。我们会关注行业发展趋势和职业需求变化，提供前瞻性的发展建议，同时培养学生的学习能力、创新能力和适应能力，使其能够应对就业市场的变化和挑战。
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 成功案例 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">成功案例</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例一：专业能力提升</h3>
              <p className="text-gray-700 mb-4">
                计算机专业大二学生通过系统规划和能力培养，在专业核心课程取得优异成绩，同时参与多个实践项目，积累了丰富的项目经验，最终获得知名互联网公司的实习机会。
              </p>
              <div className="text-blue-600 font-medium">规划价值：理论实践结合，能力全面提升</div>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例二：深造之路</h3>
              <p className="text-gray-700 mb-4">
                金融专业大三学生通过专业发展规划，明确了学术研究方向，积极参与科研项目，发表学术论文，最终成功申请到国外知名大学的硕士项目。
              </p>
              <div className="text-blue-600 font-medium">规划价值：明确方向，学术突破</div>
            </div>
          </div>
        </div>
      </section>

      {/* 客户见证 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">客户见证</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "通过专业发展规划，我找到了适合自己的发展方向，学习更有目标性，也更加高效。不仅专业成绩提高了，还积累了宝贵的实践经验，为未来的就业打下了坚实基础。"
              </div>
              <div className="text-gray-600 font-medium">— 小陈，计算机专业大三学生</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "专业规划帮助我明确了学术研究方向，指导我如何系统地积累科研经验和提升学术能力，最终成功申请到了理想的研究生项目。"
              </div>
              <div className="text-gray-600 font-medium">— 小林，经济学专业大四学生</div>
            </div>
          </div>
        </div>
      </section>

      {/* 开启服务 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6 text-gray-800">开启规划之旅</h2>
            <p className="text-gray-600 mb-8">
              让我们一起规划你的专业发展路径，提升核心竞争力，成就美好未来
            </p>
            <Link href="/contact" className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              立即预约
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}