'use client';

import React from 'react';
import Link from 'next/link';
import { FiArrowLeft, FiTarget, FiBarChart2, FiUsers, FiCheck, FiBook, FiActivity, FiHeart, FiStar, FiGlobe } from 'react-icons/fi';

export default function OverseasStudyPage() {
  return (
    <main className="min-h-screen">
      {/* 页面标题区 - 渐变背景 */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 py-20 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-4">
              <Link href="/services" className="text-white hover:text-blue-100 flex items-center">
                <FiArrowLeft className="mr-2" /> 返回服务体系
              </Link>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white">海外留学申请指导</h1>
            <p className="text-xl text-blue-100">
              专业规划海外留学路径，提供院校选择、申请材料准备和面试辅导，助您实现世界名校梦想。
            </p>
            <div className="mt-10">
              <Link href="/contact" className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
                立即咨询
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 服务介绍 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-center text-gray-800">服务介绍</h2>
            <div className="bg-blue-50 p-8 rounded-xl">
              <p className="text-lg text-gray-700 leading-relaxed">
                我们的海外留学申请指导服务由具有丰富国际教育背景的顾问团队提供，覆盖美国、英国、加拿大、澳大利亚等主要留学国家，针对本科、硕士和博士不同阶段的申请需求。
              </p>
              <p className="text-lg text-gray-700 leading-relaxed mt-4">
                从留学规划、院校选择到文书准备、面试辅导，我们提供全方位的专业指导，帮助学生提高申请竞争力，获得理想院校的录取通知书。
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务内容 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务内容</h2>
          <div className="max-w-5xl mx-auto">
            <div className="space-y-12">
              {/* 服务项目1 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">1</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiGlobe className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">留学规划与院校选择</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>个人背景评估与留学目标确定</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>国家与院校匹配分析</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>专业选择与职业发展规划</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目2 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">2</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiBook className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">申请材料准备指导</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>个人陈述与文书写作指导</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>推荐信策划与修改</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>简历优化与活动规划</span></li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 服务项目3 */}
              <div className="flex flex-col md:flex-row items-start gap-6">
                <div className="md:w-16 flex-shrink-0 bg-blue-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold">3</div>
                <div className="flex-grow">
                  <div className="flex items-center mb-4">
                    <FiUsers className="text-blue-600 text-2xl mr-3" />
                    <h3 className="text-2xl font-bold text-gray-800">面试与语言考试辅导</h3>
                  </div>
                  <div className="bg-white p-6 rounded-xl shadow-sm">
                    <ul className="space-y-3 text-gray-700">
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>院校面试技巧与模拟训练</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>托福/雅思/GRE/GMAT考试指导</span></li>
                      <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>语言能力提升方案</span></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 服务特色 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务特色</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">海外名校资源网络</h3>
              </div>
              <p className="text-gray-700">拥有全球顶尖大学的招生资源和校友网络，提供第一手申请信息和内部视角。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">专业文书团队</h3>
              </div>
              <p className="text-gray-700">由海外名校毕业的文书专家组成，熟悉各国学校的申请偏好和评判标准。</p>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl hover:shadow-md transition-shadow">
              <div className="flex items-center mb-4">
                <FiCheck className="text-blue-600 text-xl mr-3" />
                <h3 className="text-xl font-bold text-gray-800">全程一对一指导</h3>
              </div>
              <p className="text-gray-700">每位学生配备专属顾问，提供个性化申请方案和全程跟踪服务。</p>
            </div>
          </div>
        </div>
      </section>

      {/* 服务流程 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">服务流程</h2>
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <div className="absolute left-[50px] top-0 h-full w-1 bg-blue-200 md:hidden"></div>
              <div className="space-y-12">
                {/* 步骤1 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">1</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">初步咨询</h3>
                    <p className="text-gray-700">了解学生背景、学术成绩和留学意向，确定服务方案</p>
                  </div>
                </div>
                {/* 步骤2 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">2</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">留学规划</h3>
                    <p className="text-gray-700">制定个性化留学规划，包括国家选择、院校筛选和申请时间表</p>
                  </div>
                </div>
                {/* 步骤3 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">3</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">材料准备</h3>
                    <p className="text-gray-700">指导准备个人陈述、推荐信、简历等申请材料，进行多轮修改</p>
                  </div>
                </div>
                {/* 步骤4 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">4</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">申请提交</h3>
                    <p className="text-gray-700">协助完成在线申请表格填写和材料提交，确保申请质量</p>
                  </div>
                </div>
                {/* 步骤5 */}
                <div className="flex flex-col md:flex-row items-start gap-6">
                  <div className="z-10 flex items-center justify-center w-16 h-16 rounded-full bg-blue-600 text-white font-bold text-xl">5</div>
                  <div className="flex-grow bg-white p-6 rounded-xl shadow-sm">
                    <h3 className="text-xl font-bold mb-2 text-gray-800">后续服务</h3>
                    <p className="text-gray-700">提供面试辅导、签证指导和行前准备等后续服务</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 适用人群 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">适用人群</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">申请阶段</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>本科留学申请者</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>硕士研究生申请者</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>博士及高级学位申请者</span></li>
              </ul>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">特别适合</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>希望申请世界顶尖名校的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>需要全面提升申请竞争力的学生</span></li>
                <li className="flex items-start"><span className="text-blue-500 mr-2">•</span><span>缺乏海外申请经验的家庭</span></li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 常见问题 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">常见问题</h2>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                申请海外名校需要提前多久准备？
              </div>
              <div className="p-5 text-gray-600">
                申请世界顶尖名校建议提前1-2年开始准备，包括学术成绩提升、标准化考试、课外活动和个人能力培养等。硕士申请至少提前1年，本科申请建议高中阶段就开始规划。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                如何提高申请竞争力？
              </div>
              <div className="p-5 text-gray-600">
                提高申请竞争力需要多方面努力：保持优异的学术成绩、取得理想的标准化考试分数、参与有深度的课外活动、获得相关领域的实习或研究经历、准备出色的申请文书，以及展现个人独特价值和潜力。
              </div>
            </div>
            <div className="bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
              <div className="bg-blue-50 p-5 font-medium text-gray-800 border-l-4 border-blue-600">
                申请费用和奖学金情况如何？
              </div>
              <div className="p-5 text-gray-600">
                海外留学费用因国家、学校和专业而异。我们会提供详细的费用分析和规划，并指导学生申请各类奖学金、助学金和校内工作机会，帮助降低留学成本。
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 成功案例 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">成功案例</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例一：常春藤名校录取</h3>
              <p className="text-gray-700 mb-4">
                普通高中学生通过系统规划和全面提升，最终获得哈佛大学、耶鲁大学等多所常春藤名校录取，并获得部分奖学金。
              </p>
              <div className="text-blue-600 font-medium">关键因素：早期规划、学术卓越、独特背景故事</div>
            </div>
            <div className="bg-blue-50 p-6 rounded-xl">
              <h3 className="text-xl font-bold mb-4 text-gray-800">案例二：跨专业申请成功</h3>
              <p className="text-gray-700 mb-4">
                工科背景学生成功申请到伦敦政治经济学院商业分析专业，实现了专业转换和职业发展方向的调整。
              </p>
              <div className="text-blue-600 font-medium">关键因素：专业背景转化、实习经历、有说服力的个人陈述</div>
            </div>
          </div>
        </div>
      </section>

      {/* 客户见证 */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-12 text-center text-gray-800">客户见证</h2>
          <div className="max-w-4xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "从高二开始接受留学规划指导，顾问老师帮我制定了详细的提升计划和申请策略。最终我收到了多所美国顶尖大学的录取，非常感谢团队的专业指导！"
              </div>
              <div className="text-gray-600 font-medium">— 张同学，斯坦福大学录取</div>
            </div>
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <div className="text-gray-700 mb-4">
                "作为一名工作多年后再申请留学的学生，我面临很多挑战。顾问团队不仅帮我梳理了申请思路，还针对性地提升了我的申请亮点，最终获得了理想院校的录取。"
              </div>
              <div className="text-gray-600 font-medium">— 李先生，伦敦商学院MBA录取</div>
            </div>
          </div>
        </div>
      </section>

      {/* 开启服务 */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6 text-gray-800">开启留学申请之旅</h2>
            <p className="text-gray-600 mb-8">
              让我们一起规划您的海外留学之路，实现世界名校梦想
            </p>
            <Link href="/contact" className="bg-blue-600 text-white hover:bg-blue-700 px-8 py-3 rounded-full font-medium text-lg inline-block transition-colors">
              立即预约
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}