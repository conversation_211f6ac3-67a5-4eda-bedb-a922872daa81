'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useParams } from 'next/navigation';

export default function TeamMemberPage() {
  const params = useParams();
  const memberId = parseInt(params.id as string);

  // 团队成员数据
  const teamMembers = [
    {
      id: 1,
      name: '王老师',
      title: '首席生涯规划师',
      specialties: ['K12教育规划', '新高考选科', '大学专业选择'],
      imageUrl: '/team/member-1.jpg',
      bio: '王老师拥有超过15年的教育咨询经验，成功指导上千名学生进入理想的学府与职业道路。专注于K12教育规划和新高考选科指导，具有丰富的实战经验。',
      experience: '15年+',
      education: '北京师范大学教育学博士',
      achievements: ['指导学生超过1000名', '高考志愿填报成功率98%', '获得教育部优秀咨询师称号'],
      availableTime: ['周一至周五 9:00-18:00', '周六 9:00-17:00'],
      detailedBio: '王老师毕业于北京师范大学教育学专业，获得博士学位。在过去的15年中，她专注于K12教育规划和升学指导领域，帮助众多学生找到适合自己的教育路径。她对新高考政策和选科策略有深入研究，能够根据学生的兴趣、能力和未来发展方向，提供个性化的选科建议和升学规划。\n\n王老师善于与青少年沟通，能够准确把握学生的需求和潜力，并结合当前教育政策和趋势，制定科学合理的教育规划方案。她的咨询风格温和而有力，注重培养学生的自主学习能力和生涯规划意识。',
      cases: [
        '帮助一位理科偏弱的学生通过合理选科和规划，最终考入理想的985大学',
        '指导一位对未来迷茫的初中生发现自己的兴趣所在，制定明确的高中学习计划',
        '为一个留学家庭设计完整的国际教育衔接方案，实现平稳过渡'
      ]
    },
    {
      id: 2,
      name: '李博士',
      title: '资深心理咨询师',
      specialties: ['青少年心理辅导', '家庭关系调适', '情绪压力管理'],
      imageUrl: '/team/member-2.jpg',
      bio: '李博士专注于青少年心理健康领域，以其深深的专业知识和丰富的实践经验，帮助众多家庭和学生走出困境。擅长青少年心理问题诊断和治疗。',
      experience: '12年+',
      education: '中科院心理研究所临床心理学博士',
      achievements: ['心理咨询案例超过800例', '发表学术论文20余篇', '国家二级心理咨询师'],
      availableTime: ['周二至周六 10:00-19:00', '周日 14:00-18:00'],
      detailedBio: '李博士毕业于中科院心理研究所，获得临床心理学博士学位。她在青少年心理健康领域有12年以上的专业经验，擅长处理青少年常见的心理问题，如学习压力、人际关系、情绪管理等。她采用认知行为疗法、家庭系统治疗等多种方法，帮助青少年建立健康的心理状态和积极的生活态度。\n\n李博士同时关注家庭关系对青少年成长的影响，善于通过家庭咨询改善亲子关系，创造良好的家庭教育环境。她温和而坚定的咨询风格，使她能够与青少年建立良好的咨询关系，有效帮助他们走出心理困境。',
      cases: [
        '帮助一位有严重考试焦虑的高中生克服心理障碍，重拾学习信心',
        '通过家庭治疗改善一个问题家庭的亲子关系，解决孩子的行为问题',
        '为一位抑郁症青少年提供长期心理支持，成功帮助其回归正常学习生活'
      ]
    },
    {
      id: 3,
      name: '赵顾问',
      title: '职业发展规划专家',
      specialties: ['大学生就业指导', '职场进阶规划', '创业咨询'],
      imageUrl: '/team/member-3.jpg',
      bio: '赵顾问在人力资源和职业发展领域有独到见解，擅长为不同阶段的职场人士提供个性化的发展建议。曾任知名企业HR总监，具有丰富的企业管理经验。',
      experience: '18年+',
      education: '清华大学MBA，人力资源管理硕士',
      achievements: ['服务企业客户200+家', '职业规划成功案例1500+', 'GCDF全球职业规划师'],
      availableTime: ['周一至周五 14:00-20:00', '周六 9:00-18:00'],
      detailedBio: '赵顾问拥有清华大学MBA学位和人力资源管理硕士学位，曾在多家知名企业担任HR总监职位。她在职业发展规划领域有18年的丰富经验，对人才发展和职业规划有独到见解。她擅长帮助大学生进行职业定位和就业指导，为职场人士提供职业转型和晋升规划，以及为创业者提供创业咨询服务。\n\n赵顾问的咨询风格注重实用性和可操作性，她善于结合当前就业市场趋势和个人特质，制定切实可行的职业发展方案。她的咨询服务得到了众多客户的高度评价，被认为是能够真正帮助人们实现职业目标的专业顾问。',
      cases: [
        '帮助一位跨专业就业的应届毕业生成功进入理想行业',
        '为一位职场瓶颈期的中层管理者设计职业突破方案，成功晋升高管',
        '指导一位创业者完善商业计划，获得风投融资'
      ]
    },
    {
      id: 4,
      name: '张教授',
      title: '国际教育专家',
      specialties: ['海外留学规划', '国际课程指导', '语言能力提升'],
      imageUrl: '/team/member-4.jpg',
      bio: '张教授在国际教育领域深耕多年，对各国教育体系和申请流程了如指掌。曾在美国、英国多所知名大学担任招生官，为学生提供最权威的留学指导。',
      experience: '20年+',
      education: '哈佛大学教育学博士',
      achievements: ['成功送出留学生2000+名', '藤校录取率35%', '获得国际教育贡献奖'],
      availableTime: ['周一至周五 9:00-17:00', '周六 10:00-16:00'],
      detailedBio: '张教授拥有哈佛大学教育学博士学位，在国际教育领域有20多年的经验。她曾在美国和英国多所知名大学担任招生官，对国际教育体系和留学申请流程有深入了解。她擅长为学生提供个性化的留学规划，包括学校选择、专业定位、申请材料准备等全方位指导。\n\n张教授对各国教育体系的差异和特点有深入研究，能够帮助学生根据自身情况选择最适合的留学国家和院校。她的咨询服务注重培养学生的国际视野和跨文化能力，为学生的海外学习生活做好全面准备。',
      cases: [
        '指导一位普通大学学生成功申请到哈佛大学研究生项目',
        '为一个家庭设计从小学到大学的完整国际教育路径',
        '帮助一位语言成绩不理想的学生通过特色申请材料获得名校录取'
      ]
    },
    {
      id: 5,
      name: '陈老师',
      title: '学习能力提升专家',
      specialties: ['学习方法指导', '注意力训练', '记忆力提升'],
      imageUrl: '/team/member-5.jpg',
      bio: '陈老师专注于学习能力提升和认知训练，运用科学的方法帮助学生提高学习效率。在注意力训练和记忆力提升方面有独特的教学方法。',
      experience: '10年+',
      education: '华东师范大学认知心理学硕士',
      achievements: ['学习能力提升案例500+', '开发专利学习方法3项', '出版学习指导书籍2本'],
      availableTime: ['周一至周五 15:00-19:00', '周日 9:00-17:00'],
      detailedBio: '陈老师毕业于华东师范大学，获得认知心理学硕士学位。她专注于学习能力提升和认知训练领域，开发了多种有效的学习方法和记忆技巧。她擅长针对不同学生的学习特点，制定个性化的学习能力提升方案，帮助学生克服学习障碍，提高学习效率。\n\n陈老师的教学方法融合了认知心理学、教育学和脑科学的最新研究成果，通过科学的训练方法帮助学生提升注意力、记忆力和思维能力。她的课程生动有趣，深受学生喜爱，被誉为能够真正改变学习方式的老师。',
      cases: [
        '帮助一位注意力不集中的学生通过系统训练，显著提高学习成绩',
        '为一位记忆力差的高中生定制记忆力提升方案，成功应对高考挑战',
        '指导一位学习方法不当的初中生改变学习习惯，提高学习效率'
      ]
    },
    {
      id: 6,
      name: '刘顾问',
      title: '家庭教育指导师',
      specialties: ['亲子关系改善', '家庭沟通技巧', '教育理念指导'],
      imageUrl: '/team/member-6.jpg',
      bio: '刘顾问致力于家庭教育指导，帮助家长建立正确的教育理念，改善亲子关系。在家庭教育咨询领域有着丰富的实践经验和深刻的理论基础。',
      experience: '8年+',
      education: '北京大学家庭教育学硕士',
      achievements: ['家庭教育指导案例600+', '家长满意度99%', '家庭教育高级指导师'],
      availableTime: ['周二至周六 10:00-18:00', '周日 14:00-17:00'],
      detailedBio: '刘顾问毕业于北京大学，获得家庭教育学硕士学位。她在家庭教育咨询领域有8年的丰富经验，致力于帮助家长建立科学的教育理念，掌握有效的家庭教育方法。她擅长分析家庭教育中的问题，提供针对性的解决方案，帮助家长改善亲子关系，创造和谐的家庭教育环境。\n\n刘顾问的咨询风格温和而有力，她善于倾听家长的困惑，理解家庭的独特性，提供既有理论基础又切实可行的建议。她的家庭教育讲座和工作坊受到广泛好评，被认为是能够真正帮助家长解决实际问题的专业指导。',
      cases: [
        '帮助一个亲子关系紧张的家庭重建信任和沟通',
        '指导一位过度焦虑的妈妈调整教育方式，减轻孩子的学习压力',
        '为一个教育理念冲突的家庭找到平衡点，建立一致的教育方向'
      ]
    }
  ];

  // 根据ID查找团队成员
  const member = teamMembers.find(m => m.id === memberId) || teamMembers[0];

  return (
    <main className="min-h-screen">
      {/* 顾问详情头部 */}
      <section className="pt-24 pb-16 bg-gradient-to-r from-blue-600 to-sky-400 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-[url('/hero/team-bg.svg')] bg-center bg-no-repeat bg-cover opacity-20"></div>
        <div className="absolute inset-0 bg-blue-900/30"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col lg:flex-row items-center gap-10">
            <div className="w-full lg:w-1/3 flex justify-center">
              <div className="relative w-64 h-64 rounded-full overflow-hidden border-4 border-white shadow-xl">
                <Image 
                  src={member.imageUrl}
                  alt={member.name}
                  layout="fill"
                  objectFit="cover"
                  className="transition-transform duration-500 hover:scale-110"
                />
              </div>
            </div>
            <div className="w-full lg:w-2/3 text-center lg:text-left">
              <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white drop-shadow-sm">{member.name}</h1>
              <p className="text-xl text-sky-100 mb-6">{member.title}</p>
              <div className="flex flex-wrap gap-3 mb-8 justify-center lg:justify-start">
                {member.specialties.map(spec => (
                  <span key={spec} className="inline-block bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm">{spec}</span>
                ))}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sky-100 text-sm">工作经验</p>
                    <p className="font-medium">{member.experience}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center mr-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path d="M12 14l9-5-9-5-9 5 9 5z" />
                      <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sky-100 text-sm">教育背景</p>
                    <p className="font-medium">{member.education}</p>
                  </div>
                </div>
              </div>
              <Link 
                href={`/appointment/${member.id}`} 
                className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-4 rounded-full font-medium text-lg inline-block transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 focus:outline-none"
              >
                预约咨询
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline-block ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 顾问详细介绍 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-16">
              <h2 className="text-3xl font-bold mb-8 text-gray-800 relative inline-block">
                个人简介
                <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full"></span>
              </h2>
              <div className="prose prose-lg max-w-none text-gray-600">
                {member.detailedBio.split('\n\n').map((paragraph, index) => (
                  <p key={index} className="mb-4">{paragraph}</p>
                ))}
              </div>
            </div>

            <div className="mb-16">
              <h2 className="text-3xl font-bold mb-8 text-gray-800 relative inline-block">
                专业成就
                <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full"></span>
              </h2>
              <ul className="space-y-4">
                {member.achievements.map((achievement, index) => (
                  <li key={index} className="flex items-start">
                    <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-4 flex-shrink-0 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <span className="text-gray-700">{achievement}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div className="mb-16">
              <h2 className="text-3xl font-bold mb-8 text-gray-800 relative inline-block">
                成功案例
                <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full"></span>
              </h2>
              <div className="space-y-6">
                {member.cases.map((caseItem, index) => (
                  <div key={index} className="bg-blue-50 p-6 rounded-lg border-l-4 border-blue-500">
                    <p className="text-gray-700">{caseItem}</p>
                  </div>
                ))}
              </div>
            </div>

            <div className="mb-16">
              <h2 className="text-3xl font-bold mb-8 text-gray-800 relative inline-block">
                可预约时间
                <span className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 to-sky-400 rounded-full"></span>
              </h2>
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                <ul className="space-y-4">
                  {member.availableTime.map((time, index) => (
                    <li key={index} className="flex items-center">
                      <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 flex items-center justify-center mr-4 flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <span className="text-gray-700">{time}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <p className="text-gray-600 text-sm">* 以上为常规咨询时间，如需其他时间段，请在预约时备注说明</p>
                </div>
              </div>
            </div>

            <div className="text-center mt-12">
              <Link 
                href={`/appointment/${member.id}`} 
                className="bg-gradient-to-r from-blue-600 to-sky-500 text-white px-8 py-4 rounded-lg font-medium text-lg inline-block transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
              >
                立即预约咨询
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 inline-block ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
              <div className="mt-4">
                <Link href="/team" className="text-blue-600 hover:text-blue-800 font-medium inline-flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  返回团队页面
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}