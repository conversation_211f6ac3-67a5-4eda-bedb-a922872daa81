'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

export default function TeamPage() {
  // 团队成员数据
  const teamMembers = [
    {
      id: 1,
      name: '王老师',
      title: '首席生涯规划师',
      specialties: ['K12教育规划', '新高考选科', '大学专业选择'],
      imageUrl: '/team/member-1.jpg',
      bio: '王老师拥有超过15年的教育咨询经验，成功指导上千名学生进入理想的学府与职业道路。专注于K12教育规划和新高考选科指导，具有丰富的实战经验。',
      experience: '15年+',
      education: '北京师范大学教育学博士',
      achievements: ['指导学生超过1000名', '高考志愿填报成功率98%', '获得教育部优秀咨询师称号'],
      availableTime: ['周一至周五 9:00-18:00', '周六 9:00-17:00']
    },
    {
      id: 2,
      name: '李博士',
      title: '资深心理咨询师',
      specialties: ['青少年心理辅导', '家庭关系调适', '情绪压力管理'],
      imageUrl: '/team/member-2.jpg',
      bio: '李博士专注于青少年心理健康领域，以其深深的专业知识和丰富的实践经验，帮助众多家庭和学生走出困境。擅长青少年心理问题诊断和治疗。',
      experience: '12年+',
      education: '中科院心理研究所临床心理学博士',
      achievements: ['心理咨询案例超过800例', '发表学术论文20余篇', '国家二级心理咨询师'],
      availableTime: ['周二至周六 10:00-19:00', '周日 14:00-18:00']
    },
    {
      id: 3,
      name: '赵顾问',
      title: '职业发展规划专家',
      specialties: ['大学生就业指导', '职场进阶规划', '创业咨询'],
      imageUrl: '/team/member-3.jpg',
      bio: '赵顾问在人力资源和职业发展领域有独到见解，擅长为不同阶段的职场人士提供个性化的发展建议。曾任知名企业HR总监，具有丰富的企业管理经验。',
      experience: '18年+',
      education: '清华大学MBA，人力资源管理硕士',
      achievements: ['服务企业客户200+家', '职业规划成功案例1500+', 'GCDF全球职业规划师'],
      availableTime: ['周一至周五 14:00-20:00', '周六 9:00-18:00']
    },
    {
      id: 4,
      name: '张教授',
      title: '国际教育专家',
      specialties: ['海外留学规划', '国际课程指导', '语言能力提升'],
      imageUrl: '/team/member-4.jpg',
      bio: '张教授在国际教育领域深耕多年，对各国教育体系和申请流程了如指掌。曾在美国、英国多所知名大学担任招生官，为学生提供最权威的留学指导。',
      experience: '20年+',
      education: '哈佛大学教育学博士',
      achievements: ['成功送出留学生2000+名', '藤校录取率35%', '获得国际教育贡献奖'],
      availableTime: ['周一至周五 9:00-17:00', '周六 10:00-16:00']
    },
    {
      id: 5,
      name: '陈老师',
      title: '学习能力提升专家',
      specialties: ['学习方法指导', '注意力训练', '记忆力提升'],
      imageUrl: '/team/member-5.jpg',
      bio: '陈老师专注于学习能力提升和认知训练，运用科学的方法帮助学生提高学习效率。在注意力训练和记忆力提升方面有独特的教学方法。',
      experience: '10年+',
      education: '华东师范大学认知心理学硕士',
      achievements: ['学习能力提升案例500+', '开发专利学习方法3项', '出版学习指导书籍2本'],
      availableTime: ['周一至周五 15:00-19:00', '周日 9:00-17:00']
    },
    {
      id: 6,
      name: '刘顾问',
      title: '家庭教育指导师',
      specialties: ['亲子关系改善', '家庭沟通技巧', '教育理念指导'],
      imageUrl: '/team/member-6.jpg',
      bio: '刘顾问致力于家庭教育指导，帮助家长建立正确的教育理念，改善亲子关系。在家庭教育咨询领域有着丰富的实践经验和深刻的理论基础。',
      experience: '8年+',
      education: '北京大学家庭教育学硕士',
      achievements: ['家庭教育指导案例600+', '家长满意度99%', '家庭教育高级指导师'],
      availableTime: ['周二至周六 10:00-18:00', '周日 14:00-17:00']
    }
  ];

  return (
    <main className="min-h-screen">
      {/* 页面标题区域 */}
      <section className="py-24 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-sky-400 z-0">
          <div className="absolute inset-0 bg-[url('/hero/team-bg.svg')] bg-center bg-no-repeat bg-cover opacity-20"></div>
          <div className="absolute inset-0 bg-blue-900/30"></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 text-white drop-shadow-sm">专业团队</h1>
            <p className="text-xl text-sky-100 mb-10 leading-relaxed">
              汇聚行业顶尖专家，为您提供最专业、最贴心的教育与生涯规划服务
            </p>
            <Link 
              href="/appointment" 
              className="bg-white text-blue-700 hover:bg-blue-50 px-8 py-4 rounded-full font-medium text-lg inline-block transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-105 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600 focus:outline-none"
            >
              立即预约咨询
            </Link>
          </div>
        </div>
      </section>

      {/* 团队成员展示 */}
      <section className="py-20 bg-gradient-to-b from-white to-sky-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1 bg-sky-100 text-sky-700 rounded-full text-sm font-medium mb-4">专业团队</span>
            <h2 className="text-4xl font-bold mb-6 text-gray-800 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-sky-500">我们的专家团队</h2>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">每一位团队成员都具备深厚的专业背景和丰富的实践经验，致力于为您提供最优质的服务</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member) => (
              <div key={member.id} className="bg-white rounded-xl shadow-lg overflow-hidden group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-sky-100">
                <div className="relative h-64 w-full overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-blue-900/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10"></div>
                  <Image 
                    src={member.imageUrl}
                    alt={member.name}
                    layout="fill"
                    objectFit="cover"
                    className="transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-blue-600">
                    {member.experience}
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-xl font-bold mb-1 text-gray-800 group-hover:text-blue-600 transition-colors">{member.name}</h3>
                      <p className="text-blue-600 font-medium">{member.title}</p>
                    </div>
                    <div className="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center group-hover:bg-blue-600 transition-colors">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600 group-hover:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    </div>
                  </div>
                  
                  <div className="mb-4">
                    {member.specialties.map(spec => (
                      <span key={spec} className="inline-block bg-sky-50 text-sky-700 text-xs px-3 py-1.5 rounded-full mr-2 mb-2 border border-sky-100 group-hover:bg-sky-600 group-hover:text-white group-hover:border-sky-500 transition-colors">{spec}</span>
                    ))}
                  </div>
                  
                  <p className="text-gray-600 mb-4 text-sm line-clamp-3">{member.bio}</p>
                  
                  <div className="pt-4 border-t border-gray-100 space-y-3">
                    <Link 
                      href={`/team/${member.id}`}
                      className="w-full bg-gradient-to-r from-blue-600 to-sky-500 text-white px-4 py-2.5 rounded-lg font-medium transition-all duration-300 flex items-center justify-center group-hover:shadow-lg text-sm"
                    >
                      查看详情
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-2 group-hover:ml-3 transition-all duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                      </svg>
                    </Link>
                    <Link 
                      href={`/appointment/${member.id}`}
                      className="w-full bg-white border-2 border-blue-600 text-blue-600 px-4 py-2.5 rounded-lg font-medium transition-all duration-300 flex items-center justify-center hover:bg-blue-600 hover:text-white text-sm"
                    >
                      预约咨询
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 团队优势 */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-1 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-4">团队优势</span>
            <h2 className="text-4xl font-bold mb-6 text-gray-800 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-sky-500">为什么选择我们</h2>
            <p className="text-gray-600 max-w-3xl mx-auto text-lg">我们的团队具备以下核心优势，确保为您提供最专业的服务</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                ),
                title: '专业资质',
                description: '团队成员均具备相关专业资质和丰富实践经验'
              },
              {
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                ),
                title: '团队协作',
                description: '多学科背景专家协作，提供全方位专业服务'
              },
              {
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                  </svg>
                ),
                title: '成功案例',
                description: '累计服务客户超过5000名，成功率高达95%'
              },
              {
                icon: (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                ),
                title: '持续服务',
                description: '提供长期跟踪服务，确保规划方案有效实施'
              }
            ].map((advantage, index) => (
              <div key={index} className="text-center p-6 rounded-xl bg-sky-50 hover:bg-white hover:shadow-lg transition-all duration-300 group">
                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-sky-400 text-white flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  {advantage.icon}
                </div>
                <h3 className="text-xl font-bold mb-3 text-gray-800 group-hover:text-blue-600 transition-colors">{advantage.title}</h3>
                <p className="text-gray-600">{advantage.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 联系我们 */}
      <section className="py-20 bg-gradient-to-b from-sky-50 to-white">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">准备开始您的规划之旅？</h2>
            <p className="text-lg text-gray-600 mb-10">我们的专业团队随时为您提供个性化的咨询服务，让我们一起规划您的美好未来</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/appointment" 
                className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-blue-600 to-sky-500 text-white font-medium rounded-lg hover:from-blue-700 hover:to-sky-600 transition-all duration-300 shadow-md hover:shadow-lg transform hover:-translate-y-1 group"
              >
                立即预约咨询
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2 group-hover:ml-3 transition-all duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Link>
              <Link 
                href="/contact" 
                className="inline-flex items-center justify-center px-8 py-4 bg-white border-2 border-blue-600 text-blue-600 font-medium rounded-lg hover:bg-blue-600 hover:text-white transition-all duration-300 shadow-md hover:shadow-lg"
              >
                联系我们
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}