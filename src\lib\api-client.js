// 主网站的API客户端（无需认证）

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000/api';

// 通用的fetch包装器
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;

  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || '请求失败');
    }

    return data;
  } catch (error) {
    console.error('API请求失败:', error);
    throw error;
  }
}

// 获取已发布的文章列表
export async function getPublishedArticles(params = {}) {
  const searchParams = new URLSearchParams({
    status: 'published',
    ...params
  });

  return apiRequest(`/articles?${searchParams}`);
}

// 获取单篇文章
export async function getArticle(id) {
  return apiRequest(`/articles/${id}`);
}

// 获取文章分类
export async function getCategories() {
  return apiRequest('/categories?status=active');
}

// 获取服务列表
export async function getServices() {
  return apiRequest('/services?status=active');
}

// 获取单个服务
export async function getService(id) {
  return apiRequest(`/services/${id}`);
}

// 获取案例列表
export async function getCases() {
  return apiRequest('/cases?status=published');
}

// 获取单个案例
export async function getCase(id) {
  return apiRequest(`/cases/${id}`);
}

// 获取团队成员
export async function getTeamMembers() {
  return apiRequest('/team?status=active');
}

// 获取FAQ列表
export async function getFAQs() {
  return apiRequest('/faqs?status=active');
}

// 获取Banner列表
export async function getBanners() {
  return apiRequest('/banners?status=active');
}

// 获取系统设置
export async function getSettings() {
  return apiRequest('/settings');
}

// 提交联系表单
export async function submitContact(data) {
  return apiRequest('/contact', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 提交咨询表单
export async function submitInquiry(data) {
  return apiRequest('/inquiries', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 获取咨询师列表
export async function getConsultants(params = {}) {
  const searchParams = new URLSearchParams({
    status: 'active',
    ...params
  });

  return apiRequest(`/consultants?${searchParams}`);
}

// 获取单个咨询师
export async function getConsultant(id) {
  return apiRequest(`/consultants/${id}`);
}

// 获取咨询师可用时间
export async function getConsultantAvailability(id, params = {}) {
  const searchParams = new URLSearchParams(params);
  return apiRequest(`/consultants/${id}/availability?${searchParams}`);
}

// 提交预约申请
export async function submitAppointment(data) {
  return apiRequest('/appointments', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

// 获取预约列表（用户查看自己的预约）
export async function getAppointments(params = {}) {
  const searchParams = new URLSearchParams(params);
  return apiRequest(`/appointments?${searchParams}`);
}

// 获取单个预约详情
export async function getAppointment(id) {
  return apiRequest(`/appointments/${id}`);
}
