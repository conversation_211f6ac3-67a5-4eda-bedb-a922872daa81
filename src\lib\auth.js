import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { getDatabase } from './database.js';

const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret';

// 生成JWT令牌
export function generateToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '24h' });
}

// 验证JWT令牌
export function verifyToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    throw new Error('Invalid token');
  }
}

// 从请求头中提取令牌
export function extractTokenFromHeader(authHeader) {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
}

// 验证用户身份
export async function authenticateUser(username, password) {
  const db = await getDatabase();
  
  try {
    // 查找用户
    const user = await db.get(
      'SELECT * FROM users WHERE username = ? AND status = "active"',
      [username]
    );

    if (!user) {
      throw new Error('用户名或密码错误');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      throw new Error('用户名或密码错误');
    }

    // 返回用户信息（不包含密码）
    const { password_hash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  } catch (error) {
    throw error;
  }
}

// 根据ID获取用户信息
export async function getUserById(userId) {
  const db = await getDatabase();
  
  try {
    const user = await db.get(
      'SELECT id, username, email, name, role, avatar_url, phone, position, bio, status, created_at, updated_at FROM users WHERE id = ? AND status = "active"',
      [userId]
    );

    return user;
  } catch (error) {
    throw error;
  }
}

// 中间件：验证请求的认证状态
export async function requireAuth(request) {
  const authHeader = request.headers.get('authorization');
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    throw new Error('未提供认证令牌');
  }

  try {
    const decoded = verifyToken(token);
    const user = await getUserById(decoded.id);
    
    if (!user) {
      throw new Error('用户不存在或已被禁用');
    }

    return user;
  } catch (error) {
    throw new Error('认证失败: ' + error.message);
  }
}

// 中间件：验证管理员权限
export async function requireAdmin(request) {
  const user = await requireAuth(request);
  
  if (user.role !== 'admin') {
    throw new Error('需要管理员权限');
  }

  return user;
}

// 中间件：验证编辑权限（管理员或编辑）
export async function requireEditor(request) {
  const user = await requireAuth(request);
  
  if (!['admin', 'editor'].includes(user.role)) {
    throw new Error('需要编辑权限');
  }

  return user;
}

// 记录活动日志
export async function logActivity(userId, action, module, details = null, level = 'info', request = null) {
  const db = await getDatabase();
  
  try {
    let ipAddress = null;
    let userAgent = null;

    if (request) {
      // 尝试获取真实IP地址
      ipAddress = request.headers.get('x-forwarded-for') || 
                  request.headers.get('x-real-ip') || 
                  'unknown';
      userAgent = request.headers.get('user-agent') || 'unknown';
    }

    await db.run(`
      INSERT INTO activity_logs (user_id, action, module, level, ip_address, user_agent, details)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [
      userId,
      action,
      module,
      level,
      ipAddress,
      userAgent,
      details ? JSON.stringify(details) : null
    ]);
  } catch (error) {
    console.error('记录活动日志失败:', error);
    // 不抛出错误，避免影响主要功能
  }
}

// 哈希密码
export async function hashPassword(password) {
  return await bcrypt.hash(password, 10);
}

// 验证密码强度
export function validatePassword(password) {
  if (!password || password.length < 6) {
    throw new Error('密码长度至少为6位');
  }
  
  // 可以添加更多密码强度验证规则
  return true;
}

// 验证邮箱格式
export function validateEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new Error('邮箱格式不正确');
  }
  return true;
}

// 生成随机密码
export function generateRandomPassword(length = 8) {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let password = '';
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return password;
}
