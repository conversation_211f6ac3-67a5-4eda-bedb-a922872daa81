import fs from 'fs/promises';
import path from 'path';
import bcrypt from 'bcryptjs';

const DB_DIR = './data';
const DB_FILES = {
  users: path.join(DB_DIR, 'users.json'),
  articles: path.join(DB_DIR, 'articles.json'),
  categories: path.join(DB_DIR, 'categories.json'),
  services: path.join(DB_DIR, 'services.json'),
  cases: path.join(DB_DIR, 'cases.json'),
  faqs: path.join(DB_DIR, 'faqs.json'),
  team: path.join(DB_DIR, 'team.json'),
  banners: path.join(DB_DIR, 'banners.json'),
  inquiries: path.join(DB_DIR, 'inquiries.json'),
  settings: path.join(DB_DIR, 'settings.json'),
  logs: path.join(DB_DIR, 'logs.json'),
  consultants: path.join(DB_DIR, 'consultants.json'),
  appointments: path.join(DB_DIR, 'appointments.json')
};

let dbCache = {};

// 初始化数据库
export async function initDatabase() {
  try {
    // 创建数据目录
    await fs.mkdir(DB_DIR, { recursive: true });

    // 初始化所有数据文件
    for (const [table, filePath] of Object.entries(DB_FILES)) {
      try {
        await fs.access(filePath);
      } catch {
        // 文件不存在，创建空数组
        await fs.writeFile(filePath, JSON.stringify([], null, 2));
      }
    }

    // 插入初始数据
    await insertInitialData();

    console.log('数据库初始化成功');
    return true;
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

// 获取数据库实例
export async function getDatabase() {
  await initDatabase();
  return {
    get: async (table, id) => {
      const data = await readTable(table);
      return data.find(item => item.id === id);
    },
    getAll: async (table) => {
      return await readTable(table);
    },
    insert: async (table, item) => {
      const data = await readTable(table);
      const newId = data.length > 0 ? Math.max(...data.map(i => i.id)) + 1 : 1;
      const newItem = {
        ...item,
        id: newId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      data.push(newItem);
      await writeTable(table, data);
      return newItem;
    },
    update: async (table, id, updates) => {
      const data = await readTable(table);
      const index = data.findIndex(item => item.id === id);
      if (index === -1) throw new Error('记录不存在');

      data[index] = {
        ...data[index],
        ...updates,
        updated_at: new Date().toISOString()
      };
      await writeTable(table, data);
      return data[index];
    },
    delete: async (table, id) => {
      const data = await readTable(table);
      const index = data.findIndex(item => item.id === id);
      if (index === -1) throw new Error('记录不存在');

      const deleted = data.splice(index, 1)[0];
      await writeTable(table, data);
      return deleted;
    },
    query: async (table, conditions = {}) => {
      const data = await readTable(table);
      return data.filter(item => {
        return Object.entries(conditions).every(([key, value]) => {
          if (typeof value === 'string' && value.includes('%')) {
            // 简单的LIKE查询
            const pattern = value.replace(/%/g, '');
            return item[key] && item[key].toString().toLowerCase().includes(pattern.toLowerCase());
          }
          return item[key] === value;
        });
      });
    },
    count: async (table, conditions = {}) => {
      const data = await readTable(table);
      return data.filter(item => {
        return Object.entries(conditions).every(([key, value]) => {
          if (typeof value === 'string' && value.includes('%')) {
            // 简单的LIKE查询
            const pattern = value.replace(/%/g, '');
            return item[key] && item[key].toString().toLowerCase().includes(pattern.toLowerCase());
          }
          return item[key] === value;
        });
      }).length;
    }
  };
}

// 读取表数据
async function readTable(table) {
  const filePath = DB_FILES[table];
  if (!filePath) throw new Error(`未知的表: ${table}`);

  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`读取表 ${table} 失败:`, error);
    return [];
  }
}

// 写入表数据
async function writeTable(table, data) {
  const filePath = DB_FILES[table];
  if (!filePath) throw new Error(`未知的表: ${table}`);

  try {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    // 清除缓存
    delete dbCache[table];
  } catch (error) {
    console.error(`写入表 ${table} 失败:`, error);
    throw error;
  }
}

// 插入初始数据
async function insertInitialData() {
  try {
    // 检查是否已有用户数据
    const users = await readTable('users');
    if (users.length > 0) {
      return; // 已有数据，跳过初始化
    }

    // 创建默认管理员用户
    const adminPasswordHash = await bcrypt.hash('admin123', 10);
    const editorPasswordHash = await bcrypt.hash('editor123', 10);

    const defaultUsers = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        password_hash: adminPasswordHash,
        name: '系统管理员',
        role: 'admin',
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        username: 'editor',
        email: '<EMAIL>',
        password_hash: editorPasswordHash,
        name: '内容编辑',
        role: 'editor',
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    await writeTable('users', defaultUsers);

    // 插入默认文章分类
    const defaultCategories = [
      {
        id: 1,
        name: '教育规划',
        slug: 'education-planning',
        description: '关于教育规划的文章',
        sort_order: 1,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        name: '职业发展',
        slug: 'career-development',
        description: '职业发展相关内容',
        sort_order: 2,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 3,
        name: '留学指导',
        slug: 'study-abroad',
        description: '留学申请和指导',
        sort_order: 3,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 4,
        name: '行业资讯',
        slug: 'industry-news',
        description: '教育行业最新资讯',
        sort_order: 4,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    await writeTable('categories', defaultCategories);

    // 插入默认系统设置
    const defaultSettings = [{
      id: 1,
      settings: JSON.stringify({
        siteName: '武汉思立恒教育科技有限公司',
        siteDescription: '您的教育与生涯导航专家',
        contactEmail: '<EMAIL>',
        contactPhone: '027-12345678',
        address: '武汉市洪山区光谷大道123号',
        icp: '鄂ICP备12345678号',
        seoKeywords: '教育规划,生涯规划,升学规划,高考志愿填报,职业规划',
        seoDescription: '武汉思立恒教育科技有限公司提供专业的教育规划与生涯导航服务',
        logoUrl: '/images/logo.png',
        faviconUrl: '/favicon.ico',
        footerCopyright: '© 2024 武汉思立恒教育科技有限公司 版权所有',
        socialMedia: {
          weixin: 'slhgw_weixin',
          weibo: 'slhgw_weibo',
          zhihu: 'slhgw_zhihu'
        }
      }),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }];

    await writeTable('settings', defaultSettings);

    // 插入默认咨询师
    const defaultConsultants = [
      {
        id: 1,
        name: '张教授',
        email: '<EMAIL>',
        phone: '13800138001',
        specialty: '教育规划,升学指导',
        experience_years: 15,
        education: '华中师范大学教育学博士',
        certifications: JSON.stringify(['国家二级心理咨询师', '生涯规划师', '高级教育咨询师']),
        bio: '从事教育咨询工作15年，专注于中小学生教育规划和升学指导，帮助数千名学生成功规划学业和职业发展道路。',
        avatar_url: '/images/consultants/zhang.jpg',
        available_hours: JSON.stringify({
          monday: [
            { start: '09:00', end: '12:00' },
            { start: '14:00', end: '17:00' }
          ],
          tuesday: [
            { start: '09:00', end: '12:00' },
            { start: '14:00', end: '17:00' }
          ],
          wednesday: [
            { start: '09:00', end: '12:00' },
            { start: '14:00', end: '17:00' }
          ],
          thursday: [
            { start: '09:00', end: '12:00' },
            { start: '14:00', end: '17:00' }
          ],
          friday: [
            { start: '09:00', end: '12:00' },
            { start: '14:00', end: '17:00' }
          ],
          saturday: [
            { start: '09:00', end: '12:00' }
          ],
          sunday: []
        }),
        hourly_rate: 300,
        languages: JSON.stringify(['中文', '英文']),
        status: 'active',
        rating: 4.8,
        total_appointments: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        name: '李老师',
        email: '<EMAIL>',
        phone: '13800138002',
        specialty: '职业规划,留学咨询',
        experience_years: 10,
        education: '武汉大学心理学硕士',
        certifications: JSON.stringify(['国家二级心理咨询师', '职业规划师', '留学咨询师']),
        bio: '专注于大学生职业规划和留学申请指导，具有丰富的海外教育背景和咨询经验。',
        avatar_url: '/images/consultants/li.jpg',
        available_hours: JSON.stringify({
          monday: [
            { start: '10:00', end: '12:00' },
            { start: '15:00', end: '18:00' }
          ],
          tuesday: [
            { start: '10:00', end: '12:00' },
            { start: '15:00', end: '18:00' }
          ],
          wednesday: [
            { start: '10:00', end: '12:00' },
            { start: '15:00', end: '18:00' }
          ],
          thursday: [
            { start: '10:00', end: '12:00' },
            { start: '15:00', end: '18:00' }
          ],
          friday: [
            { start: '10:00', end: '12:00' },
            { start: '15:00', end: '18:00' }
          ],
          saturday: [
            { start: '14:00', end: '17:00' }
          ],
          sunday: []
        }),
        hourly_rate: 250,
        languages: JSON.stringify(['中文', '英文']),
        status: 'active',
        rating: 4.9,
        total_appointments: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 3,
        name: '王博士',
        email: '<EMAIL>',
        phone: '13800138003',
        specialty: '心理咨询,学习能力提升',
        experience_years: 12,
        education: '华中科技大学心理学博士',
        certifications: JSON.stringify(['国家一级心理咨询师', '学习能力指导师', '家庭教育指导师']),
        bio: '专业心理咨询师，擅长青少年心理健康和学习能力提升，帮助学生建立良好的学习习惯和心理状态。',
        avatar_url: '/images/consultants/wang.jpg',
        available_hours: JSON.stringify({
          monday: [
            { start: '08:30', end: '11:30' },
            { start: '13:30', end: '16:30' }
          ],
          tuesday: [
            { start: '08:30', end: '11:30' },
            { start: '13:30', end: '16:30' }
          ],
          wednesday: [
            { start: '08:30', end: '11:30' },
            { start: '13:30', end: '16:30' }
          ],
          thursday: [
            { start: '08:30', end: '11:30' },
            { start: '13:30', end: '16:30' }
          ],
          friday: [
            { start: '08:30', end: '11:30' },
            { start: '13:30', end: '16:30' }
          ],
          saturday: [
            { start: '09:00', end: '12:00' },
            { start: '14:00', end: '17:00' }
          ],
          sunday: [
            { start: '14:00', end: '17:00' }
          ]
        }),
        hourly_rate: 350,
        languages: JSON.stringify(['中文']),
        status: 'active',
        rating: 4.7,
        total_appointments: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    await writeTable('consultants', defaultConsultants);

    console.log('初始数据插入完成');
  } catch (error) {
    console.error('插入初始数据失败:', error);
    throw error;
  }
}


