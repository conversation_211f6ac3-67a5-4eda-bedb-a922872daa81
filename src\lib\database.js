import fs from 'fs/promises';
import path from 'path';
import bcrypt from 'bcryptjs';

const DB_DIR = './data';
const DB_FILES = {
  users: path.join(DB_DIR, 'users.json'),
  articles: path.join(DB_DIR, 'articles.json'),
  categories: path.join(DB_DIR, 'categories.json'),
  services: path.join(DB_DIR, 'services.json'),
  cases: path.join(DB_DIR, 'cases.json'),
  faqs: path.join(DB_DIR, 'faqs.json'),
  team: path.join(DB_DIR, 'team.json'),
  banners: path.join(DB_DIR, 'banners.json'),
  inquiries: path.join(DB_DIR, 'inquiries.json'),
  settings: path.join(DB_DIR, 'settings.json'),
  logs: path.join(DB_DIR, 'logs.json')
};

let dbCache = {};

// 初始化数据库
export async function initDatabase() {
  try {
    // 创建数据目录
    await fs.mkdir(DB_DIR, { recursive: true });

    // 初始化所有数据文件
    for (const [table, filePath] of Object.entries(DB_FILES)) {
      try {
        await fs.access(filePath);
      } catch {
        // 文件不存在，创建空数组
        await fs.writeFile(filePath, JSON.stringify([], null, 2));
      }
    }

    // 插入初始数据
    await insertInitialData();

    console.log('数据库初始化成功');
    return true;
  } catch (error) {
    console.error('数据库初始化失败:', error);
    throw error;
  }
}

// 获取数据库实例
export async function getDatabase() {
  await initDatabase();
  return {
    get: async (table, id) => {
      const data = await readTable(table);
      return data.find(item => item.id === id);
    },
    getAll: async (table) => {
      return await readTable(table);
    },
    insert: async (table, item) => {
      const data = await readTable(table);
      const newId = data.length > 0 ? Math.max(...data.map(i => i.id)) + 1 : 1;
      const newItem = {
        ...item,
        id: newId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      data.push(newItem);
      await writeTable(table, data);
      return newItem;
    },
    update: async (table, id, updates) => {
      const data = await readTable(table);
      const index = data.findIndex(item => item.id === id);
      if (index === -1) throw new Error('记录不存在');

      data[index] = {
        ...data[index],
        ...updates,
        updated_at: new Date().toISOString()
      };
      await writeTable(table, data);
      return data[index];
    },
    delete: async (table, id) => {
      const data = await readTable(table);
      const index = data.findIndex(item => item.id === id);
      if (index === -1) throw new Error('记录不存在');

      const deleted = data.splice(index, 1)[0];
      await writeTable(table, data);
      return deleted;
    },
    query: async (table, conditions = {}) => {
      const data = await readTable(table);
      return data.filter(item => {
        return Object.entries(conditions).every(([key, value]) => {
          if (typeof value === 'string' && value.includes('%')) {
            // 简单的LIKE查询
            const pattern = value.replace(/%/g, '');
            return item[key] && item[key].toString().toLowerCase().includes(pattern.toLowerCase());
          }
          return item[key] === value;
        });
      });
    },
    count: async (table, conditions = {}) => {
      const results = await this.query(table, conditions);
      return results.length;
    }
  };
}

// 读取表数据
async function readTable(table) {
  const filePath = DB_FILES[table];
  if (!filePath) throw new Error(`未知的表: ${table}`);

  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`读取表 ${table} 失败:`, error);
    return [];
  }
}

// 写入表数据
async function writeTable(table, data) {
  const filePath = DB_FILES[table];
  if (!filePath) throw new Error(`未知的表: ${table}`);

  try {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    // 清除缓存
    delete dbCache[table];
  } catch (error) {
    console.error(`写入表 ${table} 失败:`, error);
    throw error;
  }
}

// 插入初始数据
async function insertInitialData() {
  try {
    // 检查是否已有用户数据
    const users = await readTable('users');
    if (users.length > 0) {
      return; // 已有数据，跳过初始化
    }

    // 创建默认管理员用户
    const adminPasswordHash = await bcrypt.hash('admin123', 10);
    const editorPasswordHash = await bcrypt.hash('editor123', 10);

    const defaultUsers = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        password_hash: adminPasswordHash,
        name: '系统管理员',
        role: 'admin',
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        username: 'editor',
        email: '<EMAIL>',
        password_hash: editorPasswordHash,
        name: '内容编辑',
        role: 'editor',
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    await writeTable('users', defaultUsers);

    // 插入默认文章分类
    const defaultCategories = [
      {
        id: 1,
        name: '教育规划',
        slug: 'education-planning',
        description: '关于教育规划的文章',
        sort_order: 1,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 2,
        name: '职业发展',
        slug: 'career-development',
        description: '职业发展相关内容',
        sort_order: 2,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 3,
        name: '留学指导',
        slug: 'study-abroad',
        description: '留学申请和指导',
        sort_order: 3,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: 4,
        name: '行业资讯',
        slug: 'industry-news',
        description: '教育行业最新资讯',
        sort_order: 4,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    await writeTable('categories', defaultCategories);

    // 插入默认系统设置
    const defaultSettings = [{
      id: 1,
      settings: JSON.stringify({
        siteName: '武汉思立恒教育科技有限公司',
        siteDescription: '您的教育与生涯导航专家',
        contactEmail: '<EMAIL>',
        contactPhone: '027-12345678',
        address: '武汉市洪山区光谷大道123号',
        icp: '鄂ICP备12345678号',
        seoKeywords: '教育规划,生涯规划,升学规划,高考志愿填报,职业规划',
        seoDescription: '武汉思立恒教育科技有限公司提供专业的教育规划与生涯导航服务',
        logoUrl: '/images/logo.png',
        faviconUrl: '/favicon.ico',
        footerCopyright: '© 2024 武汉思立恒教育科技有限公司 版权所有',
        socialMedia: {
          weixin: 'slhgw_weixin',
          weibo: 'slhgw_weibo',
          zhihu: 'slhgw_zhihu'
        }
      }),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }];

    await writeTable('settings', defaultSettings);

    console.log('初始数据插入完成');
  } catch (error) {
    console.error('插入初始数据失败:', error);
    throw error;
  }
}


