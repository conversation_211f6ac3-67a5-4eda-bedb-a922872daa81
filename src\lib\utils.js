// API响应工具函数

// 成功响应
export function successResponse(data, message = 'Success') {
  return Response.json({
    success: true,
    message,
    data
  }, { status: 200 });
}

// 错误响应
export function errorResponse(message, status = 400, details = null) {
  return Response.json({
    success: false,
    message,
    details
  }, { status });
}

// 分页响应
export function paginatedResponse(items, total, page, limit, message = 'Success') {
  const totalPages = Math.ceil(total / limit);

  return Response.json({
    success: true,
    message,
    data: {
      items,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    }
  }, { status: 200 });
}

// 验证必填字段
export function validateRequiredFields(data, requiredFields) {
  const missingFields = [];

  for (const field of requiredFields) {
    if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
      missingFields.push(field);
    }
  }

  if (missingFields.length > 0) {
    throw new Error(`缺少必填字段: ${missingFields.join(', ')}`);
  }
}

// 验证分页参数
export function validatePaginationParams(searchParams) {
  const page = Math.max(1, parseInt(searchParams.get('page')) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit')) || 10));
  const offset = (page - 1) * limit;

  return { page, limit, offset };
}

// 验证排序参数
export function validateSortParams(searchParams, allowedFields = []) {
  const sortBy = searchParams.get('sortBy') || 'created_at';
  const sortOrder = searchParams.get('sortOrder') || 'desc';

  // 验证排序字段
  if (allowedFields.length > 0 && !allowedFields.includes(sortBy)) {
    throw new Error(`无效的排序字段: ${sortBy}`);
  }

  // 验证排序方向
  if (!['asc', 'desc'].includes(sortOrder.toLowerCase())) {
    throw new Error(`无效的排序方向: ${sortOrder}`);
  }

  return { sortBy, sortOrder: sortOrder.toLowerCase() };
}

// 生成唯一的slug
export function generateSlug(title, existingSlugs = []) {
  let slug = title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .replace(/-+/g, '-') // 多个连字符合并为一个
    .trim('-'); // 移除首尾连字符

  // 如果slug已存在，添加数字后缀
  let counter = 1;
  let uniqueSlug = slug;
  while (existingSlugs.includes(uniqueSlug)) {
    uniqueSlug = `${slug}-${counter}`;
    counter++;
  }

  return uniqueSlug;
}

// 清理HTML内容
export function sanitizeHtml(html) {
  if (!html) return '';

  // 基本的HTML清理，移除潜在危险的标签和属性
  return html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
    .replace(/on\w+="[^"]*"/gi, '') // 移除事件处理器
    .replace(/javascript:/gi, ''); // 移除javascript:协议
}

// 格式化日期
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return null;

  const d = new Date(date);
  if (isNaN(d.getTime())) return null;

  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');

  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

// 验证URL格式
export function validateUrl(url) {
  if (!url) return true; // 允许空值

  try {
    new URL(url);
    return true;
  } catch {
    throw new Error('URL格式不正确');
  }
}

// 验证邮箱格式
export function validateEmail(email) {
  if (!email) return true; // 允许空值

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new Error('邮箱格式不正确');
  }
  return true;
}

// 验证手机号格式（中国大陆）
export function validatePhone(phone) {
  if (!phone) return true; // 允许空值

  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(phone)) {
    throw new Error('手机号格式不正确');
  }
  return true;
}

// 处理文件上传路径
export function processImageUrl(url) {
  if (!url) return null;

  // 如果是相对路径，确保以/开头
  if (!url.startsWith('http') && !url.startsWith('/')) {
    return '/' + url;
  }

  return url;
}

// 生成摘要
export function generateSummary(content, maxLength = 200) {
  if (!content) return '';

  // 移除HTML标签
  const textContent = content.replace(/<[^>]*>/g, '');

  if (textContent.length <= maxLength) {
    return textContent;
  }

  return textContent.substring(0, maxLength).trim() + '...';
}

// 验证状态值
export function validateStatus(status, allowedStatuses) {
  if (!allowedStatuses.includes(status)) {
    throw new Error(`无效的状态值: ${status}。允许的值: ${allowedStatuses.join(', ')}`);
  }
  return true;
}

// 处理搜索关键词
export function processSearchKeyword(keyword) {
  if (!keyword) return '';

  return keyword.trim().replace(/[%_]/g, '\\$&'); // 转义SQL通配符
}

// 构建搜索条件
export function buildSearchCondition(keyword, searchFields) {
  if (!keyword || !searchFields.length) {
    return { condition: '', params: [] };
  }

  const processedKeyword = `%${processSearchKeyword(keyword)}%`;
  const conditions = searchFields.map(field => `${field} LIKE ?`);
  const params = new Array(searchFields.length).fill(processedKeyword);

  return {
    condition: `(${conditions.join(' OR ')})`,
    params
  };
}

// 错误处理包装器
export function withErrorHandling(handler) {
  return async (request, context) => {
    try {
      return await handler(request, context);
    } catch (error) {
      console.error('API错误:', error);

      // 根据错误类型返回不同的状态码
      if (error.message.includes('认证') || error.message.includes('权限')) {
        return errorResponse(error.message, 401);
      }

      if (error.message.includes('不存在') || error.message.includes('未找到')) {
        return errorResponse(error.message, 404);
      }

      if (error.message.includes('已存在') || error.message.includes('重复')) {
        return errorResponse(error.message, 409);
      }

      return errorResponse(error.message || '服务器内部错误', 500);
    }
  };
}

// 数据库事务包装器
export async function withTransaction(db, callback) {
  await db.run('BEGIN TRANSACTION');

  try {
    const result = await callback(db);
    await db.run('COMMIT');
    return result;
  } catch (error) {
    await db.run('ROLLBACK');
    throw error;
  }
}
