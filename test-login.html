<!DOCTYPE html>
<html>
<head>
    <title>测试登录API</title>
</head>
<body>
    <h1>测试登录API</h1>
    <button onclick="testLogin()">测试登录</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            try {
                console.log('开始测试登录...');
                const response = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                console.log('响应状态:', response.status);
                const data = await response.json();
                console.log('响应数据:', data);

                document.getElementById('result').innerHTML =
                    '<h3>响应状态: ' + response.status + '</h3>' +
                    '<h3>响应数据:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                console.error('登录测试错误:', error);
                document.getElementById('result').innerHTML =
                    '<h3>错误:</h3><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
